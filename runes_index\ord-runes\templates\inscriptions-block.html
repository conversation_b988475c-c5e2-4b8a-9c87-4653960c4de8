<h1>Inscriptions in <a href=/block/{{ &self.block }}>Block {{ &self.block }}</a></h1>
<div class=thumbnails>
%% for id in &self.inscriptions {
  {{ Iframe::thumbnail(*id) }}
%% }
</div>
<div class=center>
%% if let Some(prev_block) = &self.prev_block {
  <a class=prev href=/inscriptions/block/{{ prev_block }}>{{ prev_block }}</a>
&bull;
%% }
%% if let Some(prev_page) = &self.prev_page {
  <a class=prev href=/inscriptions/block/{{ &self.block }}/{{ prev_page }}>prev</a>
%% } else {
prev
%% }
%% if let Some(next_page) = &self.next_page {
  <a class=next href=/inscriptions/block/{{ &self.block }}/{{ next_page }}>next</a>
%% } else {
next
%% }
%% if let Some(next_block) = &self.next_block {
&bull;
  <a class=next href=/inscriptions/block/{{ next_block }}>{{ next_block }}</a>
%% }
</div>
