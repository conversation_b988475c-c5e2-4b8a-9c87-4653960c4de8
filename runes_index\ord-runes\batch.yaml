# example batch file

# inscription modes:
# - `same-sat`: inscribe on the same sat
# - `satpoints`: inscribe on the first sat of specified satpoint's output
# - `separate-outputs`: inscribe on separate postage-sized outputs
# - `shared-output`: inscribe on a single output separated by postage
mode: separate-outputs

# parent inscription:
parent: 6ac5cacb768794f4fd7a78bf00f2074891fce68bd65c4ff36e77177237aacacai0

# postage for each inscription:
postage: 12345

# allow reinscribing
reinscribe: true

# sat to inscribe on, can only be used with `same-sat`:
# sat: 5000000000

# rune to etch (optional)
etching:
  # rune name
  rune: THE•BEST•RUNE
  # allow subdividing super-unit into `10^divisibility` sub-units
  divisibility: 2
  # premine
  premine: 1000.00
  # total supply, must be equal to `premine + terms.cap * terms.amount`
  supply: 10000.00
  # currency symbol
  symbol: $
  # mint terms (optional)
  terms:
    # amount per mint
    amount: 100.00
    # maximum number of mints
    cap: 90
    # mint start and end absolute block height (optional)
    height:
      start: 840000
      end: 850000
    # mint start and end block height relative to etching height (optional)
    offset:
      start: 1000
      end: 9000
  # future runes protocol changes may be opt-in. this may be for a variety of
  # reasons, including that they make light client validation harder, or simply
  # because they are too degenerate.
  #
  # setting `turbo` to `true` opts in to these future protocol changes,
  # whatever they may be.
  turbo: true

# inscriptions to inscribe
inscriptions:
  # path to inscription content
- file: mango.avif
  # inscription to delegate content to (optional)
  delegate: 6ac5cacb768794f4fd7a78bf00f2074891fce68bd65c4ff36e77177237aacacai0
  # destination (optional, if no destination is specified a new wallet change address will be used)
  destination: ******************************************
  # inscription metadata (optional)
  metadata:
    title: Delicious Mangos
    description: >
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam semper,
      ligula ornare laoreet tincidunt, odio nisi euismod tortor, vel blandit
      metus est et odio. Nullam venenatis, urna et molestie vestibulum, orci
      mi efficitur risus, eu malesuada diam lorem sed velit. Nam fermentum
      dolor et luctus euismod.

- file: token.json
  # inscription metaprotocol (optional)
  metaprotocol: DOPEPROTOCOL-42069

- file: tulip.png
  destination: **************************************************************
  metadata:
    author: Satoshi Nakamoto
