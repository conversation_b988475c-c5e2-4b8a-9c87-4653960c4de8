require('dotenv').config();
var express = require('express');
const { Pool } = require('pg')
var cors = require('cors')
const crypto = require('crypto');
const rateLimit = require('express-rate-limit');

// for self-signed cert of postgres
process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";

const EVENT_SEPARATOR = "|";

var db_pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_DATABASE || 'postgres',
  password: process.env.DB_PASSWD,
  port: parseInt(process.env.DB_PORT || "5432"),
  max: process.env.DB_MAX_CONNECTIONS || 10, // maximum number of clients!!
  ssl: process.env.DB_SSL == 'true' ? true : false
})

var use_extra_tables = process.env.USE_EXTRA_TABLES == 'true' ? true : false

const api_port = parseInt(process.env.API_PORT || "8000")
const api_host = process.env.API_HOST || '127.0.0.1'

const rate_limit_enabled = process.env.RATE_LIMIT_ENABLE || 'false'
const rate_limit_window_ms = process.env.RATE_LIMIT_WINDOW_MS || 15 * 60 * 1000
const rate_limit_max = process.env.RATE_LIMIT_MAX || 100

var app = express();
app.set('trust proxy', parseInt(process.env.API_TRUSTED_PROXY_CNT || "0"))

var corsOptions = {
  origin: '*',
  optionsSuccessStatus: 200 // some legacy browsers (IE11, various SmartTVs) choke on 204
}
app.use([cors(corsOptions)])

if (rate_limit_enabled === 'true') {
  const limiter = rateLimit({
    windowMs: rate_limit_window_ms,
    max: rate_limit_max,
    standardHeaders: true,
    legacyHeaders: false,
  })
  // Apply the delay middleware to all requests.
  app.use(limiter);
}

app.get('/v1/brc20/ip', (request, response) => response.send(request.ip))

async function query_db(query, params = []) {
  return await db_pool.query(query, params)
}

app.get('/v1/brc20/db_version', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let res = await query_db('SELECT db_version FROM brc20_indexer_version;')
    response.send(res.rows[0].db_version + '')
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
})

app.get('/v1/brc20/event_hash_version', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let res = await query_db('SELECT event_hash_version FROM brc20_indexer_version;')
    response.send(res.rows[0].event_hash_version + '')
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
})

async function get_block_height_of_db() {
  try {
    let res = await query_db('SELECT max(block_height) as max_block_height FROM brc20_block_hashes;')
    return res.rows[0].max_block_height
  } catch (err) {
    console.log(err)
    return -1
  }
}

async function get_extras_block_height_of_db() {
  try {
    let res = await query_db('SELECT max(block_height) as max_block_height FROM brc20_extras_block_hashes;')
    return res.rows[0].max_block_height
  } catch (err) {
    console.log(err)
    return -1
  }
}

app.get('/v1/brc20/extras_block_height', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let block_height = await get_extras_block_height_of_db()
    response.send(block_height + '')
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
})

app.get('/v1/brc20/block_height', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let block_height = await get_block_height_of_db()
    response.send(block_height + '')
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
})

// get a given ticker balance of a given pkscript at the start of a given block height
app.get('/v1/brc20/balance_on_block', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let block_height = request.query.block_height
    let address = request.query.address || ''
    let pkscript = request.query.pkscript || ''
    let tick = request.query.ticker.toLowerCase()

    let current_block_height = await get_block_height_of_db()
    if (block_height > current_block_height + 1) {
      response.status(400).send({ error: 'block not indexed yet', result: null })
      return
    }

    // 添加对钱包地址的支持，类似于 Runes API 的实现
    let pkscript_selector = 'pkscript'
    let pkscript_selector_value = pkscript
    if (address != '') {
      pkscript_selector = 'wallet'
      pkscript_selector_value = address
    }

    let query =  `select overall_balance, available_balance
                  from brc20_historic_balances
                  where block_height < $1
                    and ` + pkscript_selector + ` = $2
                    and tick = $3
                  order by id desc
                  limit 1;`
    let res = await query_db(query, [block_height, pkscript_selector_value, tick])
    if (res.rows.length == 0) {
      response.status(400).send({ error: 'no balance found', result: null })
      return
    }
    response.send({ error: null, result: res.rows[0] })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

// get all brc20 activity of a given block height
app.get('/v1/brc20/activity_on_block', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let block_height = request.query.block_height

    let current_block_height = await get_block_height_of_db()
    if (block_height > current_block_height) {
      response.status(400).send({ error: 'block not indexed yet', result: null })
      return
    }

    let res1 = await query_db('select event_type_name, event_type_id from brc20_event_types;')
    let event_type_id_to_name = {}
    res1.rows.forEach((row) => {
      event_type_id_to_name[row.event_type_id] = row.event_type_name
    })

    let query =  `select event, event_type, inscription_id
                  from brc20_events
                  where block_height = $1
                  order by id asc;`
    let res = await query_db(query, [block_height])
    let result = []
    for (const row of res.rows) {
      let event = row.event
      let event_type = event_type_id_to_name[row.event_type]
      let inscription_id = row.inscription_id
      event.event_type = event_type
      event.inscription_id = inscription_id
      result.push(event)
    }
    response.send({ error: null, result: result })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});


app.get('/v1/brc20/get_current_balance_of_wallet', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let address = request.query.address || ''
    let pkscript = request.query.pkscript || ''
    let tick = request.query.ticker.toLowerCase()

    let current_block_height = await get_block_height_of_db()
    let balance = null
    if (!use_extra_tables) {
      let query = ` select overall_balance, available_balance
                    from brc20_historic_balances
                    where pkscript = $1
                      and tick = $2
                    order by id desc
                    limit 1;`
      let params = [pkscript, tick]
      if (address != '') {
        query = query.replace('pkscript', 'wallet')
        params = [address, tick]
      }

      let res = await query_db(query, params)
      if (res.rows.length == 0) {
        response.status(400).send({ error: 'no balance found', result: null })
        return
      }
      balance = res.rows[0]
    } else {
      let query = ` select overall_balance, available_balance
                    from brc20_current_balances
                    where pkscript = $1
                      and tick = $2
                    limit 1;`
      let params = [pkscript, tick]
      if (address != '') {
        query = query.replace('pkscript', 'wallet')
        params = [address, tick]
      }

      let res = await query_db(query, params)
      if (res.rows.length == 0) {
        response.status(400).send({ error: 'no balance found', result: null })
        return
      }
      balance = res.rows[0]
    }

    balance.block_height = current_block_height
    response.send({ error: null, result: balance })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

app.get('/v1/brc20/get_valid_tx_notes_of_wallet', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    if (!use_extra_tables) {
      response.status(400).send({ error: 'not supported', result: null })
      return
    }

    let address = request.query.address || ''
    let pkscript = request.query.pkscript || ''

    let current_block_height = await get_block_height_of_db()
    let query = ` select tick, inscription_id, amount, block_height as genesis_height
                  from brc20_unused_tx_inscrs
                  where current_holder_pkscript = $1
                  order by tick asc;`
    let params = [pkscript]
    if (address != '') {
      query = query.replace('pkscript', 'wallet')
      params = [address]
    }

    let res = await query_db(query, params)
    if (res.rows.length == 0) {
      response.status(400).send({ error: 'no unused tx found', result: null })
      return
    }
    let result = {
      unused_txes: res.rows,
      block_height: current_block_height
    }

    response.send({ error: null, result: result })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

app.get('/v1/brc20/get_valid_tx_notes_of_ticker', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    if (!use_extra_tables) {
      response.status(400).send({ error: 'not supported', result: null })
      return
    }

    let tick = request.query.ticker.toLowerCase() || ''

    let current_block_height = await get_block_height_of_db()
    let query = ` select current_holder_pkscript, current_holder_wallet, inscription_id, amount, block_height as genesis_height
                  from brc20_unused_tx_inscrs
                  where tick = $1
                  order by current_holder_pkscript asc;`
    let params = [tick]

    let res = await query_db(query, params)
    if (res.rows.length == 0) {
      response.status(400).send({ error: 'no unused tx found', result: null })
      return
    }
    let result = {
      unused_txes: res.rows,
      block_height: current_block_height
    }

    response.send({ error: null, result: result })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

app.get('/v1/brc20/holders', async (request, response) => {
  try {
    //console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    if (!use_extra_tables) {
      response.status(400).send({ error: 'not supported', result: null })
      return
    }

    let tick = request.query.ticker.toLowerCase() || ''
    let limit = parseInt(request.query.limit || '0') // 0 表示不限制
    let simple = request.query.simple === 'true' // 简略输出模式，不输出 pkscript

    let current_block_height = await get_block_height_of_db()

    // 首先获取总数（只统计余额大于0的地址）
    let countQuery = `SELECT COUNT(*) as total
                      FROM brc20_current_balances
                      WHERE tick = $1 AND overall_balance::numeric > 0`
    let countRes = await query_db(countQuery, [tick])
    let total = parseInt(countRes.rows[0].total)

    // 构建主查询，使用 JOIN 获取 decimals 和 limit_per_mint 信息
    let query = `SELECT
                   cb.pkscript,
                   cb.wallet,
                   cb.overall_balance,
                   cb.available_balance,
                   bt.decimals,
                   bt.limit_per_mint
                 FROM brc20_current_balances cb
                 JOIN brc20_tickers bt ON cb.tick = bt.tick
                 WHERE cb.tick = $1 AND cb.overall_balance::numeric > 0
                 ORDER BY cb.overall_balance::numeric DESC`
    let params = [tick]

    // 如果有限制，添加 LIMIT 子句
    if (limit > 0) {
      query += ` LIMIT $2`
      params.push(limit)
    }

    let res = await query_db(query, params)
    if (res.rows.length == 0) {
      response.status(400).send({ error: 'no holders found', result: null })
      return
    }

    // 格式化数字为可读格式（根据小数位数）
    function formatNumber(bigIntValue, decimals) {
      let str = bigIntValue.toString()
      if (decimals === 0) {
        return str
      }

      if (str.length <= 18) {
        str = '0'.repeat(18 - str.length) + str
        str = '0.' + str
        if (decimals < 18) {
          str = str.substring(0, str.length - (18 - decimals))
        }
      } else {
        str = str.substring(0, str.length - 18) + '.' + str.substring(str.length - 18)
        if (decimals < 18) {
          str = str.substring(0, str.length - (18 - decimals))
        }
      }

      // 移除尾随的0
      if (str.includes('.')) {
        str = str.replace(/\.?0+$/, '')
      }

      return str
    }

    // 获取代币信息（从第一行获取，因为所有行的 decimals 和 limit_per_mint 都相同）
    let tokenDecimals = res.rows.length > 0 ? res.rows[0].decimals : 18
    let tokenLimitPerMint = res.rows.length > 0 ? res.rows[0].limit_per_mint : '0'

    // 格式化余额
    let formattedHolders = res.rows.map(row => {
      let holder = {
        wallet: row.wallet,
        overall_balance: formatNumber(BigInt(row.overall_balance), row.decimals),
        available_balance: formatNumber(BigInt(row.available_balance), row.decimals)
      }

      // 如果不是简略模式，添加 pkscript 字段
      if (!simple) {
        holder.pkscript = row.pkscript
      }

      return holder
    })

    let result = {
      unused_txes: formattedHolders,
      block_height: current_block_height,
      total: total,
      decimals: tokenDecimals,
      limit_per_mint: formatNumber(BigInt(tokenLimitPerMint), tokenDecimals)
    }

    response.send({ error: null, result: result })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});



app.get('/v1/brc20/get_hash_of_all_activity', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let block_height = request.query.block_height
  
    let current_block_height = await get_block_height_of_db()
    if (block_height > current_block_height) {
      response.status(400).send({ error: 'block not indexed yet', result: null })
      return
    }

    let query =  `select cumulative_event_hash, block_event_hash
                  from brc20_cumulative_event_hashes
                  where block_height = $1;`
    let res = await query_db(query, [block_height])
    let cumulative_event_hash = res.rows[0].cumulative_event_hash
    let block_event_hash = res.rows[0].block_event_hash
  
    let res2 = await query_db('select indexer_version from brc20_indexer_version;')
    let indexer_version = res2.rows[0].indexer_version
  
    response.send({ error: null, result: {
        cumulative_event_hash: cumulative_event_hash,
        block_event_hash: block_event_hash,
        indexer_version: indexer_version,
        block_height: block_height
      } 
    })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

// NOTE: this may take a few minutes to run
app.get('/v1/brc20/get_hash_of_all_current_balances', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let current_block_height = await get_block_height_of_db()
    let hash_hex = null
    if (!use_extra_tables) {
      let query = ` with tempp as (
                      select max(id) as id
                      from brc20_historic_balances
                      where block_height <= $1
                      group by pkscript, tick
                    )
                    select bhb.pkscript, bhb.tick, bhb.overall_balance, bhb.available_balance
                    from tempp t
                    left join brc20_historic_balances bhb on bhb.id = t.id
                    order by bhb.pkscript asc, bhb.tick asc;`
      let params = [current_block_height]

      let res = await query_db(query, params)
      res.rows.sort((a, b) => {
        if (a.pkscript < b.pkscript) {
          return -1
        } else if (a.pkscript > b.pkscript) {
          return 1
        } else {
          if (a.tick < b.tick) {
            return -1
          } else if (a.tick > b.tick) {
            return 1
          } else {
            return 0
          }
        }
      })
      let whole_str = ''
      res.rows.forEach((row) => {
        if (parseInt(row.overall_balance) != 0) {
          whole_str += row.pkscript + ';' + row.tick + ';' + row.overall_balance + ';' + row.available_balance + EVENT_SEPARATOR
        }
      })
      whole_str = whole_str.slice(0, -1)
      // get sha256 hash hex of the whole string
      const hash = crypto.createHash('sha256');
      hash.update(whole_str);
      hash_hex = hash.digest('hex');
    } else {
      let query = ` select pkscript, tick, overall_balance, available_balance
                    from brc20_current_balances
                    order by pkscript asc, tick asc;`
      let params = []

      let res = await query_db(query, params)
      res.rows.sort((a, b) => {
        if (a.pkscript < b.pkscript) {
          return -1
        } else if (a.pkscript > b.pkscript) {
          return 1
        } else {
          if (a.tick < b.tick) {
            return -1
          } else if (a.tick > b.tick) {
            return 1
          } else {
            return 0
          }
        }
      })
      let whole_str = ''
      res.rows.forEach((row) => {
        if (parseInt(row.overall_balance) != 0) {
          whole_str += row.pkscript + ';' + row.tick + ';' + row.overall_balance + ';' + row.available_balance + EVENT_SEPARATOR
        }
      })
      whole_str = whole_str.slice(0, -1)
      // get sha256 hash hex of the whole string
      const hash = crypto.createHash('sha256');
      hash.update(whole_str);
      hash_hex = hash.digest('hex');
    }

    let res2 = await query_db('select indexer_version from brc20_indexer_version;')
    let indexer_version = res2.rows[0].indexer_version

    response.send({ error: null, result: {
        current_balances_hash: hash_hex,
        indexer_version: indexer_version,
        block_height: current_block_height
      }
    })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

// get all events with a specific inscription id
app.get('/v1/brc20/event', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)

    let res1 = await query_db('select event_type_name, event_type_id from brc20_event_types;')
    let event_type_id_to_name = {}
    res1.rows.forEach((row) => {
      event_type_id_to_name[row.event_type_id] = row.event_type_name
    })

    let inscription_id = request.query.inscription_id;
    if(!inscription_id) {
      response.status(400).send({ error: 'inscription_id is required', result: null })
      return
    }

    let query =  `select event, event_type, inscription_id block_height
                  from brc20_events
                  where inscription_id = $1
                  order by id asc;`
    let res = await query_db(query, [inscription_id])
    let result = []
    for (const row of res.rows) {
      let event = row.event
      let event_type = event_type_id_to_name[row.event_type]
      let inscription_id = row.inscription_id
      event.event_type = event_type
      event.inscription_id = inscription_id
      result.push(event)
    }
    response.send({ error: null, result: result })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

// 获取指定 tick 的详细信息，包括剩余铸造量
app.get('/v1/brc20/tick_info', async (request, response) => {
  try {
    //console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let tick = request.query.ticker
    
    if (!tick) {
      response.status(400).send({ error: 'ticker parameter is required', result: null })
      return
    }

    tick = tick.toLowerCase()
    let current_block_height = await get_block_height_of_db()

    // 查询 tick 的基本信息
    let query = `SELECT 
                   original_tick,
                   tick,
                   max_supply,
                   decimals,
                   limit_per_mint,
                   remaining_supply,
                   burned_supply,
                   is_self_mint,
                   deploy_inscription_id,
                   block_height as deploy_block_height
                 FROM brc20_tickers 
                 WHERE tick = $1;`
    
    let res = await query_db(query, [tick])
    
    if (res.rows.length == 0) {
      response.status(404).send({ error: 'ticker not found', result: null })
      return
    }

    let tickInfo = res.rows[0]
    
    // 计算铸造进度
    let max_supply = BigInt(tickInfo.max_supply)
    let remaining_supply = BigInt(tickInfo.remaining_supply)
    let minted_supply = max_supply - remaining_supply
    let burned_supply = BigInt(tickInfo.burned_supply || 0)
    let circulating_supply = minted_supply - burned_supply
    
    // 计算铸造进度百分比
    let mint_progress = max_supply > 0n ? Number((minted_supply * 10000n) / max_supply) / 100 : 0

    // 格式化数字为可读格式（根据小数位数）
    function formatNumber(bigIntValue, decimals) {
      let str = bigIntValue.toString()
      if (decimals === 0) {
        return str
      }
      
      if (str.length <= 18) {
        str = '0'.repeat(18 - str.length) + str
        str = '0.' + str
        if (decimals < 18) {
          str = str.substring(0, str.length - (18 - decimals))
        }
      } else {
        str = str.substring(0, str.length - 18) + '.' + str.substring(str.length - 18)
        if (decimals < 18) {
          str = str.substring(0, str.length - (18 - decimals))
        }
      }
      
      // 移除尾随的0
      if (str.includes('.')) {
        str = str.replace(/\.?0+$/, '')
      }
      
      return str
    }

    let result = {
      tick: tickInfo.original_tick,
      tick_lower: tickInfo.tick,
      max_supply: formatNumber(max_supply, tickInfo.decimals),
      minted_supply: formatNumber(minted_supply, tickInfo.decimals),
      remaining_supply: formatNumber(remaining_supply, tickInfo.decimals),
      circulating_supply: formatNumber(circulating_supply, tickInfo.decimals),
      burned_supply: formatNumber(burned_supply, tickInfo.decimals),
      mint_progress_percentage: mint_progress,
      decimals: tickInfo.decimals,
      limit_per_mint: formatNumber(BigInt(tickInfo.limit_per_mint), tickInfo.decimals),
      is_self_mint: tickInfo.is_self_mint,
      deploy_inscription_id: tickInfo.deploy_inscription_id,
      deploy_block_height: tickInfo.deploy_block_height,
      is_fully_minted: remaining_supply === 0n,
      current_block_height: current_block_height
    }

    response.send({ error: null, result: result })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

// 获取所有可铸造的 ticks 列表（剩余供应量大于0）
app.get('/v1/brc20/mintable_ticks', async (request, response) => {
  try {
    //console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    
    let current_block_height = await get_block_height_of_db()
    let page = parseInt(request.query.page || '1')
    let limit = parseInt(request.query.limit || '50')
    let offset = (page - 1) * limit

    // 限制每页最大100条记录
    if (limit > 100) limit = 100

    // 查询可铸造的 ticks
    let query = `SELECT 
                   original_tick,
                   tick,
                   max_supply,
                   decimals,
                   limit_per_mint,
                   remaining_supply,
                   burned_supply,
                   is_self_mint,
                   deploy_inscription_id,
                   block_height as deploy_block_height
                 FROM brc20_tickers 
                 WHERE remaining_supply > 0
                 ORDER BY remaining_supply DESC
                 LIMIT $1 OFFSET $2;`
    
    let res = await query_db(query, [limit, offset])
    
    // 获取总数
    let countQuery = `SELECT COUNT(*) as total FROM brc20_tickers WHERE remaining_supply > 0;`
    let countRes = await query_db(countQuery)
    let total = parseInt(countRes.rows[0].total)

    // 格式化结果
    function formatNumber(bigIntValue, decimals) {
      let str = bigIntValue.toString()
      if (decimals === 0) {
        return str
      }
      
      if (str.length <= 18) {
        str = '0'.repeat(18 - str.length) + str
        str = '0.' + str
        if (decimals < 18) {
          str = str.substring(0, str.length - (18 - decimals))
        }
      } else {
        str = str.substring(0, str.length - 18) + '.' + str.substring(str.length - 18)
        if (decimals < 18) {
          str = str.substring(0, str.length - (18 - decimals))
        }
      }
      
      if (str.includes('.')) {
        str = str.replace(/\.?0+$/, '')
      }
      
      return str
    }

    let ticks = res.rows.map(row => {
      let max_supply = BigInt(row.max_supply)
      let remaining_supply = BigInt(row.remaining_supply)
      let minted_supply = max_supply - remaining_supply
      let mint_progress = max_supply > 0n ? Number((minted_supply * 10000n) / max_supply) / 100 : 0

      return {
        tick: row.original_tick,
        tick_lower: row.tick,
        remaining_supply: formatNumber(remaining_supply, row.decimals),
        max_supply: formatNumber(max_supply, row.decimals),
        mint_progress_percentage: mint_progress,
        decimals: row.decimals,
        limit_per_mint: formatNumber(BigInt(row.limit_per_mint), row.decimals),
        is_self_mint: row.is_self_mint,
        deploy_block_height: row.deploy_block_height
      }
    })

    let result = {
      ticks: ticks,
      pagination: {
        page: page,
        limit: limit,
        total: total,
        total_pages: Math.ceil(total / limit)
      },
      current_block_height: current_block_height
    }

    response.send({ error: null, result: result })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

// 获取指定 tick 的详细信息，包括部署者信息和交易信息
app.get('/v1/brc20/new_tick_info', async (request, response) => {
  try {
    //console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    let tick = request.query.ticker

    if (!tick) {
      response.status(400).send({ error: 'ticker parameter is required', result: null })
      return
    }

    tick = tick.toLowerCase()
    let current_block_height = await get_block_height_of_db()

    // 联合查询 tick 的基本信息、部署者信息和交易信息
    let query = `SELECT
                   bt.original_tick,
                   bt.tick,
                   bt.max_supply,
                   bt.decimals,
                   bt.limit_per_mint,
                   bt.remaining_supply,
                   bt.burned_supply,
                   bt.is_self_mint,
                   bt.deploy_inscription_id,
                   bt.block_height as deploy_block_height,
                   be.event->>'deployer_pkScript' as deployer_pkscript,
                   be.event->>'deployer_wallet' as deployer_wallet,
                   ot.new_satpoint as deploy_satpoint,
                   ot.new_output_value as deploy_output_value
                 FROM brc20_tickers bt
                 LEFT JOIN brc20_events be ON bt.deploy_inscription_id = be.inscription_id AND be.event_type = 0
                 LEFT JOIN ord_transfers ot ON bt.deploy_inscription_id = ot.inscription_id
                 WHERE bt.tick = $1
                 ORDER BY ot.id ASC
                 LIMIT 1;`

    let res = await query_db(query, [tick])

    if (res.rows.length == 0) {
      response.status(404).send({ error: 'ticker not found', result: null })
      return
    }

    let tickInfo = res.rows[0]

    // 获取持有者总数（只统计余额大于0的地址）
    let holdersTotal = 0
    if (use_extra_tables) {
      try {
        let holdersQuery = `SELECT COUNT(*) as total
                           FROM brc20_current_balances
                           WHERE tick = $1 AND overall_balance::numeric > 0`
        let holdersRes = await query_db(holdersQuery, [tick])
        holdersTotal = parseInt(holdersRes.rows[0].total)
      } catch (err) {
        console.log('Error getting holders count:', err)
        // 如果获取失败，设为 null 而不是中断整个请求
        holdersTotal = null
      }
    }

    // 计算铸造进度
    let max_supply = BigInt(tickInfo.max_supply)
    let remaining_supply = BigInt(tickInfo.remaining_supply)
    let minted_supply = max_supply - remaining_supply
    let burned_supply = BigInt(tickInfo.burned_supply || 0)
    let circulating_supply = minted_supply - burned_supply

    // 计算铸造进度百分比
    let mint_progress = max_supply > 0n ? Number((minted_supply * 10000n) / max_supply) / 100 : 0

    // 格式化数字为可读格式（根据小数位数）
    function formatNumber(bigIntValue, decimals) {
      let str = bigIntValue.toString()
      if (decimals === 0) {
        return str
      }

      if (str.length <= 18) {
        str = '0'.repeat(18 - str.length) + str
        str = '0.' + str
        if (decimals < 18) {
          str = str.substring(0, str.length - (18 - decimals))
        }
      } else {
        str = str.substring(0, str.length - 18) + '.' + str.substring(str.length - 18)
        if (decimals < 18) {
          str = str.substring(0, str.length - (18 - decimals))
        }
      }

      // 移除尾随的0
      if (str.includes('.')) {
        str = str.replace(/\.?0+$/, '')
      }

      return str
    }

    // 从 inscription_id 或 deploy_satpoint 中提取交易ID
    let deploy_txid = null
    let deploy_inscription_index = null

    // 首先尝试从 inscription_id 解析 (格式: txid:index 或 txidi{index})
    if (tickInfo.deploy_inscription_id) {
      if (tickInfo.deploy_inscription_id.includes(':')) {
        let parts = tickInfo.deploy_inscription_id.split(':')
        if (parts.length === 2) {
          deploy_txid = parts[0]
          deploy_inscription_index = parseInt(parts[1])
        }
      } else if (tickInfo.deploy_inscription_id.includes('i')) {
        let parts = tickInfo.deploy_inscription_id.split('i')
        if (parts.length === 2) {
          deploy_txid = parts[0]
          deploy_inscription_index = parseInt(parts[1])
        }
      }
    }

    // 如果从 inscription_id 解析失败，尝试从 deploy_satpoint 解析
    if (!deploy_txid && tickInfo.deploy_satpoint) {
      let satpoint_parts = tickInfo.deploy_satpoint.split(':')
      if (satpoint_parts.length >= 1) {
        deploy_txid = satpoint_parts[0]
      }
    }

    let result = {
      tick: tickInfo.original_tick,
      tick_lower: tickInfo.tick,
      max_supply: formatNumber(max_supply, tickInfo.decimals),
      minted_supply: formatNumber(minted_supply, tickInfo.decimals),
      remaining_supply: formatNumber(remaining_supply, tickInfo.decimals),
      circulating_supply: formatNumber(circulating_supply, tickInfo.decimals),
      burned_supply: formatNumber(burned_supply, tickInfo.decimals),
      mint_progress_percentage: mint_progress,
      decimals: tickInfo.decimals,
      limit_per_mint: formatNumber(BigInt(tickInfo.limit_per_mint), tickInfo.decimals),
      is_self_mint: tickInfo.is_self_mint,
      deploy_inscription_id: tickInfo.deploy_inscription_id,
      deploy_block_height: tickInfo.deploy_block_height,
      is_fully_minted: remaining_supply === 0n,
      current_block_height: current_block_height,
      // 新增的部署者和交易信息
      deployer_wallet: tickInfo.deployer_wallet || null,
      deploy_txid: deploy_txid,
      deploy_inscription_index: deploy_inscription_index,
      // 持有者总数
      holders_total: holdersTotal
    }

    response.send({ error: null, result: result })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

// 搜索 ordinals 文本内容
app.get('/v1/brc20/search_ordinals_text', async (request, response) => {
  try {
    console.log(`${request.protocol}://${request.get('host')}${request.originalUrl}`)
    
    let search_text = request.query.text || ''
    let limit = parseInt(request.query.limit || '50')
    let page = parseInt(request.query.page || '1')
    let offset = (page - 1) * limit

    // 验证参数
    if (!search_text.trim()) {
      response.status(400).send({ error: 'search text is required', result: null })
      return
    }

    // 限制每页最大100条记录
    if (limit > 100) limit = 100

    let current_block_height = await get_block_height_of_db()

    // 搜索查询 - 在 text_content 和 content 字段中搜索
    // 使用 ILIKE 进行不区分大小写的模糊搜索
    let searchQuery = `
      SELECT 
        oc.inscription_id,
        oc.content,
        oc.text_content,
        oc.content_type,
        oc.metaprotocol,
        oc.block_height,
        onti.inscription_number
      FROM ord_content oc
      LEFT JOIN ord_number_to_id onti ON oc.inscription_id = onti.inscription_id
      WHERE (
        oc.text_content ILIKE $1 
        OR oc.content::text ILIKE $1
      )
      ORDER BY oc.block_height DESC, oc.id DESC
      LIMIT $2 OFFSET $3
    `

    // 计算总数的查询
    let countQuery = `
      SELECT COUNT(*) as total
      FROM ord_content oc
      WHERE (
        oc.text_content ILIKE $1 
        OR oc.content::text ILIKE $1
      )
    `

    let searchPattern = `%${search_text}%`

    // 执行搜索和计数查询
    let [searchRes, countRes] = await Promise.all([
      query_db(searchQuery, [searchPattern, limit, offset]),
      query_db(countQuery, [searchPattern])
    ])

    let total = parseInt(countRes.rows[0].total)

    // 格式化结果
    let results = searchRes.rows.map(row => {
      let result = {
        inscription_id: row.inscription_id,
        inscription_number: row.inscription_number,
        content_type: row.content_type,
        metaprotocol: row.metaprotocol,
        block_height: row.block_height
      }

      // 根据内容类型决定返回哪个字段
      if (row.content) {
        result.content = row.content
      } else if (row.text_content) {
        result.text_content = row.text_content
      }

      return result
    })

    let result = {
      search_text: search_text,
      results: results,
      pagination: {
        page: page,
        limit: limit,
        total: total,
        total_pages: Math.ceil(total / limit)
      },
      current_block_height: current_block_height
    }

    response.send({ error: null, result: result })
  } catch (err) {
    console.log(err)
    response.status(500).send({ error: 'internal error', result: null })
  }
});

app.listen(api_port, api_host);
