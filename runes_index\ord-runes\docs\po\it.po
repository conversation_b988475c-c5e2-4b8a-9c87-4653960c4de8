
msgid ""
msgstr ""
"Project-Id-Version: Manuale della Teoria Ordinale\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: Cicero.sats @Cicero_sats <<EMAIL>>\n"
"Language-Team: Italian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: it\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: src/SUMMARY.md:4
#: src/introduction.md:1
msgid "Introduction"
msgstr "Introduzione"

#: src/SUMMARY.md:5
msgid "Overview"
msgstr "Panoramica"

#: src/SUMMARY.md:6
#: src/digital-artifacts.md:1
msgid "Digital Artifacts"
msgstr "Artefatti Digitali"

#: src/SUMMARY.md:7
#: src/SUMMARY.md:17
#: src/overview.md:221
#: src/inscriptions.md:1
msgid "Inscriptions"
msgstr "Iscrizioni"

#: src/SUMMARY.md:8
#: src/inscriptions/metadata.md:1
msgid "Metadata"
msgstr "Metadati"

#: src/SUMMARY.md:9
#: src/inscriptions/provenance.md:1
msgid "Provenance"
msgstr "Provenienza"

#: src/SUMMARY.md:10
#: src/inscriptions/recursion.md:1
msgid "Recursion"
msgstr "Recursion"

#: src/SUMMARY.md:11
#: src/inscriptions/pointer.md:1
msgid "Pointer"
msgstr "Pointer"

#: src/SUMMARY.md:12
msgid "FAQ"
msgstr "FAQ"

#: src/SUMMARY.md:13
msgid "Contributing"
msgstr "Contribuire"

#: src/SUMMARY.md:14
#: src/donate.md:1
msgid "Donate"
msgstr "Donare"

#: src/SUMMARY.md:15
msgid "Guides"
msgstr "Guide"

#: src/SUMMARY.md:16
msgid "Explorer"
msgstr "Esploratore"

#: src/SUMMARY.md:18
#: src/guides/batch-inscribing.md:1
msgid "Batch Inscribing"
msgstr "Iscrivere in Batch"

#: src/SUMMARY.md:19
#: src/guides/sat-hunting.md:1
msgid "Sat Hunting"
msgstr "Sat Hunting"

#: src/SUMMARY.md:20
#: src/guides/teleburning.md:1
msgid "Teleburning"
msgstr "Teleburning"

#: src/SUMMARY.md:21
#: src/guides/collecting.md:1
msgid "Collecting"
msgstr "Collezionare"

#: src/SUMMARY.md:22
#: src/guides/sat-hunting.md:239
msgid "Sparrow Wallet"
msgstr "Portafoglio Sparrow"

#: src/SUMMARY.md:23
#: src/guides/testing.md:1
msgid "Testing"
msgstr "Testare"

#: src/SUMMARY.md:24
#: src/guides/moderation.md:1
msgid "Moderation"
msgstr "Moderazione"

#: src/SUMMARY.md:25
#: src/guides/reindexing.md:1
msgid "Reindexing"
msgstr "Reindicizzazione"

#: src/SUMMARY.md:26
msgid "Bounties"
msgstr "Premi"

#: src/SUMMARY.md:27
msgid "Bounty 0: 100,000 sats Claimed!"
msgstr "Premio 0: 100,000 sats Riscossi!"

#: src/SUMMARY.md:28
msgid "Bounty 1: 200,000 sats Claimed!"
msgstr "Premio 1: 200,000 sats Riscossi!"

#: src/SUMMARY.md:29
msgid "Bounty 2: 300,000 sats Claimed!"
msgstr "Premio 2: 300,000 sats Riscossi!"

#: src/SUMMARY.md:30
msgid "Bounty 3: 400,000 sats"
msgstr "Premio 3: 400,000 sats"

#: src/introduction.md:4
msgid ""
"This handbook is a guide to ordinal theory. Ordinal theory concerns itself "
"with satoshis, giving them individual identities and allowing them to be "
"tracked, transferred, and imbued with meaning."
msgstr ""
"Questo manuale è una guida alla teoria Ordinale. La teoria ordinale si "
"occupa dei satoshi, attribuendo loro un'identità individuale e "
"consentendo di tracciarli, trasferirli e attribuire loro un significato."

#: src/introduction.md:8
msgid ""
"Satoshis, not bitcoin, are the atomic, native currency of the Bitcoin "
"network. One bitcoin can be sub-divided into 100,000,000 satoshis, but no "
"further."
msgstr ""
"I satoshi, e non i bitcoin, sono l’unità più piccola di un Bitcoin. Un bitcoin "
"può essere suddiviso in 100.000.000 di satoshis, ma non oltre."

#: src/introduction.md:11
msgid ""
"Ordinal theory does not require a sidechain or token aside from Bitcoin, and "
"can be used without any changes to the Bitcoin network. It works right now."
msgstr ""
"La teoria ordinale non richiede una sidechain o un token oltre al Bitcoin e "
"può essere utilizzata senza alcuna modifica alla rete Bitcoin. Funziona "
"già adesso."

#: src/introduction.md:14
msgid ""
"Ordinal theory imbues satoshis with numismatic value, allowing them to be "
"collected and traded as curios."
msgstr ""
"La teoria ordinale conferisce ai satoshi un valore numismatico, "
"consentendo loro di essere collezionati e scambiati come collezionabili."

#: src/introduction.md:17
msgid ""
"Individual satoshis can be inscribed with arbitrary content, creating unique "
"Bitcoin-native digital artifacts that can be held in Bitcoin wallets and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"I singoli satoshi possono essere iscritti con un contenuto arbitrario, "
"creando artefatti digitali unici nativi di Bitcoin che possono essere "
"conservati in portafogli Bitcoin e trasferiti con transazioni Bitcoin. Le "
"iscrizioni sono durevoli, immutabili, sicure e decentralizzate come Bitcoin "
"stesso."

#: src/introduction.md:22
msgid ""
"Other, more unusual use-cases are possible: off-chain colored-coins, public "
"key infrastructure with key rotation, a decentralized replacement for the "
"DNS. For now though, such use-cases are speculative, and exist only in the "
"minds of fringe ordinal theorists."
msgstr ""
"Sono possibili altri casi d'uso più insoliti: monete colorate off-chain, "
"infrastrutture a chiave pubblica con rotazione delle chiavi, una "
"sostituzione decentralizzata del DNS. Per ora, però, questi casi d'uso "
"sono speculativi ed esistono solo nella mente di teorici ordinali di "
"nicchia."

#: src/introduction.md:27
msgid "For more details on ordinal theory, see the [overview](overview.md)."
msgstr "Per maggiori dettagli sulla teoria ordinale, consulta la [panoramica](overview.md)."

#: src/introduction.md:29
msgid "For more details on inscriptions, see [inscriptions](inscriptions.md)."
msgstr "Per maggiori dettagli sulle iscrizioni, vedi [iscrizioni](inscriptions.md)."

#: src/introduction.md:31
msgid ""
"When you're ready to get your hands dirty, a good place to start is with "
"[inscriptions](guides/inscriptions.md), a curious species of digital "
"artifact enabled by ordinal theory."
msgstr ""
"Quando sei pronto a sporcarti le mani, un buon punto di partenza è "
"rappresentato dalle iscrizioni, una curiosa specie di artefatto digitale reso "
"possibile dalla teoria ordinale."

#: src/introduction.md:35
msgid "Links"
msgstr "Collegamenti"

#: src/introduction.md:38
msgid "[GitHub](https://github.com/ordinals/ord/)"
msgstr "[GitHub](https://github.com/ordinals/ord/)"

#: src/introduction.md:39
msgid "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src/introduction.md:40
msgid "[Discord](https://discord.gg/ordinals)"
msgstr "[Discord](https://discord.gg/ordinals)"

#: src/introduction.md:41
msgid "[Open Ordinals Institute Website](https://ordinals.org/)"
msgstr "[Sito web dell'Istituto aperto degli Ordinali](https://ordinals.org/)"

#: src/introduction.md:42
msgid "[Open Ordinals Institute X](https://x.com/ordinalsorg)"
msgstr "[X dell' Istituto aperto degli Ordinali](https://x.com/ordinalsorg)"

#: src/introduction.md:43
msgid "[Mainnet Block Explorer](https://ordinals.com)"
msgstr "[Esploratore di blocchi Mainnet](https://ordinals.com)"

#: src/introduction.md:44
msgid "[Signet Block Explorer](https://signet.ordinals.com)"
msgstr "[Esploratore di blocchi Signet](https://signet.ordinals.com)"

#: src/introduction.md:46
msgid "Videos"
msgstr "Video"

#: src/introduction.md:49
msgid ""
"[Ordinal Theory Explained: Satoshi Serial Numbers and NFTs on "
"Bitcoin](https://www.youtube.com/watch?v=rSS0O2KQpsI)"
msgstr ""
"[La teoria degli Ordinali spiegata: Numeri di serie dei Satoshi e NFT "
"su Bitcoin](https://www.youtube.com/watch?v=rSS0O2KQpsI)"

#: src/introduction.md:50
msgid ""
"[Ordinals Workshop with "
"Rodarmor](https://www.youtube.com/watch?v=MC_haVa6N3I)"
msgstr ""
"[Workshop sugli Ordinali con Rodarmor "
"Rodarmor](https://www.youtube.com/watch?v=MC_haVa6N3I)"

#: src/introduction.md:51
msgid ""
"[Ordinal Art: Mint Your own NFTs on Bitcoin w/ "
"@rodarmor](https://www.youtube.com/watch?v=j5V33kV3iqo)"
msgstr ""
"[Arte ordinale: Inscrivi i tuoi NFT su Bitcoin "
"con @rodarmor](https://www.youtube.com/watch?v=j5V33kV3iqo)"

#: src/overview.md:1
msgid "Ordinal Theory Overview"
msgstr "Panoramica sulla teoria degli Ordinali"

#: src/overview.md:4
msgid ""
"Ordinals are a numbering scheme for satoshis that allows tracking and "
"transferring individual sats. These numbers are called [ordinal "
"numbers](https://ordinals.com). Satoshis are numbered in the order in which "
"they're mined, and transferred from transaction inputs to transaction "
"outputs first-in-first-out. Both the numbering scheme and the transfer "
"scheme rely on _order_, the numbering scheme on the _order_ in which "
"satoshis are mined, and the transfer scheme on the _order_ of transaction "
"inputs and outputs. Thus the name, _ordinals_."
msgstr ""
"Gli Ordinali sono uno schema di numerazione per i satoshi che consente "
"di tracciare e trasferire i singoli sats. Questi numeri sono chiamati [numeri "
"ordinali](https://ordinals.com). I Satoshi sono numerati nell'ordine in cui "
"sono stati minati e vengono trasferiti in base alla sequenza delle transazioni "
"in entrata e in uscita, secondo il metodo FIFO (First In, First Out). Sia lo schema "
"di numerazione che quello di trasferimento si basano sull'ordine, quello di "
"numerazione si basa sull'ordine di estrazione dei satoshi e quello di "
"trasferimento si basa sull'ordine degli ingressi e delle uscite delle "
"transazioni. Da qui il nome _Ordinali_."

#: src/overview.md:13
msgid ""
"Technical details are available in [the "
"BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)."
msgstr ""
"I dettagli tecnici sono disponibili [nel "
"BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)."

#: src/overview.md:16
msgid ""
"Ordinal theory does not require a separate token, another blockchain, or any "
"changes to Bitcoin. It works right now."
msgstr ""
"La teoria degli Ordinali non richiede un token separato, un'altra blockchain o "
"modifiche a Bitcoin. Funziona già adesso."

#: src/overview.md:19
msgid "Ordinal numbers have a few different representations:"
msgstr "I numeri ordinali hanno diverse rappresentazioni:"

#: src/overview.md:21
msgid ""
"_Integer notation_: "
"[`2099994106992659`](https://ordinals.com/sat/2099994106992659) The ordinal "
"number, assigned according to the order in which the satoshi was mined."
msgstr ""
"_Notazione integrale_: "
"[`2099994106992659`](https://ordinals.com/sat/2099994106992659) Il numero "
"ordinale, assegnato in base all'ordine in cui il Satoshi è stato minato."

#: src/overview.md:26
msgid ""
"_Decimal notation_: "
"[`3891094.16797`](https://ordinals.com/sat/3891094.16797) The first number "
"is the block height in which the satoshi was mined, the second the offset of "
"the satoshi within the block."
msgstr ""
"_Notazione decimale_: "
"[`3891094.16797`](https://ordinals.com/sat/3891094.16797) Il numero prima del "
"punto è l'altezza del blocco in cui il satoshi è stato minato, "
"il secondo è l'offset del satoshi all'interno del blocco."

#: src/overview.md:31
msgid ""
"_Degree notation_: "
"[`3°111094′214″16797‴`](https://ordinals.com/sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). "
"We'll get to that in a moment."
msgstr ""
"_Notazione sessagesimale_: "
"[`3°111094′214″16797‴`](https://ordinals.com/sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). "
"Ci arriveremo tra poco."

#: src/overview.md:35
msgid ""
"_Percentile notation_: "
"[`99.**************%`](https://ordinals.com/sat/99.**************%25) . The "
"satoshi's position in Bitcoin's supply, expressed as a percentage."
msgstr ""
"_Notazione percentile_: "
"[`99.**************%`](https://ordinals.com/sat/99.**************%25) . Posizione "
"del satoshi sull'intera supply di Bitcoin (21 milioni), espressa in percentuale."

#: src/overview.md:39
msgid ""
"_Name_: [`satoshi`](https://ordinals.com/sat/satoshi). An encoding of the "
"ordinal number using the characters `a` through `z`."
msgstr ""
"_Nome_: [`satoshi`](https://ordinals.com/sat/satoshi). Una codifica del "
"numero ordinale che utilizza i caratteri da `a` a `z`."

#: src/overview.md:42
msgid ""
"Arbitrary assets, such as NFTs, security tokens, accounts, or stablecoins "
"can be attached to satoshis using ordinal numbers as stable identifiers."
msgstr ""
"Sui satoshi possono essere allegati un certo tipo di dati, come NFT, "
"security token, conti o stablecoins, utilizzando i numeri ordinali come "
"identificatori stabili."

#: src/overview.md:45
msgid ""
"Ordinals is an open-source project, developed [on "
"GitHub](https://github.com/ordinals/ord). The project consists of a BIP "
"describing the ordinal scheme, an index that communicates with a Bitcoin "
"Core node to track the location of all satoshis, a wallet that allows making "
"ordinal-aware transactions, a block explorer for interactive exploration of "
"the blockchain, functionality for inscribing satoshis with digital "
"artifacts, and this manual."
msgstr ""
"Ordinals è un progetto open-source, sviluppato [su "
"GitHub](https://github.com/ordinals/ord). Il progetto consiste "
"in un BIP che descrive lo schema ordinale, un indice che comunica con un nodo "
"Bitcoin Core per tenere traccia della posizione di tutti i satoshis, un portafoglio "
"che consente di effettuare transazioni degli ordinali riconoscendoli come tali, un "
"block explorer per l'esplorazione interattiva della blockchain, una funzionalità per "
"inscrivere artefatti digitali sui Satoshi e questo manuale."

#: src/overview.md:52
msgid "Rarity"
msgstr "La Rarità"

#: src/overview.md:55
msgid ""
"Humans are collectors, and since satoshis can now be tracked and "
"transferred, people will naturally want to collect them. Ordinal theorists "
"can decide for themselves which sats are rare and desirable, but there are "
"some hints…"
msgstr ""
"Gli esseri umani sono collezionisti e, siccome da ora i satoshi possono "
"essere tracciati e trasferiti, le persone vorranno naturalmente collezionarli. "
"I teorici degli Ordinali possono decidere da soli quali satoshi sono rari e "
"desiderabili, ma ecco qualche suggerimento..."


#: src/overview.md:59
msgid ""
"Bitcoin has periodic events, some frequent, some more uncommon, and these "
"naturally lend themselves to a system of rarity. These periodic events are:"
msgstr ""
"Il Bitcoin ha eventi periodici, alcuni più frequenti, altri meno, che si "
"prestano naturalmente a un sistema di rarità. Questi eventi periodici sono:"


#: src/overview.md:62
msgid ""
"_Blocks_: A new block is mined approximately every 10 minutes, from now "
"until the end of time."
msgstr ""
"_Blocchi_: Ogni 10 minuti circa viene estratto un nuovo blocco, da ora "
"fino alla fine dei tempi."

#: src/overview.md:65
msgid ""
"_Difficulty adjustments_: Every 2016 blocks, or approximately every two "
"weeks, the Bitcoin network responds to changes in hashrate by adjusting the "
"difficulty target which blocks must meet in order to be accepted."
msgstr ""
"_Aggiustamenti della difficoltà_: Ogni 2016 blocchi, o circa ogni due "
"settimane, la rete Bitcoin risponde alle variazioni dell'hashrate modificando "
"l'obiettivo di difficoltà che i blocchi devono soddisfare per essere accettati."

#: src/overview.md:69
msgid ""
"_Halvings_: Every 210,000 blocks, or roughly every four years, the amount of "
"new sats created in every block is cut in half."
msgstr ""
"_Dimezzamenti_: Ogni 210.000 blocchi, o all'incirca ogni quattro anni, la "
"quantità di nuovi satoshi creati in ogni blocco viene dimezzata."

#: src/overview.md:72
msgid ""
"_Cycles_: Every six halvings, something magical happens: the halving and the "
"difficulty adjustment coincide. This is called a conjunction, and the time "
"period between conjunctions a cycle. A conjunction occurs roughly every 24 "
"years. The first conjunction should happen sometime in 2032."
msgstr ""
"_Cicli_: Ogni sei dimezzamenti, accade qualcosa di magico: l'halving e "
"l'adeguamento della difficoltà coincidono. Questo si chiama congiunzione e il "
"periodo di tempo che intercorre tra una congiunzione e l'altra si chiama ciclo. "
"Una congiunzione si verifica all'incirca ogni 24 anni. La prima congiunzione "
"dovrebbe verificarsi nel 2032."

#: src/overview.md:77
msgid "This gives us the following rarity levels:"
msgstr "Questo ci dà i seguenti livelli di rarità:"

#: src/overview.md:79
msgid "`common`: Any sat that is not the first sat of its block"
msgstr "`comune`: qualsiasi satoshi che non sia il primo del suo blocco"

#: src/overview.md:80
msgid "`uncommon`: The first sat of each block"
msgstr "`non comune`: il primo sat di ciascun blocco"

#: src/overview.md:81
msgid "`rare`: The first sat of each difficulty adjustment period"
msgstr "`raro`: il primo sat di ogni periodo di aggiustamento della difficoltà"

#: src/overview.md:82
msgid "`epic`: The first sat of each halving epoch"
msgstr "`epico`: il primo sat del primo blocco conseguente all'Halving"

#: src/overview.md:83
msgid "`legendary`: The first sat of each cycle"
msgstr "`leggendario`: il primo sat di ogni ciclo"

#: src/overview.md:84
msgid "`mythic`: The first sat of the genesis block"
msgstr "`mitico`: il primo sat del blocco della genesi"

#: src/overview.md:86
msgid ""
"Which brings us to degree notation, which unambiguously represents an "
"ordinal number in a way that makes the rarity of a satoshi easy to see at a "
"glance:"
msgstr ""
"Questo ci porta alla notazione sessagesimale, che rappresenta inequivocabilmente "
"un numero ordinale in un modo che rende la rarità di un satoshi facile da vedere "
"a colpo d'occhio:"

#: src/overview.md:89
msgid ""
"```\n"
"A°B′C″D‴\n"
"│ │ │ ╰─ Index of sat in the block\n"
"│ │ ╰─── Index of block in difficulty adjustment period\n"
"│ ╰───── Index of block in halving epoch\n"
"╰─────── Cycle, numbered starting from 0\n"
"```"
msgstr ""
"```\n"
"A°B′C″D‴\n"
"│ │ │ ╰─ Indice del sat nel blocco\n"
"│ │ ╰─── Indice del blocco nel periodo di aggiustamento della difficoltà\n"
"│ ╰───── Indice del blocco nell'epoca di dimezzamento\n"
"╰─────── Ciclo, numerato a partire da 0\n"
"```"

#: src/overview.md:97
msgid ""
"Ordinal theorists often use the terms \"hour\", \"minute\", \"second\", and "
"\"third\" for _A_, _B_, _C_, and _D_, respectively."
msgstr ""
"I teorici degli Ordinali usano spesso i termini \"ora\", \"minuto\", \"secondo\", e "
"\"terzo\" per _A_, _B_, _C_, e _D_, rispettivamente."

#: src/overview.md:100
msgid "Now for some examples. This satoshi is common:"
msgstr "Ora alcuni esempi. Questo satoshi è comune:"

#: src/overview.md:102
msgid ""
"```\n"
"1°1′1″1‴\n"
"│ │ │ ╰─ Not first sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── Not first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″1‴\n"
"│ │ │ ╰─ Non è il primo sat nel blocco\n"
"│ │ ╰─── Non è il primo blocco nel periodo di aggiustamento della difficoltà\n"
"│ ╰───── Non è il primo blocco nell'epoca di dimezzamento\n"
"╰─────── Secondo ciclo\n"
"```"

#: src/overview.md:111
msgid "This satoshi is uncommon:"
msgstr "Questo satoshi è non comune:"

#: src/overview.md:113
msgid ""
"```\n"
"1°1′1″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── Not first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″0‴\n"
"│ │ │ ╰─ E' il primo sat nel blocco\n"
"│ │ ╰─── Non è il primo blocco nel periodo di aggiustamento della difficoltà\n"
"│ ╰───── Non è il primo blocco nell'epoca di dimezzamento\n"
"╰─────── Secondo ciclo\n"
"```"

#: src/overview.md:121
msgid "This satoshi is rare:"
msgstr "Questo satoshi è raro:"

#: src/overview.md:123
msgid ""
"```\n"
"1°1′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── Not the first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′0″0‴\n"
"│ │ │ ╰─ E' il primo sat nel blocco\n"
"│ │ ╰─── E' il primo blocco nel periodo di aggiustamento della difficoltà\n"
"│ ╰───── Non è il primo blocco nell'epoca di dimezzamento\n"
"╰─────── Secondo ciclo\n"
"```"

#: src/overview.md:131
msgid "This satoshi is epic:"
msgstr "Questo satoshi è epico:"

#: src/overview.md:133
msgid ""
"```\n"
"1°0′1″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°0′1″0‴\n"
"│ │ │ ╰─ E' il primo sat nel blocco\n"
"│ │ ╰─── Non è il primo blocco nel periodo di aggiustamento della difficoltà\n"
"│ ╰───── E' il primo blocco nell'epoca di dimezzamento\n"
"╰─────── Secondo ciclo\n"
"```"

#: src/overview.md:141
msgid "This satoshi is legendary:"
msgstr "Questo satoshi è leggendario:"

#: src/overview.md:143
msgid ""
"```\n"
"1°0′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°0′0″0‴\n"
"│ │ │ ╰─ E' il primo sat nel blocco\n"
"│ │ ╰─── E' il primo blocco nel periodo di aggiustamento della difficoltà\n"
"│ ╰───── E' il primo blocco nell'epoca di dimezzamento\n"
"╰─────── Secondo ciclo\n"
"```"

#: src/overview.md:151
msgid "And this satoshi is mythic:"
msgstr "Infine questo è il satoshi mitico:"

#: src/overview.md:153
msgid ""
"```\n"
"0°0′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── First cycle\n"
"```"
msgstr ""
"```\n"
"0°0′0″0‴\n"
"│ │ │ ╰─ E' il primo sat nel blocco\n"
"│ │ ╰─── E' il primo blocco nel periodo di aggiustamento della difficoltà\n"
"│ ╰───── E' il primo blocco nell'epoca di dimezzamento\n"
"╰─────── Primo ciclo\n"
"```"

#: src/overview.md:161
msgid ""
"If the block offset is zero, it may be omitted. This is the uncommon satoshi "
"from above:"
msgstr ""
"Se l'offset del blocco è zero, può essere omesso. Questo è il satoshi non "
"comune di cui sopra:"

#: src/overview.md:164
msgid ""
"```\n"
"1°1′1″\n"
"│ │ ╰─ Not first block in difficulty adjustment period\n"
"│ ╰─── Not first block in halving epoch\n"
"╰───── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″\n"
"│ │ ╰─ Non è il primo blocco nel periodo di aggiustamento della difficoltà\n"
"│ ╰─── Non è il primo blocco nell'epoca di dimezzamento\n"
"╰───── Secondo ciclo\n"
"```"

#: src/overview.md:171
msgid "Rare Satoshi Supply"
msgstr "Disponibilità di Satoshi Rari"

#: src/overview.md:174
msgid "Total Supply"
msgstr "Disponibilità Totale"

#: src/overview.md:176
msgid "`common`: 2.1 quadrillion"
msgstr "`comune`: 2.1 quadrilioni"

#: src/overview.md:177
msgid "`uncommon`: 6,929,999"
msgstr "`non comune`: 6,929,999"

#: src/overview.md:178
msgid "`rare`: 3437"
msgstr "`raro`: 3437"

#: src/overview.md:179
msgid "`epic`: 32"
msgstr "`epico`: 32"

#: src/overview.md:180
msgid "`legendary`: 5"
msgstr "`leggendario`: 5"

#: src/overview.md:181
#: src/overview.md:190
msgid "`mythic`: 1"
msgstr "`mitico`: 1"

#: src/overview.md:183
msgid "Current Supply"
msgstr "Offerta Attuale"

#: src/overview.md:185
msgid "`common`: 1.9 quadrillion"
msgstr "`comune`: 1.9 quadrilioni"

#: src/overview.md:186
msgid "`uncommon`: 808,262"
msgstr "`non comune`: 808,262"

#: src/overview.md:187
msgid "`rare`: 369"
msgstr "`raro`: 369"

#: src/overview.md:188
msgid "`epic`: 3"
msgstr "`epico`: 3"

#: src/overview.md:189
msgid "`legendary`: 0"
msgstr "`leggendario`: 0"

#: src/overview.md:192
msgid ""
"At the moment, even uncommon satoshis are quite rare. As of this writing, "
"745,855 uncommon satoshis have been mined - one per 25.6 bitcoin in "
"circulation."
msgstr ""
"Ad oggi, anche i satoshi non comuni sono piuttosto rari. Al momento in cui "
"scriviamo, sono stati estratti 808.262 satoshi non comuni, uno ogni 25,6 "
"bitcoin in circolazione."


#: src/overview.md:196
msgid "Names"
msgstr "I nomi"

#: src/overview.md:199
msgid ""
"Each satoshi has a name, consisting of the letters _A_ through _Z_, that get "
"shorter the further into the future the satoshi was mined. They could start "
"short and get longer, but then all the good, short names would be trapped in "
"the unspendable genesis block."
msgstr ""
"Ogni satoshi ha un nome, composto dalle lettere dalla _A_ alla _Z_, che diventa "
"tanto più corto quanto più il satoshi sarà minato nel futuro. Sarebbero potuti "
"iniziare corti e diventare più lunghi, ma poi tutti i nomi buoni e corti "
"sarebbero rimasti intrappolati nel blocco genesis non spendibile."

#: src/overview.md:204
msgid ""
"As an example, 1905530482684727°'s name is \"iaiufjszmoba\". The name of the "
"last satoshi to be mined is \"a\". Every combination of 10 characters or "
"less is out there, or will be out there, someday."
msgstr ""
"Ad esempio, il nome di 1905530482684727° è \"iaiufjszmoba\". Il nome dell'ultimo "
"satoshi da estrarre è \"a\". Ogni combinazione di 10 caratteri o meno è là fuori, "
"o sarà là fuori, un giorno."

#: src/overview.md:208
msgid "Exotics"
msgstr "Esotici"

#: src/overview.md:211
msgid ""
"Satoshis may be prized for reasons other than their name or rarity. This "
"might be due to a quality of the number itself, like having an integer "
"square or cube root. Or it might be due to a connection to a historical "
"event, such as satoshis from block 477,120, the block in which SegWit "
"activated, or 2099999997689999°, the last satoshi that will ever be mined."
msgstr ""
"I satoshi possono essere apprezzati per ragioni diverse dal loro nome o "
"dalla loro rarità. Ciò potrebbe essere dovuto a una qualità del numero "
"stesso, come il fatto di avere una radice quadrata o cubica intera. Oppure "
"può essere dovuto a un collegamento con un evento storico, come i satoshi del "
"blocco 477.120, il blocco in cui è stato attivato SegWit, o 2099999997689999°, "
"l'ultimo satoshi che sarà mai estratto."

#: src/overview.md:217
msgid ""
"Such satoshis are termed \"exotic\". Which satoshis are exotic and what "
"makes them so is subjective. Ordinal theorists are encouraged to seek out "
"exotics based on criteria of their own devising."
msgstr ""
"Tali satoshi sono definiti \"esotici\". Quali siano i satoshi esotici e cosa "
"li renda tali è soggettivo. I teorici degli Ordinali sono incoraggiati a cercare "
"satoshi esotici in base alle proprie preferenze."


#: src/overview.md:224
msgid ""
"Satoshis can be inscribed with arbitrary content, creating Bitcoin-native "
"digital artifacts. Inscribing is done by sending the satoshi to be inscribed "
"in a transaction that reveals the inscription content on-chain. This content "
"is then inextricably linked to that satoshi, turning it into an immutable "
"digital artifact that can be tracked, transferred, hoarded, bought, sold, "
"lost, and rediscovered."
msgstr ""
"I satoshi possono essere iscritti con contenuti arbitrari, creando artefatti "
"digitali nativi di Bitcoin. L'iscrizione avviene inviando il satoshi da iscrivere "
"in una transazione che rivela il contenuto dell'iscrizione on-chain. Questo contenuto "
"è quindi ora indissolubilmente legato a quel satoshi, trasformandolo in un artefatto "
"digitale immutabile che può essere tracciato, trasferito, collezionato, comprato, "
"venduto, perso e ritrovato."

#: src/overview.md:231
msgid "Archaeology"
msgstr "Archeologia"

#: src/overview.md:234
msgid ""
"A lively community of archaeologists devoted to cataloging and collecting "
"early NFTs has sprung up. [Here's a great summary of historical NFTs by "
"Chainleft.](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)"
msgstr ""
"È nata una vivace comunità di archeologi dedicata alla catalogazione e alla raccolta dei "
"primi NFT. [Ecco un ottimo riassunto di NFT storici a cura "
"di Chainleft.](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)" 

#: src/overview.md:238
msgid ""
"A commonly accepted cut-off for early NFTs is March 19th, 2018, the date the "
"first ERC-721 contract, [SU SQUARES](https://tenthousandsu.com/), was "
"deployed on Ethereum."
msgstr ""
"La data di inizio comunemente accettata per i primi NFT è il 19 marzo 2018, "
"data in cui il primo contratto ERC-721, [SU SQUARES](https://tenthousandsu.com/), "
"è stato distribuito su Ethereum."

#: src/overview.md:242
msgid ""
"Whether or not ordinals are of interest to NFT archaeologists is an open "
"question! In one sense, ordinals were created in early 2022, when the "
"Ordinals specification was finalized. In this sense, they are not of "
"historical interest."
msgstr ""
"Se gli Ordinali siano o meno di interesse per gli archeologi degli NFT è una "
"questione aperta! In un certo senso, gli ordinali sono stati creati all'inizio "
"del 2022, quando è stata finalizzata la specifica Ordinals. Visti cosi, non hanno "
"un grande interesse storico."

#: src/overview.md:247
msgid ""
"In another sense though, ordinals were in fact created by Satoshi Nakamoto "
"in 2009 when he mined the Bitcoin genesis block. In this sense, ordinals, "
"and especially early ordinals, are certainly of historical interest."
msgstr ""
"In un altro senso, invece, gli ordinali sono stati creati da Satoshi Nakamoto "
"nel 2009, quando ha estratto il blocco genesis di Bitcoin. In questo modo, gli "
"ordinali, e soprattutto i primi ordinali, sono certamente di interesse storico."

#: src/overview.md:251
msgid ""
"Many ordinal theorists favor the latter view. This is not least because the "
"ordinals were independently discovered on at least two separate occasions, "
"long before the era of modern NFTs began."
msgstr ""
"Molti teorici degli ordinali sono favorevoli a quest'ultimo punto di vista. "
"Anche perché gli ordinali sono stati scoperti in modo indipendente in almeno "
"due occasioni distinte, molto prima che iniziasse l'era degli NFT moderni."

#: src/overview.md:255
msgid ""
"On August 21st, 2012, Charlie Lee [posted a proposal to add proof-of-stake "
"to Bitcoin to the Bitcoin Talk "
"forum](https://bitcointalk.org/index.php?topic=102355.0). This wasn't an "
"asset scheme, but did use the ordinal algorithm, and was implemented but "
"never deployed."
msgstr ""
"Il 21 agosto 2012, Charlie Lee [ha pubblicato sul forum Bitcoin Talk una "
"proposta per aggiungere la proof-of-stake a "
"Bitcoin](https://bitcointalk.org/index.php?topic=102355.0). Non si "
"trattava di un sistema di beni, ma utilizzava già l'algoritmo ordinale ed è "
"stato implementato ma mai distribuito."

#: src/overview.md:261
msgid ""
"On October 8th, 2012, jl2012 [posted a scheme to the same "
"forum](https://bitcointalk.org/index.php?topic=117224.0) which uses decimal "
"notation and has all the important properties of ordinals. The scheme was "
"discussed but never implemented."
msgstr ""
"L'8 ottobre 2012, jl2012 ha [pubblicato sullo stesso forum uno "
"schema](https://bitcointalk.org/index.php?topic=117224.0) che utilizza "
"la notazione decimale e presenta tutte le proprietà importanti degli ordinali. "
"Lo schema è stato discusso ma mai implementato."

#: src/overview.md:266
msgid ""
"These independent inventions of ordinals indicate in some way that ordinals "
"were discovered, or rediscovered, and not invented. The ordinals are an "
"inevitability of the mathematics of Bitcoin, stemming not from their modern "
"documentation, but from their ancient genesis. They are the culmination of a "
"sequence of events set in motion with the mining of the first block, so many "
"years ago."
msgstr ""
"Queste invenzioni indipendenti degli ordinali indicano in qualche modo che "
"gli ordinali sono stati scoperti, o riscoperti, ma non inventati. Gli ordinali "
"sono inevitabili nella matematica dei Bitcoin, e non derivano dalla loro "
"documentazione moderna, ma dalla loro genesi antica. Sono il culmine di una "
"sequenza di eventi avviati con l'estrazione del primo blocco, molti anni fa."

#: src/digital-artifacts.md:4
msgid ""
"Imagine a physical artifact. A rare coin, say, held safe for untold years in "
"the dark, secret clutch of a Viking hoard, now dug from the earth by your "
"grasping hands. It…"
msgstr ""
"Immaginate un artefatto fisico. Una moneta rara, ad esempio, tenuta al sicuro "
"per anni nell'oscuro e segreto nascondiglio di un tesoro vichingo e ora "
"disseppellita dalla terra dalle vostre mani. Essa…"

#: src/digital-artifacts.md:8
msgid ""
"…has an owner. You. As long as you keep it safe, nobody can take it from you."
msgstr ""
"…ha un proprietario. Voi. Finché lo terrete al sicuro, nessuno potrà portarvelo via."

#: src/digital-artifacts.md:10
msgid "…is complete. It has no missing parts."
msgstr "…è completo. Non ha parti mancanti."

#: src/digital-artifacts.md:12
msgid ""
"…can only be changed by you. If you were a trader, and you made your way to "
"18th century China, none but you could stamp it with your chop-mark."
msgstr ""
"…può essere cambiato solo da voi. Se foste un commerciante e vi recaste nella "
"Cina del XVIII secolo, solo voi potreste imprimere il vostro marchio."

#: src/digital-artifacts.md:15
msgid ""
"…can only be disposed of by you. The sale, trade, or gift is yours to make, "
"to whomever you wish."
msgstr ""
"…può essere smaltito solo da voi. La vendita, il commercio o il dono sono "
"di vostra competenza, a chi volete."

#: src/digital-artifacts.md:18
msgid ""
"What are digital artifacts? Simply put, they are the digital equivalent of "
"physical artifacts."
msgstr ""
"Cosa sono gli artefatti digitali? In poche parole, sono l'equivalente digitale "
"degli artefatti fisici."

#: src/digital-artifacts.md:21
msgid ""
"For a digital thing to be a digital artifact, it must be like that coin of "
"yours:"
msgstr ""
"Perché un oggetto digitale sia un artefatto digitale, deve essere come la "
"vostra moneta:"

#: src/digital-artifacts.md:24
msgid ""
"Digital artifacts can have owners. A number is not a digital artifact, "
"because nobody can own it."
msgstr ""
"Gli artefatti digitali possono avere un proprietario. Un numero non è un "
"artefatto digitale, perché nessuno può possederlo."

#: src/digital-artifacts.md:27
msgid ""
"Digital artifacts are complete. An NFT that points to off-chain content on "
"IPFS or Arweave is incomplete, and thus not a digital artifact."
msgstr ""
"Gli artefatti digitali sono completi. Un NFT che punta a contenuti off-chain "
"su IPFS o Arweave è incompleto e quindi non è un artefatto digitale."

#: src/digital-artifacts.md:30
msgid ""
"Digital artifacts are permissionless. An NFT which cannot be sold without "
"paying a royalty is not permissionless, and thus not a digital artifact."
msgstr ""
"Gli artefatti digitali sono privi di permessi. Un NFT che non può essere "
"venduto senza pagare una royalty non è privo di permessi e quindi non è "
"un artefatto digitale."

#: src/digital-artifacts.md:33
msgid ""
"Digital artifacts are uncensorable. Perhaps you can change a database entry "
"on a centralized ledger today, but maybe not tomorrow, and thus one cannot "
"be a digital artifact."
msgstr ""
"Gli artefatti digitali sono incensurabili. Forse oggi si può cambiare una "
"voce di un database su un libro mastro centralizzato, ma forse non domani, "
"e quindi non può essere un artefatto digitale."

#: src/digital-artifacts.md:37
msgid ""
"Digital artifacts are immutable. An NFT with an upgrade key is not a digital "
"artifact."
msgstr ""
"Gli artefatti digitali sono immutabili. Un NFT con una chiave di aggiornamento "
"non è un artefatto digitale."

#: src/digital-artifacts.md:40
msgid ""
"The definition of a digital artifact is intended to reflect what NFTs "
"_should_ be, sometimes are, and what inscriptions _always_ are, by their "
"very nature."
msgstr ""
"La definizione di artefatto digitale intende riflettere ciò che gli NFT "
"_dovrebbero_ essere, e talvolta sono, e ciò che le iscrizioni sono _sempre_, "
"per loro stessa natura."

#: src/inscriptions.md:4
msgid ""
"Inscriptions inscribe sats with arbitrary content, creating bitcoin-native "
"digital artifacts, more commonly known as NFTs. Inscriptions do not require "
"a sidechain or separate token."
msgstr ""
"Le iscrizioni iscrivono i sats con contenuti arbitrari, creando artefatti "
"digitali nativi di bitcoin, più comunemente noti come NFT. Le iscrizioni non "
"richiedono una sidechain o un token separato."

#: src/inscriptions.md:8
msgid ""
"These inscribed sats can then be transferred using bitcoin transactions, "
"sent to bitcoin addresses, and held in bitcoin UTXOs. These transactions, "
"addresses, and UTXOs are normal bitcoin transactions, addresses, and UTXOS "
"in all respects, with the exception that in order to send individual sats, "
"transactions must control the order and value of inputs and outputs "
"according to ordinal theory."
msgstr ""
"Questi sat iscritti possono essere trasferiti con transazioni bitcoin, inviati "
"a indirizzi bitcoin e conservati in UTXO di Bitcoin. Queste transazioni, indirizzi "
"e UTXO sono normali transazioni, indirizzi e UTXO bitcoin a tutti gli effetti, con "
"l'eccezione che per inviare i singoli sat, le transazioni devono controllare l'ordine "
"e il valore degli input e degli output secondo la teoria ordinale."

#: src/inscriptions.md:15
msgid ""
"The inscription content model is that of the web. An inscription consists of "
"a content type, also known as a MIME type, and the content itself, which is "
"a byte string. This allows inscription content to be returned from a web "
"server, and for creating HTML inscriptions that use and remix the content of "
"other inscriptions."
msgstr ""
"Il modello di contenuto dell'iscrizione è quello del web. Un'iscrizione consiste "
"in un tipo di contenuto, noto anche come tipo MIME, e nel contenuto stesso, che è "
"una stringa di byte. Ciò consente di restituire il contenuto dell'iscrizione da un "
"server web e di creare iscrizioni HTML che utilizzano e rimescolano il contenuto di "
"altre iscrizioni."

#: src/inscriptions.md:21
msgid ""
"Inscription content is entirely on-chain, stored in taproot script-path "
"spend scripts. Taproot scripts have very few restrictions on their content, "
"and additionally receive the witness discount, making inscription content "
"storage relatively economical."
msgstr ""
"Il contenuto dell'iscrizione è interamente on-chain,archiviato negli script di "
"spesa del percorso dello script taproot. Gli script taproot hanno pochissime restrizioni "
"sui loro contenuti e ricevono inoltre lo sconto testimone, rendendo relativamente economica "
"la memorizzazione dei contenuti delle iscrizioni."

#: src/inscriptions.md:26
msgid ""
"Since taproot script spends can only be made from existing taproot outputs, "
"inscriptions are made using a two-phase commit/reveal procedure. First, in "
"the commit transaction, a taproot output committing to a script containing "
"the inscription content is created. Second, in the reveal transaction, the "
"output created by the commit transaction is spent, revealing the inscription "
"content on-chain."
msgstr ""
"Poiché gli script taproot possono essere spesi solo a partire da output taproot "
"esistenti, le iscrizioni vengono effettuate utilizzando una procedura di commit/reveal "
"in due fasi. In primo luogo, nella transazione di commit, viene creata un'uscita taproot "
"che esegue il commit di uno script contenente il contenuto dell'iscrizione. In secondo luogo, "
"nella transazione reveal, l'output creato dalla transazione commit viene speso, rivelando il "
"contenuto dell'iscrizione sulla catena."

#: src/inscriptions.md:33
msgid ""
"Inscription content is serialized using data pushes within unexecuted "
"conditionals, called \"envelopes\". Envelopes consist of an `OP_FALSE OP_IF "
"… OP_ENDIF` wrapping any number of data pushes. Because envelopes are "
"effectively no-ops, they do not change the semantics of the script in which "
"they are included, and can be combined with any other locking script."
msgstr ""
"Il contenuto dell'iscrizione viene serializzato utilizzando push di dati "
"all'interno di condizioni non eseguite, chiamate \"buste\" (envelopes). "
"Queste buste consistono in un `OP_FALSE OP_IF "
"… OP_ENDIF` che avvolge un numero qualsiasi di push di dati. Poiché questi "
"involucri sono effettivamente no-ops, non cambiano la semantica dello script "
"in cui sono inclusi e possono essere combinati con qualsiasi altro script di chiusura."


#: src/inscriptions.md:39
msgid ""
"A text inscription containing the string \"Hello, world!\" is serialized as "
"follows:"
msgstr ""
"Un'iscrizione di testo contenente la stringa \"Hello, world!\" viene serializzata "
"come segue:"

#: src/inscriptions.md:42
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions.md:53
msgid ""
"First the string `ord` is pushed, to disambiguate inscriptions from other "
"uses of envelopes."
msgstr ""
"Per prima cosa viene inserita la stringa `ord`, per distinguere le iscrizioni "
"da altri usi delle buste."

#: src/inscriptions.md:56
msgid ""
"`OP_PUSH 1` indicates that the next push contains the content type, and "
"`OP_PUSH 0`indicates that subsequent data pushes contain the content itself. "
"Multiple data pushes must be used for large inscriptions, as one of "
"taproot's few restrictions is that individual data pushes may not be larger "
"than 520 bytes."
msgstr ""
"`OP_PUSH 1` indica che il prossimo push contiene il tipo di contenuto, mentre "
"`OP_PUSH 0` indica che i successivi push di dati contengono il contenuto stesso. "
"Per iscrizioni di grandi dimensioni è necessario utilizzare più push di dati, "
"poiché una delle poche restrizioni di taproot è che i singoli push di dati non "
"possono essere più grandi di 520 byte."

#: src/inscriptions.md:62
msgid ""
"The inscription content is contained within the input of a reveal "
"transaction, and the inscription is made on the first sat of its input. This "
"sat can then be tracked using the familiar rules of ordinal theory, allowing "
"it to be transferred, bought, sold, lost to fees, and recovered."
msgstr ""
"Il contenuto dell'iscrizione è contenuto nell'input di una transazione di "
"rivelazione e l'iscrizione viene effettuata sul primo satoshi del suo input. "
"Questo satoshi può quindi essere rintracciato utilizzando le note regole della "
"teoria ordinale, consentendo di trasferirlo, comprarlo, venderlo, perderlo nelle "
"fees e recuperarlo."

#: src/inscriptions.md:67
msgid "Content"
msgstr "Il Contenuto"

#: src/inscriptions.md:70
msgid ""
"The data model of inscriptions is that of a HTTP response, allowing "
"inscription content to be served by a web server and viewed in a web browser."
msgstr ""
"Il modello di dati delle iscrizioni è quello di una risposta HTTP, che rende "
"disponibile il contenuto dell'iscrizione da un server web e lo rende visualizzabile "
"in un browser web."

#: src/inscriptions.md:73
msgid "Fields"
msgstr "I Campi"

#: src/inscriptions.md:76
msgid ""
"Inscriptions may include fields before an optional body. Each field consists "
"of two data pushes, a tag and a value."
msgstr ""
"Le iscrizioni possono includere campi prima di un corpo opzionale. Ogni campo è "
"composto da due dati, un tag e un valore."

#: src/inscriptions.md:79
msgid ""
"Currently, the only defined field is `content-type`, with a tag of `1`, "
"whose value is the MIME type of the body."
msgstr ""
"Attualmente, l'unico campo definito è il `content-type`, con un tag di `1`, "
"il cui valore è il tipo MIME del corpo."

#: src/inscriptions.md:82
msgid ""
"The beginning of the body and end of fields is indicated with an empty data "
"push."
msgstr ""
"L'inizio del corpo e la fine dei campi sono indicati con un push di dati "
"vuoto."

#: src/inscriptions.md:85
msgid ""
"Unrecognized tags are interpreted differently depending on whether they are "
"even or odd, following the \"it's okay to be odd\" rule used by the "
"Lightning Network."
msgstr ""
"I tag non riconosciuti vengono interpretati in modo diverso a seconda che siano "
"pari o dispari, seguendo la regola \"va bene essere dispari\" utilizzata dalla "
"Lightning Network."

#: src/inscriptions.md:89
msgid ""
"Even tags are used for fields which may affect creation, initial assignment, "
"or transfer of an inscription. Thus, inscriptions with unrecognized even "
"fields must be displayed as \"unbound\", that is, without a location."
msgstr ""
"I tag pari sono utilizzati per i campi che possono influenzare la creazione, "
"l'assegnazione iniziale o il trasferimento di un'iscrizione. Pertanto, le "
"iscrizioni con campi pari non riconosciuti devono essere visualizzate "
"come \"non vincolate\", cioè senza una posizione."

#: src/inscriptions.md:93
msgid ""
"Odd tags are used for fields which do not affect creation, initial "
"assignment, or transfer, such as additional metadata, and thus are safe to "
"ignore."
msgstr ""
"I tag dispari sono utilizzati per i campi che non influiscono sulla creazione, "
"sull'assegnazione iniziale o sul trasferimento, come i metadati aggiuntivi, e "
"quindi possono essere tranquillamente ignorati."

#: src/inscriptions.md:96
msgid "Inscription IDs"
msgstr "ID dell'iscrizione"

#: src/inscriptions.md:99
msgid ""
"The inscriptions are contained within the inputs of a reveal transaction. In "
"order to uniquely identify them they are assigned an ID of the form:"
msgstr ""
"Le iscrizioni sono contenute negli input di una transazione di rivelazione. Per "
"identificarle in modo univoco, viene loro assegnato un ID del tipo:"

#: src/inscriptions.md:102
msgid "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"
msgstr "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"

#: src/inscriptions.md:104
msgid ""
"The part in front of the `i` is the transaction ID (`txid`) of the reveal "
"transaction. The number after the `i` defines the index (starting at 0) of "
"new inscriptions being inscribed in the reveal transaction."
msgstr ""
"La parte davanti alla `i` è l'ID della transazione (`txid`) della transazione rivelata. "
"Il numero dopo la `i` definisce l'indice (a partire da 0) delle nuove iscrizioni che "
"vengono inserite nella transazione di rivelazione."

#: src/inscriptions.md:108
msgid ""
"Inscriptions can either be located in different inputs, within the same "
"input or a combination of both. In any case the ordering is clear, since a "
"parser would go through the inputs consecutively and look for all "
"inscription `envelopes`."
msgstr ""
"Le iscrizioni possono trovarsi in ingressi diversi, all'interno dello stesso "
"ingresso o in una combinazione di entrambi. In ogni caso, l'ordine è chiaro, "
"poiché un parser dovrebbe passare in rassegna gli ingressi consecutivamente e "
"cercare tutte le `buste` di iscrizioni."

#: src/inscriptions.md:112
msgid "Input"
msgstr "Input"

#: src/inscriptions.md:112
msgid "Inscription Count"
msgstr "Conta delle Iscrizioni"

#: src/inscriptions.md:112
msgid "Indices"
msgstr "Indici"

#: src/inscriptions.md:114
#: src/inscriptions.md:117
msgid "0"
msgstr "0"

#: src/inscriptions.md:114
#: src/inscriptions.md:116
msgid "2"
msgstr "2"

#: src/inscriptions.md:114
msgid "i0, i1"
msgstr "i0, i1"

#: src/inscriptions.md:115
#: src/inscriptions.md:115
#: src/inscriptions.md:118
msgid "1"
msgstr "1"

#: src/inscriptions.md:115
msgid "i2"
msgstr "i2"

#: src/inscriptions.md:116
#: src/inscriptions.md:117
msgid "3"
msgstr "3"

#: src/inscriptions.md:116
msgid "i3, i4, i5"
msgstr "i3, i4, i5"

#: src/inscriptions.md:118
msgid "4"
msgstr "4"

#: src/inscriptions.md:118
msgid "i6"
msgstr "i6"

#: src/inscriptions.md:120
msgid "Sandboxing"
msgstr "Sandboxing"

#: src/inscriptions.md:123
msgid ""
"HTML and SVG inscriptions are sandboxed in order to prevent references to "
"off-chain content, thus keeping inscriptions immutable and self-contained."
msgstr ""
"Le iscrizioni HTML e SVG sono sottoposte a sandboxing per evitare riferimenti "
"a contenuti esterni alla catena, mantenendo così le iscrizioni immutabili e autonome."

#: src/inscriptions.md:126
msgid ""
"This is accomplished by loading HTML and SVG inscriptions inside `iframes` "
"with the `sandbox` attribute, as well as serving inscription content with "
"`Content-Security-Policy` headers."
msgstr ""
"Ciò si ottiene caricando le iscrizioni HTML e SVG all'interno di `iframes` con "
"l'attributo `sandbox` e servendo il contenuto dell'iscrizione con intestazioni "
"`Content-Security-Policy`."

#: src/inscriptions/metadata.md:4
msgid ""
"Inscriptions may include [CBOR](https://cbor.io/) metadata, stored as data "
"pushes in fields with tag `5`. Since data pushes are limited to 520 bytes, "
"metadata longer than 520 bytes must be split into multiple tag `5` fields, "
"which will then be concatenated before decoding."
msgstr ""
"Le iscrizioni possono contenere metadati [CBOR](https://cbor.io/), memorizzati "
"come push di dati in campi con tag `5`. Poiché i data push sono limitati a 520 "
"byte, i metadati più lunghi di 520 byte devono essere suddivisi in più campi con "
"tag `5`, che saranno poi concatenati prima della decodifica."

#: src/inscriptions/metadata.md:9
msgid ""
"Metadata is human readable, and all metadata will be displayed to the user "
"with its inscription. Inscribers are encouraged to consider how metadata "
"will be displayed, and make metadata concise and attractive."
msgstr ""
"I metadata sono facili da leggere per l'uomo e tutti i metadata saranno "
"visualizzati dall'utente con la loro iscrizione. Coloro che iscrivono sono "
"incoraggiati a considerare il modo in cui i metadati saranno visualizzati e a "
"renderli concisi e attraenti."

#: src/inscriptions/metadata.md:13
msgid "Metadata is rendered to HTML for display as follows:"
msgstr "I metadati sono renderizzati in HTML per la visualizzazione come segue:"

#: src/inscriptions/metadata.md:15
msgid ""
"`null`, `true`, `false`, numbers, floats, and strings are rendered as plain "
"text."
msgstr ""
"`null`, `true`, `false`, numbers, floats, e strings vengono renderizzate come "
"testo normale."

#: src/inscriptions/metadata.md:17
msgid "Byte strings are rendered as uppercase hexadecimal."
msgstr "Le stringhe di byte vengono rese come esadecimali maiuscole."

#: src/inscriptions/metadata.md:18
msgid ""
"Arrays are rendered as `<ul>` tags, with every element wrapped in `<li>` "
"tags."
msgstr ""
"Gli array sono resi come tag `<ul>`, con ogni elemento avvolto dai tag `<li>`."

#: src/inscriptions/metadata.md:20
msgid ""
"Maps are rendered as `<dl>` tags, with every key wrapped in `<dt>` tags, and "
"every value wrapped in `<dd>` tags."
msgstr ""
"Le mappe sono rese come  `<dl>` tags, con ogni chiave racchiusa in tag `<dt>` e "
"ogni valore racchiuso in tag `<dd>`."

#: src/inscriptions/metadata.md:22
msgid ""
"Tags are rendered as the tag , enclosed in a `<sup>` tag, followed by the "
"value."
msgstr ""
"I tag sono resi come il tag , racchiuso in un tag `<sup>`, seguito dal valore."

#: src/inscriptions/metadata.md:25
msgid ""
"CBOR is a complex spec with many different data types, and multiple ways of "
"representing the same data. Exotic data types, such as tags, floats, and "
"bignums, and encoding such as indefinite values, may fail to display "
"correctly or at all. Contributions to `ord` to remedy this are welcome."
msgstr ""
"CBOR è una specifica complessa con molti tipi di dati diversi e molteplici "
"modi di rappresentare gli stessi dati. I tipi di dati esotici, come i tag, "
"floats e bignums, e le codifiche come i valori indefiniti, potrebbero non "
"essere visualizzati correttamente o affatto. Sono benvenuti i contributi "
"ad `ord` per porre rimedio a questo problema."

#: src/inscriptions/metadata.md:30
#: src/inscriptions/provenance.md:27
#: src/guides/teleburning.md:23
#: src/guides/testing.md:18
#: src/guides/reindexing.md:15
msgid "Example"
msgstr "Esempio"

#: src/inscriptions/metadata.md:33
msgid ""
"Since CBOR is not human readable, in these examples it is represented as "
"JSON. Keep in mind that this is _only_ for these examples, and JSON metadata "
"will _not_ be displayed correctly."
msgstr ""
"Poiché il CBOR non è leggibile dall'uomo, in questi esempi è rappresentato come "
"JSON. Si tenga presente che questo è _solo_ per questi esempi e che i metadati "
"JSON _non_ saranno visualizzati correttamente."

#: src/inscriptions/metadata.md:37
msgid ""
"The metadata `{\"foo\":\"bar\",\"baz\":[null,true,false,0]}` would be "
"included in an inscription as:"
msgstr ""
"I metadati `{\"foo\":\"bar\",\"baz\":[null,true,false,0]}` verrebbero "
"inclusi in un'iscrizione come:"

#: src/inscriptions/metadata.md:39
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"foo\":\"bar\",\"baz\":[null,true,false,0]}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"foo\":\"bar\",\"baz\":[null,true,false,0]}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/metadata.md:48
msgid "And rendered as:"
msgstr "E renderizzati come:"

#: src/inscriptions/metadata.md:50
msgid ""
"```\n"
"<dl>\n"
"  ...\n"
"  <dt>metadata</dt>\n"
"  <dd>\n"
"    <dl>\n"
"      <dt>foo</dt>\n"
"      <dd>bar</dd>\n"
"      <dt>baz</dt>\n"
"      <dd>\n"
"        <ul>\n"
"          <li>null</li>\n"
"          <li>true</li>\n"
"          <li>false</li>\n"
"          <li>0</li>\n"
"        </ul>\n"
"      </dd>\n"
"    </dl>\n"
"  </dd>\n"
"  ...\n"
"</dl>\n"
"```"
msgstr ""
"```\n"
"<dl>\n"
"  ...\n"
"  <dt>metadata</dt>\n"
"  <dd>\n"
"    <dl>\n"
"      <dt>foo</dt>\n"
"      <dd>bar</dd>\n"
"      <dt>baz</dt>\n"
"      <dd>\n"
"        <ul>\n"
"          <li>null</li>\n"
"          <li>true</li>\n"
"          <li>false</li>\n"
"          <li>0</li>\n"
"        </ul>\n"
"      </dd>\n"
"    </dl>\n"
"  </dd>\n"
"  ...\n"
"</dl>\n"
"```"

#: src/inscriptions/metadata.md:73
msgid "Metadata longer than 520 bytes must be split into multiple fields:"
msgstr "I metadati di lunghezza superiore a 520 byte devono essere suddivisi in più campi:"

#: src/inscriptions/metadata.md:75
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"very\":\"long\",\"metadata\":'\n"
"    OP_PUSH 0x05 OP_PUSH '\"is\",\"finally\":\"done\"}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"very\":\"long\",\"metadata\":'\n"
"    OP_PUSH 0x05 OP_PUSH '\"is\",\"finally\":\"done\"}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/metadata.md:85
msgid ""
"Which would then be concatinated into "
"`{\"very\":\"long\",\"metadata\":\"is\",\"finally\":\"done\"}`."
msgstr ""
"Che verrebbero poi concatenati in "
"`{\"very\":\"long\",\"metadata\":\"is\",\"finally\":\"done\"}`."

#: src/inscriptions/provenance.md:4
msgid ""
"The owner of an inscription can create child inscriptions, trustlessly "
"establishing the provenance of those children on-chain as having been "
"created by the owner of the parent inscription. This can be used for "
"collections, with the children of a parent inscription being members of the "
"same collection."
msgstr ""
"Il proprietario di un'iscrizione può creare iscrizioni figlie (child), "
"stabilendo in modo affidabile la provenienza di queste ultime on-chain come "
"se fossero state create dal proprietario dell'iscrizione madre (parent). "
"Questo può essere utilizzato per le collezioni, dove i figli di un'iscrizione "
"madre sono membri della stessa collezione."

#: src/inscriptions/provenance.md:9
msgid ""
"Children can themselves have children, allowing for complex hierarchies. For "
"example, an artist might create an inscription representing themselves, with "
"sub inscriptions representing collections that they create, with the "
"children of those sub inscriptions being items in those collections."
msgstr ""
"I figli possono avere a loro volta dei figli, consentendo di creare gerarchie "
"complesse. Ad esempio, un artista potrebbe creare un'iscrizione che rappresenta "
"se stesso, con sotto-iscrizioni che rappresentano le collezioni da lui create, e "
"i figli di queste sotto-iscrizioni sono elementi di tali collezioni."

#: src/inscriptions/provenance.md:14
msgid "Specification"
msgstr "Specifiche"

#: src/inscriptions/provenance.md:16
msgid "To create a child inscription C with parent inscription P:"
msgstr "Per creare un'iscrizione child C con un'iscrizione parent P:"

#: src/inscriptions/provenance.md:18
msgid "Create an inscribe transaction T as usual for C."
msgstr "Creare una transazione di iscrizione T come di consueto per C."

#: src/inscriptions/provenance.md:19
msgid "Spend the parent P in one of the inputs of T."
msgstr "Spendere il genitore P in uno degli ingressi di T."

#: src/inscriptions/provenance.md:20
msgid ""
"Include tag `3`, i.e. `OP_PUSH 3`, in C, with the value of the serialized "
"binary inscription ID of P, serialized as the 32-byte `TXID`, followed by "
"the four-byte little-endian `INDEX`, with trailing zeroes omitted."
msgstr ""
"Includere il tag `3`, cioè `OP_PUSH 3`, in C, con il valore dell'ID binario "
"serializzato dell'iscrizione P, serializzato come `TXID` a 32 byte, seguito "
"dal little-endian `INDEX` a quattro byte, con gli zeri finali omessi."

#: src/inscriptions/provenance.md:24
msgid ""
"_NB_ The bytes of a bitcoin transaction ID are reversed in their text "
"representation, so the serialized transaction ID will be in the opposite "
"order."
msgstr ""
"_NB_ I byte dell'ID di una transazione bitcoin sono invertiti nella loro "
"rappresentazione testuale, quindi l'ID della transazione serializzato sarà "
"nell'ordine opposto."

#: src/inscriptions/provenance.md:29
msgid ""
"An example of a child inscription of "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"
msgstr ""
"Un esempio di un iscrizione figlia di "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"

#: src/inscriptions/provenance.md:32
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:45
msgid ""
"Note that the value of tag `3` is binary, not hex, and that for the child "
"inscription to be recognized as a child, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` must be "
"spent as one of the inputs of the inscribe transaction."
msgstr ""
"Si noti che il valore del tag `3` è binario, non esadecimale, e che affinché "
"l'iscrizione del figlio sia riconosciuta come tale, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` deve essere "
"speso come uno degli input della transazione d'iscrizione."

#: src/inscriptions/provenance.md:50
msgid ""
"Example encoding of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"
msgstr ""
"Esempio di codifica dell'ID iscrizione "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"

#: src/inscriptions/provenance.md:53
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100ff\n"
"  …\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100ff\n"
"  …\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:63
msgid ""
"And of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"
msgstr ""
"E dell'ID di iscrizione "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"

#: src/inscriptions/provenance.md:65
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a090807060504030201000001\n"
"  …\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a090807060504030201000001\n"
"  …\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:75
msgid "Notes"
msgstr "Note"

#: src/inscriptions/provenance.md:77
msgid ""
"The tag `3` is used because it is the first available odd tag. Unrecognized "
"odd tags do not make an inscription unbound, so child inscriptions would be "
"recognized and tracked by old versions of `ord`."
msgstr ""
"L'etichetta `3` viene utilizzata perché è la prima etichetta dispari "
"disponibile. Le etichette dispari non riconosciute non rendono un'iscrizione "
"non vincolata, quindi le iscrizioni figlie verrebbero riconosciute e tracciate "
"dalle vecchie versioni di `ord`."

#: src/inscriptions/provenance.md:81
msgid ""
"A collection can be closed by burning the collection's parent inscription, "
"which guarantees that no more items in the collection can be issued."
msgstr ""
"Una collezione può essere chiusa bruciando l'iscrizione madre della collezione, "
"il che garantisce che non possano essere emessi altri elementi della collezione."

#: src/inscriptions/recursion.md:4
msgid ""
"An important exception to [sandboxing](../inscriptions.md#sandboxing) is "
"recursion: access to `ord`'s `/content` endpoint is permitted, allowing "
"inscriptions to access the content of other inscriptions by requesting "
"`/content/<INSCRIPTION_ID>`."
msgstr ""
"Un'importante eccezione al [sandboxing](../inscriptions.md#sandboxing) è la "
"recursion: l'accesso all'endpoint `/content` di `ord` è consentito, "
"permettendo alle iscrizioni di accedere al contenuto di altre iscrizioni "
"richiedendo `/content/<INSCRIPTION_ID>`."

#: src/inscriptions/recursion.md:9
msgid "This has a number of interesting use-cases:"
msgstr "Questo ha una serie di casi d'uso interessanti:"

#: src/inscriptions/recursion.md:11
msgid "Remixing the content of existing inscriptions."
msgstr "Remixare il contenuto di iscrizioni esistenti."

#: src/inscriptions/recursion.md:13
msgid ""
"Publishing snippets of code, images, audio, or stylesheets as shared public "
"resources."
msgstr ""
"Pubblicare frammenti di codice, immagini, audio o fogli di stile come risorse "
"pubbliche condivise."

#: src/inscriptions/recursion.md:16
msgid ""
"Generative art collections where an algorithm is inscribed as JavaScript, "
"and instantiated from multiple inscriptions with unique seeds."
msgstr ""
"Collezioni di arte generativa in cui un algoritmo è inscritto come "
"JavaScript e istanziato da più iscrizioni con semi unici."

#: src/inscriptions/recursion.md:19
msgid ""
"Generative profile picture collections where accessories and attributes are "
"inscribed as individual images, or in a shared texture atlas, and then "
"combined, collage-style, in unique combinations in multiple inscriptions."
msgstr ""
"Collezioni di immagini di profili generativi in cui gli accessori e gli attributi "
"sono inscritti come immagini individuali o in un atlante di texture condiviso e poi "
"combinati, in stile collage, in combinazioni uniche in più iscrizioni."

#: src/inscriptions/recursion.md:23
msgid "A few other endpoints that inscriptions may access are the following:"
msgstr "Alcuni altri endpoint a cui le iscrizioni possono accedere sono i seguenti:"

#: src/inscriptions/recursion.md:25
msgid "`/blockheight`: latest block height."
msgstr "`/blockheight`: ultima altezza del blocco."

#: src/inscriptions/recursion.md:26
msgid "`/blockhash`: latest block hash."
msgstr "`/blockhash`: hash del blocco più recente."

#: src/inscriptions/recursion.md:27
msgid "`/blockhash/<HEIGHT>`: block hash at given block height."
msgstr "`/blockhash/<HEIGHT>`: hash del blocco data la sua altezza."

#: src/inscriptions/recursion.md:28
msgid "`/blocktime`: UNIX time stamp of latest block."
msgstr "`/blocktime`: Ora UNIX dell'ultimo blocco."

#: src/inscriptions/pointer.md:4
msgid ""
"In order to make an inscription on a sat other than the first of its input, "
"a zero-based integer, called the \"pointer\", can be provided with tag `2`, "
"causing the inscription to be made on the sat at the given position in the "
"outputs. If the pointer is equal to or greater than the number of total sats "
"in the outputs of the inscribe transaction, it is ignored, and the "
"inscription is made as usual. The value of the pointer field is a little "
"endian integer, with trailing zeroes ignored."
msgstr ""
"Per effettuare un'iscrizione su un sat diverso dal primo del suo input, è possibile "
"fornire un intero a base zero, chiamato \"puntatore\", con il tag `2`, che fa sì che "
"l'iscrizione venga effettuata sul sat nella posizione data nei suoi outputs. Se il "
"puntatore è uguale o superiore al numero di sat totali presenti nelle uscite della "
"transazione inscritte, viene ignorato e l'iscrizione viene effettuata come di consueto. "
"Il valore del campo del puntatore è un intero little endian, con gli zeri finali ignorati."

#: src/inscriptions/pointer.md:12
msgid ""
"An even tag is used, so that old versions of `ord` consider the inscription "
"to be unbound, instead of assigning it, incorrectly, to the first sat."
msgstr ""
"Viene utilizzato un tag pari, in modo che le vecchie versioni di `ord` considerino "
"l'iscrizione come non vincolata, invece di assegnarla, erroneamente, al primo sat."

#: src/inscriptions/pointer.md:15
msgid ""
"This can be used to create multiple inscriptions in a single transaction on "
"different sats, when otherwise they would be made on the same sat."
msgstr ""
"Questo può essere usato per creare più iscrizioni in una singola transazione "
"su satoshi diversi, quando altrimenti verrebbero fatte sullo stesso satoshi."

#: src/inscriptions/pointer.md:18
msgid "Examples"
msgstr "Esempi"

#: src/inscriptions/pointer.md:21
msgid "An inscription with pointer 255:"
msgstr "Un'iscrizione con puntatore 255:"

#: src/inscriptions/pointer.md:23
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 2\n"
"  OP_PUSH 0xff\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 2\n"
"  OP_PUSH 0xff\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/pointer.md:36
msgid "An inscription with pointer 256:"
msgstr "Un'iscrizione con puntatore 256:"

#: src/inscriptions/pointer.md:38
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 2\n"
"  OP_PUSH 0x0001\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 2\n"
"  OP_PUSH 0x0001\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/pointer.md:51
msgid "An inscription with pointer 256, with trailing zeroes, which are ignored:"
msgstr "Un'iscrizione con puntatore 256, con zeri finali, che vengono ignorati:"

#: src/inscriptions/pointer.md:53
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 2\n"
"  OP_PUSH 0x000100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 2\n"
"  OP_PUSH 0x000100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/faq.md:1
msgid "Ordinal Theory FAQ"
msgstr "FAQ sulla Teoria Ordinale"

#: src/faq.md:4
msgid "What is ordinal theory?"
msgstr "Cos'è la teoria ordinale?"

#: src/faq.md:7
msgid ""
"Ordinal theory is a protocol for assigning serial numbers to satoshis, the "
"smallest subdivision of a bitcoin, and tracking those satoshis as they are "
"spent by transactions."
msgstr ""
"La teoria ordinale è un protocollo per l'assegnazione di numeri seriali ai "
"satoshi, la più piccola unità di un bitcoin, e per il monitoraggio di tali "
"satoshi man mano che vengono spesi nelle transazioni."

#: src/faq.md:11
msgid ""
"These serial numbers are large numbers, like this 804766073970493. Every "
"satoshi, which is ¹⁄₁₀₀₀₀₀₀₀₀ of a bitcoin, has an ordinal number."
msgstr ""
"Questi numeri di serie sono grandi numeri, come questo 804766073970493. "
"Ogni satoshi, ovvero ¹⁄₁₀₀₀₀₀₀₀₀ (1/100 milioni) di un bitcoin, ha un numero ordinale."

#: src/faq.md:14
msgid ""
"Does ordinal theory require a side chain, a separate token, or changes to "
"Bitcoin?"
msgstr ""
"La teoria degli ordinali richiede una side chain, un token separato o "
"modifiche a Bitcoin?"

#: src/faq.md:17
msgid ""
"Nope! Ordinal theory works right now, without a side chain, and the only "
"token needed is bitcoin itself."
msgstr ""
"No! La teoria ordinale funziona già adesso, senza una catena secondaria, "
"e l'unico token necessario è il bitcoin stesso."

#: src/faq.md:20
msgid "What is ordinal theory good for?"
msgstr "A cosa serve la teoria ordinale?"

#: src/faq.md:23
msgid ""
"Collecting, trading, and scheming. Ordinal theory assigns identities to "
"individual satoshis, allowing them to be individually tracked and traded, as "
"curios and for numismatic value."
msgstr ""
"A collezionare, scambiare e progettare. La teoria ordinale assegna identità ai "
"singoli satoshi, consentendo loro di essere rintracciati e scambiati individualmente, "
"come collezionabili e per il loro valore numismatico."

#: src/faq.md:27
msgid ""
"Ordinal theory also enables inscriptions, a protocol for attaching arbitrary "
"content to individual satoshis, turning them into bitcoin-native digital "
"artifacts."
msgstr ""
"La teoria ordinale consente anche di inserire iscrizioni, un protocollo per "
"allegare contenuti arbitrari ai singoli satoshi, trasformandoli in artefatti "
"digitali nativi di bitcoin."

#: src/faq.md:31
msgid "How does ordinal theory work?"
msgstr "Come funziona la teoria ordinale?"

#: src/faq.md:34
msgid ""
"Ordinal numbers are assigned to satoshis in the order in which they are "
"mined. The first satoshi in the first block has ordinal number 0, the second "
"has ordinal number 1, and the last satoshi of the first block has ordinal "
"number 4,999,999,999."
msgstr ""
"I numeri ordinali vengono assegnati ai satoshi nell'ordine in cui vengono minati. "
"Il primo satoshi del primo blocco ha il numero ordinale 0, il secondo ha il numero "
"ordinale 1, e l'ultimo satoshi del primo blocco ha il numero ordinale 4.999.999.999."

#: src/faq.md:39
msgid ""
"Satoshis live in outputs, but transactions destroy outputs and create new "
"ones, so ordinal theory uses an algorithm to determine how satoshis hop from "
"the inputs of a transaction to its outputs."
msgstr ""
"I satoshi vivono negli output, ma le transazioni distruggono gli output e ne "
"creano di nuovi, quindi la teoria ordinale utilizza un algoritmo per determinare "
"come i satoshi passano dagli input di una transazione ai suoi output."

#: src/faq.md:43
msgid "Fortunately, that algorithm is very simple."
msgstr "Fortunatamente, questo algoritmo è molto semplice."

#: src/faq.md:45
msgid ""
"Satoshis transfer in first-in-first-out order. Think of the inputs to a "
"transaction as being a list of satoshis, and the outputs as a list of slots, "
"waiting to receive a satoshi. To assign input satoshis to slots, go through "
"each satoshi in the inputs in order, and assign each to the first available "
"slot in the outputs."
msgstr ""
"I satoshi vengono trasferiti nell'ordine first-in-first-out (FIFO). Considerate "
"gli input di una transazione come un elenco di satoshi e gli output come un elenco "
"di slot in attesa di ricevere un satoshi. Per assegnare i satoshi input agli slot, "
"si esamina ogni input satoshi in ordine e lo si assegna al primo slot disponibile "
"degli output."

#: src/faq.md:51
msgid ""
"Let's imagine a transaction with three inputs and two outputs. The inputs "
"are on the left of the arrow and the outputs are on the right, all labeled "
"with their values:"
msgstr ""
"Immaginiamo una transazione con tre input e due output. Gli ingressi sono a "
"sinistra della freccia e le uscite a destra, tutte etichettate con i loro valori:"

#: src/faq.md:55
msgid ""
"```\n"
"[2] [1] [3] → [4] [2]\n"
"```"
msgstr ""
"```\n"
"[2] [1] [3] → [4] [2]\n"
"```"

#: src/faq.md:59
msgid ""
"Now let's label the same transaction with the ordinal numbers of the "
"satoshis that each input contains, and question marks for each output slot. "
"Ordinal numbers are large, so let's use letters to represent them:"
msgstr ""
"Ora etichettiamo la stessa transazione con i numeri ordinali dei satoshi "
"che ogni input contiene e con i punti interrogativi per ogni output slot. "
"I numeri ordinali sono grandi, quindi usiamo delle lettere per rappresentarli:"

#: src/faq.md:63
msgid ""
"```\n"
"[a b] [c] [d e f] → [? ? ? ?] [? ?]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [? ? ? ?] [? ?]\n"
"```"

#: src/faq.md:67
msgid ""
"To figure out which satoshi goes to which output, go through the input "
"satoshis in order and assign each to a question mark:"
msgstr ""
"Per capire quale satoshi va a quale uscita, scorrete gli input satoshi in "
"ordine e assegnate a ciascuno un punto interrogativo:"

#: src/faq.md:70
msgid ""
"```\n"
"[a b] [c] [d e f] → [a b c d] [e f]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [a b c d] [e f]\n"
"```"

#: src/faq.md:74
msgid ""
"What about fees, you might ask? Good question! Let's imagine the same "
"transaction, this time with a two satoshi fee. Transactions with fees send "
"more satoshis in the inputs than are received by the outputs, so to make our "
"transaction into one that pays fees, we'll remove the second output:"
msgstr ""
"E le tasse, vi chiederete? Bella domanda! Immaginiamo la stessa transazione, "
"questa volta con una commissione di due satoshi. Le transazioni con commissioni "
"inviano più satoshi in ingresso di quanti ne ricevano in uscita, quindi per "
"trasformare la nostra transazione in una che paga le commissioni, rimuoveremo "
"il secondo output:"

#: src/faq.md:79
msgid ""
"```\n"
"[2] [1] [3] → [4]\n"
"```"
msgstr ""
"```\n"
"[2] [1] [3] → [4]\n"
"```"

#: src/faq.md:83
msgid "The satoshis "
msgstr "I satoshi "

#: src/faq.md:83
msgid "e"
msgstr "e"

#: src/faq.md:83
msgid " and "
msgstr " e "

#: src/faq.md:83
msgid "f"
msgstr "f"

#: src/faq.md:83
msgid " now have nowhere to go in the outputs:"
msgstr " ora non hanno più spazio negli output:"

#: src/faq.md:86
msgid ""
"```\n"
"[a b] [c] [d e f] → [a b c d]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [a b c d]\n"
"```"

#: src/faq.md:90
msgid ""
"So they go to the miner who mined the block as fees. [The "
"BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) has the "
"details, but in short, fees paid by transactions are treated as extra inputs "
"to the coinbase transaction, and are ordered how their corresponding "
"transactions are ordered in the block. The coinbase transaction of the block "
"might look like this:"
msgstr ""
"Quindi vanno al minatore che ha estratto il blocco come commissioni. [Il "
"BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) contiene i "
"dettagli, ma in breve le commissioni pagate dalle transazioni sono trattate come "
"input aggiuntivi alla transazione coinbase e sono ordinate come sono ordinate le "
"transazioni corrispondenti nel blocco. La transazione coinbase del blocco potrebbe "
"apparire come segue:"

#: src/faq.md:97
msgid ""
"```\n"
"[SUBSIDY] [e f] → [SUBSIDY e f]\n"
"```"
msgstr ""
"```\n"
"[SUBSIDY] [e f] → [SUBSIDY e f]\n"
"```"

#: src/faq.md:101
msgid "Where can I find the nitty-gritty details?"
msgstr "Dove posso trovare i dettagli più precisi?"

#: src/faq.md:104
msgid "[The BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[Nel BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src/faq.md:106
msgid ""
"Why are sat inscriptions called \"digital artifacts\" instead of \"NFTs\"?"
msgstr ""
"Perché le iscrizioni sat sono chiamate \"artefatti digitali\" anziché \"NFT\"?"

#: src/faq.md:109
msgid ""
"An inscription is an NFT, but the term \"digital artifact\" is used instead, "
"because it's simple, suggestive, and familiar."
msgstr ""
"Un'iscrizione è un NFT, ma si usa il termine \"artefatto digitale\" perché è "
"semplice, suggestivo e familiare."

#: src/faq.md:112
msgid ""
"The phrase \"digital artifact\" is highly suggestive, even to someone who "
"has never heard the term before. In comparison, NFT is an acronym, and "
"doesn't provide any indication of what it means if you haven't heard the "
"term before."
msgstr ""
"L'espressione \"artefatto digitale\" è molto suggestiva, anche per chi non "
"l'ha mai sentita prima. In confronto, NFT è un acronimo e non fornisce alcuna "
"indicazione sul suo significato se non lo si è mai sentito prima."

#: src/faq.md:116
msgid ""
"Additionally, \"NFT\" feels like financial terminology, and the both word "
"\"fungible\" and sense of the word \"token\" as used in \"NFT\" is uncommon "
"outside of financial contexts."
msgstr ""
"Inoltre, \"NFT\" sembra una terminologia finanziaria, e sia la parola \"fungibile\" "
"che il senso della parola \"token\" usata in \"NFT\" sono poco comuni al di "
"fuori dei contesti finanziari."

#: src/faq.md:120
msgid "How do sat inscriptions compare to…"
msgstr "Come si confrontano le iscrizioni con…"

#: src/faq.md:123
msgid "Ethereum NFTs?"
msgstr "NFT di Ethereum?"

#: src/faq.md:125
msgid "_Inscriptions are always immutable._"
msgstr "_Le iscrizioni sono sempre immutabili._"

#: src/faq.md:127
msgid ""
"There is simply no way to for the creator of an inscription, or the owner of "
"an inscription, to modify it after it has been created."
msgstr ""
"Semplicemente non c'è modo per il creatore di un'iscrizione, o per il proprietario "
"di un'iscrizione, di modificarla dopo che è stata creata."

#: src/faq.md:130
msgid ""
"Ethereum NFTs _can_ be immutable, but many are not, and can be changed or "
"deleted by the NFT contract owner."
msgstr ""
"Gli NFT di Ethereum _possono_ essere immutabili, ma molti non lo sono e "
"possono essere modificati o cancellati dal proprietario del contratto NFT."

#: src/faq.md:133
msgid ""
"In order to make sure that a particular Ethereum NFT is immutable, the "
"contract code must be audited, which requires detailed knowledge of the EVM "
"and Solidity semantics."
msgstr ""
"Per assicurarsi che un particolare NFT di Ethereum sia immutabile, è necessario "
"verificare il codice del contratto, il che richiede una conoscenza dettagliata "
"di EVM e Solidity."

#: src/faq.md:137
msgid ""
"It is very hard for a non-technical user to determine whether or not a given "
"Ethereum NFT is mutable or immutable, and Ethereum NFT platforms make no "
"effort to distinguish whether an NFT is mutable or immutable, and whether "
"the contract source code is available and has been audited."
msgstr ""
"È molto difficile per un utente non tecnico determinare se un determinato NFT "
"di Ethereum sia mutabile o immutabile, e le piattaforme NFT di Ethereum non fanno "
"alcuno sforzo per distinguere se un NFT è mutabile o immutabile e se il codice "
"sorgente del contratto è disponibile ed è stato verificato."

#: src/faq.md:142
msgid "_Inscription content is always on-chain._"
msgstr "_Il contenuto dell'iscrizione è sempre on-chain._"

#: src/faq.md:144
msgid ""
"There is no way for an inscription to refer to off-chain content. This makes "
"inscriptions more durable, because content cannot be lost, and scarcer, "
"because inscription creators must pay fees proportional to the size of the "
"content."
msgstr ""
"Non c'è modo per un'iscrizione di fare riferimento a contenuti fuori catena. "
"Questo rende le iscrizioni più durature, perché il contenuto non può essere "
"perso, e più scarse, perché i creatori di iscrizioni devono pagare commissioni "
"proporzionali alla dimensione del contenuto."

#: src/faq.md:148
msgid ""
"Some Ethereum NFT content is on-chain, but much is off-chain, and is stored "
"on platforms like IPFS or Arweave, or on traditional, fully centralized web "
"servers. Content on IPFS is not guaranteed to continue to be available, and "
"some NFT content stored on IPFS has already been lost. Platforms like "
"Arweave rely on weak economic assumptions, and will likely fail "
"catastrophically when these economic assumptions are no longer met. "
"Centralized web servers may disappear at any time."
msgstr ""
"Alcuni contenuti di Ethereum NFT sono on-chain, ma molti sono off-chain e sono "
"archiviati su piattaforme come IPFS o Arweave, o su server web tradizionali e "
"completamente centralizzati. Non è garantito che i contenuti su IPFS continuino "
"a essere disponibili e alcuni contenuti NFT archiviati su IPFS sono già andati persi. "
"Piattaforme come Arweave si basano su presupposti economici deboli e probabilmente "
"falliranno in modo catastrofico quando questi presupposti economici non saranno più "
"soddisfatti. I server web centralizzati possono scomparire in qualsiasi momento."

#: src/faq.md:156
msgid ""
"It is very hard for a non-technical user to determine where the content of a "
"given Ethereum NFT is stored."
msgstr ""
"Per un utente non tecnico è molto difficile determinare dove sia memorizzato "
"il contenuto di un determinato NFT di Ethereum."

#: src/faq.md:159
msgid "_Inscriptions are much simpler._"
msgstr "_Le iscrizioni sono molto più semplici._"

#: src/faq.md:161
msgid ""
"Ethereum NFTs depend on the Ethereum network and virtual machine, which are "
"highly complex, constantly changing, and which introduce changes via "
"backwards-incompatible hard forks."
msgstr ""
"Gli NFT di Ethereum dipendono dalla rete e dalla macchina virtuale di Ethereum, "
"che sono molto complesse, in continua evoluzione e che introducono cambiamenti "
"tramite hard fork incompatibili con le versioni precedenti."

#: src/faq.md:165
msgid ""
"Inscriptions, on the other hand, depend on the Bitcoin blockchain, which is "
"relatively simple and conservative, and which introduces changes via "
"backwards-compatible soft forks."
msgstr ""
"Le iscrizioni, invece, dipendono dalla blockchain di Bitcoin, che è relativamente "
"semplice e conservativa e che introduce modifiche tramite soft fork "
"compatibili \"all'indietro\"."

#: src/faq.md:169
msgid "_Inscriptions are more secure._"
msgstr "_Le iscrizioni sono più sicure._"

#: src/faq.md:171
msgid ""
"Inscriptions inherit Bitcoin's transaction model, which allow a user to see "
"exactly which inscriptions are being transferred by a transaction before "
"they sign it. Inscriptions can be offered for sale using partially signed "
"transactions, which don't require allowing a third party, such as an "
"exchange or marketplace, to transfer them on the user's behalf."
msgstr ""
"Le iscrizioni ereditano il modello di transazione di Bitcoin, che consente "
"all'utente di vedere esattamente quali iscrizioni vengono trasferite da una "
"transazione prima di firmarla. Le iscrizioni possono essere messe in vendita "
"utilizzando transazioni parzialmente firmate, che non richiedono l'autorizzazione "
"di una terza parte, come un exchange o un marketplace, a trasferirle per conto dell'utente."

#: src/faq.md:177
msgid ""
"By comparison, Ethereum NFTs are plagued with end-user security "
"vulnerabilities. It is commonplace to blind-sign transactions, grant "
"third-party apps unlimited permissions over a user's NFTs, and interact with "
"complex and unpredictable smart contracts. This creates a minefield of "
"hazards for Ethereum NFT users which are simply not a concern for ordinal "
"theorists."
msgstr ""
"In confronto, gli NFT di Ethereum sono afflitti da vulnerabilità della sicurezza "
"dell'utente finale. È consuetudine firmare le transazioni alla cieca, concedere "
"alle applicazioni di terze parti permessi illimitati sugli NFT di un utente e "
"interagire con smart contracts complessi e imprevedibili. Questo crea un campo "
"minato di pericoli per gli utenti di Ethereum NFT che non sono semplicemente una "
"preoccupazione per i teorici ordinali."

#: src/faq.md:183
msgid "_Inscriptions are scarcer._"
msgstr "_Le iscrizioni sono più scarse._"

#: src/faq.md:185
msgid ""
"Inscriptions require bitcoin to mint, transfer, and store. This seems like a "
"downside on the surface, but the raison d'etre of digital artifacts is to be "
"scarce and thus valuable."
msgstr ""
"Le iscrizioni richiedono bitcoin per essere coniate, trasferite e conservate. "
"In apparenza questo sembra un aspetto negativo, ma la ragion d'essere degli artefatti "
"digitali è quella di essere scarsi e quindi preziosi."

#: src/faq.md:189
msgid ""
"Ethereum NFTs, on the other hand, can be minted in virtually unlimited "
"qualities with a single transaction, making them inherently less scarce, and "
"thus, potentially less valuable."
msgstr ""
"Gli NFT di Ethereum, invece, possono essere coniati in qualità virtualmente "
"illimitata con una singola transazione, il che li rende intrinsecamente meno "
"scarsi e quindi potenzialmente meno preziosi."

#: src/faq.md:193
msgid "_Inscriptions do not pretend to support on-chain royalties._"
msgstr "_Le iscrizioni non pretendono di supportare le royalties on-chain._"

#: src/faq.md:195
msgid ""
"On-chain royalties are a good idea in theory but not in practice. Royalty "
"payment cannot be enforced on-chain without complex and invasive "
"restrictions. The Ethereum NFT ecosystem is currently grappling with "
"confusion around royalties, and is collectively coming to grips with the "
"reality that on-chain royalties, which were messaged to artists as an "
"advantage of NFTs, are not possible, while platforms race to the bottom and "
"remove royalty support."
msgstr ""
"Le royalties on-chain sono una buona idea in teoria, ma non in pratica. "
"Il pagamento delle royalty non può essere imposto on-chain senza restrizioni "
"complesse e invasive. L'ecosistema NFT di Ethereum è attualmente alle prese con "
"la confusione sulle royalties e sta affrontando collettivamente la realtà che le "
"royalties on-chain, che sono state comunicate agli artisti come un vantaggio degli NFT, "
"non sono possibili, mentre le piattaforme corrono al ribasso e rimuovono il supporto "
"alle royalties."

#: src/faq.md:202
msgid ""
"Inscriptions avoid this situation entirely by making no false promises of "
"supporting royalties on-chain, thus avoiding the confusion, chaos, and "
"negativity of the Ethereum NFT situation."
msgstr ""
"Le iscrizioni evitano completamente questa situazione, non facendo false "
"promesse sul supporto delle royalties on-chain, evitando così la confusione, "
"il caos e la negatività della situazione NFT di Ethereum."

#: src/faq.md:206
msgid "_Inscriptions unlock new markets._"
msgstr "_Le iscrizioni sbloccano nuovi mercati._"

#: src/faq.md:208
msgid ""
"Bitcoin's market capitalization and liquidity are greater than Ethereum's by "
"a large margin. Much of this liquidity is not available to Ethereum NFTs, "
"since many Bitcoiners prefer not to interact with the Ethereum ecosystem due "
"to concerns related to simplicity, security, and decentralization."
msgstr ""
"La capitalizzazione di mercato e la liquidità di Bitcoin sono superiori a quelle "
"di Ethereum con un ampio margine. Gran parte di questa liquidità non è disponibile "
"per gli NFT di Ethereum, poiché molti Bitcoiners preferiscono non interagire con "
"l'ecosistema di Ethereum a causa di preoccupazioni legate alla semplicità, alla "
"sicurezza e alla decentralizzazione."

#: src/faq.md:213
msgid ""
"Such Bitcoiners may be more interested in inscriptions than Ethereum NFTs, "
"unlocking new classes of collector."
msgstr ""
"Questi Bitcoiners potrebbero essere più interessati alle Iscrizioni che agli NFT "
"di Ethereum, sbloccando nuove classi di collezionisti."

#: src/faq.md:216
msgid "_Inscriptions have a richer data model._"
msgstr "_Le iscrizioni hanno un modello di dati più ricco._"

#: src/faq.md:218
msgid ""
"Inscriptions consist of a content type, also known as a MIME type, and "
"content, which is an arbitrary byte string. This is the same data model used "
"by the web, and allows inscription content to evolve with the web, and come "
"to support any kind of content supported by web browsers, without requiring "
"changes to the underlying protocol."
msgstr ""
"Le iscrizioni consistono in un tipo di contenuto, noto anche come tipo MIME, "
"e in un secondo contenuto, che è una stringa di byte arbitraria. Questo è lo "
"stesso modello di dati utilizzato dal web e consente ai contenuti delle iscrizioni "
"di evolversi con il web e di supportare qualsiasi tipo di contenuto supportato dai "
"browser web, senza richiedere modifiche al protocollo sottostante."

#: src/faq.md:224
msgid "RGB and Taro assets?"
msgstr "Gli asset RGB e Taro?"

#: src/faq.md:226
msgid ""
"RGB and Taro are both second-layer asset protocols built on Bitcoin. "
"Compared to inscriptions, they are much more complicated, but much more "
"featureful."
msgstr ""
"RGB e Taro sono entrambi protocolli di asset di secondo livello costruiti "
"su Bitcoin. Rispetto alle iscrizioni, sono molto più complicati, ma molto "
"più ricchi di funzionalità."

#: src/faq.md:229
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas the primary use-case of RGB and Taro are fungible tokens, so the "
"user experience for inscriptions is likely to be simpler and more polished "
"than the user experience for RGB and Taro NFTs."
msgstr ""
"La teoria ordinale è stata progettata da zero per gli artefatti digitali, "
"mentre il caso d'uso principale di RGB e Taro sono i token fungibili, quindi "
"l'esperienza dell'utente per le iscrizioni sarà probabilmente più semplice e "
"raffinata di quella per gli NFT di RGB e Taro."

#: src/faq.md:234
msgid ""
"RGB and Taro both store content off-chain, which requires additional "
"infrastructure, and which may be lost. By contrast, inscription content is "
"stored on-chain, and cannot be lost."
msgstr ""
"RGB e Taro memorizzano entrambi i contenuti off-chain, il che richiede "
"un'infrastruttura aggiuntiva e per questo motivo possono andare persi. "
"Al contrario, i contenuti delle iscrizioni sono memorizzati on-chain e "
"non possono andare perduti."

#: src/faq.md:238
msgid ""
"Ordinal theory, RGB, and Taro are all very early, so this is speculation, "
"but ordinal theory's focus may give it the edge in terms of features for "
"digital artifacts, including a better content model, and features like "
"globally unique symbols."
msgstr ""
"La teoria ordinale, RGB e Taro sono ancora agli inizi, quindi si tratta di "
"speculazione, ma l'attenzione sugli Ordinali potrebbe darle un vantaggio "
"in termini di caratteristiche per gli artefatti digitali, tra cui un modello di "
"contenuto migliore e caratteristiche come i simboli unici a livello globale."

#: src/faq.md:243
msgid "Counterparty assets?"
msgstr "Asset di Counterparty?"

#: src/faq.md:245
msgid ""
"Counterparty has its own token, XCP, which is required for some "
"functionality, which makes most bitcoiners regard it as an altcoin, and not "
"an extension or second layer for bitcoin."
msgstr ""
"Counterparty ha un proprio token, XCP, che è necessario per alcune funzionalità, "
"il che fa sì che la maggior parte dei bitcoiners la consideri un'altcoin, e non "
"un'estensione o un secondo livello per bitcoin."

#: src/faq.md:249
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas Counterparty was primarily designed for financial token issuance."
msgstr ""
"La teoria ordinale è stata progettata da zero per gli artefatti digitali, "
"mentre Counterparty è stata progettata principalmente per l'emissione "
"di token finanziari."

#: src/faq.md:252
msgid "Inscriptions for…"
msgstr "Iscrizioni per…"

#: src/faq.md:255
msgid "Artists"
msgstr "Artisti"

#: src/faq.md:257
msgid ""
"_Inscriptions are on Bitcoin._ Bitcoin is the digital currency with the "
"highest status and greatest chance of long-term survival. If you want to "
"guarantee that your art survives into the future, there is no better way to "
"publish it than as inscriptions."
msgstr ""
"_Le iscrizioni sono su Bitcoin._ Il Bitcoin è la valuta digitale con lo status "
"più elevato e le maggiori possibilità di sopravvivenza a lungo termine. "
"Se volete garantire che la vostra arte sopravviva nel futuro, non c'è modo "
"migliore di pubblicarla che sotto forma di iscrizioni."

#: src/faq.md:262
msgid ""
"_Cheaper on-chain storage._ At $20,000 per BTC and the minimum relay fee of "
"1 satoshi per vbyte, publishing inscription content costs $50 per 1 million "
"bytes."
msgstr ""
"_Archiviazione on-chain più economica._ Con il BTC a $20.000 e una tariffa minima "
"di 1 satoshi per vbyte, la pubblicazione di contenuti di iscrizioni costa $50 "
"per 1 milione di byte."

#: src/faq.md:266
msgid ""
"_Inscriptions are early!_ Inscriptions are still in development, and have "
"not yet launched on mainnet. This gives you an opportunity to be an early "
"adopter, and explore the medium as it evolves."
msgstr ""
"_Le iscrizioni sono \"early\"!_ Le iscrizioni sono ancora in fase di sviluppo "
"e non sono ancora state lanciate sulla mainnet. Questo vi dà l'opportunità di "
"essere un early adopter e di esplorare questo strumento mentre si evolve."

#: src/faq.md:270
msgid ""
"_Inscriptions are simple._ Inscriptions do not require writing or "
"understanding smart contracts."
msgstr ""
"_Le iscrizioni sono semplici._ Le iscrizioni non richiedono la scrittura "
"o la comprensione di smart contracts."

#: src/faq.md:273
msgid ""
"_Inscriptions unlock new liquidity._ Inscriptions are more accessible and "
"appealing to bitcoin holders, unlocking an entirely new class of collector."
msgstr ""
"_Le iscrizioni sbloccano nuova liquidità._ Le iscrizioni sono più accessibili "
"e attraenti per i detentori di bitcoin, e liberano una classe completamente "
"nuova di collezionisti."

#: src/faq.md:276
msgid ""
"_Inscriptions are designed for digital artifacts._ Inscriptions are designed "
"from the ground up to support NFTs, and feature a better data model, and "
"features like globally unique symbols and enhanced provenance."
msgstr ""
"_Le iscrizioni sono progettate per gli artefatti digitali._ Le iscrizioni sono "
"state progettate da zero per supportare gli NFT e presentano un modello di dati "
"migliore, oltre a caratteristiche come simboli unici a livello globale e una "
"provenienza migliorata."

#: src/faq.md:280
msgid ""
"_Inscriptions do not support on-chain royalties._ This is negative, but only "
"depending on how you look at it. On-chain royalties have been a boon for "
"creators, but have also created a huge amount of confusion in the Ethereum "
"NFT ecosystem. The ecosystem now grapples with this issue, and is engaged in "
"a race to the bottom, towards a royalties-optional future. Inscriptions have "
"no support for on-chain royalties, because they are technically infeasible. "
"If you choose to create inscriptions, there are many ways you can work "
"around this limitation: withhold a portion of your inscriptions for future "
"sale, to benefit from future appreciation, or perhaps offer perks for users "
"who respect optional royalties."
msgstr ""
"_Le iscrizioni non supportano le royalties on-chain._ Questo è un aspetto negativo, "
"ma dipende da come lo si guarda. Le royalties on-chain sono state una manna per i "
"creatori, ma hanno anche creato un'enorme confusione nell'ecosistema Ethereum NFT. "
"L'ecosistema è ora alle prese con questo problema ed è impegnato in una corsa al ribasso, "
"verso un futuro di royalties facoltative. Le iscrizioni non supportano le royalties sulla "
"catena, perché sono tecnicamente inapplicabili. Se scegliete di creare iscrizioni, potete "
"aggirare questa limitazione in molti modi: trattenendo una parte delle vostre iscrizioni "
"per una futura vendita, per beneficiare di un futuro apprezzamento, o magari offrendo "
"vantaggi agli utenti che rispettano le royalties opzionali."

#: src/faq.md:291
msgid "Collectors"
msgstr "Collezionisti"

#: src/faq.md:293
msgid ""
"_Inscriptions are simple, clear, and have no surprises._ They are always "
"immutable and on-chain, with no special due diligence required."
msgstr ""
"_Le iscrizioni sono semplici, chiare e senza sorprese._ Sono sempre immutabili "
"e on-chain, senza che sia necessaria una particolare due diligence."

#: src/faq.md:296
msgid ""
"_Inscriptions are on Bitcoin._ You can verify the location and properties of "
"inscriptions easily with Bitcoin full node that you control."
msgstr ""
"_Le iscrizioni sono su Bitcoin._ È possibile verificare facilmente la posizione "
"e le proprietà delle iscrizioni con il nodo Bitcoin completo che si controlla."

#: src/faq.md:299
msgid "Bitcoiners"
msgstr "Bitcoiners"

#: src/faq.md:301
msgid ""
"Let me begin this section by saying: the most important thing that the "
"Bitcoin network does is decentralize money. All other use-cases are "
"secondary, including ordinal theory. The developers of ordinal theory "
"understand and acknowledge this, and believe that ordinal theory helps, at "
"least in a small way, Bitcoin's primary mission."
msgstr ""
"Permettetemi di iniziare questa sezione dicendo che la cosa più importante che "
"la rete Bitcoin fa è decentralizzare il denaro. Tutti gli altri casi d'uso sono "
"secondari, compresa la teoria ordinale. Gli sviluppatori della teoria ordinale lo "
"capiscono e lo riconoscono, e credono che la teoria ordinale aiuti, almeno in "
"minima parte, la missione primaria di Bitcoin."

#: src/faq.md:307
msgid ""
"Unlike many other things in the altcoin space, digital artifacts have merit. "
"There are, of course, a great deal of NFTs that are ugly, stupid, and "
"fraudulent. However, there are many that are fantastically creative, and "
"creating and collecting art has been a part of the human story since its "
"inception, and predates even trade and money, which are also ancient "
"technologies."
msgstr ""
"A differenza di molte altre cose nello spazio delle altcoin, gli artefatti "
"digitali hanno un valore. Ci sono, ovviamente, molti NFT che sono brutti, "
"stupidi e fraudolenti. Tuttavia, ce ne sono molti che sono fantasticamente creativi, "
"e la creazione e il collezionismo di opere d'arte fanno parte della storia dell'uomo "
"fin dalla sua nascita, e precedono persino il commercio e il denaro, che sono "
"anch'essi tecnologie antiche."

#: src/faq.md:314
msgid ""
"Bitcoin provides an amazing platform for creating and collecting digital "
"artifacts in a secure, decentralized way, that protects users and artists in "
"the same way that it provides an amazing platform for sending and receiving "
"value, and for all the same reasons."
msgstr ""
"Bitcoin offre una piattaforma straordinaria per la creazione e la raccolta di "
"artefatti digitali in modo sicuro e decentralizzato, che protegge gli utenti e "
"gli artisti nello stesso modo in cui fornisce una piattaforma straordinaria per "
"l'invio e la ricezione di valore, e per tutte le stesse ragioni."

#: src/faq.md:319
msgid ""
"Ordinals and inscriptions increase demand for Bitcoin block space, which "
"increase Bitcoin's security budget, which is vital for safeguarding "
"Bitcoin's transition to a fee-dependent security model, as the block subsidy "
"is halved into insignificance."
msgstr ""
"Gli ordinali e le iscrizioni aumentano la domanda di spazio per i blocchi di "
"Bitcoin, aumentando il budget di sicurezza di Bitcoin, che è fondamentale "
"per salvaguardare la transizione di Bitcoin a un modello di sicurezza dipendente "
"dalle commissioni, in quanto la ricompensa economica espressa in BTC per i blocchi "
"si dimezza (a causa dell' halving) fino a diventare insignificante."

#: src/faq.md:324
msgid ""
"Inscription content is stored on-chain, and thus the demand for block space "
"for use in inscriptions is unlimited. This creates a buyer of last resort "
"for _all_ Bitcoin block space. This will help support a robust fee market, "
"which ensures that Bitcoin remains secure."
msgstr ""
"I contenuti delle iscrizioni sono memorizzati on-chain e quindi la domanda di "
"spazio per i blocchi da utilizzare per le iscrizioni è illimitata. Questo crea "
"un acquirente di ultima istanza per _tutto_ lo spazio dei blocchi Bitcoin. Ciò "
"contribuirà a sostenere un solido mercato delle commissioni, che garantisce la "
"sicurezza del Bitcoin."

#: src/faq.md:329
msgid ""
"Inscriptions also counter the narrative that Bitcoin cannot be extended or "
"used for new use-cases. If you follow projects like DLCs, Fedimint, "
"Lightning, Taro, and RGB, you know that this narrative is false, but "
"inscriptions provide a counter argument which is easy to understand, and "
"which targets a popular and proven use case, NFTs, which makes it highly "
"legible."
msgstr ""
"Le iscrizioni contrastano anche la tesi secondo cui Bitcoin non può essere "
"esteso o utilizzato per nuovi casi d'uso. Se si seguono progetti come DLC, "
"Fedimint, Lightning, Taro e RGB, si sa che questa tesi è falsa, ma le iscrizioni "
"forniscono un'argomentazione contraria facile da capire e che si rivolge a un "
"caso d'uso popolare e comprovato, gli NFT, il che la rende altamente leggibile."

#: src/faq.md:335
msgid ""
"If inscriptions prove, as the authors hope, to be highly sought after "
"digital artifacts with a rich history, they will serve as a powerful hook "
"for Bitcoin adoption: come for the fun, rich art, stay for the decentralized "
"digital money."
msgstr ""
"Se le iscrizioni si riveleranno, come sperano gli autori, artefatti digitali "
"molto ricercati e ricchi di storia, serviranno da potente gancio per l'adozione "
"di Bitcoin: venite per il divertimento, la ricchezza artistica, rimanete per la "
"moneta digitale decentralizzata."

#: src/faq.md:339
msgid ""
"Inscriptions are an extremely benign source of demand for block space. "
"Unlike, for example, stablecoins, which potentially give large stablecoin "
"issuers influence over the future of Bitcoin development, or DeFi, which "
"might centralize mining by introducing opportunities for MEV, digital art "
"and collectables on Bitcoin, are unlikely to produce individual entities "
"with enough power to corrupt Bitcoin. Art is decentralized."
msgstr ""
"Le iscrizioni sono una fonte estremamente benevola di domanda di spazio per "
"i blocchi. A differenza, ad esempio, delle stablecoin, che potrebbero dare ai "
"grandi emittenti di stablecoin un'influenza sul futuro dello sviluppo di Bitcoin, "
"o della DeFi, che potrebbe centralizzare il mining introducendo opportunità di MEV, "
"è improbabile che l'arte digitale e gli oggetti da collezione su Bitcoin producano o "
"attraggano entità individuali con un potere sufficiente a corrompere "
"Bitcoin. L'arte è decentralizzata."

#: src/faq.md:346
msgid ""
"Inscription users and service providers are incentivized to run Bitcoin full "
"nodes, to publish and track inscriptions, and thus throw their economic "
"weight behind the honest chain."
msgstr ""
"Gli utenti delle iscrizioni e i fornitori di servizi sono incentivati a gestire "
"i nodi completi di Bitcoin, a pubblicare e a tenere traccia delle iscrizioni, "
"e quindi a far valere il loro peso economico dietro la \"catena onesta\"."

#: src/faq.md:350
msgid ""
"Ordinal theory and inscriptions do not meaningfully affect Bitcoin's "
"fungibility. Bitcoin users can ignore both and be unaffected."
msgstr ""
"La teoria ordinale e le iscrizioni non influiscono significativamente sulla "
"fungibilità di Bitcoin. Gli utenti di Bitcoin possono ignorarle e non risentirne."

#: src/faq.md:353
msgid ""
"We hope that ordinal theory strengthens and enriches bitcoin, and gives it "
"another dimension of appeal and functionality, enabling it more effectively "
"serve its primary use case as humanity's decentralized store of value."
msgstr ""
"Ci auguriamo che la teoria ordinale rafforzi e arricchisca il bitcoin, conferendogli "
"un'ulteriore dimensione di fascino e funzionalità, consentendogli di servire più "
"efficacemente il suo caso d'uso primario come riserva di valore decentralizzata dell'umanità."

#: src/contributing.md:1
msgid "Contributing to `ord`"
msgstr "Contribuire ad `ord`"

#: src/contributing.md:4
msgid "Suggested Steps"
msgstr "Passi Suggeriti"

#: src/contributing.md:7
msgid "Find an issue you want to work on."
msgstr "Trovate un problema su cui volete lavorare."

#: src/contributing.md:8
msgid ""
"Figure out what would be a good first step towards resolving the issue. This "
"could be in the form of code, research, a proposal, or suggesting that it be "
"closed, if it's out of date or not a good idea in the first place."
msgstr ""
"Cercate di capire quale sarebbe il primo passo da fare per risolvere il problema. "
"Questo potrebbe essere sotto forma di codice, ricerca, proposta o suggerimento di "
"chiudere il problema, se è obsoleto o non è una buona idea."

#: src/contributing.md:11
msgid ""
"Comment on the issue with an outline of your suggested first step, and "
"asking for feedback. Of course, you can dive in and start writing code or "
"tests immediately, but this avoids potentially wasted effort, if the issue "
"is out of date, not clearly specified, blocked on something else, or "
"otherwise not ready to implement."
msgstr ""
"Commentate il problema con una bozza del primo passo che avete suggerito e "
"chiedete un feedback. Naturalmente, potete tuffarvi e iniziare a scrivere "
"codice o test immediatamente, ma questo evita sforzi potenzialmente sprecati, "
"se il problema non è aggiornato, non è chiaramente specificato, è bloccato su "
"qualcos'altro o non è pronto per essere implementato."

#: src/contributing.md:16
msgid ""
"If the issue requires a code change or bugfix, open a draft PR with tests, "
"and ask for feedback. This makes sure that everyone is on the same page "
"about what needs to be done, or what the first step in solving the issue "
"should be. Also, since tests are required, writing the tests first makes it "
"easy to confirm that the change can be tested easily."
msgstr ""
"Se il problema richiede una modifica del codice o un bugfix, aprite una bozza "
"di PR con i test e chiedete un feedback. In questo modo ci si assicura che tutti "
"siano d'accordo su ciò che deve essere fatto o su quale dovrebbe essere il primo "
"passo per risolvere il problema. Inoltre, dato che i test sono necessari, "
"scriverli per primi facilita la conferma che la modifica può essere testata facilmente."

#: src/contributing.md:21
msgid ""
"Mash the keyboard randomly until the tests pass, and refactor until the code "
"is ready to submit."
msgstr ""
"Schiacciate la tastiera a caso finché i test non passano, e rifattorizzate "
"finché il codice non è pronto per essere inviato."

#: src/contributing.md:23
msgid "Mark the PR as ready to review."
msgstr "Contrassegnare la PR come pronta per la revisione."

#: src/contributing.md:24
msgid "Revise the PR as needed."
msgstr "Rivedere la PR se necessario."

#: src/contributing.md:25
msgid "And finally, mergies!"
msgstr "E infine, fate il merge!"

#: src/contributing.md:27
msgid "Start small"
msgstr "Iniziare con poco"

#: src/contributing.md:30
msgid ""
"Small changes will allow you to make an impact quickly, and if you take the "
"wrong tack, you won't have wasted much time."
msgstr ""
"Piccole modifiche vi permetteranno di avere un impatto rapido e, se prendete "
"la strada sbagliata, non avrete perso molto tempo."

#: src/contributing.md:33
msgid "Ideas for small issues:"
msgstr "Idee per piccoli problemi:"

#: src/contributing.md:34
msgid "Add a new test or test case that increases test coverage"
msgstr "Aggiungere un nuovo test o un caso di test che aumenti la copertura dei test"

#: src/contributing.md:35
msgid "Add or improve documentation"
msgstr "Aggiungere o migliorare la documentazione"

#: src/contributing.md:36
msgid ""
"Find an issue that needs more research, and do that research and summarize "
"it in a comment"
msgstr ""
"Individuare un problema che necessita di ulteriori ricerche, effettuarle e "
"riassumerle in un commento"

#: src/contributing.md:38
msgid "Find an out-of-date issue and comment that it can be closed"
msgstr "Trovare un problema non aggiornato e commentare che può essere chiuso"

#: src/contributing.md:39
msgid ""
"Find an issue that shouldn't be done, and provide constructive feedback "
"detailing why you think that is the case"
msgstr ""
"Trovare un problema che non dovrebbe essere fatto e fornire un feedback "
"costruttivo spiegando perché si pensa che sia così"

#: src/contributing.md:42
msgid "Merge early and often"
msgstr "Unire presto e spesso"

#: src/contributing.md:45
msgid ""
"Break up large tasks into multiple smaller steps that individually make "
"progress. If there's a bug, you can open a PR that adds a failing ignored "
"test. This can be merged, and the next step can be to fix the bug and "
"unignore the test. Do research or testing, and report on your results. Break "
"a feature into small sub-features, and implement them one at a time."
msgstr ""
"Suddividete i compiti più grandi in più fasi più piccole che fanno progredire "
"individualmente. Se c'è un bug, si può aprire una PR che aggiunge un test ignorato "
"che fallisce. Questo può essere unito e il passo successivo può essere la correzione "
"del bug e la cancellazione del test. Fare ricerche o test e riferire i risultati. "
"Scomporre una funzione in piccole sotto-funzioni e implementarle una alla volta."

#: src/contributing.md:51
msgid ""
"Figuring out how to break down a larger PR into smaller PRs where each can "
"be merged is an art form well-worth practicing. The hard part is that each "
"PR must itself be an improvement."
msgstr ""
"Capire come suddividere una PR più grande in PR più piccole che possono essere "
"unite è una forma d'arte che vale la pena di praticare. La parte difficile è "
"che ogni PR deve essere di per sé un miglioramento."

#: src/contributing.md:55
msgid ""
"I strive to follow this advice myself, and am always better off when I do."
msgstr ""
"Io stesso mi sforzo di seguire questo consiglio e mi trovo sempre meglio quando lo faccio."

#: src/contributing.md:57
msgid ""
"Small changes are fast to write, review, and merge, which is much more fun "
"than laboring over a single giant PR that takes forever to write, review, "
"and merge. Small changes don't take much time, so if you need to stop "
"working on a small change, you won't have wasted much time as compared to a "
"larger change that represents many hours of work. Getting a PR in quickly "
"improves the project a little bit immediately, instead of having to wait a "
"long time for larger improvement. Small changes are less likely to "
"accumulate merge conflict. As the Athenians said: _The fast commit what they "
"will, the slow merge what they must._"
msgstr ""
"Le piccole modifiche sono veloci da scrivere, revisionare e unire, il che è molto "
"più divertente che faticare su una singola PR gigante che richiede un'eternità per "
"essere scritta, revisionata e aggiunta. Le piccole modifiche non richiedono molto tempo, "
"quindi se dovete interrompere il lavoro su una piccola modifica, non avrete perso molto "
"tempo rispetto a una modifica più grande che rappresenta molte ore di lavoro. L'introduzione "
"rapida di una PR migliora il progetto in modo immediato, invece di dover aspettare a lungo "
"per ottenere miglioramenti più consistenti. Le piccole modifiche hanno meno probabilità di "
"accumulare conflitti di fusione. Come dicevano gli ateniesi: _I veloci fanno il commit di "
"ciò che vogliono, i lenti fanno il merge di ciò che devono._"

#: src/contributing.md:67
msgid "Get help"
msgstr "Chiedere aiuto"

#: src/contributing.md:70
msgid ""
"If you're stuck for more than 15 minutes, ask for help, like a Rust Discord, "
"Stack Exchange, or in a project issue or discussion."
msgstr ""
"Se siete bloccati per più di 15 minuti, chiedete aiuto, ad esempio su Rust Discord, "
"Stack Exchange, o in un problema o discussione del progetto."

#: src/contributing.md:73
msgid "Practice hypothesis-driven debugging"
msgstr "Praticate il debugging basato sulle ipotesi"

#: src/contributing.md:76
msgid ""
"Formulate a hypothesis as to what is causing the problem. Figure out how to "
"test that hypothesis. Perform that tests. If it works, great, you fixed the "
"issue or now you know how to fix the issue. If not, repeat with a new "
"hypothesis."
msgstr ""
"Formulate un'ipotesi sulla causa del problema. Cercate di capire come testare "
"questa ipotesi. Eseguite i test. Se funziona, è possibile risolvere il problema "
"o sapere come risolverlo. In caso contrario, ripetete con una nuova ipotesi."

#: src/contributing.md:81
msgid "Pay attention to error messages"
msgstr "Prestate attenzione ai messaggi di errore"

#: src/contributing.md:84
msgid "Read all error messages and don't tolerate warnings."
msgstr "Leggete tutti i messaggi di errore e non ignorate gli avvisi."

#: src/donate.md:4
msgid ""
"Ordinals is open-source and community funded. The current lead maintainer of "
"`ord` is [raphjaph](https://github.com/raphjaph/). Raph's work on `ord` is "
"entirely funded by donations. If you can, please consider donating!"
msgstr ""
"Ordinals è open-source e finanziato dalla comunità. L'attuale manutentore "
"principale di `ord` è [raphjaph](https://github.com/raphjaph/). Il lavoro di Raph "
"su `ord` è interamente finanziato da donazioni. Se potete, prendete in considerazione "
"l'idea di fare una donazione!"

#: src/donate.md:8
msgid ""
"The donation address for Bitcoin is "
"[**************************************************************](https://mempool.space/address/**************************************************************). "
"The donation address for inscriptions is "
"[**************************************************************](https://mempool.space/address/**************************************************************)."
msgstr ""
"L'indirizzo per le donazioni in Bitcoin è "
"[**************************************************************](https://mempool.space/address/**************************************************************). "
"L'indirizzo di donazione per le iscrizioni è "
"[**************************************************************](https://mempool.space/address/**************************************************************)."



#: src/donate.md:11
msgid ""
"Both addresses are in a 2 of 4 multisig wallet with keys held by "
"[raphjaph](https://twitter.com/raphjaph), "
"[erin](https://twitter.com/realizingerin), "
"[rodarmor](https://twitter.com/rodarmor), and "
"[ordinally](https://twitter.com/veryordinally)."
msgstr ""
"Entrambi gli indirizzi sono in un portafoglio multisig 2 su 4 con chiavi detenute da "
"[raphjaph](https://twitter.com/raphjaph), "
"[erin](https://twitter.com/realizingerin), "
"[rodarmor](https://twitter.com/rodarmor) e "
"[ordinally](https://twitter.com/veryordinally)."

#: src/donate.md:17
msgid ""
"Donations received will go towards funding maintenance and development of "
"`ord`, as well as hosting costs for [ordinals.com](https://ordinals.com)."
msgstr ""
"Le donazioni ricevute serviranno a finanziare la manutenzione e lo sviluppo "
"di `ord`, nonché i costi di hosting di [ordinals.com](https://ordinals.com)."

#: src/donate.md:20
msgid "Thank you for donating!"
msgstr "Grazie per le donazioni!"

#: src/guides.md:1
msgid "Ordinal Theory Guides"
msgstr "Guide alla Teoria Ordinale"

#: src/guides.md:4
msgid ""
"See the table of contents for a list of guides, including a guide to the "
"explorer, a guide for sat hunters, and a guide to inscriptions."
msgstr ""
"Per un elenco delle guide si veda l'indice dei contenuti, che comprende una "
"guida per l'esploratore, una guida per i sat hunter e una guida alle iscrizioni."

#: src/guides/explorer.md:1
msgid "Ordinal Explorer"
msgstr "Esploratore Ordinale"

#: src/guides/explorer.md:4
msgid ""
"The `ord` binary includes a block explorer. We host a instance of the block "
"explorer on mainnet at [ordinals.com](https://ordinals.com), and on signet "
"at [signet.ordinals.com](https://signet.ordinals.com)."
msgstr ""
"Il binario `ord` include un esploratore di blocchi. Un'istanza del block "
"explorer è ospitata su mainnet all'indirizzo [ordinals.com](https://ordinals.com) e "
"su signet all'indirizzo [signet.ordinals.com](https://signet.ordinals.com)."

#: src/guides/explorer.md:8
msgid "Running The Explorer"
msgstr "Esecuzione Dell'Esploratore"

#: src/guides/explorer.md:9
msgid "The server can be run locally with:"
msgstr "Il server può essere eseguito localmente con:"

#: src/guides/explorer.md:11
msgid "`ord server`"
msgstr "`ord server`"

#: src/guides/explorer.md:13
msgid "To specify a port add the `--http-port` flag:"
msgstr "Per specificare una porta aggiungere `--http-port` flag:"

#: src/guides/explorer.md:15
msgid "`ord server --http-port 8080`"
msgstr "`ord server --http-port 8080`"

#: src/guides/explorer.md:17
msgid ""
"To enable the JSON-API endpoints add the `--enable-json-api` or `-j` flag "
"(see [here](#json-api) for more info):"
msgstr ""
"Per abilitare gli endpoint JSON-API, aggiungere `--enable-json-api` o `-j` flag "
"(vedere [qui](#json-api) per maggiori informazioni):"

#: src/guides/explorer.md:20
msgid "`ord --enable-json-api server`"
msgstr "`ord --enable-json-api server`"

#: src/guides/explorer.md:22
msgid "To test how your inscriptions will look you can run:"
msgstr "Per testare come verrebbero le vostre iscrizioni, si può eseguire:"

#: src/guides/explorer.md:24
msgid "`ord preview <FILE1> <FILE2> ...`"
msgstr "`ord preview <FILE1> <FILE2> ...`"

#: src/guides/explorer.md:26
msgid "Search"
msgstr "Ricerca"

#: src/guides/explorer.md:29
msgid "The search box accepts a variety of object representations."
msgstr "La casella di ricerca accetta una varietà di rappresentazioni di oggetti."

#: src/guides/explorer.md:31
msgid "Blocks"
msgstr "Blocchi"

#: src/guides/explorer.md:33
msgid "Blocks can be searched by hash, for example, the genesis block:"
msgstr "I blocchi possono essere ricercati per hash, ad esempio il blocco genesis:"

#: src/guides/explorer.md:35
msgid ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://ordinals.com/search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"
msgstr ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://ordinals.com/search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"

#: src/guides/explorer.md:37
msgid "Transactions"
msgstr "Transazioni"

#: src/guides/explorer.md:39
msgid ""
"Transactions can be searched by hash, for example, the genesis block "
"coinbase transaction:"
msgstr ""
"Le transazioni possono essere ricercate per hash, ad esempio la "
"transazione coinbase del blocco genesis:"

#: src/guides/explorer.md:42
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"

#: src/guides/explorer.md:44
msgid "Outputs"
msgstr "Uscite"

#: src/guides/explorer.md:46
msgid ""
"Transaction outputs can searched by outpoint, for example, the only output "
"of the genesis block coinbase transaction:"
msgstr ""
"Gli output delle transazioni possono essere ricercati per outpoint, ad esempio "
"l'unico output della transazione coinbase del blocco genesis:"

#: src/guides/explorer.md:49
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"

#: src/guides/explorer.md:51
msgid "Sats"
msgstr "Sats"

#: src/guides/explorer.md:53
msgid ""
"Sats can be searched by integer, their position within the entire bitcoin "
"supply:"
msgstr ""
"I satoshi possono essere ricercati per numero intero, la loro posizione "
"all'interno dell'intera offerta di bitcoin:"

#: src/guides/explorer.md:56
msgid "[2099994106992659](https://ordinals.com/search/2099994106992659)"
msgstr "[2099994106992659](https://ordinals.com/search/2099994106992659)"

#: src/guides/explorer.md:58
msgid "By decimal, their block and offset within that block:"
msgstr "Per decimale, il loro blocco e l'offset all'interno di quel blocco:"

#: src/guides/explorer.md:60
msgid "[481824.0](https://ordinals.com/search/481824.0)"
msgstr "[481824.0](https://ordinals.com/search/481824.0)"

#: src/guides/explorer.md:62
msgid ""
"By degree, their cycle, blocks since the last halving, blocks since the last "
"difficulty adjustment, and offset within their block:"
msgstr ""
"Per grado, il ciclo, i blocchi dall'ultimo halving, i blocchi dall'ultimo "
"aggiustamento della difficoltà e l'offset all'interno del blocco:"

#: src/guides/explorer.md:65
msgid "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"
msgstr "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"

#: src/guides/explorer.md:67
msgid ""
"By name, their base 26 representation using the letters \"a\" through \"z\":"
msgstr ""
"Per nome, la loro rappresentazione in base 26 utilizzando le lettere da \"a\" a \"z\":"

#: src/guides/explorer.md:69
msgid "[ahistorical](https://ordinals.com/search/ahistorical)"
msgstr "[ahistorical](https://ordinals.com/search/ahistorical)"

#: src/guides/explorer.md:71
msgid ""
"Or by percentile, the percentage of bitcoin's supply that has been or will "
"have been issued when they are mined:"
msgstr ""
"O per percentile, la percentuale della fornitura di bitcoin che è stata o "
"sarà emessa al momento della loro estrazione:"

#: src/guides/explorer.md:74
msgid "[100%](https://ordinals.com/search/100%)"
msgstr "[100%](https://ordinals.com/search/100%)"

#: src/guides/explorer.md:76
msgid "JSON-API"
msgstr "JSON-API"

#: src/guides/explorer.md:79
msgid ""
"You can run `ord` with the `--enable-json-api` flag to access endpoints that "
"return JSON instead of HTML if you set the HTTP `Accept: application/json` "
"header. The structure of theses objects closely follows what is shown in the "
"HTML. These endpoints are:"
msgstr ""
"È possibile eseguire `ord` con il flag `--enable-json-api` per accedere a endpoint che "
"restituiscono JSON anziché HTML se si imposta l'intestazione HTTP `Accept: application/json`. "
"La struttura di questi oggetti segue da vicino quella mostrata nell'HTML. Questi endpoint sono:"

#: src/guides/explorer.md:84
msgid "`/inscription/<INSCRIPTION_ID>`"
msgstr "`/inscription/<INSCRIPTION_ID>`"

#: src/guides/explorer.md:85
msgid "`/inscriptions`"
msgstr "`/inscriptions`"

#: src/guides/explorer.md:86
msgid "`/inscriptions/block/<BLOCK_HEIGHT>`"
msgstr "`/inscriptions/block/<BLOCK_HEIGHT>`"

#: src/guides/explorer.md:87
msgid "`/inscriptions/block/<BLOCK_HEIGHT>/<PAGE_INDEX>`"
msgstr "`/inscriptions/block/<BLOCK_HEIGHT>/<PAGE_INDEX>`"

#: src/guides/explorer.md:88
msgid "`/inscriptions/<FROM>`"
msgstr "`/inscriptions/<FROM>`"

#: src/guides/explorer.md:89
msgid "`/inscriptions/<FROM>/<N>`"
msgstr "`/inscriptions/<FROM>/<N>`"

#: src/guides/explorer.md:90
#: src/guides/explorer.md:91
msgid "`/output/<OUTPOINT>`"
msgstr "`/output/<OUTPOINT>`"

#: src/guides/explorer.md:92
msgid "`/sat/<SAT>`"
msgstr "`/sat/<SAT>`"

#: src/guides/explorer.md:94
msgid "To get a list of the latest 100 inscriptions you would do:"
msgstr "Per ottenere un elenco delle ultime 100 iscrizioni è necessario procedere come segue:"

#: src/guides/explorer.md:96
msgid ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/inscriptions'\n"
"```"
msgstr ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/inscriptions'\n"
"```"

#: src/guides/explorer.md:100
msgid ""
"To see information about a UTXO, which includes inscriptions inside it, do:"
msgstr ""
"Per visualizzare le informazioni su un UTXO, che includono le iscrizioni "
"al suo interno, procedere come segue:"

#: src/guides/explorer.md:102
msgid ""
"```\n"
"curl -s -H \"Accept: application/json\" "
"'http://0.0.0.0:80/output/bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed:0'\n"
"```"
msgstr ""
"```\n"
"curl -s -H \"Accept: application/json\" "
"'http://0.0.0.0:80/output/bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed:0'\n"
"```"

#: src/guides/explorer.md:106
msgid "Which returns:"
msgstr "Che restituisce:"

#: src/guides/explorer.md:108
msgid ""
"```\n"
"{\n"
"  \"value\": 10000,\n"
"  \"script_pubkey\": \"OP_PUSHNUM_1 OP_PUSHBYTES_32 "
"156cc4878306157720607cdcb4b32afa4cc6853868458d7258b907112e5a434b\",\n"
"  \"address\": "
"\"bc1pz4kvfpurqc2hwgrq0nwtfve2lfxvdpfcdpzc6ujchyr3ztj6gd9sfr6ayf\",\n"
"  \"transaction\": "
"\"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed\",\n"
"  \"sat_ranges\": null,\n"
"  \"inscriptions\": [\n"
"    \"6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0\"\n"
"  ]\n"
"}\n"
"```"
msgstr ""
"```\n"
"{\n"
"  \"value\": 10000,\n"
"  \"script_pubkey\": \"OP_PUSHNUM_1 OP_PUSHBYTES_32 "
"156cc4878306157720607cdcb4b32afa4cc6853868458d7258b907112e5a434b\",\n"
"  \"address\": "
"\"bc1pz4kvfpurqc2hwgrq0nwtfve2lfxvdpfcdpzc6ujchyr3ztj6gd9sfr6ayf\",\n"
"  \"transaction\": "
"\"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed\",\n"
"  \"sat_ranges\": null,\n"
"  \"inscriptions\": [\n"
"    \"6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0\"\n"
"  ]\n"
"}\n"
"```"

#: src/guides/inscriptions.md:1
msgid "Ordinal Inscription Guide"
msgstr "Guida all'Iscrizione Ordinale"

#: src/guides/inscriptions.md:4
msgid ""
"Individual sats can be inscribed with arbitrary content, creating "
"Bitcoin-native digital artifacts that can be held in a Bitcoin wallet and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"I singoli satoshi possono essere iscritti con contenuti arbitrari, creando "
"artefatti digitali nativi di Bitcoin che possono essere conservati in un "
"portafoglio Bitcoin e trasferiti tramite transazioni Bitcoin. Le iscrizioni "
"sono durevoli, immutabili, sicure e decentralizzate come Bitcoin stesso."

#: src/guides/inscriptions.md:9
msgid ""
"Working with inscriptions requires a Bitcoin full node, to give you a view "
"of the current state of the Bitcoin blockchain, and a wallet that can create "
"inscriptions and perform sat control when constructing transactions to send "
"inscriptions to another wallet."
msgstr ""
"Per lavorare con le iscrizioni è necessario un nodo Bitcoin completo, per "
"avere una visione dello stato attuale della blockchain Bitcoin, e un portafoglio "
"in grado di creare iscrizioni e di eseguire il controllo sat quando si costruiscono "
"transazioni per inviare iscrizioni a un altro portafoglio."

#: src/guides/inscriptions.md:14
msgid ""
"Bitcoin Core provides both a Bitcoin full node and wallet. However, the "
"Bitcoin Core wallet cannot create inscriptions and does not perform sat "
"control."
msgstr ""
"Bitcoin Core fornisce sia un nodo completo Bitcoin che un portafoglio. "
"Tuttavia, il portafoglio di Bitcoin Core non può creare iscrizioni e non "
"esegue il controllo sat."

#: src/guides/inscriptions.md:17
msgid ""
"This requires [`ord`](https://github.com/ordinals/ord), the ordinal utility. "
"`ord` doesn't implement its own wallet, so `ord wallet` subcommands interact "
"with Bitcoin Core wallets."
msgstr ""
"Per questo è necessario [`ord`](https://github.com/ordinals/ord), l'utilità ordinale. "
"`ord` non implementa un proprio portafoglio, quindi i sottocomandi del `portafoglio ord` "
"interagiscono con i portafogli di Bitcoin Core."

#: src/guides/inscriptions.md:21
msgid "This guide covers:"
msgstr "Questa guida tratta di:"

#: src/guides/inscriptions.md:23
#: src/guides/inscriptions.md:40
msgid "Installing Bitcoin Core"
msgstr "Installazione di Bitcoin Core"

#: src/guides/inscriptions.md:24
msgid "Syncing the Bitcoin blockchain"
msgstr "Sincronizzare la blockchain di Bitcoin"

#: src/guides/inscriptions.md:25
msgid "Creating a Bitcoin Core wallet"
msgstr "Creare un portafoglio Bitcoin Core"

#: src/guides/inscriptions.md:26
msgid "Using `ord wallet receive` to receive sats"
msgstr "Usare `ord wallet receive` per ricevere sats"

#: src/guides/inscriptions.md:27
msgid "Creating inscriptions with `ord wallet inscribe`"
msgstr "Creare iscrizioni con `ord wallet inscribe`"

#: src/guides/inscriptions.md:28
msgid "Sending inscriptions with `ord wallet send`"
msgstr "Inviare iscrizioni con `ord wallet send`"

#: src/guides/inscriptions.md:29
msgid "Receiving inscriptions with `ord wallet receive`"
msgstr "Ricevere iscrizioni con `ord wallet receive`"

#: src/guides/inscriptions.md:30
msgid "Batch inscribing with `ord wallet inscribe --batch`"
msgstr "Inscrivere in batch con `ord wallet inscribe --batch`"

#: src/guides/inscriptions.md:32
msgid "Getting Help"
msgstr "Ricevere Aiuto"

#: src/guides/inscriptions.md:35
msgid ""
"If you get stuck, try asking for help on the [Ordinals Discord "
"Server](https://discord.com/invite/87cjuz4FYg), or checking GitHub for "
"relevant [issues](https://github.com/ordinals/ord/issues) and "
"[discussions](https://github.com/ordinals/ord/discussions)."
msgstr ""
"Se siete bloccati, provate a chiedere aiuto su [Ordinals Discord "
"Server](https://discord.com/invite/87cjuz4FYg), o a controllare su GitHub "
"[i problemi](https://github.com/ordinals/ord/issues) e "
"[le discussioni](https://github.com/ordinals/ord/discussions) pertinenti."

#: src/guides/inscriptions.md:43
msgid ""
"Bitcoin Core is available from [bitcoincore.org](https://bitcoincore.org/) "
"on the [download page](https://bitcoincore.org/en/download/)."
msgstr ""
"Bitcoin Core è disponibile su [bitcoincore.org](https://bitcoincore.org/) alla "
"[pagina di download](https://bitcoincore.org/en/download/)."

#: src/guides/inscriptions.md:46
msgid "Making inscriptions requires Bitcoin Core 24 or newer."
msgstr "Per effettuare iscrizioni è necessario Bitcoin Core 24 o più recente."

#: src/guides/inscriptions.md:48
msgid ""
"This guide does not cover installing Bitcoin Core in detail. Once Bitcoin "
"Core is installed, you should be able to run `bitcoind -version` "
"successfully from the command line. Do _NOT_ use `bitcoin-qt`."
msgstr ""
"Questa guida non tratta in dettaglio l'installazione di Bitcoin Core. Una volta "
"installato Bitcoin Core, si dovrebbe essere in grado di eseguire con "
"successo `bitcoind -version` dalla riga di comando. _NON_ utilizzare `bitcoin-qt`."

#: src/guides/inscriptions.md:52
msgid "Configuring Bitcoin Core"
msgstr "Configurazione di Bitcoin Core"

#: src/guides/inscriptions.md:55
msgid "`ord` requires Bitcoin Core's transaction index and rest interface."
msgstr "`ord` richiede l'indice delle transazioni e l'interfaccia rest di Bitcoin Core."

#: src/guides/inscriptions.md:57
msgid ""
"To configure your Bitcoin Core node to maintain a transaction index, add the "
"following to your `bitcoin.conf`:"
msgstr ""
"Per configurare il tuo nodo Bitcoin Core in modo che mantenga un indice delle "
"transazioni, aggiungere quanto segue al tuo `bitcoin.conf`:"

#: src/guides/inscriptions.md:60
#: src/guides/sat-hunting.md:30
msgid ""
"```\n"
"txindex=1\n"
"```"
msgstr ""
"```\n"
"txindex=1\n"
"```"

#: src/guides/inscriptions.md:64
msgid "Or, run `bitcoind` with `-txindex`:"
msgstr "Oppure, eseguire `bitcoind` con `-txindex`:"

#: src/guides/inscriptions.md:66
#: src/guides/inscriptions.md:78
msgid ""
"```\n"
"bitcoind -txindex\n"
"```"
msgstr ""
"```\n"
"bitcoind -txindex\n"
"```"

#: src/guides/inscriptions.md:70
msgid ""
"Details on creating or modifying your `bitcoin.conf` file can be found "
"[here](https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md)."
msgstr ""
"I dettagli sulla creazione o la modifica del file `bitcoin.conf` sono "
"disponibili [qui](https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md)."

#: src/guides/inscriptions.md:73
msgid "Syncing the Bitcoin Blockchain"
msgstr "Sincronizzazione della catena di Bitcoin"

#: src/guides/inscriptions.md:76
msgid "To sync the chain, run:"
msgstr "Per sincronizzare la catena, eseguire:"

#: src/guides/inscriptions.md:82
msgid "…and leave it running until `getblockcount`:"
msgstr "…e lasciarlo in esecuzione fino a `getblockcount`:"

#: src/guides/inscriptions.md:84
msgid ""
"```\n"
"bitcoin-cli getblockcount\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli getblockcount\n"
"```"

#: src/guides/inscriptions.md:88
msgid ""
"agrees with the block count on a block explorer like [the mempool.space "
"block explorer](https://mempool.space/). `ord` interacts with `bitcoind`, so "
"you should leave `bitcoind` running in the background when you're using "
"`ord`."
msgstr ""
"concorda con il conteggio dei blocchi su un block explorer come [mempool.space "
"block explorer](https://mempool.space/). `ord` interagisce con `bitcoind`, quindi "
"si dovrebbe lasciare `bitcoind` in esecuzione in background quando si utilizza "
"`ord`."

#: src/guides/inscriptions.md:92
msgid ""
"The blockchain takes about 600GB of disk space. If you have an external "
"drive you want to store blocks on, use the configuration option "
"`blocksdir=<external_drive_path>`. This is much simpler than using the "
"`datadir` option because the cookie file will still be in the default "
"location for `bitcoin-cli` and `ord` to find."
msgstr ""
"La blockchain occupa circa 600 GB di spazio su disco. Se si dispone di un disco esterno "
"su cui memorizzare i blocchi, utilizzare l'opzione di configurazione "
"`blocksdir=<external_drive_path>`. È molto più semplice che usare l'opzione `datadir`, "
"perché il file cookie sarà ancora nella posizione predefinita per essere "
"trovato da `bitcoin-cli` e `ord`."

#: src/guides/inscriptions.md:98
#: src/guides/collecting/sparrow-wallet.md:173
msgid "Troubleshooting"
msgstr "Risoluzione dei problemi"

#: src/guides/inscriptions.md:101
msgid ""
"Make sure you can access `bitcoind` with `bitcoin-cli -getinfo` and that it "
"is fully synced."
msgstr ""
"Assicurarsi di poter accedere a `bitcoind` con `bitcoin-cli -getinfo` e che sia "
"completamente sincronizzato."

#: src/guides/inscriptions.md:104
msgid ""
"If `bitcoin-cli -getinfo` returns `Could not connect to the server`, "
"`bitcoind` is not running."
msgstr ""
"Se `bitcoin-cli -getinfo` restituisce `Could not connect to the server`, "
"`bitcoind` non è in esecuzione."

#: src/guides/inscriptions.md:107
msgid ""
"Make sure `rpcuser`, `rpcpassword`, or `rpcauth` are _NOT_ set in your "
"`bitcoin.conf` file. `ord` requires using cookie authentication. Make sure "
"there is a file `.cookie` in your bitcoin data directory."
msgstr ""
"Assicurarsi che `rpcuser`, `rpcpassword`, o `rpcauth` _NON_ siano impostati "
"nel file `bitcoin.conf`. `ord` richiede l'uso dell'autenticazione tramite "
"cookie. Assicurarsi che ci sia un file `.cookie` nella tua directory "
"dei dati di bitcoin."

#: src/guides/inscriptions.md:111
msgid ""
"If `bitcoin-cli -getinfo` returns `Could not locate RPC credentials`, then "
"you must specify the cookie file location. If you are using a custom data "
"directory (specifying the `datadir` option), then you must specify the "
"cookie location like `bitcoin-cli "
"-rpccookiefile=<your_bitcoin_datadir>/.cookie -getinfo`. When running `ord` "
"you must specify the cookie file location with "
"`--cookie-file=<your_bitcoin_datadir>/.cookie`."
msgstr ""
"Se `bitcoin-cli -getinfo` restituisce `Could not locate RPC credentials`, è "
"necessario specificare la posizione del file cookie. Se si utilizza una directory "
"di dati personalizzata (specificando l'opzione `datadir`), è necessario specificare "
"la posizione del cookie come `bitcoin-cli "
"-rpccookiefile=<your_bitcoin_datadir>/.cookie -getinfo`. Quando si esegue `ord` "
"è necessario specificare la posizione del file cookie con "
"`--cookie-file=<your_bitcoin_datadir>/.cookie`."

#: src/guides/inscriptions.md:119
msgid ""
"Make sure you do _NOT_ have `disablewallet=1` in your `bitcoin.conf` file. "
"If `bitcoin-cli listwallets` returns `Method not found` then the wallet is "
"disabled and you won't be able to use `ord`."
msgstr ""
"Assicurarsi di _NON_ avere `disablewallet=1` nel file `bitcoin.conf`. "
"Se `bitcoin-cli listwallets` restituisce `Method not found`, il portafoglio è "
"disabilitato e non sarà possibile utilizzare `ord`."

#: src/guides/inscriptions.md:123
msgid ""
"Make sure `txindex=1` is set. Run `bitcoin-cli getindexinfo` and it should "
"return something like"
msgstr ""
"Assicurarsi che `txindex=1` sia impostato. Eseguite `bitcoin-cli getindexinfo` e dovrebbe "
"restituire qualcosa come"

#: src/guides/inscriptions.md:125
msgid ""
"```json\n"
"{\n"
"  \"txindex\": {\n"
"    \"synced\": true,\n"
"    \"best_block_height\": 776546\n"
"  }\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"txindex\": {\n"
"    \"synced\": true,\n"
"    \"best_block_height\": 776546\n"
"  }\n"
"}\n"
"```"

#: src/guides/inscriptions.md:133
msgid ""
"If it only returns `{}`, `txindex` is not set. If it returns `\"synced\": "
"false`, `bitcoind` is still creating the `txindex`. Wait until `\"synced\": "
"true` before using `ord`."
msgstr ""
"Se restituisce solo `{}`, il `txindex` non è impostato. Se restituisce `\"synced\": "
"false`, `bitcoind` sta ancora creando il `txindex`. Attendere fino a `\"synced\": "
"true` prima di usare `ord`."

#: src/guides/inscriptions.md:137
msgid ""
"If you have `maxuploadtarget` set it can interfere with fetching blocks for "
"`ord` index. Either remove it or set `whitebind=127.0.0.1:8333`."
msgstr ""
"Se è impostato `maxuploadtarget`, può interferire con il recupero dei blocchi "
"per l'indice `ord`. Rimuoverlo o impostare `whitebind=127.0.0.1:8333`."

#: src/guides/inscriptions.md:140
msgid "Installing `ord`"
msgstr "Installazione di `ord`"

#: src/guides/inscriptions.md:143
msgid ""
"The `ord` utility is written in Rust and can be built from "
"[source](https://github.com/ordinals/ord). Pre-built binaries are available "
"on the [releases page](https://github.com/ordinals/ord/releases)."
msgstr ""
"L'utilità `ord` è scritta in Rust e può essere compilata dalla "
"[sorgente](https://github.com/ordinals/ord). I binari precompilati sono disponibili "
"nella [pagina dei rilasci](https://github.com/ordinals/ord/releases)."

#: src/guides/inscriptions.md:147
msgid "You can install the latest pre-built binary from the command line with:"
msgstr "È possibile installare l'ultimo binario precostruito dalla riga di comando con:"

#: src/guides/inscriptions.md:149
msgid ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"
msgstr ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"

#: src/guides/inscriptions.md:153
msgid "Once `ord` is installed, you should be able to run:"
msgstr "Una volta installato `ord`, dovrebbe essere possibile eseguirlo:"

#: src/guides/inscriptions.md:155
msgid ""
"```\n"
"ord --version\n"
"```"
msgstr ""
"```\n"
"ord --version\n"
"```"

#: src/guides/inscriptions.md:159
msgid "Which prints out `ord`'s version number."
msgstr "Che stampa il numero di versione di `ord`."

#: src/guides/inscriptions.md:161
msgid "Creating a Bitcoin Core Wallet"
msgstr "Creare un Bitcoin Core Wallet"

#: src/guides/inscriptions.md:164
msgid ""
"`ord` uses Bitcoin Core to manage private keys, sign transactions, and "
"broadcast transactions to the Bitcoin network."
msgstr ""
"`ord` utilizza Bitcoin Core per gestire le chiavi private, firmare le "
"transazioni e trasmettere le transazioni alla rete Bitcoin."

#: src/guides/inscriptions.md:167
msgid "To create a Bitcoin Core wallet named `ord` for use with `ord`, run:"
msgstr "Per creare un portafoglio Bitcoin Core chiamato `ord` da usare con `ord`, eseguire:"

#: src/guides/inscriptions.md:169
msgid ""
"```\n"
"ord wallet create\n"
"```"
msgstr ""
"```\n"
"ord wallet create\n"
"```"

#: src/guides/inscriptions.md:173
msgid "Receiving Sats"
msgstr "Ricevere i Sats"

#: src/guides/inscriptions.md:176
msgid ""
"Inscriptions are made on individual sats, using normal Bitcoin transactions "
"that pay fees in sats, so your wallet will need some sats."
msgstr ""
"Le iscrizioni vengono effettuate su singoli sat, utilizzando le normali transazioni "
"Bitcoin che pagano le commissioni in sats, quindi il vostro portafoglio avrà bisogno "
"di alcuni sats."

#: src/guides/inscriptions.md:179
msgid "Get a new address from your `ord` wallet by running:"
msgstr "Ottenere un nuovo indirizzo dal proprio portafoglio `ord` eseguendo:"

#: src/guides/inscriptions.md:181
#: src/guides/inscriptions.md:269
#: src/guides/inscriptions.md:297
msgid ""
"```\n"
"ord wallet receive\n"
"```"
msgstr ""
"```\n"
"ord wallet receive\n"
"```"

#: src/guides/inscriptions.md:185
msgid "And send it some funds."
msgstr "E inviategli dei fondi."

#: src/guides/inscriptions.md:187
msgid "You can see pending transactions with:"
msgstr "È possibile vedere le transazioni in sospeso con:"

#: src/guides/inscriptions.md:189
#: src/guides/inscriptions.md:281
#: src/guides/inscriptions.md:308
msgid ""
"```\n"
"ord wallet transactions\n"
"```"
msgstr ""
"```\n"
"ord wallet transactions\n"
"```"

#: src/guides/inscriptions.md:193
msgid ""
"Once the transaction confirms, you should be able to see the transactions "
"outputs with `ord wallet outputs`."
msgstr ""
"Una volta confermata la transazione, si dovrebbe essere in grado di vedere "
"i risultati delle transazioni con `ord wallet outputs`."

#: src/guides/inscriptions.md:196
msgid "Creating Inscription Content"
msgstr "Creazione del contenuto dell'iscrizione"

#: src/guides/inscriptions.md:199
msgid ""
"Sats can be inscribed with any kind of content, but the `ord` wallet only "
"supports content types that can be displayed by the `ord` block explorer."
msgstr ""
"I sats possono essere iscritti con qualsiasi tipo di contenuto, ma il portafoglio `ord` "
"supporta solo i tipi di contenuto che possono essere visualizzati "
"dall'explorer di blocchi `ord`."

#: src/guides/inscriptions.md:202
msgid ""
"Additionally, inscriptions are included in transactions, so the larger the "
"content, the higher the fee that the inscription transaction must pay."
msgstr ""
"Inoltre, le iscrizioni sono incluse nelle transazioni, quindi più grande è il "
"contenuto, più alta è la commissione che la transazione di iscrizione deve pagare."

#: src/guides/inscriptions.md:205
msgid ""
"Inscription content is included in transaction witnesses, which receive the "
"witness discount. To calculate the approximate fee that an inscribe "
"transaction will pay, divide the content size by four and multiply by the "
"fee rate."
msgstr ""
"I contenuti delle iscrizioni sono inclusi nei witness delle transazioni, che "
"ricevono uno sconto sulle commissioni. Per calcolare la tariffa approssimativa "
"che una transazione di iscrizione dovrà pagare, dividere la dimensione del contenuto "
"per quattro e moltiplicare per la tariffa."

#: src/guides/inscriptions.md:209
msgid ""
"Inscription transactions must be less than 400,000 weight units, or they "
"will not be relayed by Bitcoin Core. One byte of inscription content costs "
"one weight unit. Since an inscription transaction includes not just the "
"inscription content, limit inscription content to less than 400,000 weight "
"units. 390,000 weight units should be safe."
msgstr ""
"Le transazioni di iscrizione devono essere inferiori a 400.000 unità di peso, "
"altrimenti non saranno trasmesse dal Bitcoin Core. Un byte di contenuto di "
"iscrizione costa un'unità di peso. Poiché una transazione di iscrizione non "
"include solo il contenuto dell'iscrizione, limitate il contenuto dell'iscrizione a "
"meno di 400.000 unità di peso. 390.000 unità di peso dovrebbero essere sicure."

#: src/guides/inscriptions.md:215
msgid "Creating Inscriptions"
msgstr "Creazione di iscrizioni"

#: src/guides/inscriptions.md:218
msgid "To create an inscription with the contents of `FILE`, run:"
msgstr "Per creare un'iscrizione con il contenuto di `FILE`, eseguire:"

#: src/guides/inscriptions.md:220
msgid ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE FILE\n"
"```"
msgstr ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE FILE\n"
"```"

#: src/guides/inscriptions.md:224
msgid ""
"Ord will output two transactions IDs, one for the commit transaction, and "
"one for the reveal transaction, and the inscription ID. Inscription IDs are "
"of the form `TXIDiN`, where `TXID` is the transaction ID of the reveal "
"transaction, and `N` is the index of the inscription in the reveal "
"transaction."
msgstr ""
"Ord produrrà due ID di transazione, uno per la transazione di commit e uno "
"per la transazione di reveal, e l'ID dell'iscrizione. Gli ID delle iscrizioni "
"sono della forma `TXIDiN`, dove `TXID` è l'ID della transazione di rivelazione "
"e `N` è l'indice dell'iscrizione nella transazione di rivelazione."

#: src/guides/inscriptions.md:229
msgid ""
"The commit transaction commits to a tapscript containing the content of the "
"inscription, and the reveal transaction spends from that tapscript, "
"revealing the content on chain and inscribing it on the first sat of the "
"input that contains the corresponding tapscript."
msgstr ""
"La transazione di commit esegue il commit su un tapscript contenente il contenuto "
"dell'iscrizione e la transazione di reveal esegue il commit da quel tapscript, "
"rivelando il contenuto sulla catena e iscrivendolo sul primo sat dell'input che "
"contiene il tapscript corrispondente."

#: src/guides/inscriptions.md:234
msgid ""
"Wait for the reveal transaction to be mined. You can check the status of the "
"commit and reveal transactions using  [the mempool.space block "
"explorer](https://mempool.space/)."
msgstr ""
"Attendere che la transazione reveal venga minata. È possibile controllare lo "
"stato delle transazioni di commit e di reveal utilizzando [il block explorer "
"di mempool.space](https://mempool.space/)."

#: src/guides/inscriptions.md:238
msgid ""
"Once the reveal transaction has been mined, the inscription ID should be "
"printed when you run:"
msgstr ""
"Una volta che la transazione reveal è stata estratta, l'ID dell'iscrizione "
"dovrebbe essere stampato durante l'esecuzione:"

#: src/guides/inscriptions.md:241
#: src/guides/inscriptions.md:288
#: src/guides/inscriptions.md:314
msgid ""
"```\n"
"ord wallet inscriptions\n"
"```"
msgstr ""
"```\n"
"ord wallet inscriptions\n"
"```"

#: src/guides/inscriptions.md:245
msgid "Parent-Child Inscriptions"
msgstr "Iscrizioni Genitore-Figlio"

#: src/guides/inscriptions.md:248
msgid ""
"Parent-child inscriptions enable what is colloquially known as collections, "
"see [provenance](../inscriptions/provenance.md) for more information."
msgstr ""
"Le iscrizioni \"parent-child\" consentono di creare quelle che in gergo "
"si chiamano collezioni, per maggiori informazioni, si veda la "
"voce [provenienza](../inscriptions/provenance.md)."

#: src/guides/inscriptions.md:251
msgid ""
"To make an inscription a child of another, the parent inscription has to be "
"inscribed and present in the wallet. To choose a parent run `ord wallet "
"inscriptions` and copy the inscription id (`<PARENT_INSCRIPTION_ID>`)."
msgstr ""
"Per rendere un'iscrizione figlia di un'altra, l'iscrizione parent deve essere "
"iscritta e presente nel portafoglio. Per scegliere un genitore, "
"eseguire `ord wallet inscriptions` e copiare l'id dell'iscrizione (`<PARENT_INSCRIPTION_ID>`)."

#: src/guides/inscriptions.md:255
msgid "Now inscribe the child inscription and specify the parent like so:"
msgstr "Ora iscrivete l'iscrizione figlia e specificate il genitore in questo modo:"

#: src/guides/inscriptions.md:257
msgid ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE --parent <PARENT_INSCRIPTION_ID> "
"CHILD_FILE\n"
"```"
msgstr ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE --parent <PARENT_INSCRIPTION_ID> "
"CHILD_FILE\n"
"```"

#: src/guides/inscriptions.md:261
msgid ""
"This relationship cannot be added retroactively, the parent has to be "
"present at inception of the child."
msgstr ""
"Questo rapporto non può essere aggiunto retroattivamente, il genitore "
"deve essere presente al momento dell'iscrizione del bambino."

#: src/guides/inscriptions.md:264
msgid "Sending Inscriptions"
msgstr "Invio di iscrizioni"

#: src/guides/inscriptions.md:267
msgid "Ask the recipient to generate a new address by running:"
msgstr "Chiedere al destinatario di generare un nuovo indirizzo eseguendo:"

#: src/guides/inscriptions.md:273
msgid "Send the inscription by running:"
msgstr "Inviare l’iscrizione eseguendo:"

#: src/guides/inscriptions.md:275
msgid ""
"```\n"
"ord wallet send --fee-rate <FEE_RATE> <ADDRESS> <INSCRIPTION_ID>\n"
"```"
msgstr ""
"```\n"
"ord wallet send --fee-rate <FEE_RATE> <ADDRESS> <INSCRIPTION_ID>\n"
"```"

#: src/guides/inscriptions.md:279
#: src/guides/inscriptions.md:307
msgid "See the pending transaction with:"
msgstr "Vedere la transazione in sospeso con:"

#: src/guides/inscriptions.md:285
msgid ""
"Once the send transaction confirms, the recipient can confirm receipt by "
"running:"
msgstr ""
"Una volta confermata la transazione di invio, il destinatario può confermare "
"la ricezione eseguendo:"

#: src/guides/inscriptions.md:292
msgid "Receiving Inscriptions"
msgstr "Ricevere Iscrizioni"

#: src/guides/inscriptions.md:295
msgid "Generate a new receive address using:"
msgstr "Generare un nuovo indirizzo di ricezione utilizzando:"

#: src/guides/inscriptions.md:301
msgid "The sender can transfer the inscription to your address using:"
msgstr "Il mittente può trasferire l'iscrizione al vostro indirizzo utilizzando:"

#: src/guides/inscriptions.md:303
msgid ""
"```\n"
"ord wallet send ADDRESS INSCRIPTION_ID\n"
"```"
msgstr ""
"```\n"
"ord wallet send ADDRESS INSCRIPTION_ID\n"
"```"

#: src/guides/inscriptions.md:312
msgid ""
"Once the send transaction confirms, you can can confirm receipt by running:"
msgstr ""
"Una volta confermata la transazione di invio, è possibile confermare la "
"ricezione eseguendo:"

#: src/guides/batch-inscribing.md:4
msgid ""
"Multiple inscriptions can be created inscriptions at the same time using the "
"[pointer field](./../inscriptions/pointer.md). This is especially helpful "
"for collections, or other cases when multiple inscriptions should share the "
"same parent, since the parent can passed into a reveal transaction that "
"creates multiple children."
msgstr ""
"È possibile creare più iscrizioni contemporaneamente utilizzando il "
"[campo puntatore](./../inscriptions/pointer.md). Questo è particolarmente utile "
"per le collezioni o per altri casi in cui più iscrizioni devono condividere lo "
"stesso genitore, poiché il genitore può essere passato in una transazione di "
"rivelazione che crea più figli."

#: src/guides/batch-inscribing.md:10
msgid ""
"To create a batch inscription using a batchfile in `batch.yaml`, run the "
"following command:"
msgstr ""
"Per creare un'iscrizione batch usando un batchfile in `batch.yaml`, eseguire "
"il seguente comando:"

#: src/guides/batch-inscribing.md:13
msgid ""
"```bash\n"
"ord wallet inscribe --fee-rate 21 --batch batch.yaml\n"
"```"
msgstr ""
"```bash\n"
"ord wallet inscribe --fee-rate 21 --batch batch.yaml\n"
"```"

#: src/guides/batch-inscribing.md:17
msgid "Example `batch.yaml`"
msgstr "Esempio `batch.yaml`"

#: src/guides/batch-inscribing.md:20
msgid ""
"```yaml\n"
"# example batch file\n"
"\n"
"# there are two modes:\n"
"# - `separate-outputs`: place all inscriptions in separate postage-sized "
"outputs\n"
"# - `shared-output`: place inscriptions in a single output separated by "
"postage\n"
"mode: separate-outputs\n"
"\n"
"# parent inscription:\n"
"parent: 6ac5cacb768794f4fd7a78bf00f2074891fce68bd65c4ff36e77177237aacacai0\n"
"\n"
"# inscriptions to inscribe\n"
"#\n"
"# each inscription has the following fields:\n"
"#\n"
"# `inscription`: path to inscription contents\n"
"# `metadata`: inscription metadata (optional)\n"
"# `metaprotocol`: inscription metaprotocol (optional)\n"
"inscriptions:\n"
"  - file: mango.avif\n"
"    metadata:\n"
"      title: Delicious Mangos\n"
"      description: >\n"
"        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam "
"semper,\n"
"        ligula ornare laoreet tincidunt, odio nisi euismod tortor, vel "
"blandit\n"
"        metus est et odio. Nullam venenatis, urna et molestie vestibulum, "
"orci\n"
"        mi efficitur risus, eu malesuada diam lorem sed velit. Nam "
"fermentum\n"
"        dolor et luctus euismod.\n"
"\n"
"  - file: token.json\n"
"    metaprotocol: brc-20\n"
"\n"
"  - file: tulip.png\n"
"    metadata:\n"
"      author: Satoshi Nakamoto\n"
"```"
msgstr ""
"```yaml\n"
"# example batch file\n"
"\n"
"# there are two modes:\n"
"# - `separate-outputs`: place all inscriptions in separate postage-sized "
"outputs\n"
"# - `shared-output`: place inscriptions in a single output separated by "
"postage\n"
"mode: separate-outputs\n"
"\n"
"# parent inscription:\n"
"parent: 6ac5cacb768794f4fd7a78bf00f2074891fce68bd65c4ff36e77177237aacacai0\n"
"\n"
"# inscriptions to inscribe\n"
"#\n"
"# each inscription has the following fields:\n"
"#\n"
"# `inscription`: path to inscription contents\n"
"# `metadata`: inscription metadata (optional)\n"
"# `metaprotocol`: inscription metaprotocol (optional)\n"
"inscriptions:\n"
"  - file: mango.avif\n"
"    metadata:\n"
"      title: Delicious Mangos\n"
"      description: >\n"
"        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam "
"semper,\n"
"        ligula ornare laoreet tincidunt, odio nisi euismod tortor, vel "
"blandit\n"
"        metus est et odio. Nullam venenatis, urna et molestie vestibulum, "
"orci\n"
"        mi efficitur risus, eu malesuada diam lorem sed velit. Nam "
"fermentum\n"
"        dolor et luctus euismod.\n"
"\n"
"  - file: token.json\n"
"    metaprotocol: brc-20\n"
"\n"
"  - file: tulip.png\n"
"    metadata:\n"
"      author: Satoshi Nakamoto\n"
"```"

#: src/guides/sat-hunting.md:4
msgid ""
"_This guide is out of date. Since it was written, the `ord` binary was "
"changed to only build the full satoshi index when the `--index-sats` flag is "
"supplied. Additionally, `ord` now has a built-in wallet that wraps a Bitcoin "
"Core wallet. See `ord wallet --help`._"
msgstr ""
"_Questa guida non è aggiornata. Da quando è stata scritta, il binario `ord` è "
"stato modificato per costruire l'indice satoshi completo solo quando viene "
"fornito il flag `--index-sats`. Inoltre, `ord` ha ora un portafoglio incorporato "
"che racchiude un portafoglio Bitcoin Core. Vedere `ord wallet --help`._"

#: src/guides/sat-hunting.md:9
msgid ""
"Ordinal hunting is difficult but rewarding. The feeling of owning a wallet "
"full of UTXOs, redolent with the scent of rare and exotic sats, is beyond "
"compare."
msgstr ""
"La caccia agli ordinali è difficile ma gratificante. La sensazione di possedere "
"un portafoglio pieno di UTXO, colmo di satoshi rari ed esotici, non ha paragoni."

#: src/guides/sat-hunting.md:12
msgid ""
"Ordinals are numbers for satoshis. Every satoshi has an ordinal number and "
"every ordinal number has a satoshi."
msgstr ""
"Gli ordinali sono numeri per i satoshi. Ogni satoshi ha un numero ordinale e "
"ogni numero ordinale ha un satoshi."

#: src/guides/sat-hunting.md:15
msgid "Preparation"
msgstr "Preparazione"

#: src/guides/sat-hunting.md:18
msgid "There are a few things you'll need before you start."
msgstr "Sono necessarie alcune cose prima di iniziare."

#: src/guides/sat-hunting.md:20
msgid ""
"First, you'll need a synced Bitcoin Core node with a transaction index. To "
"turn on transaction indexing, pass `-txindex` on the command-line:"
msgstr ""
"Innanzitutto, è necessario un nodo Bitcoin Core sincronizzato con un indice "
"delle transazioni. Per attivare l'indicizzazione delle transazioni, "
"passare `-txindex` alla riga di comando:"

#: src/guides/sat-hunting.md:23
msgid ""
"```sh\n"
"bitcoind -txindex\n"
"```"
msgstr ""
"```sh\n"
"bitcoind -txindex\n"
"```"

#: src/guides/sat-hunting.md:27
msgid ""
"Or put the following in your [Bitcoin configuration "
"file](https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"
msgstr ""
"Oppure inserire quanto segue nel tuo [file di configurazione "
"di Bitcoin](https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"

#: src/guides/sat-hunting.md:34
msgid ""
"Launch it and wait for it to catch up to the chain tip, at which point the "
"following command should print out the current block height:"
msgstr ""
"Avviatelo e attendete che raggiunga la punta della catena; a questo punto il comando "
"seguente dovrebbe stampare l'altezza del blocco corrente:"

#: src/guides/sat-hunting.md:37
msgid ""
"```sh\n"
"bitcoin-cli getblockcount\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getblockcount\n"
"```"

#: src/guides/sat-hunting.md:41
msgid "Second, you'll need a synced `ord` index."
msgstr "In secondo luogo, è necessario un indice `ord` sincronizzato."

#: src/guides/sat-hunting.md:43
msgid "Get a copy of `ord` from [the repo](https://github.com/ordinals/ord/)."
msgstr "Ottenere una copia di `ord` [dal repo](https://github.com/ordinals/ord/)."

#: src/guides/sat-hunting.md:45
msgid ""
"Run `RUST_LOG=info ord index`. It should connect to your bitcoin core node "
"and start indexing."
msgstr ""
"Eseguire `RUST_LOG=info ord index`. Dovrebbe connettersi al vostro nodo "
"bitcoin core e iniziare l'indicizzazione."

#: src/guides/sat-hunting.md:48
msgid "Wait for it to finish indexing."
msgstr "Attendere che finisca l'indicizzazione."

#: src/guides/sat-hunting.md:50
msgid "Third, you'll need a wallet with UTXOs that you want to search."
msgstr "In terzo luogo, è necessario un portafoglio con gli UTXO che si desidera cercare."

#: src/guides/sat-hunting.md:52
msgid "Searching for Rare Ordinals"
msgstr "Ricerca degli Ordinali Rari"

#: src/guides/sat-hunting.md:55
msgid "Searching for Rare Ordinals in a Bitcoin Core Wallet"
msgstr "Ricerca degli ordinali rari in un portafoglio Bitcoin Core"

#: src/guides/sat-hunting.md:57
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"searching for rare ordinals in a Bitcoin Core wallet is Easy. Assuming your "
"wallet is named `foo`:"
msgstr ""
"Il comando `ord wallet` è solo un wrapper attorno all'API RPC di Bitcoin Core, "
"quindi la ricerca di ordinali rari in un portafoglio Bitcoin Core è semplice. "
"Supponendo che il vostro portafoglio si chiami `foo`:"

#: src/guides/sat-hunting.md:61
msgid "Load your wallet:"
msgstr "Caricate il vostro wallet:"

#: src/guides/sat-hunting.md:63
msgid ""
"```sh\n"
"bitcoin-cli loadwallet foo\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli loadwallet foo\n"
"```"

#: src/guides/sat-hunting.md:67
msgid "Display any rare ordinals wallet `foo`'s UTXOs:"
msgstr "Visualizzare ogni UTXO raro nel portafoglio Ordinale di `foo`:"

#: src/guides/sat-hunting.md:69
#: src/guides/sat-hunting.md:132
#: src/guides/sat-hunting.md:233
msgid ""
"```sh\n"
"ord wallet sats\n"
"```"
msgstr ""
"```sh\n"
"ord wallet sats\n"
"```"

#: src/guides/sat-hunting.md:73
msgid "Searching for Rare Ordinals in a Non-Bitcoin Core Wallet"
msgstr "Ricerca di Ordinali Rari in un Non-Bitcoin Core Wallet"

#: src/guides/sat-hunting.md:75
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"to search for rare ordinals in a non-Bitcoin Core wallet, you'll need to "
"import your wallet's descriptors into Bitcoin Core."
msgstr ""
"Il comando `ord wallet` è solo un involucro dell'API RPC di Bitcoin Core, quindi "
"per cercare gli ordinali rari in un portafoglio non Bitcoin Core, è necessario "
"importare i descrittori del proprio portafoglio in Bitcoin Core."

#: src/guides/sat-hunting.md:79
msgid ""
"[Descriptors](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors.md) "
"describe the ways that wallets generate private keys and public keys."
msgstr ""
"[I descrittori](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors.md) "
"descrivono il modo in cui i portafogli generano le chiavi private e le chiavi pubbliche."

#: src/guides/sat-hunting.md:82
msgid ""
"You should only import descriptors into Bitcoin Core for your wallet's "
"public keys, not its private keys."
msgstr ""
"È opportuno importare in Bitcoin Core solo i descrittori per le chiavi "
"pubbliche del portafoglio, non quelli delle chiavi private."

#: src/guides/sat-hunting.md:85
msgid ""
"If your wallet's public key descriptor is compromised, an attacker will be "
"able to see your wallet's addresses, but your funds will be safe."
msgstr ""
"Se il descrittore della chiave pubblica del portafoglio viene compromesso, un "
"aggressore sarà in grado di vedere gli indirizzi del portafoglio, ma i vostri "
"fondi saranno al sicuro."

#: src/guides/sat-hunting.md:88
msgid ""
"If your wallet's private key descriptor is compromised, an attacker can "
"drain your wallet of funds."
msgstr ""
"Se il descrittore della chiave privata del vostro portafoglio è compromesso, "
"un aggressore può svuotare i fondi del vostro portafoglio."

#: src/guides/sat-hunting.md:91
msgid ""
"Get the wallet descriptor from the wallet whose UTXOs you want to search for "
"rare ordinals. It will look something like this:"
msgstr ""
"Ottenete il descrittore del portafoglio del quale volete cercare gli UTXO rari. "
"Avrà un aspetto simile a questo:"

#: src/guides/sat-hunting.md:94
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\n"
"```"

#: src/guides/sat-hunting.md:98
msgid "Create a watch-only wallet named `foo-watch-only`:"
msgstr "Create un portafoglio solo visualizzazione chiamato `foo-watch-only`:"

#: src/guides/sat-hunting.md:100
msgid ""
"```sh\n"
"bitcoin-cli createwallet foo-watch-only true true\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli createwallet foo-watch-only true true\n"
"```"

#: src/guides/sat-hunting.md:104
msgid "Feel free to give it a better name than `foo-watch-only`!"
msgstr "Sentitevi liberi di dargli un nome migliore di `foo-watch-only`!"

#: src/guides/sat-hunting.md:106
msgid "Load the `foo-watch-only` wallet:"
msgstr "Caricate il `foo-watch-only` wallet:"

#: src/guides/sat-hunting.md:108
#: src/guides/sat-hunting.md:199
msgid ""
"```sh\n"
"bitcoin-cli loadwallet foo-watch-only\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli loadwallet foo-watch-only\n"
"```"

#: src/guides/sat-hunting.md:112
msgid "Import your wallet descriptors into `foo-watch-only`:"
msgstr "Importate i descrittori del vostro portafoglio in `foo-watch-only`:"

#: src/guides/sat-hunting.md:114
msgid ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\", "
"\"timestamp\":0 }]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\", "
"\"timestamp\":0 }]'\n"
"```"

#: src/guides/sat-hunting.md:119
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of `\"timestamp\"` instead of "
"`0`. This will reduce the time it takes for Bitcoin Core to search for your "
"wallet's UTXOs."
msgstr ""
"Se si conosce il timestamp Unix in cui il proprio portafoglio ha iniziato a "
"ricevere transazioni, è possibile utilizzarlo per il valore di `\"timestamp\"` invece "
"di `0`. Questo ridurrà il tempo necessario a Bitcoin Core per cercare gli UTXO "
"del portafoglio."

#: src/guides/sat-hunting.md:124
#: src/guides/sat-hunting.md:225
msgid "Check that everything worked:"
msgstr "Verificate che tutto abbia funzionato:"

#: src/guides/sat-hunting.md:126
#: src/guides/sat-hunting.md:227
msgid ""
"```sh\n"
"bitcoin-cli getwalletinfo\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getwalletinfo\n"
"```"

#: src/guides/sat-hunting.md:130
#: src/guides/sat-hunting.md:231
msgid "Display your wallet's rare ordinals:"
msgstr "Visualizzate gli ordinali rari del portafoglio:"

#: src/guides/sat-hunting.md:136
msgid ""
"Searching for Rare Ordinals in a Wallet that Exports Multi-path Descriptors"
msgstr ""
"Ricerca di Ordinali Rari in un portafoglio che esporta descrittori Multi-path"

#: src/guides/sat-hunting.md:138
msgid ""
"Some descriptors describe multiple paths in one descriptor using angle "
"brackets, e.g., `<0;1>`. Multi-path descriptors are not yet supported by "
"Bitcoin Core, so you'll first need to convert them into multiple "
"descriptors, and then import those multiple descriptors into Bitcoin Core."
msgstr ""
"Alcuni descrittori descrivono più percorsi in un unico descrittore utilizzando "
"parentesi angolari, ad esempio `<0;1>`. I descrittori multipercorso non sono ancora "
"supportati da Bitcoin Core, quindi è necessario prima convertirli in descrittori "
"multipli e poi importare questi descrittori multipli in Bitcoin Core."

#: src/guides/sat-hunting.md:143
msgid ""
"First get the multi-path descriptor from your wallet. It will look something "
"like this:"
msgstr ""
"Per prima cosa, ottenere il descrittore multipercorso dal proprio portafoglio. "
"Il suo aspetto sarà simile a questo:"

#: src/guides/sat-hunting.md:146
msgid ""
"```\n"
"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/<0;1>/*)#fw76ulgt\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/<0;1>/*)#fw76ulgt\n"
"```"

#: src/guides/sat-hunting.md:150
msgid "Create a descriptor for the receive address path:"
msgstr "Crea un descrittore per il percorso dell'indirizzo di ricezione:"

#: src/guides/sat-hunting.md:152
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)\n"
"```"

#: src/guides/sat-hunting.md:156
msgid "And the change address path:"
msgstr "E il percorso di modifica dell'indirizzo:"

#: src/guides/sat-hunting.md:158
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)\n"
"```"

#: src/guides/sat-hunting.md:162
msgid ""
"Get and note the checksum for the receive address descriptor, in this case "
"`tpnxnxax`:"
msgstr ""
"Ottenete e annotate il checksum del descrittore dell'indirizzo di "
"ricezione, in questo caso `tpnxnxax`:"

#: src/guides/sat-hunting.md:165
msgid ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)'\n"
"```"

#: src/guides/sat-hunting.md:170
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src/guides/sat-hunting.md:180
msgid "And for the change address descriptor, in this case `64k8wnd7`:"
msgstr "E per il descrittore dell'indirizzo di modifica, in questo caso `64k8wnd7`:"

#: src/guides/sat-hunting.md:182
msgid ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)'\n"
"```"

#: src/guides/sat-hunting.md:187
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src/guides/sat-hunting.md:197
msgid "Load the wallet you want to import the descriptors into:"
msgstr "Caricate il portafoglio in cui si desidera importare i descrittori:"

#: src/guides/sat-hunting.md:203
msgid ""
"Now import the descriptors, with the correct checksums, into Bitcoin Core."
msgstr ""
"Ora importate i descrittori, con le checksum corrette, in Bitcoin Core."

#: src/guides/sat-hunting.md:205
msgid ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"

#: src/guides/sat-hunting.md:220
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of the `\"timestamp\"` fields "
"instead of `0`. This will reduce the time it takes for Bitcoin Core to "
"search for your wallet's UTXOs."
msgstr ""
"Se si conosce il timestamp Unix in cui il portafoglio ha iniziato a ricevere "
"transazioni, è possibile utilizzarlo per il valore dei campi `\"timestamp\"` invece "
"di `0`. Questo ridurrà il tempo necessario a Bitcoin Core per cercare "
"gli UTXO del portafoglio."

#: src/guides/sat-hunting.md:237
msgid "Exporting Descriptors"
msgstr "Esportare Descrittori"

#: src/guides/sat-hunting.md:241
msgid ""
"Navigate to the `Settings` tab, then to `Script Policy`, and press the edit "
"button to display the descriptor."
msgstr ""
"Passare alla scheda `Settings`, quindi a `Script Policy` e premete il pulsante "
"di modifica per visualizzare il descrittore."

#: src/guides/sat-hunting.md:244
msgid "Transferring Ordinals"
msgstr "Trasferire Ordinali"

#: src/guides/sat-hunting.md:246
msgid ""
"The `ord` wallet supports transferring specific satoshis. You can also use "
"`bitcoin-cli` commands `createrawtransaction`, "
"`signrawtransactionwithwallet`, and `sendrawtransaction`, how to do so is "
"complex and outside the scope of this guide."
msgstr ""
"Il portafoglio `ord` supporta il trasferimento di specifici satoshi. È anche "
"possibile utilizzare i comandi `bitcoin-cli` commands `createrawtransaction`, "
"`signrawtransactionwithwallet`, e `sendrawtransaction`, il modo di eseguire ciò "
"è complesso e non rientra nello scopo di questa guida."

#: src/guides/teleburning.md:4
msgid ""
"Teleburn addresses can be used to burn assets on other blockchains, leaving "
"behind in the smoking rubble a sort of forwarding address pointing to an "
"inscription on Bitcoin."
msgstr ""
"Gli indirizzi teleburn possono essere utilizzati per bruciare asset su altre "
"blockchain, lasciando dietro di sé, tra le macerie fumanti, una sorta di "
"indirizzo di spedizione che punta a un'iscrizione su Bitcoin."

#: src/guides/teleburning.md:8
msgid ""
"Teleburning an asset means something like, \"I'm out. Find me on Bitcoin.\""
msgstr ""
"Teleburnare un asset significa qualcosa come "
"\"Non esisto più su questa chain. Trovatemi su Bitcoin.\""

#: src/guides/teleburning.md:10
msgid ""
"Teleburn addresses are derived from inscription IDs. They have no "
"corresponding private key, so assets sent to a teleburn address are burned. "
"Currently, only Ethereum teleburn addresses are suppported. Pull requests "
"adding teleburn addresses for other chains are welcome."
msgstr ""
"Gli indirizzi Teleburn derivano dagli ID delle iscrizioni. Non hanno una chiave "
"privata corrispondente, quindi gli asset inviati a un indirizzo teleburn vengono "
"bruciati. Attualmente sono supportati solo gli indirizzi teleburn di Ethereum. Le "
"richieste di pull per l'aggiunta di indirizzi teleburn per altre catene sono benvenute."

#: src/guides/teleburning.md:15
msgid "Ethereum"
msgstr "Ethereum"

#: src/guides/teleburning.md:18
msgid ""
"Ethereum teleburn addresses are derived by taking the first 20 bytes of the "
"SHA-256 hash of the inscription ID, serialized as 36 bytes, with the first "
"32 bytes containing the transaction ID, and the last four bytes containing "
"big-endian inscription index, and interpreting it as an Ethereum address."
msgstr ""
"Gli indirizzi teleburn di Ethereum sono ricavati prendendo i primi 20 byte "
"dell'hash SHA-256 dell'ID dell'iscrizione, serializzato come 36 byte, con i "
"primi 32 byte contenenti l'ID della transazione e gli ultimi quattro byte "
"contenenti l'indice dell'iscrizione in big-endian, e interpretandolo "
"come un indirizzo Ethereum."

#: src/guides/teleburning.md:26
msgid ""
"The ENS domain name [rodarmor.eth](https://app.ens.domains/rodarmor.eth), "
"was teleburned to [inscription "
"zero](https://ordinals.com/inscription/6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0)."
msgstr ""
"Il dominio ENS [rodarmor.eth](https://app.ens.domains/rodarmor.eth), "
"è stato teleburnato all'[iscrizione "
"zero](https://ordinals.com/inscription/6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0)."

#: src/guides/teleburning.md:30
msgid ""
"Running the inscription ID of inscription zero is "
"`6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0`."
msgstr ""
"L'ID dell'iscrizione zero è "
"`6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0`."

#: src/guides/teleburning.md:33
msgid ""
"Passing `6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0` "
"to the teleburn command:"
msgstr ""
"Passaggio di `6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0` "
"al comando teleburn:"

#: src/guides/teleburning.md:36
msgid ""
"```bash\n"
"$ ord teleburn "
"6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0\n"
"```"
msgstr ""
"```bash\n"
"$ ord teleburn "
"6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0\n"
"```"

#: src/guides/teleburning.md:40
msgid "Returns:"
msgstr "Restituisce:"

#: src/guides/teleburning.md:42
msgid ""
"```json\n"
"{\n"
"  \"ethereum\": \"******************************************\"\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"ethereum\": \"******************************************\"\n"
"}\n"
"```"

#: src/guides/teleburning.md:48
msgid ""
"Indicating that `******************************************` is the Ethereum "
"teleburn address for inscription zero, which is, indeed, the current owner, "
"on Ethereum, of `rodarmor.eth`."
msgstr ""
"Indicando che `******************************************` è l'indirizzo teleburn "
"di Ethereum per l'iscrizione zero, che è effettivamente l'attuale proprietario, "
"su Ethereum, di `rodarmor.eth`."

#: src/guides/collecting.md:4
msgid ""
"Currently, [ord](https://github.com/ordinals/ord/) is the only wallet "
"supporting sat-control and sat-selection, which are required to safely store "
"and send rare sats and inscriptions, hereafter ordinals."
msgstr ""
"Attualmente, [ord](https://github.com/ordinals/ord/) è l'unico portafoglio che "
"supporta il controllo dei satoshi e la loro selezione, necessari per conservare "
"e inviare in modo sicuro i satoshi e le iscrizioni rari, identificati come ordinali."

#: src/guides/collecting.md:8
msgid ""
"The recommended way to send, receive, and store ordinals is with `ord`, but "
"if you are careful, it is possible to safely store, and in some cases send, "
"ordinals with other wallets."
msgstr ""
"Il modo consigliato per inviare, ricevere e conservare gli ordinali è con `ord`, "
"ma se si fa attenzione è possibile conservare e in alcuni casi inviare ordinali "
"con altri portafogli."

#: src/guides/collecting.md:12
msgid ""
"As a general note, receiving ordinals in an unsupported wallet is not "
"dangerous. Ordinals can be sent to any bitcoin address, and are safe as long "
"as the UTXO that contains them is not spent. However, if that wallet is then "
"used to send bitcoin, it may select the UTXO containing the ordinal as an "
"input, and send the inscription or spend it to fees."
msgstr ""
"Come nota generale, ricevere ordinali in un portafoglio non supportato non è "
"pericoloso. Gli ordinali possono essere inviati a qualsiasi indirizzo bitcoin e "
"sono sicuri finché l'UTXO che li contiene non viene speso. Tuttavia, se quel "
"portafoglio viene poi utilizzato per inviare bitcoin, potrebbe selezionare l'UTXO "
"contenente l'ordinale come input e inviare l'iscrizione o spenderla in commissioni."

#: src/guides/collecting.md:18
msgid ""
"A [guide](./collecting/sparrow-wallet.md) to creating an `ord`\\-compatible "
"wallet with [Sparrow Wallet](https://sparrowwallet.com/), is available in "
"this handbook."
msgstr ""
"Una [guida](./collecting/sparrow-wallet.md) alla creazione di un portafoglio "
"compatibile con gli ordinali con [Sparrow Wallet](https://sparrowwallet.com/) "
"è disponibile in questo manuale."

#: src/guides/collecting.md:21
msgid ""
"Please note that if you follow this guide, you should not use the wallet you "
"create to send BTC, unless you perform manual coin-selection to avoid "
"sending ordinals."
msgstr ""
"Si noti che se si segue questa guida, non si deve usare il portafoglio creato "
"per inviare BTC, a meno che non si esegua una selezione manuale delle monete per "
"evitare l'invio di ordinali."

#: src/guides/collecting/sparrow-wallet.md:1
msgid "Collecting Inscriptions and Ordinals with Sparrow Wallet"
msgstr "Collezionare Iscrizioni e Ordinali con il Portafoglio Sparrow"

#: src/guides/collecting/sparrow-wallet.md:4
msgid ""
"Users who cannot or have not yet set up the "
"[ord](https://github.com/ordinals/ord) wallet can receive inscriptions and "
"ordinals with alternative bitcoin wallets, as long as they are _very_ "
"careful about how they spend from that wallet."
msgstr ""
"Gli utenti che non possono o non hanno ancora configurato il portafoglio "
"[ord](https://github.com/ordinals/ord) possono ricevere iscrizioni e ordinali "
"con portafogli bitcoin alternativi, a patto che facciano _molta_ attenzione "
"a come spendono da quel portafoglio."

#: src/guides/collecting/sparrow-wallet.md:6
msgid ""
"This guide gives some basic steps on how to create a wallet with [Sparrow "
"Wallet](https://sparrowwallet.com/) which is compatible with `ord` and can "
"be later imported into `ord`"
msgstr ""
"Questa guida fornisce alcuni passaggi di base su come creare un portafoglio "
"con [Sparrow Wallet](https://sparrowwallet.com/) che sia compatibile "
"con `ord` e possa essere successivamente importato in `ord`"

#: src/guides/collecting/sparrow-wallet.md:8
msgid "⚠️⚠️ Warning!! ⚠️⚠️"
msgstr "⚠️⚠️ Attenzione!! ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:9
msgid ""
"As a general rule if you take this approach, you should use this wallet with "
"the Sparrow software as a receive-only wallet."
msgstr ""
"Come regola generale, se si adotta questo approccio, si dovrebbe utilizzare "
"questo portafoglio con il software Sparrow come portafoglio di sola ricezione."

#: src/guides/collecting/sparrow-wallet.md:11
msgid ""
"Do not spend any satoshis from this wallet unless you are sure you know what "
"you are doing. You could very easily inadvertently lose access to your "
"ordinals and inscriptions if you don't heed this warning."
msgstr ""
"Non spendere i satoshi da questo portafoglio se non si è sicuri di sapere cosa si "
"sta facendo. È molto facile che si perda inavvertitamente l'accesso agli ordinali "
"e alle iscrizioni se non si presta attenzione a questo avvertimento."

#: src/guides/collecting/sparrow-wallet.md:13
msgid "Wallet Setup & Receiving"
msgstr "Setup del Wallet & Ricezione"

#: src/guides/collecting/sparrow-wallet.md:15
msgid ""
"Download the Sparrow Wallet from the [releases "
"page](https://sparrowwallet.com/download/) for your particular operating "
"system."
msgstr ""
"Download del portafoglio Sparrow dalla [pagina "
"di release](https://sparrowwallet.com/download/) per il proprio sistema operativo."

#: src/guides/collecting/sparrow-wallet.md:17
msgid "Select `File -> New Wallet` and create a new wallet called `ord`."
msgstr "Selezionate `File -> New Wallet` e create un nuovo wallet chiamato `ord`."

#: src/guides/collecting/sparrow-wallet.md:19
msgid "![](images/wallet_setup_01.png)"
msgstr "![](images/wallet_setup_01.png)"

#: src/guides/collecting/sparrow-wallet.md:21
msgid ""
"Change the `Script Type` to `Taproot (P2TR)` and select the `New or Imported "
"Software Wallet` option."
msgstr ""
"Cambiate il `Script Type` in `Taproot (P2TR)` e selezionate l'opzione `New or Imported "
"Software Wallet`."

#: src/guides/collecting/sparrow-wallet.md:23
msgid "![](images/wallet_setup_02.png)"
msgstr "![](images/wallet_setup_02.png)"

#: src/guides/collecting/sparrow-wallet.md:25
msgid ""
"Select `Use 12 Words` and then click `Generate New`. Leave the passphrase "
"blank."
msgstr ""
"Selezionate `Use 12 Words` e dopo cliccate `Generate New`. Lasciate la Passphrase "
"vuota."

#: src/guides/collecting/sparrow-wallet.md:27
msgid "![](images/wallet_setup_03.png)"
msgstr "![](images/wallet_setup_03.png)"

#: src/guides/collecting/sparrow-wallet.md:29
msgid ""
"A new 12 word BIP39 seed phrase will be generated for you. Write this down "
"somewhere safe as this is your backup to get access to your wallet. NEVER "
"share or show this seed phrase to anyone else."
msgstr ""
"Verrà generata una nuova seed phrase BIP39 di 12 parole. Scrivetela in un "
"posto sicuro, perché è il modo per accedere al vostro portafoglio. Non "
"condividete o mostrate MAI questa frase a nessuno."

#: src/guides/collecting/sparrow-wallet.md:31
msgid "Once you have written down the seed phrase click `Confirm Backup`."
msgstr "Una volta annotata la seed phrase cliccate `Confirm Backup`."

#: src/guides/collecting/sparrow-wallet.md:33
msgid "![](images/wallet_setup_04.png)"
msgstr "![](images/wallet_setup_04.png)"

#: src/guides/collecting/sparrow-wallet.md:35
msgid ""
"Re-enter the seed phrase which you wrote down, and then click `Create "
"Keystore`."
msgstr ""
"Reinserite la seed phrase annotata, quindi fate clic su `Create Keystore`."

#: src/guides/collecting/sparrow-wallet.md:37
msgid "![](images/wallet_setup_05.png)"
msgstr "![](images/wallet_setup_05.png)"

#: src/guides/collecting/sparrow-wallet.md:39
msgid "Click `Import Keystore`."
msgstr "Click `Import Keystore`."

#: src/guides/collecting/sparrow-wallet.md:41
msgid "![](images/wallet_setup_06.png)"
msgstr "![](images/wallet_setup_06.png)"

#: src/guides/collecting/sparrow-wallet.md:43
msgid "Click `Apply`. Add a password for the wallet if you want to."
msgstr "Click `Apply`. Aggiungete una password per il wallet se preferite."

#: src/guides/collecting/sparrow-wallet.md:45
msgid "![](images/wallet_setup_07.png)"
msgstr "![](images/wallet_setup_07.png)"

#: src/guides/collecting/sparrow-wallet.md:47
msgid ""
"You now have a wallet which is compatible with `ord`, and can be imported "
"into `ord` using the BIP39 Seed Phrase. To receive ordinals or inscriptions, "
"click on the `Receive` tab and copy a new address."
msgstr ""
"Ora si dispone di un portafoglio compatibile con `ord`, che può essere importato "
"in `ord` utilizzando la seedphrase BIP39. Per ricevere ordinali o iscrizioni, fate "
"click sulla scheda `Receive` e copiate un nuovo indirizzo."

#: src/guides/collecting/sparrow-wallet.md:49
msgid ""
"Each time you want to receive you should use a brand-new address, and not "
"re-use existing addresses."
msgstr ""
"Ogni volta che si desidera ricevere si deve utilizzare un indirizzo nuovo di "
"zecca e non riutilizzare quelli esistenti."

#: src/guides/collecting/sparrow-wallet.md:51
msgid ""
"Note that bitcoin is different to some other blockchain wallets, in that "
"this wallet can generate an unlimited number of new addresses. You can "
"generate a new address by clicking on the `Get Next Address` button. You can "
"see all of your addresses in the `Addresses` tab of the app."
msgstr ""
"Si noti che bitcoin è diverso da altri portafogli blockchain, in quanto questo "
"portafoglio può generare un numero illimitato di nuovi indirizzi. È possibile "
"generare un nuovo indirizzo facendo click sul pulsante `Get Next Address`. "
"Tutti gli indirizzi sono visibili nella scheda `Addresses` dell'applicazione."

#: src/guides/collecting/sparrow-wallet.md:53
msgid ""
"You can add a label to each address, so you can keep track of what it was "
"used for."
msgstr ""
"È possibile aggiungere un'etichetta a ogni indirizzo, in modo da tenere "
"traccia dell'uso che ne è stato fatto."

#: src/guides/collecting/sparrow-wallet.md:55
msgid "![](images/wallet_setup_08.png)"
msgstr "![](images/wallet_setup_08.png)"

#: src/guides/collecting/sparrow-wallet.md:57
msgid "Validating / Viewing Received Inscriptions"
msgstr "Convalida/ Visualizzazione delle Iscrizioni Ricevute"

#: src/guides/collecting/sparrow-wallet.md:59
msgid ""
"Once you have received an inscription you will see a new transaction in the "
"`Transactions` tab of Sparrow, as well as a new UTXO in the `UTXOs` tab."
msgstr ""
"Una volta ricevuta un'iscrizione, si vedrà una nuova transazione nella "
"scheda `Transactions` di Sparrow e un nuovo UTXO nella scheda `UTXOs`."

#: src/guides/collecting/sparrow-wallet.md:61
msgid ""
"Initially this transaction may have an \"Unconfirmed\" status, and you will "
"need to wait for it to be mined into a bitcoin block before it is fully "
"received."
msgstr ""
"Inizialmente questa transazione potrebbe avere uno stato \"Unconfirmed\" e sarà "
"necessario attendere che venga minata in un blocco di bitcoin prima che "
"venga ricevuta completamente."

#: src/guides/collecting/sparrow-wallet.md:63
msgid "![](images/validating_viewing_01.png)"
msgstr "![](images/validating_viewing_01.png)"

#: src/guides/collecting/sparrow-wallet.md:65
msgid ""
"To track the status of your transaction you can right-click on it,  select "
"`Copy Transaction ID` and then paste that transaction id into "
"[mempool.space](https://mempool.space)."
msgstr ""
"Per seguire lo stato della transazione è possibile fare clic con il tasto destro "
"del mouse, selezionare `Copy Transaction ID` e incollare l'ID transazione "
"in [mempool.space](https://mempool.space)."

#: src/guides/collecting/sparrow-wallet.md:67
msgid "![](images/validating_viewing_02.png)"
msgstr "![](images/validating_viewing_02.png)"

#: src/guides/collecting/sparrow-wallet.md:69
msgid ""
"Once the transaction has confirmed, you can validate and view your "
"inscription by heading over to the `UTXOs` tab, finding the UTXO you want to "
"check, right-clicking on the `Output` and selecting `Copy Transaction "
"Output`. This transaction output id can then be pasted into the "
"[ordinals.com](https://ordinals.com) search."
msgstr ""
"Una volta confermata la transazione, è possibile convalidare e visualizzare "
"l'iscrizione andando alla scheda `UTXOs`, trovando l'UTXO che si desidera "
"controllare, facendo clic con il pulsante destro del mouse sull'`Output` e "
"selezionando `Copy Transaction Output`. L'ID della transazione può essere "
"incollato nella ricerca di [ordinals.com](https://ordinals.com)."

#: src/guides/collecting/sparrow-wallet.md:72
msgid "Freezing UTXO's"
msgstr "Congelamento degli UTXO"

#: src/guides/collecting/sparrow-wallet.md:73
msgid ""
"As explained above, each of your inscriptions is stored in an Unspent "
"Transaction Output (UTXO). You want to be very careful not to accidentally "
"spend your inscriptions, and one way to make it harder for this to happen is "
"to freeze the UTXO."
msgstr ""
"Come spiegato in precedenza, ogni iscrizione è memorizzata in un Output di "
"transazione non speso (UTXO). Per evitare di spendere accidentalmente le "
"iscrizioni e per rendere più difficile che ciò accada si può congelare l'UTXO."

#: src/guides/collecting/sparrow-wallet.md:75
msgid ""
"To do this, go to the `UTXOs` tab, find the UTXO you want to freeze, "
"right-click on the `Output` and select `Freeze UTXO`."
msgstr ""
"Per farlo, andate nella scheda `UTXOs`, trovate l'UTXO che volete congelare, "
"fate click con il tasto destro del mouse sull'`Output` e selezionate `Freeze UTXO`."

#: src/guides/collecting/sparrow-wallet.md:77
msgid ""
"This UTXO (Inscription) is now un-spendable within the Sparrow Wallet until "
"you unfreeze it."
msgstr ""
"Questo UTXO (Iscrizione) non potrà più essere speso nel Portafoglio Sparrow "
"fino a quando non verrà sbloccato."

#: src/guides/collecting/sparrow-wallet.md:79
msgid "Importing into `ord` wallet"
msgstr "Importare nell' `ord` wallet"

#: src/guides/collecting/sparrow-wallet.md:81
msgid ""
"For details on setting up Bitcoin Core and the `ord` wallet check out the "
"[Inscriptions Guide](../inscriptions.md)"
msgstr ""
"Per maggiori dettagli sull'impostazione di Bitcoin Core e del portafoglio `ord`, "
"consultare la [Guida alle Iscrizioni](../inscriptions.md)"

#: src/guides/collecting/sparrow-wallet.md:83
msgid ""
"When setting up `ord`, instead of running `ord wallet create` to create a "
"brand-new wallet, you can import your existing wallet using `ord wallet "
"restore \"BIP39 SEED PHRASE\"` using the seed phrase you generated with "
"Sparrow Wallet."
msgstr ""
"Quando si configura `ord`, invece di eseguire `ord wallet create` per creare "
"un portafoglio nuovo di zecca, è possibile importare il portafoglio esistente "
"usando `ord wallet restore \"BIP39 SEED PHRASE\"` utilizzando la seed phrase "
"generata con Sparrow Wallet."

#: src/guides/collecting/sparrow-wallet.md:85
msgid ""
"There is currently a [bug](https://github.com/ordinals/ord/issues/1589) "
"which causes an imported wallet to not be automatically rescanned against "
"the blockchain. To work around this you will need to manually trigger a "
"rescan using the bitcoin core cli: `bitcoin-cli -rpcwallet=ord "
"rescanblockchain 767430`"
msgstr ""
"Attualmente esiste un [bug](https://github.com/ordinals/ord/issues/1589) "
"che fa sì che un portafoglio importato non venga automaticamente ricontrollato "
"rispetto alla blockchain. Per ovviare a questo problema è necessario attivare "
"manualmente una nuova scansione utilizzando il bitcoin core cli: "
"`bitcoin-cli -rpcwallet=ord rescanblockchain 767430`"

#: src/guides/collecting/sparrow-wallet.md:88
msgid ""
"You can then check your wallet's inscriptions using `ord wallet inscriptions`"
msgstr ""
"È quindi possibile controllare le iscrizioni del proprio portafoglio "
"utilizzando `ord wallet inscriptions`"

#: src/guides/collecting/sparrow-wallet.md:90
msgid ""
"Note that if you have previously created a wallet with `ord`, then you will "
"already have a wallet with the default name, and will need to give your "
"imported wallet a different name. You can use the `--wallet` parameter in "
"all `ord` commands to reference a different wallet, eg:"
msgstr ""
"Si noti che se si è precedentemente creato un portafoglio con `ord`, si avrà "
"già un portafoglio con il nome predefinito e si dovrà dare un nome diverso al "
"portafoglio importato. È possibile utilizzare il parametro `--wallet` in tutti "
"i comandi `ord` per fare riferimento a un portafoglio diverso, ad es:"

#: src/guides/collecting/sparrow-wallet.md:92
msgid "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"
msgstr "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"

#: src/guides/collecting/sparrow-wallet.md:94
msgid "`ord --wallet ord_from_sparrow wallet inscriptions`"
msgstr "`ord --wallet ord_from_sparrow wallet inscriptions`"

#: src/guides/collecting/sparrow-wallet.md:96
msgid "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"
msgstr "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"

#: src/guides/collecting/sparrow-wallet.md:98
msgid "Sending inscriptions with Sparrow Wallet"
msgstr "Invio di iscrizioni con Sparrow Wallet"

#: src/guides/collecting/sparrow-wallet.md:100
msgid "⚠️⚠️ Warning ⚠️⚠️"
msgstr "⚠️⚠️ Attenzione ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:101
msgid ""
"While it is highly recommended that you set up a bitcoin core node and run "
"the `ord` software, there are certain limited ways you can send inscriptions "
"out of Sparrow Wallet in a safe way. Please note that this is not "
"recommended, and you should only do this if you fully understand what you "
"are doing."
msgstr ""
"Sebbene sia altamente raccomandato creare un nodo bitcoin core ed eseguire il "
"software `ord`, esistono alcuni modi limitati per inviare iscrizioni da Sparrow "
"Wallet in modo sicuro. Si noti che questa operazione è sconsigliata e deve essere "
"eseguita solo se si è pienamente consapevoli di ciò che si sta facendo."

#: src/guides/collecting/sparrow-wallet.md:103
msgid ""
"Using the `ord` software will remove much of the complexity we are "
"describing here, as it is able to automatically and safely handle sending "
"inscriptions in an easy way."
msgstr ""
"L'utilizzo del software `ord` eliminerà gran parte della complessità descritta "
"in questa guida, poiché è in grado di gestire automaticamente e in modo sicuro "
"l'invio di iscrizioni in modo semplice."

#: src/guides/collecting/sparrow-wallet.md:105
msgid "⚠️⚠️ Additional Warning ⚠️⚠️"
msgstr "⚠️⚠️ Avvertenze Aggiuntive ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:106
msgid ""
"Don't use your sparrow inscriptions wallet to do general sends of "
"non-inscription bitcoin. You can setup a separate wallet in sparrow if you "
"need to do normal bitcoin transactions, and keep your inscriptions wallet "
"separate."
msgstr ""
"Non utilizzate il vostro portafoglio sparrow delle inscriptions per effettuare "
"invii generici di bitcoin non legati a iscrizioni. È possibile configurare un "
"portafoglio separato in sparrow per effettuare normali transazioni in bitcoin e "
"tenere separato il portafoglio delle iscrizioni."

#: src/guides/collecting/sparrow-wallet.md:108
msgid "Bitcoin's UTXO model"
msgstr "Il modello UTXO di Bitcoin"

#: src/guides/collecting/sparrow-wallet.md:109
msgid ""
"Before sending any transaction it's important that you have a good mental "
"model for bitcoin's Unspent Transaction Output (UTXO) system. The way "
"Bitcoin works is fundamentally different to many other blockchains such as "
"Ethereum. In Ethereum generally you have a single address in which you store "
"ETH, and you cannot differentiate between any of the ETH -  it is just all a "
"single value of the total amount in that address. Bitcoin works very "
"differently in that we generate a new address in the wallet for each "
"receive, and every time you receive sats to an address in your wallet you "
"are creating a new UTXO. Each UTXO can be seen and managed individually. You "
"can select specific UTXO's which you want to spend, and you can choose not "
"to spend certain UTXO's."
msgstr ""
"Prima di inviare qualsiasi transazione è importante avere consapevolezza e "
"conoscenza del sistema UTXO (Unspent Transaction Output) di Bitcoin. Il modo "
"in cui Bitcoin funziona è diverso da quello di molte altre blockchain come "
"Ethereum. In Ethereum in genere si ha un unico indirizzo in cui si memorizzano "
"gli ETH, e non si può fare una distinzione tra gli ETH: si tratta di un unico "
"valore dell'importo totale in quell'indirizzo. Il Bitcoin funziona in modo molto "
"diverso, in quanto viene generato un nuovo indirizzo nel portafoglio per ogni "
"ricezione, e ogni volta che si ricevono satoshi nel portafoglio si crea un nuovo "
"UTXO. Ogni UTXO può essere visto e gestito individualmente. Si possono selezionare "
"UTXO specifici da spendere e si può scegliere di non spendere determinati UTXO."

#: src/guides/collecting/sparrow-wallet.md:111
msgid ""
"Some Bitcoin wallets do not expose this level of detail, and they just show "
"you a single summed up value of all the bitcoin in your wallet. However, "
"when sending inscriptions it is important that you use a wallet like Sparrow "
"which allows for UTXO control."
msgstr ""
"Alcuni portafogli Bitcoin non espongono questo livello di dettaglio e si limitano "
"a mostrare un unico valore sommato di tutti i bitcoin presenti nel portafoglio. "
"Tuttavia, quando si inviano iscrizioni è importante utilizzare un portafoglio come "
"Sparrow che consente il controllo degli UTXO."

#: src/guides/collecting/sparrow-wallet.md:113
msgid "Inspecting your inscription before sending"
msgstr "Controllare l'iscrizione prima dell'invio"

#: src/guides/collecting/sparrow-wallet.md:114
msgid ""
"Like we have previously described inscriptions are inscribed onto sats, and "
"sats are stored within UTXOs. UTXO's are a collection of satoshis with some "
"particular value of the number of satoshis (the output value). Usually (but "
"not always) the inscription will be inscribed on the first satoshi in the "
"UTXO."
msgstr ""
"Come abbiamo descritto in precedenza, le iscrizioni sono iscritte su sats e i "
"sats sono memorizzati all'interno degli UTXO. Gli UTXO sono una collezione di "
"satoshi con un particolare valore del numero di satoshi (il valore di uscita). "
"Di solito (ma non sempre) l'iscrizione viene fatta sul primo satoshi dell'UTXO."

#: src/guides/collecting/sparrow-wallet.md:116
msgid ""
"When inspecting your inscription before sending the main thing you will want "
"to check is which satoshi in the UTXO your inscription is inscribed on."
msgstr ""
"Quando si controlla l'iscrizione prima dell'invio, la cosa principale da verificare "
"è su quale satoshi dell'UTXO è iscritta l'iscrizione."

#: src/guides/collecting/sparrow-wallet.md:118
msgid ""
"To do this, you can follow the [Validating / Viewing Received "
"Inscriptions](./sparrow-wallet.md#validating--viewing-received-inscriptions) "
"described above to find the inscription page for your inscription on "
"ordinals.com"
msgstr ""
"Per farlo, si può seguire la procedura di [Convalida/Visualizzazione delle "
"Iscrizioni Ricevute](./sparrow-wallet.md#validating--viewing-received-inscriptions) "
"descritta sopra per trovare la pagina dell'iscrizione su ordinals.com"

#: src/guides/collecting/sparrow-wallet.md:120
msgid ""
"There you will find some metadata about your inscription which looks like "
"the following:"
msgstr ""
"Lì si trovano alcuni metadata relativi all'iscrizione, come quelli "
"che seguono:"

#: src/guides/collecting/sparrow-wallet.md:122
msgid "![](images/sending_01.png)"
msgstr "![](images/sending_01.png)"

#: src/guides/collecting/sparrow-wallet.md:124
msgid "There is a few of important things to check here:"
msgstr "Ci sono alcune cose importanti da vedere in questo caso:"

#: src/guides/collecting/sparrow-wallet.md:125
msgid ""
"The `output` identifier matches the identifier of the UTXO you are going to "
"send"
msgstr ""
"L'`output` identificatore corrisponde all'identificatore dell'UTXO che si "
"intende inviare"

#: src/guides/collecting/sparrow-wallet.md:126
msgid ""
"The `offset` of the inscription is `0` (this means that the inscription is "
"located on the first sat in the UTXO)"
msgstr ""
"L'`offset` dell'iscrizione è 0 (ciò significa che l'iscrizione si trova "
"sul primo sat dell'UTXO)"

#: src/guides/collecting/sparrow-wallet.md:127
msgid ""
"the `output_value` has enough sats to cover the transaction fee (postage) "
"for sending the transaction. The exact amount you will need depends on the "
"fee rate you will select for the transaction"
msgstr ""
"L'`output_value` ha un numero di sat sufficiente a coprire le commissioni di "
"transazione (\"postage\") per l'invio della transazione. L'importo esatto "
"necessario dipende dalla tariffa selezionata per la transazione"

#: src/guides/collecting/sparrow-wallet.md:129
msgid ""
"If all of the above are true for your inscription, it should be safe for you "
"to send it using the method below."
msgstr ""
"Se tutte le condizioni di cui sopra sono vere per la vostra iscrizione, l'invio "
"dovrebbe essere sicuro utilizzando il metodo seguente."

#: src/guides/collecting/sparrow-wallet.md:131
msgid ""
"⚠️⚠️ Be very careful sending your inscription particularly if the `offset` "
"value is not `0`. It is not recommended to use this method if that is the "
"case, as doing so you could accidentally send your inscription to a bitcoin "
"miner unless you know what you are doing."
msgstr ""
"⚠️⚠️ Fate molta attenzione all'invio dell'iscrizione, soprattutto se il valore "
"dell' `offset` non è `0`. Si sconsiglia di utilizzare questo metodo in tal caso, "
"in quanto si potrebbe inviare accidentalmente l'iscrizione a un miner di bitcoin, "
"a meno che non si sappia cosa si sta facendo."

#: src/guides/collecting/sparrow-wallet.md:133
msgid "Sending your inscription"
msgstr "Invio dell'iscrizione"

#: src/guides/collecting/sparrow-wallet.md:134
msgid ""
"To send an inscription navigate to the `UTXOs` tab, and find the UTXO which "
"you previously validated contains your inscription."
msgstr ""
"Per inviare un'iscrizione, andate nella scheda `UTXOs` e cercate l'UTXO che avete "
"precedentemente convalidato e che contiene la vostra iscrizione."

#: src/guides/collecting/sparrow-wallet.md:136
msgid ""
"If you previously froze the UXTO you will need to right-click on it and "
"unfreeze it."
msgstr ""
"Se l'UTXO è stato precedentemente congelato, è necessario fare click con "
"il tasto destro del mouse su di esso e sbloccarlo."

#: src/guides/collecting/sparrow-wallet.md:138
msgid ""
"Select the UTXO you want to send, and ensure that is the _only_ UTXO is "
"selected. You should see `UTXOs 1/1` in the interface. Once you are sure "
"this is the case you can hit `Send Selected`."
msgstr ""
"Selezionare l'UTXO che si desidera inviare e assicurarsi che sia l'_unico_ "
"UTXO selezionato. Nell'interfaccia dovrebbero essere visualizzati "
"gli `UTXOs 1/1`. Una volta accertato che sia così, si può "
"premere `Send Selected`."

#: src/guides/collecting/sparrow-wallet.md:140
msgid "![](images/sending_02.png)"
msgstr "![](images/sending_02.png)"

#: src/guides/collecting/sparrow-wallet.md:142
msgid ""
"You will then be presented with the transaction construction interface. "
"There is a few things you need to check here to make sure that this is a "
"safe send:"
msgstr ""
"Verrà quindi presentata l'interfaccia di costruzione della transazione. È "
"necessario verificare alcune cose per assicurarsi che l'invio sia sicuro:"

#: src/guides/collecting/sparrow-wallet.md:144
msgid ""
"The transaction should have only 1 input, and this should be the UTXO with "
"the label you want to send"
msgstr ""
"La transazione deve avere un solo ingresso, che deve essere l'UTXO con "
"l'etichetta che si vuole inviare"

#: src/guides/collecting/sparrow-wallet.md:145
msgid ""
"The transaction should have only 1 output, which is the address/label where "
"you want to send the inscription"
msgstr ""
"La transazione deve avere solo un'uscita, che è l'indirizzo/etichetta a "
"cui si vuole inviare l'iscrizione"

#: src/guides/collecting/sparrow-wallet.md:147
msgid ""
"If your transaction looks any different, for example you have multiple "
"inputs, or multiple outputs then this may not be a safe transfer of your "
"inscription, and you should abandon sending until you understand more, or "
"can import into the `ord` wallet."
msgstr ""
"Se la transazione ha un aspetto diverso, ad esempio ha più ingressi o più "
"uscite, allora potrebbe non essere un trasferimento sicuro dell'iscrizione "
"e si dovrebbe abbandonare l'invio fino a quando non se ne saprà di più o non "
"si riuscirà a importare nel portafoglio `ord`."

#: src/guides/collecting/sparrow-wallet.md:149
msgid ""
"You should set an appropriate transaction fee, Sparrow will usually "
"recommend a reasonable one, but you can also check "
"[mempool.space](https://mempool.space) to see what the recommended fee rate "
"is for sending a transaction."
msgstr ""
"È necessario impostare una tariffa di transazione appropriata; Sparrow di solito "
"ne consiglia una ragionevole, ma si può anche controllare "
"[mempool.space](https://mempool.space) per vedere qual è la tariffa raccomandata "
"per l'invio di una transazione."

#: src/guides/collecting/sparrow-wallet.md:151
msgid ""
"You should add a label for the recipient address, a label like `alice "
"address for inscription #123` would be ideal."
msgstr ""
"È necessario aggiungere un'etichetta per l'indirizzo del destinatario, "
"un'etichetta come `alice address for inscription #123` sarebbe l'ideale."

#: src/guides/collecting/sparrow-wallet.md:153
msgid ""
"Once you have checked the transaction is a safe transaction using the checks "
"above, and you are confident to send it you can click `Create Transaction`."
msgstr ""
"Una volta verificato che la transazione è sicura, utilizzando i controlli di "
"cui sopra, e che si è sicuri di poterla inviare, si può fare "
"click su `Create Transaction`."

#: src/guides/collecting/sparrow-wallet.md:155
msgid "![](images/sending_03.png)"
msgstr "![](images/sending_03.png)"

#: src/guides/collecting/sparrow-wallet.md:157
msgid ""
"Here again you can double check that your transaction looks safe, and once "
"you are confident you can click `Finalize Transaction for Signing`."
msgstr ""
"Anche qui si può verificare di nuovo che la transazione sia sicura e, una volta "
"sicuri, si può fare click su `Finalize Transaction for Signing`."

#: src/guides/collecting/sparrow-wallet.md:159
msgid "![](images/sending_04.png)"
msgstr "![](images/sending_04.png)"

#: src/guides/collecting/sparrow-wallet.md:161
msgid "Here you can triple check everything before hitting `Sign`."
msgstr ""
"Qui è possibile controllare tre volte tutto prima di "
"premere il pulsante `Sign`."

#: src/guides/collecting/sparrow-wallet.md:163
msgid "![](images/sending_05.png)"
msgstr "![](images/sending_05.png)"

#: src/guides/collecting/sparrow-wallet.md:165
msgid ""
"And then actually you get very very last chance to check everything before "
"hitting `Broadcast Transaction`. Once you broadcast the transaction it is "
"sent to the bitcoin network, and starts being propagated into the mempool."
msgstr ""
"E poi, in realtà, si ha l'ultima possibilità di controllare tutto prima di "
"premere `Broadcast Transaction`. Una volta trasmessa la transazione, questa "
"viene inviata alla rete bitcoin e inizia a essere propagata nella mempool."

#: src/guides/collecting/sparrow-wallet.md:167
msgid "![](images/sending_06.png)"
msgstr "![](images/sending_06.png)"

#: src/guides/collecting/sparrow-wallet.md:169
msgid ""
"If you want to track the status of your transaction you can copy the "
"`Transaction Id (Txid)` and paste that into "
"[mempool.space](https://mempool.space)"
msgstr ""
"Se si desidera seguire lo stato della transazione, è possibile "
"copiare `Transaction Id (Txid)` e incollarlo "
"in [mempool.space](https://mempool.space)"

#: src/guides/collecting/sparrow-wallet.md:171
msgid ""
"Once the transaction has confirmed you can check the inscription page on "
"[ordinals.com](https://ordinals.com) to validate that it has moved to the "
"new output location and address."
msgstr ""
"Una volta confermata la transazione, è possibile controllare la pagina "
"dell'iscrizione su [ordinals.com](https://ordinals.com) per verificare "
"che sia stata spostata nel nuovo indirizzo."

#: src/guides/collecting/sparrow-wallet.md:175
msgid ""
"Sparrow wallet is not showing a transaction/UTXO, but I can see it on "
"mempool.space!"
msgstr ""
"Il portafoglio Sparrow non mostra una transazione/UTXO, ma "
"la vedo su mempool.space!"

#: src/guides/collecting/sparrow-wallet.md:177
msgid ""
"Make sure that your wallet is connected to a bitcoin node. To validate this, "
"head into the `Preferences`\\-> `Server` settings, and click `Edit Existing "
"Connection`."
msgstr ""
"Assicuratevi che il vostro portafoglio sia collegato a un nodo bitcoin. Per "
"verificarlo, accedere alle `Preferences`\\-> `Server` e fare click "
"su `Edit Existing Connection`."

#: src/guides/collecting/sparrow-wallet.md:179
msgid "![](images/troubleshooting_01.png)"
msgstr "![](images/troubleshooting_01.png)"

#: src/guides/collecting/sparrow-wallet.md:181
msgid ""
"From there you can select a node and click `Test Connection` to validate "
"that Sparrow is able to connect successfully."
msgstr ""
"Da qui è possibile selezionare un nodo e fare click su `Test Connection` per "
"verificare che Sparrow sia in grado di connettersi correttamente."

#: src/guides/collecting/sparrow-wallet.md:183
msgid "![](images/troubleshooting_02.png)"
msgstr "![](images/troubleshooting_02.png)"

#: src/guides/testing.md:4
msgid ""
"Ord can be tested using the following flags to specify the test network. For "
"more information on running Bitcoin Core for testing, see [Bitcoin's "
"developer "
"documentation](https://developer.bitcoin.org/examples/testing.html)."
msgstr ""
"Ord può essere testato utilizzando i seguenti flag per specificare la rete di "
"test. Per ulteriori informazioni sull'esecuzione di Bitcoin Core per i test, "
"consultare la [documentazione per sviluppatori "
"di Bitcoin](https://developer.bitcoin.org/examples/testing.html)."

#: src/guides/testing.md:7
msgid ""
"Most `ord` commands in [inscriptions](inscriptions.md) and "
"[explorer](explorer.md) can be run with the following network flags:"
msgstr ""
"La maggior parte dei comandi di `ord` in [iscrizioni](inscriptions.md) ed "
"[explorer](explorer.md) può essere eseguita con i seguenti flag di rete:"

#: src/guides/testing.md:10
msgid "Network"
msgstr "Network"

#: src/guides/testing.md:10
msgid "Flag"
msgstr "Flag"

#: src/guides/testing.md:12
msgid "Testnet"
msgstr "Testnet"

#: src/guides/testing.md:12
msgid "`--testnet` or `-t`"
msgstr "`--testnet` o `-t`"

#: src/guides/testing.md:13
msgid "Signet"
msgstr "Signet"

#: src/guides/testing.md:13
msgid "`--signet` or `-s`"
msgstr "`--signet` o `-s`"

#: src/guides/testing.md:14
msgid "Regtest"
msgstr "Regtest"

#: src/guides/testing.md:14
msgid "`--regtest` or `-r`"
msgstr "`--regtest` o `-r`"

#: src/guides/testing.md:16
msgid "Regtest doesn't require downloading the blockchain or indexing ord."
msgstr "Regtest non richiede il download della blockchain o l'indicizzazione di ord."

#: src/guides/testing.md:21
msgid "Run bitcoind in regtest with:"
msgstr "Eseguire bitcoind in regtest con:"

#: src/guides/testing.md:22
msgid ""
"```\n"
"bitcoind -regtest -txindex\n"
"```"
msgstr ""
"```\n"
"bitcoind -regtest -txindex\n"
"```"

#: src/guides/testing.md:25
msgid "Create a wallet in regtest with:"
msgstr "Creare un portafoglio in regtest con:"

#: src/guides/testing.md:26
msgid ""
"```\n"
"ord -r wallet create\n"
"```"
msgstr ""
"```\n"
"ord -r wallet create\n"
"```"

#: src/guides/testing.md:29
msgid "Get a regtest receive address with:"
msgstr "Ottenere un indirizzo di ricezione in regtest con:"

#: src/guides/testing.md:30
msgid ""
"```\n"
"ord -r wallet receive\n"
"```"
msgstr ""
"```\n"
"ord -r wallet receive\n"
"```"

#: src/guides/testing.md:33
msgid "Mine 101 blocks (to unlock the coinbase) with:"
msgstr "Estrarre 101 blocchi (per sbloccare il coinbase) con:"

#: src/guides/testing.md:34
msgid ""
"```\n"
"bitcoin-cli -regtest generatetoaddress 101 <receive address>\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli -regtest generatetoaddress 101 <receive address>\n"
"```"

#: src/guides/testing.md:37
msgid "Inscribe in regtest with:"
msgstr "Inscrivere in regtest con:"

#: src/guides/testing.md:38
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 <file>\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 <file>\n"
"```"

#: src/guides/testing.md:41
msgid "Mine the inscription with:"
msgstr "Estrarre l'iscrizione con:"

#: src/guides/testing.md:42
msgid ""
"```\n"
"bitcoin-cli -regtest generatetoaddress 1 <receive address>\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli -regtest generatetoaddress 1 <receive address>\n"
"```"

#: src/guides/testing.md:45
msgid "View the inscription in the regtest explorer:"
msgstr "Visualizzare l'iscrizione nell'explorer di regtest:"

#: src/guides/testing.md:46
msgid ""
"```\n"
"ord -r server\n"
"```"
msgstr ""
"```\n"
"ord -r server\n"
"```"

#: src/guides/testing.md:50
msgid "Testing Recursion"
msgstr "Testare la Recursion"

#: src/guides/testing.md:53
msgid ""
"When testing out [recursion](../inscriptions/recursion.md), inscribe the "
"dependencies first (example with [p5.js](https://p5js.org)):"
msgstr ""
"Per testare la [recursion](../inscriptions/recursion.md), inscrivere prima "
"le dipendenze (esempio con [p5.js](https://p5js.org)):"

#: src/guides/testing.md:55
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 p5.js\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 p5.js\n"
"```"

#: src/guides/testing.md:58
msgid ""
"This should return a `inscription_id` which you can then reference in your "
"recursive inscription."
msgstr ""
"Questo dovrebbe restituire un `inscription_id` a cui si può fare riferimento "
"nell'iscrizione ricorsiva."

#: src/guides/testing.md:61
msgid ""
"ATTENTION: These ids will be different when inscribing on mainnet or signet, "
"so be sure to change those in your recursive inscription for each chain."
msgstr ""
"ATTENZIONE: Questi id saranno diversi quando si inscrive su mainnet o signet, "
"quindi assicuratevi di cambiarli nella vostra iscrizione ricorsiva per ogni catena."

#: src/guides/testing.md:65
msgid "Then you can inscribe your recursive inscription with:"
msgstr "Quindi è possibile inscrivere l'iscrizione ricorsiva con:"

#: src/guides/testing.md:66
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 recursive-inscription.html\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 recursive-inscription.html\n"
"```"

#: src/guides/testing.md:69
msgid "Finally you will have to mine some blocks and start the server:"
msgstr "Infine, dovrete estrarre alcuni blocchi e avviare il server:"

#: src/guides/testing.md:70
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 6 <receive address>\n"
"ord -r server\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 6 <receive address>\n"
"ord -r server\n"
"```"

#: src/guides/moderation.md:4
msgid ""
"`ord` includes a block explorer, which you can run locally with `ord server`."
msgstr ""
"`ord` include un block explorer, che può essere eseguito localmente con `ord server`."

#: src/guides/moderation.md:6
msgid ""
"The block explorer allows viewing inscriptions. Inscriptions are "
"user-generated content, which may be objectionable or unlawful."
msgstr ""
"L'esploratore di blocchi consente di visualizzare le iscrizioni. Le "
"iscrizioni sono contenuti generati dagli utenti, che possono essere "
"discutibili o illegali."

#: src/guides/moderation.md:9
msgid ""
"It is the responsibility of each individual who runs an ordinal block "
"explorer instance to understand their responsibilities with respect to "
"unlawful content, and decide what moderation policy is appropriate for their "
"instance."
msgstr ""
"È responsabilità di ogni individuo che gestisce un'istanza di Ordinal Block "
"Explorer comprendere le proprie responsabilità riguardo ai contenuti illegali "
"e decidere quale politica di moderazione sia appropriata per la propria istanza."

#: src/guides/moderation.md:13
msgid ""
"In order to prevent particular inscriptions from being displayed on an `ord` "
"instance, they can be included in a YAML config file, which is loaded with "
"the `--config` option."
msgstr ""
"Per evitare che determinate iscrizioni vengano visualizzate su un'istanza "
"di `ord`, è possibile includerle in un file di configurazione YAML, che viene "
"caricato con l'opzione `--config`."

#: src/guides/moderation.md:17
msgid ""
"To hide inscriptions, first create a config file, with the inscription ID "
"you want to hide:"
msgstr ""
"Per nascondere le iscrizioni, creare prima un file di configurazione con "
"l'ID dell'iscrizione che si vuole nascondere:"

#: src/guides/moderation.md:20
msgid ""
"```yaml\n"
"hidden:\n"
"- 0000000000000000000000000000000000000000000000000000000000000000i0\n"
"```"
msgstr ""
"```yaml\n"
"hidden:\n"
"- 0000000000000000000000000000000000000000000000000000000000000000i0\n"
"```"

#: src/guides/moderation.md:25
msgid ""
"The suggested name for `ord` config files is `ord.yaml`, but any filename "
"can be used."
msgstr ""
"Il nome suggerito per i file di configurazione di `ord` è `ord.yaml`, ma si può "
"usare qualsiasi nome di file."

#: src/guides/moderation.md:28
msgid "Then pass the file to `--config` when starting the server:"
msgstr "Passare il file a `--config` quando si avvia il server:"

#: src/guides/moderation.md:30
msgid "`ord --config ord.yaml server`"
msgstr "`ord --config ord.yaml server`"

#: src/guides/moderation.md:32
msgid ""
"Note that the `--config` option comes after `ord` but before the `server` "
"subcommand."
msgstr ""
"Si noti che l'opzione `--config` viene dopo `ord` ma prima del "
"sottocomando `server`."

#: src/guides/moderation.md:35
msgid "`ord` must be restarted in to load changes to the config file."
msgstr "`ord` deve essere riavviato per caricare le modifiche al file di configurazione."

#: src/guides/moderation.md:37
msgid "`ordinals.com`"
msgstr "`ordinals.com`"

#: src/guides/moderation.md:40
msgid ""
"The `ordinals.com` instances use `systemd` to run the `ord server` service, "
"which is called `ord`, with a config file located at `/var/lib/ord/ord.yaml`."
msgstr ""
"Le istanze `ordinals.com` utilizzano `systemd` per eseguire il servizio `ord "
"server`, chiamato `ord`, con un file di configurazione situato in `/var/lib/ord/ord.yaml`."

#: src/guides/moderation.md:43
msgid "To hide an inscription on `ordinals.com`:"
msgstr "Per nascondere un'iscrizione su `ordinals.com`:"

#: src/guides/moderation.md:45
msgid "SSH into the server"
msgstr "SSH nel server"

#: src/guides/moderation.md:46
msgid "Add the inscription ID to `/var/lib/ord/ord.yaml`"
msgstr "Aggiungere l'ID dell'iscrizione a `/var/lib/ord/ord.yaml`"

#: src/guides/moderation.md:47
msgid "Restart the service with `systemctl restart ord`"
msgstr "Riavviare il servizio con `systemctl restart ord`"

#: src/guides/moderation.md:48
msgid "Monitor the restart with `journalctl -u ord`"
msgstr "Monitorare il riavvio con `journalctl -u ord`"

#: src/guides/moderation.md:50
msgid ""
"Currently, `ord` is slow to restart, so the site will not come back online "
"immediately."
msgstr ""
"Attualmente, `ord` è lento a riavviarsi, quindi il sito non tornerà "
"online immediatamente."

#: src/guides/reindexing.md:4
msgid ""
"Sometimes the `ord` database must be reindexed, which means deleting the "
"database and restarting the indexing process with either `ord index update` "
"or `ord server`. Reasons to reindex are:"
msgstr ""
"A volte è necessario reindicizzare il database `ord`, il che significa cancellare "
"il database e riavviare il processo di indicizzazione con `ord index update` o "
"`ord server`. I motivi per la reindicizzazione sono:"

#: src/guides/reindexing.md:8
msgid "A new major release of ord, which changes the database scheme"
msgstr "Una nuova versione principale di ord, che modifica lo schema del database"

#: src/guides/reindexing.md:9
msgid "The database got corrupted somehow"
msgstr "Il database si è corrotto in qualche modo"

#: src/guides/reindexing.md:11
msgid ""
"The database `ord` uses is called [redb](https://github.com/cberner/redb), "
"so we give the index the default file name `index.redb`. By default we store "
"this file in different locations depending on your operating system."
msgstr ""
"Il database utilizzato da `ord` si chiama [redb](https://github.com/cberner/redb), "
"quindi diamo all'indice il nome predefinito di file `index.redb`. Per impostazione "
"predefinita, questo file viene memorizzato in posizioni diverse a seconda "
"del sistema operativo."

#: src/guides/reindexing.md:15
msgid "Platform"
msgstr "Piattaforma"

#: src/guides/reindexing.md:15
msgid "Value"
msgstr "Value"

#: src/guides/reindexing.md:17
msgid "Linux"
msgstr "Linux"

#: src/guides/reindexing.md:17
msgid "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"
msgstr "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"

#: src/guides/reindexing.md:17
msgid "/home/<USER>/.local/share/ord"
msgstr "/home/<USER>/.local/share/ord"

#: src/guides/reindexing.md:18
msgid "macOS"
msgstr "macOS"

#: src/guides/reindexing.md:18
msgid "`$HOME`/Library/Application Support/ord"
msgstr "`$HOME`/Library/Application Support/ord"

#: src/guides/reindexing.md:18
msgid "/Users/<USER>/Library/Application Support/ord"
msgstr "/Users/<USER>/Library/Application Support/ord"

#: src/guides/reindexing.md:19
msgid "Windows"
msgstr "Windows"

#: src/guides/reindexing.md:19
msgid "`{FOLDERID_RoamingAppData}`\\\\ord"
msgstr "`{FOLDERID_RoamingAppData}`\\\\ord"

#: src/guides/reindexing.md:19
msgid "C:\\Users\\<USER>\\AppData\\Roaming\\ord"
msgstr "C:\\Users\\<USER>\\AppData\\Roaming\\ord"

#: src/guides/reindexing.md:21
msgid ""
"So to delete the database and reindex on MacOS you would have to run the "
"following commands in the terminal:"
msgstr ""
"Per cancellare il database e reindicizzarlo su MacOS è necessario eseguire "
"i seguenti comandi nel terminale:"

#: src/guides/reindexing.md:24
msgid ""
"```bash\n"
"rm ~/Library/Application Support/ord/index.redb\n"
"ord index update\n"
"```"
msgstr ""
"```bash\n"
"rm ~/Library/Application Support/ord/index.redb\n"
"ord index update\n"
"```"

#: src/guides/reindexing.md:29
msgid ""
"You can of course also set the location of the data directory yourself with "
"`ord --datadir <DIR> index update` or give it a specific filename and path "
"with `ord --index <FILENAME> index update`."
msgstr ""
"Naturalmente potete anche impostare la posizione della directory dei dati "
"con `ord --datadir <DIR> index update` o assegnare un nome di file e un percorso "
"specifici con `ord --index <FILENAME> index update`."

#: src/bounties.md:1
msgid "Ordinal Bounty Hunting Hints"
msgstr "Suggerimenti per la Caccia alle Taglie Ordinali"

#: src/bounties.md:4
msgid ""
"The `ord` wallet can send and receive specific satoshis. Additionally, "
"ordinal theory is extremely simple. A clever hacker should be able to write "
"code from scratch to manipulate satoshis using ordinal theory in no time."
msgstr ""
"Il portafoglio `ord` può inviare e ricevere specifici satoshi. Inoltre, "
"la teoria ordinale è estremamente semplice. Un hacker intelligente dovrebbe "
"essere in grado di scrivere codice da zero per manipolare i satoshi utilizzando "
"la teoria ordinale in pochissimo tempo."

#: src/bounties.md:8
msgid ""
"For more information about ordinal theory, check out the [FAQ](./faq.md) for "
"an overview, the "
"[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) for the "
"technical details, and the [ord repo](https://github.com/ordinals/ord) for "
"the `ord` wallet and block explorer."
msgstr ""
"Per ulteriori informazioni sulla teoria ordinale, consultate le [FAQ](./faq.md) per "
"una panoramica, il "
"[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) per i dettagli "
"tecnici e la [ord repo](https://github.com/ordinals/ord) per il "
"portafoglio `ord` e il block explorer."

#: src/bounties.md:14
msgid ""
"Satoshi was the original developer of ordinal theory. However, he knew that "
"others would consider it heretical and dangerous, so he hid his knowledge, "
"and it was lost to the sands of time. This potent theory is only now being "
"rediscovered. You can help by researching rare satoshis."
msgstr ""
"Satoshi è stato lo sviluppatore originale della teoria ordinale. Tuttavia, "
"sapendo che gli altri l'avrebbero considerata eretica e pericolosa, nascose "
"la sua conoscenza, che andò perduta nelle sabbie del tempo. Questa potente teoria "
"viene riscoperta solo ora. Potete aiutarci ricercando i satoshi rari."

#: src/bounties.md:19
msgid "Good luck and godspeed!"
msgstr "Buona fortuna e siate veloci!"

#: src/bounty/0.md:1
msgid "Ordinal Bounty 0"
msgstr "Taglia Ordinale 0"

#: src/bounty/0.md:4
#: src/bounty/1.md:4
#: src/bounty/2.md:4
#: src/bounty/3.md:4
msgid "Criteria"
msgstr "Regole"

#: src/bounty/0.md:7
msgid ""
"Send a sat whose ordinal number ends with a zero to the submission address:"
msgstr ""
"Inviate un sat il cui numero ordinale finisce con uno zero all'indirizzo di ricezione:"

#: src/bounty/0.md:9
msgid "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"
msgstr "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"

#: src/bounty/0.md:11
msgid "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"
msgstr "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"

#: src/bounty/0.md:13
msgid "The sat must be the first sat of the output you send."
msgstr "Il sat deve essere il primo sat dell'output inviato."

#: src/bounty/0.md:15
#: src/bounty/1.md:14
#: src/bounty/2.md:15
#: src/bounty/3.md:63
msgid "Reward"
msgstr "Ricompensa"

#: src/bounty/0.md:18
msgid "100,000 sats"
msgstr "100.000 sats"

#: src/bounty/0.md:20
#: src/bounty/1.md:19
#: src/bounty/2.md:20
#: src/bounty/3.md:70
msgid "Submission Address"
msgstr "Indirizzo di Ricezione"

#: src/bounty/0.md:23
msgid ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"
msgstr ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"

#: src/bounty/0.md:25
#: src/bounty/1.md:24
#: src/bounty/2.md:25
#: src/bounty/3.md:75
msgid "Status"
msgstr "Status"

#: src/bounty/0.md:28
msgid ""
"Claimed by "
"[@count_null](https://twitter.com/rodarmor/status/1560793241473400833)!"
msgstr ""
"Vinta da "
"[@count_null](https://twitter.com/rodarmor/status/1560793241473400833)!"

#: src/bounty/1.md:1
msgid "Ordinal Bounty 1"
msgstr "Taglia Ordinale 1"

#: src/bounty/1.md:7
msgid ""
"The transaction that submits a UTXO containing the oldest sat, i.e., that "
"with the lowest number, amongst all submitted UTXOs will be judged the "
"winner."
msgstr ""
"La transazione che invierà un UTXO contenente il sat più vecchio, cioè quello "
"con il numero più basso, tra tutti gli UTXO inviati sarà giudicata vincitrice."

#: src/bounty/1.md:10
msgid ""
"The bounty is open for submissions until block 753984—the first block of "
"difficulty adjustment period 374. Submissions included in block 753984 or "
"later will not be considered."
msgstr ""
"La taglia è aperta alle candidature fino al blocco 753984, il primo blocco "
"del periodo di aggiustamento della difficoltà 374. Le candidature incluse "
"nel blocco 753984 o successive non saranno prese in considerazione."

#: src/bounty/1.md:17
msgid "200,000 sats"
msgstr "200.000 sats"

#: src/bounty/1.md:22
msgid ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"
msgstr ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"

#: src/bounty/1.md:27
msgid ""
"Claimed by "
"[@ordinalsindex](https://twitter.com/rodarmor/status/1569883266508853251)!"
msgstr ""
"Vinta da "
"[@ordinalsindex](https://twitter.com/rodarmor/status/1569883266508853251)!"

#: src/bounty/2.md:1
msgid "Ordinal Bounty 2"
msgstr "Taglia Ordinale 2"

#: src/bounty/2.md:7
msgid "Send an "
msgstr "Invia un "

#: src/bounty/2.md:7
msgid "uncommon"
msgstr "uncommon"

#: src/bounty/2.md:7
msgid " sat to the submission address:"
msgstr " sat all'indirizzo di ricezione:"

#: src/bounty/2.md:9
msgid "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"
msgstr "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"

#: src/bounty/2.md:11
msgid "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"
msgstr "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"

#: src/bounty/2.md:13
msgid ""
"Confirm that the submission address has not received transactions before "
"submitting your entry. Only the first successful submission will be rewarded."
msgstr ""
"Prima di inviare l'iscrizione, verificare che il wallet non abbia ricevuto "
"transazioni. Verrà premiato solo il primo invio andato a buon fine."

#: src/bounty/2.md:18
msgid "300,000 sats"
msgstr "300.000 sats"

#: src/bounty/2.md:23
msgid ""
"[`**********************************`](https://mempool.space/address/**********************************)"
msgstr ""
"[`**********************************`](https://mempool.space/address/**********************************)"

#: src/bounty/2.md:28
msgid ""
"Claimed by "
"[@utxoset](https://twitter.com/rodarmor/status/1582424455615172608)!"
msgstr ""
"Vinta da "
"[@utxoset](https://twitter.com/rodarmor/status/1582424455615172608)!"

#: src/bounty/3.md:1
msgid "Ordinal Bounty 3"
msgstr "Taglia Ordinale 3"

#: src/bounty/3.md:7
msgid ""
"Ordinal bounty 3 has two parts, both of which are based on _ordinal names_. "
"Ordinal names are a modified base-26 encoding of ordinal numbers. To avoid "
"locking short names inside the unspendable genesis block coinbase reward, "
"ordinal names get _shorter_ as the ordinal number gets _longer_. The name of "
"sat 0, the first sat to be mined is `nvtdijuwxlp` and the name of sat "
"2,099,999,997,689,999, the last sat to be mined, is `a`."
msgstr ""
"La taglia ordinale 3 ha due parti, entrambe basate sui _nomi ordinali_. I "
"nomi ordinali sono una codifica modificata in base-26 dei numeri ordinali. Per "
"evitare di bloccare i nomi brevi all'interno del blocco genesis non spendibile "
"coinbase, i nomi ordinali si _accorciano_ man mano che il numero ordinale "
"si _allunga_. Il nome del sat 0, il primo sat a essere estratto, è `nvtdijuwxlp` e "
"il nome del sat 2.099.999.997.689.999, l'ultimo sat a essere estratto, è `a`."

#: src/bounty/3.md:14
msgid ""
"The bounty is open for submissions until block 840000—the first block after "
"the fourth halvening. Submissions included in block 840000 or later will not "
"be considered."
msgstr ""
"La taglia è aperta alle candidature fino al blocco 840000, il primo blocco dopo "
"il quarto halving. Le proposte incluse nel blocco 840000 o successive non saranno "
"prese in considerazione."

#: src/bounty/3.md:18
msgid ""
"Both parts use [frequency.tsv](frequency.tsv), a list of words and the "
"number of times they occur in the [Google Books Ngram "
"dataset](http://storage.googleapis.com/books/ngrams/books/datasetsv2.html). "
"filtered to only include the names of sats which will have been mined by the "
"end of the submission period, that appear at least 5000 times in the corpus."
msgstr ""
"Entrambe le parti utilizzano [frequency.tsv](frequency.tsv), un elenco di parole "
"e del numero di volte in cui ricorrono nel [dataset Ngram di "
"Google Books](http://storage.googleapis.com/books/ngrams/books/datasetsv2.html), "
"filtrato in modo da includere solo i nomi di sat che saranno stati minati entro la "
"fine del periodo di presentazione, e che compaiono almeno 5000 volte nel corpus."

#: src/bounty/3.md:24
msgid ""
"`frequency.tsv` is a file of tab-separated values. The first column is the "
"word, and the second is the number of times it appears in the corpus. The "
"entries are sorted from least-frequently occurring to most-frequently "
"occurring."
msgstr ""
"`frequency.tsv` è un file di valori separati da tabulazioni. La prima colonna "
"è la parola e la seconda è il numero di volte in cui compare nel corpus. Le voci "
"sono ordinate dalla meno frequente alla più frequente."

#: src/bounty/3.md:29
msgid ""
"`frequency.tsv` was compiled using [this "
"program](https://github.com/casey/onegrams)."
msgstr ""
"`frequency.tsv` è stato compilato usando [questo "
"programma](https://github.com/casey/onegrams)."

#: src/bounty/3.md:32
msgid ""
"To search an `ord` wallet for sats with a name in `frequency.tsv`, use the "
"following [`ord`](https://github.com/ordinals/ord) command:"
msgstr ""
"Per cercare in un portafoglio `ord` i sats con un nome presente "
"in `frequency.tsv`, utilizzare il seguente comando [`ord`](https://github.com/ordinals/ord):"

#: src/bounty/3.md:35
msgid ""
"```\n"
"ord wallet sats --tsv frequency.tsv\n"
"```"
msgstr ""
"```\n"
"ord wallet sats --tsv frequency.tsv\n"
"```"

#: src/bounty/3.md:39
msgid ""
"This command requires the sat index, so `--index-sats` must be passed to ord "
"when first creating the index."
msgstr ""
"Questo comando richiede l'indice sat, quindi `--index-sats` deve essere passato "
"a ord quando si crea l'indice."

#: src/bounty/3.md:42
msgid "Part 0"
msgstr "Parte 0"

#: src/bounty/3.md:44
msgid "_Rare sats pair best with rare words._"
msgstr "_I sat rari si abbinano meglio alle parole rare._"

#: src/bounty/3.md:46
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the lowest number of occurrences in `frequency.tsv` shall be the winner "
"of part 0."
msgstr ""
"La transazione che invia l'UTXO contenente il sat il cui nome compare con il "
"minor numero di occorrenze in `frequency.tsv` sarà la vincitrice della parte 0."

#: src/bounty/3.md:50
msgid "Part 1"
msgstr "Parte 1"

#: src/bounty/3.md:52
msgid "_Popularity is the font of value._"
msgstr "_La popolarità è la fonte del valore._"

#: src/bounty/3.md:54
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the highest number of occurrences in `frequency.tsv` shall be the "
"winner of part 1."
msgstr ""
"La transazione che presenta l'UTXO contenente il sat il cui nome compare con il "
"maggior numero di occorrenze in `frequency.tsv` sarà la vincitrice della parte 1."

#: src/bounty/3.md:58
msgid "Tie Breaking"
msgstr "Parità"

#: src/bounty/3.md:60
msgid ""
"In the case of a tie, where two submissions occur with the same frequency, "
"the earlier submission shall be the winner."
msgstr ""
"In caso di parità, quando due invii si verificano con la stessa frequenza, "
"l'invio precedente sarà il vincitore."

#: src/bounty/3.md:66
msgid "Part 0: 200,000 sats"
msgstr "Parte 0: 200.000 sats"

#: src/bounty/3.md:67
msgid "Part 1: 200,000 sats"
msgstr "Parte 1: 200.000 sats"

#: src/bounty/3.md:68
msgid "Total: 400,000 sats"
msgstr "Totale: 400.000 sats"

#: src/bounty/3.md:73
msgid ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"
msgstr ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"

#: src/bounty/3.md:78
msgid "Unclaimed!"
msgstr "Non reclamata!"

