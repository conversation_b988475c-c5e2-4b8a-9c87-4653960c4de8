msgid ""
msgstr ""
"Project-Id-Version: Ordinal Theory Handbook\n"
"POT-Creation-Date: 2023-10-05T08:13:17+02:00\n"
"PO-Revision-Date: 2023-10-09 21:06+0200\n"
"Last-Translator: Zadur and Rupture\n"
"Language-Team: French\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.4\n"

#: src\SUMMARY.md:2 src\introduction.md:1
msgid "Introduction"
msgstr "Introduction"

#: src\SUMMARY.md:3
msgid "Overview"
msgstr "Aperçu"

#: src\SUMMARY.md:4 src\digital-artifacts.md:1
msgid "Digital Artifacts"
msgstr "Artéfacts numériques"

#: src\SUMMARY.md:5 src\SUMMARY.md:14 src\overview.md:221 src\inscriptions.md:1
msgid "Inscriptions"
msgstr "Inscriptions"

#: src\SUMMARY.md:6 src\inscriptions/metadata.md:1
msgid "Metadata"
msgstr "Métadonnées"

#: src\SUMMARY.md:7 src\inscriptions/provenance.md:1
msgid "Provenance"
msgstr "Provenance"

#: src\SUMMARY.md:8 src\inscriptions/recursion.md:1
msgid "Recursion"
msgstr "Récursion"

#: src\SUMMARY.md:9
msgid "FAQ"
msgstr "FAQ"

#: src\SUMMARY.md:10
msgid "Contributing"
msgstr "Contribuer"

#: src\SUMMARY.md:11 src\donate.md:1
msgid "Donate"
msgstr "Faire un don"

#: src\SUMMARY.md:12
msgid "Guides"
msgstr "Guides"

#: src\SUMMARY.md:13
msgid "Explorer"
msgstr "Explorateur"

#: src\SUMMARY.md:15 src\guides/sat-hunting.md:1
msgid "Sat Hunting"
msgstr "Chasse aux sats"

#: src\SUMMARY.md:16 src\guides/collecting.md:1
msgid "Collecting"
msgstr "Collectionner"

#: src\SUMMARY.md:17 src\guides/sat-hunting.md:239
msgid "Sparrow Wallet"
msgstr "Portefeuille Sparrow"

#: src\SUMMARY.md:18 src\guides/testing.md:1
msgid "Testing"
msgstr "Tests"

#: src\SUMMARY.md:19 src\guides/moderation.md:1
msgid "Moderation"
msgstr "Modération"

#: src\SUMMARY.md:20 src\guides/reindexing.md:1
msgid "Reindexing"
msgstr "Réindexation"

#: src\SUMMARY.md:21
msgid "Bounties"
msgstr "Récompenses"

#: src\SUMMARY.md:22
msgid "Bounty 0: 100,000 sats Claimed!"
msgstr "Récompense 0 : 100 000 sats Réclamés !"

#: src\SUMMARY.md:23
msgid "Bounty 1: 200,000 sats Claimed!"
msgstr "Récompense 1 : 200 000 sats Réclamés !"

#: src\SUMMARY.md:24
msgid "Bounty 2: 300,000 sats Claimed!"
msgstr "Récompense 2 : 300 000 sats Réclamés !"

#: src\SUMMARY.md:25
msgid "Bounty 3: 400,000 sats"
msgstr "Récompense 3 : 400 000 sats"

#: src\introduction.md:4
msgid ""
"This handbook is a guide to ordinal theory. Ordinal theory concerns itself "
"with satoshis, giving them individual identities and allowing them to be "
"tracked, transferred, and imbued with meaning."
msgstr ""
"Ce manuel est un guide de la théorie ordinale. La théorie ordinale "
"s’intéresse aux satoshis, leur attribuant des identités individuelles et "
"leur permettant d’être suivis, transférés et empreints de sens."

#: src\introduction.md:8
msgid ""
"Satoshis, not bitcoin, are the atomic, native currency of the Bitcoin "
"network. One bitcoin can be sub-divided into 100,000,000 satoshis, but no "
"further."
msgstr ""
"Les satoshis, et non les bitcoins, constituent la monnaie élémentaire et "
"native du réseau Bitcoin. Un bitcoin peut être subdivisé en 100 000 000 "
"satoshis, mais pas plus."

#: src\introduction.md:11
msgid ""
"Ordinal theory does not require a sidechain or token aside from Bitcoin, and "
"can be used without any changes to the Bitcoin network. It works right now."
msgstr ""
"La théorie ordinale ne nécessite pas de chaîne latérale ni de token autre "
"que bitcoin et peut être utilisée sans aucune modification du réseau "
"Bitcoin. Elle fonctionne dès maintenant."

#: src\introduction.md:14
msgid ""
"Ordinal theory imbues satoshis with numismatic value, allowing them to be "
"collected and traded as curios."
msgstr ""
"La théorie ordinale confère aux satoshis une valeur numismatique, leur "
"permettant d’être collectés et échangés comme des objets de curiosité."

#: src\introduction.md:17
msgid ""
"Individual satoshis can be inscribed with arbitrary content, creating unique "
"Bitcoin-native digital artifacts that can be held in Bitcoin wallets and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"Des satoshis individuels peuvent être inscrits avec un contenu arbitraire, "
"créant ainsi des artefacts numériques natifs de Bitcoin uniques qui peuvent "
"être conservés dans des portefeuilles Bitcoin et transférés à l’aide de "
"transactions Bitcoin. Les inscriptions sont aussi durables, immuables, "
"sécurisées et décentralisées que Bitcoin lui-même."

#: src\introduction.md:22
msgid ""
"Other, more unusual use-cases are possible: off-chain colored-coins, public "
"key infrastructure with key rotation, a decentralized replacement for the "
"DNS. For now though, such use-cases are speculative, and exist only in the "
"minds of fringe ordinal theorists."
msgstr ""
"D’autres cas d’utilisation plus inhabituels sont possibles : des colored "
"coins (pièces colorées) hors chaîne, une infrastructure de clés publiques "
"avec rotation de clés, une alternative décentralisée au DNS. Pour l’instant, "
"de tels cas d’utilisation sont spéculatifs et n’existent que dans l’esprit "
"de théoriciens d’Ordinals marginaux."

#: src\introduction.md:27
msgid "For more details on ordinal theory, see the [overview](overview.md)."
msgstr ""
"Pour plus de détails sur la théorie ordinale, consultez [l’aperçu](overview."
"md)."

#: src\introduction.md:29
msgid "For more details on inscriptions, see [inscriptions](inscriptions.md)."
msgstr ""
"Pour plus de détails sur les inscriptions, consultez la section "
"[inscriptions](inscriptions.md)."

#: src\introduction.md:31
msgid ""
"When you're ready to get your hands dirty, a good place to start is with "
"[inscriptions](guides/inscriptions.md), a curious species of digital "
"artifact enabled by ordinal theory."
msgstr ""
"Lorsque vous êtes prêt à vous plonger dans le sujet, vous pourriez commencer "
"par les [inscriptions](guides/inscriptions.md), une espèce curieuse "
"d’artefact numérique rendu possible par la théorie ordinale."

#: src\introduction.md:35
msgid "Links"
msgstr "Liens"

#: src\introduction.md:38
msgid "[GitHub](https://github.com/ordinals/ord/)"
msgstr "[GitHub](https://github.com/ordinals/ord/)"

#: src\introduction.md:39
msgid "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src\introduction.md:40
msgid "[Discord](https://discord.gg/ordinals)"
msgstr "[Discord](https://discord.gg/ordinals)"

#: src\introduction.md:41
msgid "[Open Ordinals Institute Website](https://ordinals.org/)"
msgstr "[Site web de l’Institut Open Ordinals](https://ordinals.org/)"

#: src\introduction.md:42
msgid "[Open Ordinals Institute X](https://x.com/ordinalsorg)"
msgstr "[X de l’Institut Open Ordinals](https://x.com/ordinalsorg)"

#: src\introduction.md:43
msgid "[Mainnet Block Explorer](https://ordinals.com)"
msgstr "[Explorateur de blocs Mainnet](https://ordinals.com)"

#: src\introduction.md:44
msgid "[Signet Block Explorer](https://signet.ordinals.com)"
msgstr "[Explorateur de blocs Signet](https://signet.ordinals.com)"

#: src\introduction.md:46
msgid "Videos"
msgstr "Vidéos"

#: src\introduction.md:49
msgid ""
"[Ordinal Theory Explained: Satoshi Serial Numbers and NFTs on Bitcoin]"
"(https://www.youtube.com/watch?v=rSS0O2KQpsI)"
msgstr ""
"[Théorie ordinale expliquée : Numéros de série de Satoshis et NFTs sur "
"Bitcoin](https://www.youtube.com/watch?v=rSS0O2KQpsI)"

#: src\introduction.md:50
msgid ""
"[Ordinals Workshop with Rodarmor](https://www.youtube.com/watch?"
"v=MC_haVa6N3I)"
msgstr ""
"[Workshop sur les Ordinals avec Rodarmor](https://www.youtube.com/watch?"
"v=MC_haVa6N3I)"

#: src\introduction.md:51
msgid ""
"[Ordinal Art: Mint Your own NFTs on Bitcoin w/ @rodarmor](https://www."
"youtube.com/watch?v=j5V33kV3iqo)"
msgstr ""
"[Art ordinal : Créez vos propres NFTs sur Bitcoin avec @rodarmor](https://"
"www.youtube.com/watch?v=j5V33kV3iqo)"

#: src\overview.md:1
msgid "Ordinal Theory Overview"
msgstr "Aperçu de la théorie ordinale"

#: src\overview.md:4
msgid ""
"Ordinals are a numbering scheme for satoshis that allows tracking and "
"transferring individual sats. These numbers are called [ordinal numbers]"
"(https://ordinals.com). Satoshis are numbered in the order in which they're "
"mined, and transferred from transaction inputs to transaction outputs first-"
"in-first-out. Both the numbering scheme and the transfer scheme rely on "
"_order_, the numbering scheme on the _order_ in which satoshis are mined, "
"and the transfer scheme on the _order_ of transaction inputs and outputs. "
"Thus the name, _ordinals_."
msgstr ""
"Les Ordinals sont un système de numérotation des satoshis qui permet de les "
"suivre et de les transférer de manière individuelle. Ces numéros sont "
"appelés [numéros ordinaux](https://ordinals.com). Les satoshis sont "
"numérotés selon l’ordre dans lequel ils ont été minés et sont transférés en "
"fonction de la séquence des transactions entrantes et sortantes, selon le "
"principe FIFO (First In, First Out). Le système de numérotation et le "
"système de transfert sont tous deux basés sur _l’ordre séquentiel_ ; le "
"système de numérotation repose sur _l’ordre_ de minage des satoshis, tandis "
"que le système de transfert repose sur _l’ordre_ d’entrée et de sortie des "
"transactions. D’où le nom _ordinals_."

#: src\overview.md:13
msgid ""
"Technical details are available in [the BIP](https://github.com/ordinals/ord/"
"blob/master/bip.mediawiki)."
msgstr ""
"Les détails techniques sont disponibles dans [le BIP](https://github.com/"
"ordinals/ord/blob/master/bip.mediawiki)."

#: src\overview.md:16
msgid ""
"Ordinal theory does not require a separate token, another blockchain, or any "
"changes to Bitcoin. It works right now."
msgstr ""
"La théorie ordinale fonctionne dès à présent sans modification du Bitcoin et "
"ne nécessite aucun autre token ou de chaîne latérale."

#: src\overview.md:19
msgid "Ordinal numbers have a few different representations:"
msgstr "Les nombres ordinaux ont plusieurs représentations différentes:"

#: src\overview.md:21
msgid ""
"_Integer notation_: [`2099994106992659`](https://ordinals.com/"
"sat/2099994106992659) The ordinal number, assigned according to the order in "
"which the satoshi was mined."
msgstr ""
"_Notation entière_: [`2099994106992659`](https://ordinals.com/"
"sat/2099994106992659) Le nombre ordinal attribué en fonction de l’ordre dans "
"lequel le satoshi a été miné."

#: src\overview.md:26
msgid ""
"_Decimal notation_: [`3891094.16797`](https://ordinals.com/"
"sat/3891094.16797) The first number is the block height in which the satoshi "
"was mined, the second the offset of the satoshi within the block."
msgstr ""
"_Notation décimale_: [`3891094.16797`](https://ordinals.com/"
"sat/3891094.16797) Le nombre ordinal attribué en fonction de l’ordre dans "
"lequel le satoshi a été miné."

#: src\overview.md:31
msgid ""
"_Degree notation_: [`3°111094′214″16797‴`](https://ordinals.com/"
"sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). We'll get to that in "
"a moment."
msgstr ""
"_Notation sexagésimal_: [`3°111094′214″16797‴`](https://ordinals.com/"
"sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). Nous y reviendrons "
"dans un instant."

#: src\overview.md:35
msgid ""
"_Percentile notation_: [`99.**************%`](https://ordinals.com/"
"sat/99.**************%25) . The satoshi's position in Bitcoin's supply, "
"expressed as a percentage."
msgstr ""
"_Percentile notation_: [`99.**************%`](https://ordinals.com/"
"sat/99.**************%25) . The satoshi's position in Bitcoin's supply, "
"expressed as a percentage."

#: src\overview.md:39
msgid ""
"_Name_: [`satoshi`](https://ordinals.com/sat/satoshi). An encoding of the "
"ordinal number using the characters `a` through `z`."
msgstr ""
"_Nom_: [`satoshi`](https://ordinals.com/sat/satoshi). Un encodage du nombre "
"ordinal utilisant les caractères de `a` à `z`."

#: src\overview.md:42
msgid ""
"Arbitrary assets, such as NFTs, security tokens, accounts, or stablecoins "
"can be attached to satoshis using ordinal numbers as stable identifiers."
msgstr ""
"Des actifs arbitraires, tels que des NFTs, des tokens de sécurité, des "
"comptes ou des stablecoins peuvent être attachés à des satoshis en utilisant "
"des nombres ordinaux comme identifiants stables."

#: src\overview.md:45
msgid ""
"Ordinals is an open-source project, developed [on GitHub](https://github.com/"
"ordinals/ord). The project consists of a BIP describing the ordinal scheme, "
"an index that communicates with a Bitcoin Core node to track the location of "
"all satoshis, a wallet that allows making ordinal-aware transactions, a "
"block explorer for interactive exploration of the blockchain, functionality "
"for inscribing satoshis with digital artifacts, and this manual."
msgstr ""
"Ordinals est un projet open-source, développé [sur GitHub](https://github."
"com/ordinals/ord). Le projet consiste en un BIP décrivant le schéma ordinal, "
"un index qui communique avec un nœud Bitcoin Core pour suivre l’emplacement "
"de tous les satoshis, un portefeuille qui permet d’effectuer des "
"transactions reconnaissant les ordinals, un explorateur de blocs pour "
"l’exploration interactive de la blockchain, une fonctionnalité permettant "
"d’inscrire des satoshis avec des artefacts numériques, et ce manuel."

#: src\overview.md:52
msgid "Rarity"
msgstr "Rareté"

#: src\overview.md:55
msgid ""
"Humans are collectors, and since satoshis can now be tracked and "
"transferred, people will naturally want to collect them. Ordinal theorists "
"can decide for themselves which sats are rare and desirable, but there are "
"some hints…"
msgstr ""
"Les humains sont des collectionneurs, et puisque les satoshis peuvent "
"désormais être suivis et transférés, les gens voudront naturellement les "
"collectionner. Les théoriciens d’Ordinals peuvent décider eux-mêmes quels "
"sats sont rares et désirables, mais il existe quelques Indices…"

#: src\overview.md:59
msgid ""
"Bitcoin has periodic events, some frequent, some more uncommon, and these "
"naturally lend themselves to a system of rarity. These periodic events are:"
msgstr ""
"Bitcoin connaît des événements périodiques, certains fréquents, d’autres "
"moins communs, et ceux-ci se prêtent naturellement à un système de rareté. "
"Ces événements périodiques sont les suivants :"

#: src\overview.md:62
msgid ""
"_Blocks_: A new block is mined approximately every 10 minutes, from now "
"until the end of time."
msgstr ""
"_Blocs _: Un nouveau bloc est miné toutes les 10 minutes environ, à partir "
"de maintenant jusqu’à la fin des temps."

#: src\overview.md:65
msgid ""
"_Difficulty adjustments_: Every 2016 blocks, or approximately every two "
"weeks, the Bitcoin network responds to changes in hashrate by adjusting the "
"difficulty target which blocks must meet in order to be accepted."
msgstr ""
"_Ajustements de la difficulté _: Tous les 2016 blocs, soit environ toutes "
"les deux semaines, le réseau Bitcoin réagit aux changements de taux de "
"hachage en ajustant la cible de difficulté que les blocs doivent atteindre "
"pour être acceptés."

#: src\overview.md:69
msgid ""
"_Halvings_: Every 210,000 blocks, or roughly every four years, the amount of "
"new sats created in every block is cut in half."
msgstr ""
"_Halvings _: Tous les 210 000 blocs, soit environ tous les quatre ans, la "
"quantité de nouveaux sats créés dans chaque bloc est réduite de moitié."

#: src\overview.md:72
msgid ""
"_Cycles_: Every six halvings, something magical happens: the halving and the "
"difficulty adjustment coincide. This is called a conjunction, and the time "
"period between conjunctions a cycle. A conjunction occurs roughly every 24 "
"years. The first conjunction should happen sometime in 2032."
msgstr ""
"_Cycles _: Tous les six halvings, un phénomène magique se produit: la "
"réduction de moitié et l’ajustement de la difficulté coïncident. C’est ce "
"qu’on appelle une conjonction, et la période de temps entre les conjonctions "
"représente un cycle. Une conjonction se produit environ tous les 24 ans. La "
"première conjonction devrait se produire en 2032."

#: src\overview.md:77
msgid "This gives us the following rarity levels:"
msgstr "Cela nous donne les niveaux de rareté suivants :"

#: src\overview.md:79
msgid "`common`: Any sat that is not the first sat of its block"
msgstr "`commun`: Tout sat qui n’est pas le premier sat de son bloc"

#: src\overview.md:80
msgid "`uncommon`: The first sat of each block"
msgstr "`peu commun`: Le premier sat de chaque bloc"

#: src\overview.md:81
msgid "`rare`: The first sat of each difficulty adjustment period"
msgstr "`rare`: Le premier sat de chaque période d’ajustement de la difficulté"

#: src\overview.md:82
msgid "`epic`: The first sat of each halving epoch"
msgstr "`épique`: Le premier sat après un halving"

#: src\overview.md:83
msgid "`legendary`: The first sat of each cycle"
msgstr "`légendaire`: Le premier sat de chaque cycle"

#: src\overview.md:84
msgid "`mythic`: The first sat of the genesis block"
msgstr "`mythique`: Le premier sat du bloc genesis"

#: src\overview.md:86
msgid ""
"Which brings us to degree notation, which unambiguously represents an "
"ordinal number in a way that makes the rarity of a satoshi easy to see at a "
"glance:"
msgstr ""
"Ce qui nous amène à la notation sexagésimal, qui représente de façon non "
"équivoque un nombre ordinal de manière qui facilite la perception de la "
"rareté d’un satoshi :"

#: src\overview.md:97
msgid ""
"Ordinal theorists often use the terms \"hour\", \"minute\", \"second\", and "
"\"third\" for _A_, _B_, _C_, and _D_, respectively."
msgstr ""
"Les théoriciens d’Ordinals utilisent souvent les termes « heure », « minute "
"», « seconde » et « tierce » en référence à _A_, _B_, _C_ et _D_."

#: src\overview.md:100
msgid "Now for some examples. This satoshi is common:"
msgstr "Voici quelques exemples. Ce satoshi est commun :"

#: src\overview.md:111
msgid "This satoshi is uncommon:"
msgstr "Ce satoshi est peu commun :"

#: src\overview.md:121
msgid "This satoshi is rare:"
msgstr "Ce satoshi est rare :"

#: src\overview.md:131
msgid "This satoshi is epic:"
msgstr "Ce satoshi est épique :"

#: src\overview.md:141
msgid "This satoshi is legendary:"
msgstr "Ce satoshi est légendaire :"

#: src\overview.md:151
msgid "And this satoshi is mythic:"
msgstr "Et ce satoshi est mythique :"

#: src\overview.md:161
msgid ""
"If the block offset is zero, it may be omitted. This is the uncommon satoshi "
"from above:"
msgstr ""
"Si le satoshi est le premier du bloc, le zéro peut être omis. C’est "
"l’exemple du satoshi peu commun que nous avons expliqué précédemment :"

#: src\overview.md:171
msgid "Rare Satoshi Supply"
msgstr "Offre de Satoshis rares"

#: src\overview.md:174
msgid "Total Supply"
msgstr "Offre totale"

#: src\overview.md:176
msgid "`common`: 2.1 quadrillion"
msgstr "`commun`: 2,1 quadrillions"

#: src\overview.md:177
msgid "`uncommon`: 6,929,999"
msgstr "`peu commun`: 6 929 999"

#: src\overview.md:178
msgid "`rare`: 3437"
msgstr "`rare`: 3437"

#: src\overview.md:179
msgid "`epic`: 32"
msgstr "`épique`: 32"

#: src\overview.md:180
msgid "`legendary`: 5"
msgstr "`légendaire`: 5"

#: src\overview.md:181 src\overview.md:190
msgid "`mythic`: 1"
msgstr "`mythique`: 1"

#: src\overview.md:183
msgid "Current Supply"
msgstr "Offre actuelle"

#: src\overview.md:185
msgid "`common`: 1.9 quadrillion"
msgstr "`commun`: 1,9 quadrillions"

#: src\overview.md:186
msgid "`uncommon`: 808,262"
msgstr "`peu commun`: 808 262"

#: src\overview.md:187
msgid "`rare`: 369"
msgstr "`rare`: 369"

#: src\overview.md:188
msgid "`epic`: 3"
msgstr "`épique`: 3"

#: src\overview.md:189
msgid "`legendary`: 0"
msgstr "`légendaire`: 0"

#: src\overview.md:192
msgid ""
"At the moment, even uncommon satoshis are quite rare. As of this writing, "
"745,855 uncommon satoshis have been mined - one per 25.6 bitcoin in "
"circulation."
msgstr ""
"À l’heure actuelle, même les satoshis peu communs sont assez rares. À "
"l’heure où nous écrivons ces lignes, 745 855 satoshis peu communs ont été "
"minés, soit un pour 25,6 bitcoins en circulation."

#: src\overview.md:196
msgid "Names"
msgstr "Noms"

#: src\overview.md:199
msgid ""
"Each satoshi has a name, consisting of the letters _A_ through _Z_, that get "
"shorter the further into the future the satoshi was mined. They could start "
"short and get longer, but then all the good, short names would be trapped in "
"the unspendable genesis block."
msgstr ""
"Chaque satoshi a un nom, composé des lettres _A_ à _Z_, qui devient de plus "
"en plus court au fur et à mesure que le satoshi est miné dans le futur. Ils "
"pourraient d’abord être courts, puis devenir plus longs, mais tous les bons "
"noms courts seraient alors piégés dans le bloc de genèse qui ne peut pas "
"être dépensé."

#: src\overview.md:204
msgid ""
"As an example, 1905530482684727°'s name is \"iaiufjszmoba\". The name of the "
"last satoshi to be mined is \"a\". Every combination of 10 characters or "
"less is out there, or will be out there, someday."
msgstr ""
"À titre d’exemple, le nom pour 1905530482684727° est « iaiufjszmoba ». Le "
"nom du dernier satoshi qui sera miné est « a ». Toutes les combinaisons de "
"10 caractères ou moins existent déjà, ou existeront un jour."

#: src\overview.md:208
msgid "Exotics"
msgstr "Exotiques"

#: src\overview.md:211
msgid ""
"Satoshis may be prized for reasons other than their name or rarity. This "
"might be due to a quality of the number itself, like having an integer "
"square or cube root. Or it might be due to a connection to a historical "
"event, such as satoshis from block 477,120, the block in which SegWit "
"activated, or 2099999997689999°, the last satoshi that will ever be mined."
msgstr ""
"Les satoshis peuvent être prisés pour des raisons autres que leur nom ou "
"leur rareté. Cela peut être dû à une caractéristique du nombre lui-même, "
"comme le fait d’avoir une racine carrée ou cubique. Il peut également s’agir "
"d’un lien avec un événement historique, tel que les satoshis du bloc 477 "
"120, le bloc dans lequel SegWit a été activé, ou 2099999997689999°, le tout "
"dernier satoshi qui sera miné."

#: src\overview.md:217
msgid ""
"Such satoshis are termed \"exotic\". Which satoshis are exotic and what "
"makes them so is subjective. Ordinal theorists are encouraged to seek out "
"exotics based on criteria of their own devising."
msgstr ""
"Ces satoshis sont dits « exotiques ». La question de savoir quels sont les "
"satoshis exotiques et ce qui les rend exotiques est subjective. Les "
"théoriciens d’Ordinals sont encouragés à rechercher les exotiques sur la "
"base de critères qu’ils auront eux-mêmes définis."

#: src\overview.md:224
msgid ""
"Satoshis can be inscribed with arbitrary content, creating Bitcoin-native "
"digital artifacts. Inscribing is done by sending the satoshi to be inscribed "
"in a transaction that reveals the inscription content on-chain. This content "
"is then inextricably linked to that satoshi, turning it into an immutable "
"digital artifact that can be tracked, transferred, hoarded, bought, sold, "
"lost, and rediscovered."
msgstr ""
"Les satoshis peuvent être inscrits avec un contenu arbitraire, créant ainsi "
"des artefacts numériques natifs de Bitcoin. L’inscription se fait en "
"envoyant le satoshi à inscrire dans une transaction qui révèle le contenu de "
"l’inscription sur la blockchain. Ce contenu est alors inextricablement lié à "
"ce satoshi, le transformant en un artefact numérique immuable qui peut être "
"suivi, transféré, thésaurisé, acheté, vendu, perdu et redécouvert."

#: src\overview.md:231
msgid "Archaeology"
msgstr "Archéologie"

#: src\overview.md:234
msgid ""
"A lively community of archaeologists devoted to cataloging and collecting "
"early NFTs has sprung up. [Here's a great summary of historical NFTs by "
"Chainleft.](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-"
"N29oF4iwCgX3lacrvaG9Kjko)"
msgstr ""
"Une communauté animée d’archéologues dévoués au catalogage et à la "
"collection des premiers NFTs a vu le jour. [Voici un excellent résumé des "
"NFTs historiques par Chainleft.](https://mirror.xyz/chainleft.eth/"
"MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)"

#: src\overview.md:238
msgid ""
"A commonly accepted cut-off for early NFTs is March 19th, 2018, the date the "
"first ERC-721 contract, [SU SQUARES](https://tenthousandsu.com/), was "
"deployed on Ethereum."
msgstr ""
"Le 19 mars 2018 est généralement considéré comme la date limite pour faire "
"référence aux premiers NFTs, car c’est ce jour-là que le premier contrat "
"ERC-721, [SU SQUARES](https://tenthousandsu.com/), a été déployé sur "
"Ethereum."

#: src\overview.md:242
msgid ""
"Whether or not ordinals are of interest to NFT archaeologists is an open "
"question! In one sense, ordinals were created in early 2022, when the "
"Ordinals specification was finalized. In this sense, they are not of "
"historical interest."
msgstr ""
"La question de savoir si les ordinals présentent un intérêt pour les "
"archéologues des NFTs restera ouverte ! Les ordinals ont été créés au début "
"de l’année 2022, lorsque la spécification d’Ordinals a été finalisée. En ce "
"sens, ils ne présentent pas d’intérêt historique."

#: src\overview.md:247
msgid ""
"In another sense though, ordinals were in fact created by Satoshi Nakamoto "
"in 2009 when he mined the Bitcoin genesis block. In this sense, ordinals, "
"and especially early ordinals, are certainly of historical interest."
msgstr ""
"D’un autre coté par contre, les ordinals ont en fait été créés par Satoshi "
"Nakamoto en 2009 lorsqu’il a miné le bloc de genèse de Bitcoin. En ce sens, "
"les ordinals, et en particulier les premiers ordinals, suscitent "
"certainement un intérêt historique."

#: src\overview.md:251
msgid ""
"Many ordinal theorists favor the latter view. This is not least because the "
"ordinals were independently discovered on at least two separate occasions, "
"long before the era of modern NFTs began."
msgstr ""
"De nombreux théoriciens d’Ordinals préfèrent cette dernière perspective. "
"Cela tient en partie au fait que les ordinals ont été découverts "
"indépendamment au moins à deux reprises, bien avant l’ère des NFTs modernes."

#: src\overview.md:255
msgid ""
"On August 21st, 2012, Charlie Lee [posted a proposal to add proof-of-stake "
"to Bitcoin to the Bitcoin Talk forum](https://bitcointalk.org/index.php?"
"topic=102355.0). This wasn't an asset scheme, but did use the ordinal "
"algorithm, and was implemented but never deployed."
msgstr ""
"Le 21 août 2012, Charlie Lee [a publié une proposition visant à ajouter un "
"Proof of Stake (PoS) à Bitcoin sur le forum Bitcoin Talk](https://"
"bitcointalk.org/index.php?topic=102355.0). Il ne s’agissait pas d’un système "
"d’actifs, mais sa proposition utilisait néanmoins l’algorithme ordinal et a "
"été mise en œuvre, mais n’a jamais été déployée."

#: src\overview.md:261
msgid ""
"On October 8th, 2012, jl2012 [posted a scheme to the same forum](https://"
"bitcointalk.org/index.php?topic=117224.0) which uses decimal notation and "
"has all the important properties of ordinals. The scheme was discussed but "
"never implemented."
msgstr ""
"Le 8 octobre 2012, jl2012 [a publié un schéma sur le même Forum](https://"
"bitcointalk.org/index.php?topic=117224.0) qui utilise une notation décimale "
"et possède toutes les propriétés importantes des ordinals. Le schéma a été "
"discuté mais jamais mis en œuvre."

#: src\overview.md:266
msgid ""
"These independent inventions of ordinals indicate in some way that ordinals "
"were discovered, or rediscovered, and not invented. The ordinals are an "
"inevitability of the mathematics of Bitcoin, stemming not from their modern "
"documentation, but from their ancient genesis. They are the culmination of a "
"sequence of events set in motion with the mining of the first block, so many "
"years ago."
msgstr ""
"D’une certaine manière, ces inventions indépendantes d’ordinals indiquent "
"que les ordinals ont été découverts, ou redécouverts, et non pas inventés. "
"Les ordinals sont une conséquence inévitable de la mathématique de Bitcoin, "
"qui découle non pas de leur documentation moderne, mais de leur genèse "
"ancienne. Ils sont l’aboutissement d’une séquence d’événements qui se sont "
"déroulés au fil des ans et qui ont commencé lorsque le premier bloc a été "
"miné."

#: src\digital-artifacts.md:4
msgid ""
"Imagine a physical artifact. A rare coin, say, held safe for untold years in "
"the dark, secret clutch of a Viking hoard, now dug from the earth by your "
"grasping hands. It…"
msgstr ""
"Imaginez un artefact physique. Disons, une pièce rare, conservée en sécurité "
"pendant d’innombrables années dans la cachette sombre et secrète d’un trésor "
"Viking, que vous déterrez aujourd’hui de vos propres mains. Cette pièce…"

#: src\digital-artifacts.md:8
msgid ""
"…has an owner. You. As long as you keep it safe, nobody can take it from you."
msgstr ""
"...a un propriétaire. Vous. Tant que vous la gardez en sécurité, personne ne "
"peut vous le prendre."

#: src\digital-artifacts.md:10
msgid "…is complete. It has no missing parts."
msgstr "...est complète. Il n’y a aucune partie manquante."

#: src\digital-artifacts.md:12
msgid ""
"…can only be changed by you. If you were a trader, and you made your way to "
"18th century China, none but you could stamp it with your chop-mark."
msgstr ""
"…ne peut être modifiée que par vous. Si vous étiez un marchand et que vous "
"arriviez en Chine au XVIIIe siècle, vous seul pourriez la marquer de votre "
"poinçon."

#: src\digital-artifacts.md:15
msgid ""
"…can only be disposed of by you. The sale, trade, or gift is yours to make, "
"to whomever you wish."
msgstr ""
"…ne peut être transmise que par vous. C’est à vous qu’appartient la décision "
"de la vendre, de l’échanger ou de la donner, à qui vous voulez."

#: src\digital-artifacts.md:18
msgid ""
"What are digital artifacts? Simply put, they are the digital equivalent of "
"physical artifacts."
msgstr ""
"Que sont les artefacts numériques ? Il s’agit tout simplement de "
"l’équivalent numérique des artefacts physiques."

#: src\digital-artifacts.md:21
msgid ""
"For a digital thing to be a digital artifact, it must be like that coin of "
"yours:"
msgstr ""
"Pour qu’une chose numérique soit un artefact numérique, elle doit être comme "
"votre pièce de monnaie :"

#: src\digital-artifacts.md:24
msgid ""
"Digital artifacts can have owners. A number is not a digital artifact, "
"because nobody can own it."
msgstr ""
"Les artefacts numériques peuvent avoir des propriétaires. Un nombre n’est "
"pas un artefact numérique, car personne ne peut le posséder."

#: src\digital-artifacts.md:27
msgid ""
"Digital artifacts are complete. An NFT that points to off-chain content on "
"IPFS or Arweave is incomplete, and thus not a digital artifact."
msgstr ""
"Les artefacts numériques sont complets. Un NFT qui pointe vers un contenu "
"hors chaîne sur IPFS ou Arweave est incomplet et n’est donc pas un artefact "
"numérique."

#: src\digital-artifacts.md:30
msgid ""
"Digital artifacts are permissionless. An NFT which cannot be sold without "
"paying a royalty is not permissionless, and thus not a digital artifact."
msgstr ""
"Les artefacts numériques sont sans permission. Un NFT qui ne peut être vendu "
"sans avoir à payer des redevances n’est pas sans permission et n’est donc "
"pas un artefact numérique."

#: src\digital-artifacts.md:33
msgid ""
"Digital artifacts are uncensorable. Perhaps you can change a database entry "
"on a centralized ledger today, but maybe not tomorrow, and thus one cannot "
"be a digital artifact."
msgstr ""
"Les artefacts numériques ne sont pas censurables. Il peut être possible de "
"modifier des informations dans une base de données centralisée aujourd’hui, "
"mais ça ne sera peut-être plus possible demain, et il ne peut donc s’agir "
"d’un artefact numérique."

#: src\digital-artifacts.md:37
msgid ""
"Digital artifacts are immutable. An NFT with an upgrade key is not a digital "
"artifact."
msgstr ""
"Les artefacts numériques sont immuables. Un NFT avec une clé de mise à jour "
"n’est pas un artefact numérique."

#: src\digital-artifacts.md:40
msgid ""
"The definition of a digital artifact is intended to reflect what NFTs "
"_should_ be, sometimes are, and what inscriptions _always_ are, by their "
"very nature."
msgstr ""
"La définition d’un artefact numérique vise à refléter ce que les NFTs "
"_devraient_ être, ce qu’ils sont parfois et ce que les inscriptions seront "
"_toujours_, de par leur nature même."

#: src\inscriptions.md:4
msgid ""
"Inscriptions inscribe sats with arbitrary content, creating bitcoin-native "
"digital artifacts, more commonly known as NFTs. Inscriptions do not require "
"a sidechain or separate token."
msgstr ""
"Les inscriptions inscrivent des sats avec un contenu arbitraire, créant "
"ainsi des artefacts numériques natifs de Bitcoin, plus communément appelés "
"NFTs. Les inscriptions ne nécessitent pas de chaîne latérale ou de token "
"séparé."

#: src\inscriptions.md:8
msgid ""
"These inscribed sats can then be transferred using bitcoin transactions, "
"sent to bitcoin addresses, and held in bitcoin UTXOs. These transactions, "
"addresses, and UTXOs are normal bitcoin transactions, addresses, and UTXOS "
"in all respects, with the exception that in order to send individual sats, "
"transactions must control the order and value of inputs and outputs "
"according to ordinal theory."
msgstr ""
"Ces sats inscrits peuvent ensuite être transférés au moyen de transactions "
"Bitcoin, envoyés à des adresses Bitcoin et détenus dans des UTXOs (sorties "
"de transactions non dépensées) Bitcoin. Tous ces processus sont exécutés "
"comme ils le sont normalement dans Bitcoin, à l’exception du fait que, pour "
"envoyer des satoshis individuels, les transactions doivent contrôler l’ordre "
"et la valeur des entrées et des sorties conformément à la théorie ordinale."

#: src\inscriptions.md:15
msgid ""
"The inscription content model is that of the web. An inscription consists of "
"a content type, also known as a MIME type, and the content itself, which is "
"a byte string. This allows inscription content to be returned from a web "
"server, and for creating HTML inscriptions that use and remix the content of "
"other inscriptions."
msgstr ""
"Le modèle de contenu fonctionne de manière similaire à celui du web. Une "
"inscription se compose d’un type de contenu, également connu sous le nom de "
"type MIME, et du contenu lui-même, qui est une chaîne d’octets. Cela permet "
"de récupérer le contenu de l’inscription à partir d’un serveur web et de "
"créer des entrées HTML qui utilisent le contenu d’autres inscriptions."

#: src\inscriptions.md:21
msgid ""
"Inscription content is entirely on-chain, stored in taproot script-path "
"spend scripts. Taproot scripts have very few restrictions on their content, "
"and additionally receive the witness discount, making inscription content "
"storage relatively economical."
msgstr ""
"Le contenu de l’inscription est entièrement sur la blockchain, stocké dans "
"des scripts taproot (taproot script-path spend scripts). Les scripts taproot "
"ont très peu de restrictions sur ce qu’ils peuvent contenir, et ils "
"bénéficient également de la réduction pour témoins, ce qui rend le stockage "
"du contenu de l’inscription relativement peu coûteux."

#: src\inscriptions.md:26
msgid ""
"Since taproot script spends can only be made from existing taproot outputs, "
"inscriptions are made using a two-phase commit/reveal procedure. First, in "
"the commit transaction, a taproot output committing to a script containing "
"the inscription content is created. Second, in the reveal transaction, the "
"output created by the commit transaction is spent, revealing the inscription "
"content on-chain."
msgstr ""
"Étant donné que les dépenses de script taproot (taproot script spends) ne "
"peuvent être effectuées qu’à partir de sorties taproot existantes, les "
"inscriptions sont réalisées à l’aide d’une procédure d’engagement/de "
"révélation en deux phases. Tout d’abord, dans la transaction d’engagement, "
"une sortie de script taproot est créée qui s’engage à un script contenant le "
"contenu de l’inscription. Ensuite, dans la transaction de révélation, la "
"sortie créée par la transaction d’engagement est dépensée, révélant le "
"contenu de l’inscription sur la blockchain."

#: src\inscriptions.md:33
msgid ""
"Inscription content is serialized using data pushes within unexecuted "
"conditionals, called \"envelopes\". Envelopes consist of an `OP_FALSE OP_IF "
"… OP_ENDIF` wrapping any number of data pushes. Because envelopes are "
"effectively no-ops, they do not change the semantics of the script in which "
"they are included, and can be combined with any other locking script."
msgstr ""
"Le contenu de l’inscription est sérialisé à l’aide de push de données dans "
"des structures conditionnelles non exécutées, appelées « enveloppes ». Les "
"enveloppes se composent d’un `OP_FALSE OP_IF … OP_ENDIF` enveloppant un "
"nombre quelconque de push de données. Parce que les enveloppes sont "
"effectivement des opérations nulles, elles ne modifient pas la sémantique du "
"script dans lequel elles sont incluses et peuvent être combinées avec "
"n’importe quel autre script de verrouillage."

#: src\inscriptions.md:39
msgid ""
"A text inscription containing the string \"Hello, world!\" is serialized as "
"follows:"
msgstr ""
"Une inscription textuelle contenant la chaîne « Hello, world! » est "
"sérialisée comme suit :"

#: src\inscriptions.md:42
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src\inscriptions.md:53
msgid ""
"First the string `ord` is pushed, to disambiguate inscriptions from other "
"uses of envelopes."
msgstr ""
"Tout d’abord, un push est fait avec la chaîne `ord`pour distinguer les "
"inscriptions des autres utilisations des enveloppes."

#: src\inscriptions.md:56
msgid ""
"`OP_PUSH 1` indicates that the next push contains the content type, and "
"`OP_PUSH 0`indicates that subsequent data pushes contain the content itself. "
"Multiple data pushes must be used for large inscriptions, as one of "
"taproot's few restrictions is that individual data pushes may not be larger "
"than 520 bytes."
msgstr ""
"`OP_PUSH 1` indique que le prochain push contient le type de contenu, et "
"`OP_PUSH 0` indique que les données suivantes dans le push contiennent le "
"contenu lui-même. Plusieurs pushs de données doivent être utilisés pour les "
"inscriptions volumineuses, car l’une des rares restrictions de taproot est "
"qu’un push de données ne peut pas dépasser 520 octets."

#: src\inscriptions.md:62
msgid ""
"The inscription content is contained within the input of a reveal "
"transaction, and the inscription is made on the first sat of its input. This "
"sat can then be tracked using the familiar rules of ordinal theory, allowing "
"it to be transferred, bought, sold, lost to fees, and recovered."
msgstr ""
"Le contenu de l’inscription est contenu dans l’entrée d’une transaction de "
"révélation, et l’inscription est effectuée sur le premier sat de cette "
"entrée. Ce sat peut ensuite être suivi en utilisant les règles familières de "
"la théorie ordinale, ce qui permet de le transférer, de l’acheter, de le "
"vendre, de le perdre en frais et de le récupérer."

#: src\inscriptions.md:67
msgid "Content"
msgstr "Contenu"

#: src\inscriptions.md:70
msgid ""
"The data model of inscriptions is that of a HTTP response, allowing "
"inscription content to be served by a web server and viewed in a web browser."
msgstr ""
"Le modèle de données pour les inscriptions est celui d’une réponse HTTP, "
"permettant au contenu de l’inscription d’être récupéré via un serveur web et "
"affiché dans un navigateur web."

#: src\inscriptions.md:73
msgid "Fields"
msgstr "Champs"

#: src\inscriptions.md:76
msgid ""
"Inscriptions may include fields before an optional body. Each field consists "
"of two data pushes, a tag and a value."
msgstr ""
"Les entrées peuvent inclure des champs avant un corps optionnel. Chaque "
"champ se compose de deux pushs de données, une étiquette et une valeur."

#: src\inscriptions.md:79
msgid ""
"Currently, the only defined field is `content-type`, with a tag of `1`, "
"whose value is the MIME type of the body."
msgstr ""
"Actuellement, le seul champ défini est `content-type`, avec une étiquette "
"`1`,, dont la valeur est le type MIME du corps."

#: src\inscriptions.md:82
msgid ""
"The beginning of the body and end of fields is indicated with an empty data "
"push."
msgstr ""
"Le début du corps et la fin des champs sont indiqués par un push de données "
"vide."

#: src\inscriptions.md:85
msgid ""
"Unrecognized tags are interpreted differently depending on whether they are "
"even or odd, following the \"it's okay to be odd\" rule used by the "
"Lightning Network."
msgstr ""
"Les étiquettes non reconnues sont interprétées différemment en fonction de "
"leur caractère pair ou impair, conformément à la règle « il est acceptable "
"d’être impair » utilisée par le réseau Lightning."

#: src\inscriptions.md:89
msgid ""
"Even tags are used for fields which may affect creation, initial assignment, "
"or transfer of an inscription. Thus, inscriptions with unrecognized even "
"fields must be displayed as \"unbound\", that is, without a location."
msgstr ""
"Les étiquettes paires sont utilisées pour les champs qui peuvent affecter la "
"création, l’assignation initiale ou le transfert d’une inscription. Par "
"conséquent, les inscriptions avec des champs pairs non reconnus doivent être "
"affichées comme « non liées », c’est-à-dire sans emplacement."

#: src\inscriptions.md:93
msgid ""
"Odd tags are used for fields which do not affect creation, initial "
"assignment, or transfer, such as additional metadata, and thus are safe to "
"ignore."
msgstr ""
"Les étiquettes impaires sont utilisées pour les champs qui n’affectent pas "
"la création, l’attribution initiale ou le transfert, tels que les "
"métadonnées supplémentaires, et peuvent donc être ignorées en toute sécurité."

#: src\inscriptions.md:96
msgid "Inscription IDs"
msgstr "IDs des inscriptions"

#: src\inscriptions.md:99
msgid ""
"The inscriptions are contained within the inputs of a reveal transaction. In "
"order to uniquely identify them they are assigned an ID of the form:"
msgstr ""
"Les inscriptions sont contenues dans les entrées d’une transaction de "
"révélation. Pour les identifier, on leur attribue un identifiant comme celui-"
"ci :"

#: src\inscriptions.md:102
msgid "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"
msgstr "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"

#: src\inscriptions.md:104
msgid ""
"The part in front of the `i` is the transaction ID (`txid`) of the reveal "
"transaction. The number after the `i` defines the index (starting at 0) of "
"new inscriptions being inscribed in the reveal transaction."
msgstr ""
"La partie précédant le `i` est l’identifiant de transaction (`txid`) de la "
"transaction de révélation. Le nombre après le `i` définit l’indice "
"(commençant à 0) des nouvelles inscriptions effectuées dans la transaction "
"de révélation."

#: src\inscriptions.md:108
msgid ""
"Inscriptions can either be located in different inputs, within the same "
"input or a combination of both. In any case the ordering is clear, since a "
"parser would go through the inputs consecutively and look for all "
"inscription `envelopes`."
msgstr ""
"Les inscriptions peuvent se trouver dans des entrées différentes, dans la "
"même entrée ou une combinaison des deux. Dans tous les cas, l’ordre est "
"clair, car un analyseur syntaxique parcourrait les entrées de manière "
"consécutive et rechercherait toutes les `enveloppes` d’inscription."

#: src\inscriptions.md:112
msgid "Input"
msgstr "Entrée"

#: src\inscriptions.md:112
msgid "Inscription Count"
msgstr "Nombre d’inscriptions"

#: src\inscriptions.md:112
msgid "Indices"
msgstr "Indices"

#: src\inscriptions.md:114 src\inscriptions.md:117
msgid "0"
msgstr "0"

#: src\inscriptions.md:114 src\inscriptions.md:116
msgid "2"
msgstr "2"

#: src\inscriptions.md:114
msgid "i0, i1"
msgstr "i0, i1"

#: src\inscriptions.md:115 src\inscriptions.md:118
msgid "1"
msgstr "1"

#: src\inscriptions.md:115
msgid "i2"
msgstr "i2"

#: src\inscriptions.md:116 src\inscriptions.md:117
msgid "3"
msgstr "3"

#: src\inscriptions.md:116
msgid "i3, i4, i5"
msgstr "i3, i4, i5"

#: src\inscriptions.md:118
msgid "4"
msgstr "4"

#: src\inscriptions.md:118
msgid "i6"
msgstr "i6"

#: src\inscriptions.md:120
msgid "Sandboxing"
msgstr "Sandbox"

#: src\inscriptions.md:123
msgid ""
"HTML and SVG inscriptions are sandboxed in order to prevent references to "
"off-chain content, thus keeping inscriptions immutable and self-contained."
msgstr ""
"Les inscriptions HTML et SVG sont placées dans un environnement isolé "
"(sandbox) afin d’empêcher les références à des contenus hors chaîne, ce qui "
"permet de maintenir les inscriptions immuables et autonomes, c’est-à-dire "
"contenues dans l’environnement."

#: src\inscriptions.md:126
msgid ""
"This is accomplished by loading HTML and SVG inscriptions inside `iframes` "
"with the `sandbox` attribute, as well as serving inscription content with "
"`Content-Security-Policy` headers."
msgstr ""
"Pour ce faire, les entrées HTML et SVG sont chargées dans des `iframes` avec "
"l’attribut `sandbox` et la stratégie de sécurité `Content-Security-Policy` "
"est ajoutée aux en-têtes."

#: src\inscriptions/metadata.md:4
msgid ""
"Inscriptions may include [CBOR](https://cbor.io/) metadata, stored as data "
"pushes in fields with tag `5`. Since data pushes are limited to 520 bytes, "
"metadata longer than 520 bytes must be split into multiple tag `5` fields, "
"which will then be concatenated before decoding."
msgstr ""
"Les inscriptions peuvent inclure des métadonnées [CBOR](https://cbor.io/), "
"stockées sous forme de pushs de données dans des champs avec étiquette `5`. "
"Vu que les pushs de données sont limités à 520 octets, les métadonnées de "
"plus de 520 octets doivent être divisées en plusieurs champs avec étiquette "
"`5`, qui seront ensuite concaténés avant le décodage."

#: src\inscriptions/metadata.md:9
msgid ""
"Metadata is human readable, and all metadata will be displayed to the user "
"with its inscription. Inscribers are encouraged to consider how metadata "
"will be displayed, and make metadata concise and attractive."
msgstr ""
"Les métadonnées sont lisibles par l’humain, et toutes les métadonnées seront "
"affichées à l’utilisateur avec son inscription. Les inscripteurs sont "
"encouragés à réfléchir à la manière dont les métadonnées seront affichées et "
"à les rendre concises et attrayantes."

#: src\inscriptions/metadata.md:13
msgid "Metadata is rendered to HTML for display as follows:"
msgstr ""
"Les métadonnées sont converties en HTML pour être affichées de la manière "
"suivante :"

#: src\inscriptions/metadata.md:15
msgid ""
"`null`, `true`, `false`, numbers, floats, and strings are rendered as plain "
"text."
msgstr ""
"`null` (nul), `true` (vrai), `false` (faux), les nombres, les éléments "
"flottants (floats) et les chaînes de caractères (strings) sont rendus en "
"texte brut."

#: src\inscriptions/metadata.md:17
msgid "Byte strings are rendered as uppercase hexadecimal."
msgstr "Les chaînes d’octets sont rendues sous forme hexadécimale majuscule."

#: src\inscriptions/metadata.md:18
msgid ""
"Arrays are rendered as `<ul>` tags, with every element wrapped in `<li>` "
"tags."
msgstr ""
"Les tableaux (arrays) sont rendus en étiquettes `<ul>`, chaque élément étant "
"encadré par des étiquettes `<li>`."

#: src\inscriptions/metadata.md:20
msgid ""
"Maps are rendered as `<dl>` tags, with every key wrapped in `<dt>` tags, and "
"every value wrapped in `<dd>` tags."
msgstr ""
"Les tableaux associatifs (maps) sont rendus sous forme d’étiquettes `<dl>`, "
"chaque clé étant encadrée par des étiquettes `<dt>` et chaque valeur étant "
"encadrée par des étiquettes `<dd>`."

#: src\inscriptions/metadata.md:22
msgid ""
"Tags are rendered as the tag , enclosed in a `<sup>` tag, followed by the "
"value."
msgstr ""
"Les étiquettes sont rendues sous forme de l'étiquette, encadrées par une "
"étiquette `<sup>`, suivie de la valeur."

#: src\inscriptions/metadata.md:25
msgid ""
"CBOR is a complex spec with many different data types, and multiple ways of "
"representing the same data. Exotic data types, such as tags, floats, and "
"bignums, and encoding such as indefinite values, may fail to display "
"correctly or at all. Contributions to `ord` to remedy this are welcome."
msgstr ""
"CBOR est une spécification complexe qui comporte de nombreux types de "
"données différents et de multiples façons de représenter les mêmes données. "
"Les types de données exotiques, tels que les étiquettes, les éléments "
"flottants et les bignums, ainsi que les encodages tels que les valeurs "
"indéfinies, peuvent ne pas s’afficher correctement, voire pas du tout. Les "
"contributions à `ord` visant à remédier à ce problème sont les bienvenues."

#: src\inscriptions/metadata.md:30 src\inscriptions/provenance.md:27
#: src\guides/testing.md:18 src\guides/reindexing.md:15
msgid "Example"
msgstr "Exemple"

#: src\inscriptions/metadata.md:33
msgid ""
"Since CBOR is not human readable, in these examples it is represented as "
"JSON. Keep in mind that this is _only_ for these examples, and JSON metadata "
"will _not_ be displayed correctly."
msgstr ""
"Le CBOR n’étant pas lisible par l’humain, il est représenté sous forme de "
"JSON dans les exemples suivants. Gardez à l’esprit que cela est _uniquement_ "
"pour ces exemples, et que les métadonnées JSON ne seront _pas_ affichées "
"correctement."

#: src\inscriptions/metadata.md:37
msgid ""
"The metadata `{\"foo\":\"bar\",\"baz\":[null,true,false,0]}` would be "
"included in an inscription as:"
msgstr ""
"Les métadonnées `{\"foo\":\"bar\",\"baz\":[null,true,false,0]}` seraient "
"incluses dans une inscription sous la forme suivante :"

#: src\inscriptions/metadata.md:39
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"foo\":\"bar\",\"baz\":[null,true,false,0]}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"foo\":\"bar\",\"baz\":[null,true,false,0]}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"

#: src\inscriptions/metadata.md:48
msgid "And rendered as:"
msgstr "Et rendues comme suit :"

#: src\inscriptions/metadata.md:73
msgid "Metadata longer than 520 bytes must be split into multiple fields:"
msgstr ""
"Les métadonnées de plus de 520 octets doivent être divisées en plusieurs "
"champs :"

#: src\inscriptions/metadata.md:75
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"very\":\"long\",\"metadata\":'\n"
"    OP_PUSH 0x05 OP_PUSH '\"is\",\"finally\":\"done\"}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"very\":\"long\",\"metadata\":'\n"
"    OP_PUSH 0x05 OP_PUSH '\"is\",\"finally\":\"done\"}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"

#: src\inscriptions/metadata.md:85
msgid ""
"Which would then be concatinated into `{\"very\":\"long\",\"metadata\":"
"\"is\",\"finally\":\"done\"}`."
msgstr ""
"Ce qui serait ensuite concaténé en : `{\"very\":\"long\",\"metadata\":\"is\","
"\"finally\":\"done\"}`."

#: src\inscriptions/provenance.md:4
msgid ""
"The owner of an inscription can create child inscriptions, trustlessly "
"establishing the provenance of those children on-chain as having been "
"created by the owner of the parent inscription. This can be used for "
"collections, with the children of a parent inscription being members of the "
"same collection."
msgstr ""
"Le propriétaire d’une inscription peut créer des inscriptions enfants, en "
"établissant d’une manière trustless (sans confiance) la provenance de ces "
"enfants sur la blockchain comme ayant été créés par le propriétaire de "
"l’inscription parent. Cela peut être utilisé pour les collections, de façon "
"à ce que les enfants d’une inscription parent soient membres de la même "
"collection."

#: src\inscriptions/provenance.md:9
msgid ""
"Children can themselves have children, allowing for complex hierarchies. For "
"example, an artist might create an inscription representing themselves, with "
"sub inscriptions representing collections that they create, with the "
"children of those sub inscriptions being items in those collections."
msgstr ""
"Les enfants peuvent avoir des enfants, ce qui permet de créer des "
"hiérarchies complexes. Par exemple, un artiste peut créer une inscription le "
"représentant, avec des sous-inscriptions représentant les collections qu’il "
"a créées, les enfants de ces sous-inscriptions étant des éléments de ces "
"collections."

#: src\inscriptions/provenance.md:14
msgid "Specification"
msgstr "Spécification"

#: src\inscriptions/provenance.md:16
msgid "To create a child inscription C with parent inscription P:"
msgstr "Pour créer une inscription enfant C avec une inscription parent P :"

#: src\inscriptions/provenance.md:18
msgid "Create an inscribe transaction T as usual for C."
msgstr "Créez une transaction d’inscription T comme d’habitude pour C."

#: src\inscriptions/provenance.md:19
msgid "Spend the parent P in one of the inputs of T."
msgstr "Dépensez le parent P dans l’une des entrées de T."

#: src\inscriptions/provenance.md:20
msgid ""
"Include tag `3`, i.e. `OP_PUSH 3`, in C, with the value of the serialized "
"binary inscription ID of P, serialized as the 32-byte `TXID`, followed by "
"the four-byte little-endian `INDEX`, with trailing zeroes omitted."
msgstr ""
"Incluez l’étiquette `3`, c’est-à-dire `OP_PUSH 3`, dans C, avec la valeur de "
"l’identifiant binaire sérialisé de l’inscription P, sérialisé avec le `TXID` "
"de 32 octets, suivi de l’`INDEX` en format petit-boutien de quatre octets, "
"en omettant les zéros de fin."

#: src\inscriptions/provenance.md:24
msgid ""
"_NB_ The bytes of a bitcoin transaction ID are reversed in their text "
"representation, so the serialized transaction ID will be in the opposite "
"order."
msgstr ""
"_N.B._ Les octets de l’identifiant d’une transaction Bitcoin sont inversés "
"dans leur représentation textuelle, de sorte que l’identifiant de la "
"transaction sérialisée sera dans l’ordre inverse."

#: src\inscriptions/provenance.md:29
msgid ""
"An example of a child inscription of "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"
msgstr ""
"Exemple d’une inscription enfant de "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"

#: src\inscriptions/provenance.md:32
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src\inscriptions/provenance.md:45
msgid ""
"Note that the value of tag `3` is binary, not hex, and that for the child "
"inscription to be recognized as a child, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` must be "
"spent as one of the inputs of the inscribe transaction."
msgstr ""
"Notez que la valeur de l’étiquette `3` est binaire, et non hexadécimale, et "
"que pour que l’inscription enfant soit reconnue comme telle, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` doit "
"être dépensé comme l’une des entrées de la transaction d’inscription."

#: src\inscriptions/provenance.md:50
msgid ""
"Example encoding of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"
msgstr ""
"Exemple de codage de l’inscription contenant l’ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"

#: src\inscriptions/provenance.md:63
msgid ""
"And of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"
msgstr ""
"Et de l’inscription contenant l’ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"

#: src\inscriptions/provenance.md:75
msgid "Notes"
msgstr "Notes"

#: src\inscriptions/provenance.md:77
msgid ""
"The tag `3` is used because it is the first available odd tag. Unrecognized "
"odd tags do not make an inscription unbound, so child inscriptions would be "
"recognized and tracked by old versions of `ord`."
msgstr ""
"L’étiquette `3` est utilisée parce que c’est la première étiquette impaire "
"disponible. Les étiquettes impaires inconnues ne dissocient pas les "
"inscriptions, de sorte que les inscriptions enfants seraient reconnues et "
"suivies par les anciennes versions d’`ord`."

#: src\inscriptions/provenance.md:81
msgid ""
"A collection can be closed by burning the collection's parent inscription, "
"which guarantees that no more items in the collection can be issued."
msgstr ""
"Une collection peut être fermée en brûlant l’inscription parent de la "
"collection, ce qui garantit qu’aucune autre pièce ne peut être émise dans la "
"collection."

#: src\inscriptions/recursion.md:4
msgid ""
"An important exception to [sandboxing](../inscriptions.md#sandboxing) is "
"recursion: access to `ord`'s `/content` endpoint is permitted, allowing "
"inscriptions to access the content of other inscriptions by requesting `/"
"content/<INSCRIPTION_ID>`."
msgstr ""
"Une exception importante au [sandboxing](../inscriptions.md#sandboxing) est "
"la récursion : L’accès au point de terminaison (endpoint) `/content` d’`ord` "
"est autorisé, ce qui permet aux inscriptions d’accéder au contenu d’autres "
"inscriptions en demandant `/content/<INSCRIPTION_ID>`."

#: src\inscriptions/recursion.md:9
msgid "This has a number of interesting use-cases:"
msgstr "Il existe plusieurs cas d’utilisation intéressants :"

#: src\inscriptions/recursion.md:11
msgid "Remixing the content of existing inscriptions."
msgstr "Combiner le contenu des inscriptions existantes."

#: src\inscriptions/recursion.md:13
msgid ""
"Publishing snippets of code, images, audio, or stylesheets as shared public "
"resources."
msgstr ""
"Publier des extraits de code, des images, des fichiers audio ou des feuilles "
"de style (stylesheet) en tant que ressources publiques partagées."

#: src\inscriptions/recursion.md:16
msgid ""
"Generative art collections where an algorithm is inscribed as JavaScript, "
"and instantiated from multiple inscriptions with unique seeds."
msgstr ""
"Collections d’art génératif où un algorithme est inscrit en JavaScript, et "
"instancié à partir de multiples inscriptions avec semences uniques."

#: src\inscriptions/recursion.md:19
msgid ""
"Generative profile picture collections where accessories and attributes are "
"inscribed as individual images, or in a shared texture atlas, and then "
"combined, collage-style, in unique combinations in multiple inscriptions."
msgstr ""
"Collections d’images de profil génératives où les accessoires et les "
"attributs sont inscrits en tant qu’images individuelles ou dans un atlas de "
"textures partagé, puis combinés, à la manière d’un collage, dans des "
"combinaisons uniques dans plusieurs inscriptions."

#: src\inscriptions/recursion.md:23
msgid "A few other endpoints that inscriptions may access are the following:"
msgstr ""
"Voici quelques-uns des autres points de terminaison auxquels les "
"inscriptions peuvent accéder :"

#: src\inscriptions/recursion.md:25
msgid "`/blockheight`: latest block height."
msgstr "`/blockheight`: hauteur du bloc le plus récent."

#: src\inscriptions/recursion.md:26
msgid "`/blockhash`: latest block hash."
msgstr "`/blockhash`: hachage du bloc le plus récent."

#: src\inscriptions/recursion.md:27
msgid "`/blockhash/<HEIGHT>`: block hash at given block height."
msgstr "`/blockhash/<HEIGHT>`: hachage du bloc à la hauteur donnée du bloc."

#: src\inscriptions/recursion.md:28
msgid "`/blocktime`: UNIX time stamp of latest block."
msgstr "`/blocktime`: horodatage UNIX du bloc le plus récent."

#: src\faq.md:1
msgid "Ordinal Theory FAQ"
msgstr "FAQ de la théorie ordinale"

#: src\faq.md:4
msgid "What is ordinal theory?"
msgstr "Qu’est-ce que la théorie ordinale ?"

#: src\faq.md:7
msgid ""
"Ordinal theory is a protocol for assigning serial numbers to satoshis, the "
"smallest subdivision of a bitcoin, and tracking those satoshis as they are "
"spent by transactions."
msgstr ""
"La théorie ordinale est un protocole permettant d’attribuer des nombres de "
"série aux satoshis, la plus petite unité de bitcoin, et de suivre ces "
"satoshis au fur et à mesure qu’ils sont dépensés dans des transactions."

#: src\faq.md:11
msgid ""
"These serial numbers are large numbers, like this 804766073970493. Every "
"satoshi, which is ¹⁄₁₀₀₀₀₀₀₀₀ of a bitcoin, has an ordinal number."
msgstr ""
"Ces numéros de série sont de grands nombres, par exemple le nombre "
"804766073970493. Chaque satoshi, qui est ¹⁄₁₀₀₀₀₀₀₀₀ d’un bitcoin, a un "
"numéro ordinal."

#: src\faq.md:14
msgid ""
"Does ordinal theory require a side chain, a separate token, or changes to "
"Bitcoin?"
msgstr ""
"La théorie ordinale nécessite-t-elle une chaîne latérale, un token séparé ou "
"des modifications sur Bitcoin ?"

#: src\faq.md:17
msgid ""
"Nope! Ordinal theory works right now, without a side chain, and the only "
"token needed is bitcoin itself."
msgstr ""
"Non ! La théorie ordinale fonctionne dès maintenant, sans chaîne latérale, "
"et le seul token nécessaire est tout simplement le bitcoin."

#: src\faq.md:20
msgid "What is ordinal theory good for?"
msgstr "À quoi sert la théorie ordinale ?"

#: src\faq.md:23
msgid ""
"Collecting, trading, and scheming. Ordinal theory assigns identities to "
"individual satoshis, allowing them to be individually tracked and traded, as "
"curios and for numismatic value."
msgstr ""
"À collectionner, échanger et innover. La théorie ordinale attribue une "
"identité aux satoshis, ce qui permet de les suivre et de les échanger, à la "
"fois comme curiosités et pour leur valeur numismatique."

#: src\faq.md:27
msgid ""
"Ordinal theory also enables inscriptions, a protocol for attaching arbitrary "
"content to individual satoshis, turning them into bitcoin-native digital "
"artifacts."
msgstr ""
"La théorie ordinale permet également les inscriptions, un protocole capable "
"d’attacher du contenu arbitraire à des satoshis individuels, les "
"transformant ainsi en artefacts numériques natifs de Bitcoin."

#: src\faq.md:31
msgid "How does ordinal theory work?"
msgstr "Comment fonctionne la théorie ordinale ?"

#: src\faq.md:34
msgid ""
"Ordinal numbers are assigned to satoshis in the order in which they are "
"mined. The first satoshi in the first block has ordinal number 0, the second "
"has ordinal number 1, and the last satoshi of the first block has ordinal "
"number 4,999,999,999."
msgstr ""
"Les nombres ordinaux sont attribués aux satoshis dans l’ordre dans lequel "
"ils sont minés. Le premier satoshi du premier bloc a le nombre ordinal 0, le "
"deuxième a le nombre ordinal 1 et le dernier satoshi du premier bloc a le "
"nombre ordinal 4 999 999 999."

#: src\faq.md:39
msgid ""
"Satoshis live in outputs, but transactions destroy outputs and create new "
"ones, so ordinal theory uses an algorithm to determine how satoshis hop from "
"the inputs of a transaction to its outputs."
msgstr ""
"Les satoshis se trouvent dans les sorties de transactions, mais les "
"transactions sont détruites lorsqu’elles sont émises et de nouvelles "
"transactions sont créées. La théorie ordinale utilise donc un algorithme "
"pour déterminer comment les satoshis se déplacent entre les sorties de "
"transactions et les entrées de transactions."

#: src\faq.md:43
msgid "Fortunately, that algorithm is very simple."
msgstr "Heureusement, cet algorithme est très simple."

#: src\faq.md:45
msgid ""
"Satoshis transfer in first-in-first-out order. Think of the inputs to a "
"transaction as being a list of satoshis, and the outputs as a list of slots, "
"waiting to receive a satoshi. To assign input satoshis to slots, go through "
"each satoshi in the inputs in order, and assign each to the first available "
"slot in the outputs."
msgstr ""
"Les satoshis sont transférés selon le principe FIFO (First In, First Out). "
"Considérez les entrées d’une transaction comme une liste de satoshis, et les "
"sorties comme une liste d’emplacements, en attente de recevoir un satoshi. "
"Pour attribuer les satoshis des entrées aux emplacements disponibles, "
"parcourez-les dans l’ordre et attribuez chaque satoshi au premier "
"emplacement disponible dans les sorties."

#: src\faq.md:51
msgid ""
"Let's imagine a transaction with three inputs and two outputs. The inputs "
"are on the left of the arrow and the outputs are on the right, all labeled "
"with their values:"
msgstr ""
"Imaginons une transaction avec trois entrées et deux sorties. Les entrées "
"sont à gauche de la flèche et les sorties sont à droite, avec leurs valeurs "
"respectives :"

#: src\faq.md:57
msgid ""
"Now let's label the same transaction with the ordinal numbers of the "
"satoshis that each input contains, and question marks for each output slot. "
"Ordinal numbers are large, so let's use letters to represent them:"
msgstr ""
"Voyons maintenant la même opération avec les nombres ordinaux de satoshis "
"contenus dans chaque entrée. Nous mettrons des points d’interrogation pour "
"chaque espace de sortie libre. Comme les nombres ordinaux sont grands, nous "
"utiliserons des lettres pour les représenter :"

#: src\faq.md:63
msgid ""
"To figure out which satoshi goes to which output, go through the input "
"satoshis in order and assign each to a question mark:"
msgstr ""
"Pour déterminer quel satoshi sera placé dans quelle sortie, parcourez les "
"satoshis d’entrée dans l’ordre et attribuez un point d’interrogation à "
"chacun d’eux :"

#: src\faq.md:68
msgid ""
"What about fees, you might ask? Good question! Let's imagine the same "
"transaction, this time with a two satoshi fee. Transactions with fees send "
"more satoshis in the inputs than are received by the outputs, so to make our "
"transaction into one that pays fees, we'll remove the second output:"
msgstr ""
"À ce stade, vous vous demandez peut-être ce qu’il adviendra des frais... "
"Bonne question ! Imaginons la même transaction, cette fois avec des frais de "
"deux satoshis. Les transactions avec frais contiennent plus de satoshis dans "
"les entrées que les sorties de transactions n’en reçoivent, donc pour "
"convertir notre transaction en une transaction avec frais, nous allons "
"supprimer la deuxième sortie de transaction :"

#: src\faq.md:75
msgid "The satoshis "
msgstr "Les satoshis "

#: src\faq.md:75
msgid "e"
msgstr "e"

#: src\faq.md:75
msgid " and "
msgstr " et "

#: src\faq.md:75
msgid "f"
msgstr "f"

#: src\faq.md:75
msgid " now have nowhere to go in the outputs:"
msgstr " n’ont désormais nulle part où aller dans les sorties :"

#: src\faq.md:80
msgid ""
"So they go to the miner who mined the block as fees. [The BIP](https://"
"github.com/ordinals/ord/blob/master/bip.mediawiki) has the details, but in "
"short, fees paid by transactions are treated as extra inputs to the coinbase "
"transaction, and are ordered how their corresponding transactions are "
"ordered in the block. The coinbase transaction of the block might look like "
"this:"
msgstr ""
"Ils vont donc au mineur qui a miné le bloc en tant que frais. [Le BIP]"
"(https://github.com/ordinals/ord/blob/master/bip.mediawiki) donne les "
"détails, mais en résumé, les frais payés par les transactions sont traités "
"comme des entrées supplémentaires dans la transaction coinbase, et sont "
"organisés selon l’ordre de leurs transactions correspondantes dans le bloc. "
"La transaction coinbase du bloc pourrait ressembler à ceci :"

#: src\faq.md:89
msgid "Where can I find the nitty-gritty details?"
msgstr "Où puis-je trouver les détails techniques approfondis ?"

#: src\faq.md:92
msgid "[The BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr ""
"Dans [le BIP !](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src\faq.md:94
msgid ""
"Why are sat inscriptions called \"digital artifacts\" instead of \"NFTs\"?"
msgstr ""
"Pourquoi les inscriptions de satoshis sont-elles appelées « artefacts "
"numériques » au lieu de « NFTs » ?"

#: src\faq.md:97
msgid ""
"An inscription is an NFT, but the term \"digital artifact\" is used instead, "
"because it's simple, suggestive, and familiar."
msgstr ""
"Une inscription est un NFT, mais le terme « artefact numérique » est utilisé "
"à la place, parce qu’il est simple, suggestif et familier."

#: src\faq.md:100
msgid ""
"The phrase \"digital artifact\" is highly suggestive, even to someone who "
"has never heard the term before. In comparison, NFT is an acronym, and "
"doesn't provide any indication of what it means if you haven't heard the "
"term before."
msgstr ""
"L’expression « artefact numérique » est très suggestive, même pour quelqu’un "
"qui n’a jamais entendu ce terme auparavant. En comparaison, le terme « NFT » "
"est un acronyme qui ne donne aucune indication sur ce qu’il signifie si l’on "
"n’a jamais entendu ce terme auparavant."

#: src\faq.md:104
msgid ""
"Additionally, \"NFT\" feels like financial terminology, and the both word "
"\"fungible\" and sense of the word \"token\" as used in \"NFT\" is uncommon "
"outside of financial contexts."
msgstr ""
"En outre, « NFT » ressemble à une terminologie financière, et le mot « "
"fongible » ainsi que le sens du mot « token » tel qu’il est utilisé dans « "
"NFT » ne sont pas courants en dehors des contextes financiers."

#: src\faq.md:108
msgid "How do sat inscriptions compare to…"
msgstr "Comment les inscriptions de satoshis se comparent-elles…"

#: src\faq.md:111
msgid "Ethereum NFTs?"
msgstr "Aux NFTs sur Ethereum ?"

#: src\faq.md:113
msgid "_Inscriptions are always immutable._"
msgstr "_Les inscriptions sont toujours immuables._"

#: src\faq.md:115
msgid ""
"There is simply no way to for the creator of an inscription, or the owner of "
"an inscription, to modify it after it has been created."
msgstr ""
"Il n’y a tout simplement aucun moyen pour le créateur d’une inscription, ou "
"le propriétaire d’une inscription, de la modifier après qu’elle a été créée."

#: src\faq.md:118
msgid ""
"Ethereum NFTs _can_ be immutable, but many are not, and can be changed or "
"deleted by the NFT contract owner."
msgstr ""
"Les NFTs sur Ethereum peuvent être _immuables_, mais beaucoup ne le sont pas "
"et peuvent être modifiés ou supprimés par le propriétaire du contrat NFT."

#: src\faq.md:121
msgid ""
"In order to make sure that a particular Ethereum NFT is immutable, the "
"contract code must be audited, which requires detailed knowledge of the EVM "
"and Solidity semantics."
msgstr ""
"Pour s’assurer qu’un NFT sur Ethereum est immuable, le code du contrat doit "
"être analysé, ce qui nécessite une connaissance approfondie de l’EVM et de "
"la sémantique Solidity."

#: src\faq.md:125
msgid ""
"It is very hard for a non-technical user to determine whether or not a given "
"Ethereum NFT is mutable or immutable, and Ethereum NFT platforms make no "
"effort to distinguish whether an NFT is mutable or immutable, and whether "
"the contract source code is available and has been audited."
msgstr ""
"Pour un utilisateur ne disposant pas de compétences techniques, il est assez "
"difficile de déterminer si un NFT sur Ethereum est mutable ou immuable, et "
"les plateformes de NFTs sur Ethereum ne font aucun effort pour distinguer si "
"un NFT est mutable ou immuable et si le code source du contrat est "
"disponible et a été vérifié."

#: src\faq.md:130
msgid "_Inscription content is always on-chain._"
msgstr ""
"_Le contenu de l’inscription se trouve toujours sur la blockchain du Bitcoin."
"_"

#: src\faq.md:132
msgid ""
"There is no way for an inscription to refer to off-chain content. This makes "
"inscriptions more durable, because content cannot be lost, and scarcer, "
"because inscription creators must pay fees proportional to the size of the "
"content."
msgstr ""
"Il est impossible pour une inscription de se référer à un contenu qui se "
"trouve en dehors de la blockchain du Bitcoin. Cela rend les inscriptions "
"plus durables, car le contenu ne peut pas être perdu, mais aussi plus rares, "
"car les créateurs d’inscriptions doivent payer des commissions "
"proportionnelles à la taille du contenu."

#: src\faq.md:136
msgid ""
"Some Ethereum NFT content is on-chain, but much is off-chain, and is stored "
"on platforms like IPFS or Arweave, or on traditional, fully centralized web "
"servers. Content on IPFS is not guaranteed to continue to be available, and "
"some NFT content stored on IPFS has already been lost. Platforms like "
"Arweave rely on weak economic assumptions, and will likely fail "
"catastrophically when these economic assumptions are no longer met. "
"Centralized web servers may disappear at any time."
msgstr ""
"Le contenu de certains NFTs sur Ethereum est sur la blockchain, mais "
"beaucoup ne l’est pas et est stocké sur des plateformes telles que IPFS ou "
"Arweave, ou sur des serveurs web centralisés. Il n’y a aucune garantie que "
"le contenu stocké sur IPFS continuera d’être disponible ; en fait, quelques "
"contenus de NFTs qui étaient stockés sur IPFS ont déjà été perdus. Les "
"plateformes telles qu’Arweave reposent sur des hypothèses économiques et "
"sont susceptibles d’échouer de manière catastrophique lorsque ces hypothèses "
"ne seront plus satisfaites. Les serveurs web centralisés pourraient "
"disparaître à tout moment."

#: src\faq.md:144
msgid ""
"It is very hard for a non-technical user to determine where the content of a "
"given Ethereum NFT is stored."
msgstr ""
"Il est très difficile pour un utilisateur non technique de déterminer où est "
"stocké le contenu d’un NFT sur Ethereum."

#: src\faq.md:147
msgid "_Inscriptions are much simpler._"
msgstr "_Les inscriptions sont beaucoup plus simples._"

#: src\faq.md:149
msgid ""
"Ethereum NFTs depend on the Ethereum network and virtual machine, which are "
"highly complex, constantly changing, and which introduce changes via "
"backwards-incompatible hard forks."
msgstr ""
"Les NFTs sur Ethereum dépendent du réseau et de la machine virtuelle "
"Ethereum, qui sont très complexes, évoluent constamment et introduisent des "
"changements par le biais de hard forks incompatibles avec des versions "
"précédentes."

#: src\faq.md:153
msgid ""
"Inscriptions, on the other hand, depend on the Bitcoin blockchain, which is "
"relatively simple and conservative, and which introduces changes via "
"backwards-compatible soft forks."
msgstr ""
"D’autre part, les inscriptions dépendent de la blockchain du Bitcoin, qui "
"est relativement simple et conservatrice, et qui introduit des changements "
"par le biais de « soft forks » compatibles avec des versions précédentes."

#: src\faq.md:157
msgid "_Inscriptions are more secure._"
msgstr "_Les inscriptions sont plus sûres._"

#: src\faq.md:159
msgid ""
"Inscriptions inherit Bitcoin's transaction model, which allow a user to see "
"exactly which inscriptions are being transferred by a transaction before "
"they sign it. Inscriptions can be offered for sale using partially signed "
"transactions, which don't require allowing a third party, such as an "
"exchange or marketplace, to transfer them on the user's behalf."
msgstr ""
"Les inscriptions héritent du modèle de transaction de Bitcoin, ce qui permet "
"à l’utilisateur de voir exactement quelles inscriptions sont transférées "
"dans une transaction avant de la signer. Les inscriptions peuvent être mises "
"en vente par le biais de transactions partiellement signées, ce qui ne "
"nécessite pas l’autorisation d’un tiers, tel qu’une place de marché ou une "
"plateforme d’échange, pour les transférer au nom de l’utilisateur."

#: src\faq.md:165
msgid ""
"By comparison, Ethereum NFTs are plagued with end-user security "
"vulnerabilities. It is commonplace to blind-sign transactions, grant third-"
"party apps unlimited permissions over a user's NFTs, and interact with "
"complex and unpredictable smart contracts. This creates a minefield of "
"hazards for Ethereum NFT users which are simply not a concern for ordinal "
"theorists."
msgstr ""
"En comparaison, les NFTs sur Ethereum sont criblés de vulnérabilités en "
"matière de sécurité pour l’utilisateur final. Il est courant de signer des "
"transactions à l’aveugle, d’accorder des autorisations illimitées à des "
"applications tierces sur les NFTs d’un utilisateur et d’interagir avec des "
"smart contracts complexes et imprévisibles. Cela crée un champ de mines "
"plein de dangers pour les utilisateurs de NFTs sur Ethereum qui ne sont tout "
"simplement pas une préoccupation pour les théoriciens d’Ordinals."

#: src\faq.md:171
msgid "_Inscriptions are scarcer._"
msgstr "_Les inscriptions sont plus rares._"

#: src\faq.md:173
msgid ""
"Inscriptions require bitcoin to mint, transfer, and store. This seems like a "
"downside on the surface, but the raison d'etre of digital artifacts is to be "
"scarce and thus valuable."
msgstr ""
"Les inscriptions nécessitent des bitcoins pour être créées, transférées et "
"stockées. Cela semble être un inconvénient à première vue, mais la raison "
"d’être des artefacts numériques est d’être rares et donc précieux."

#: src\faq.md:177
msgid ""
"Ethereum NFTs, on the other hand, can be minted in virtually unlimited "
"qualities with a single transaction, making them inherently less scarce, and "
"thus, potentially less valuable."
msgstr ""
"Les NFTs sur Ethereum, en revanche, peuvent être créés en quantités "
"virtuellement illimitées en une seule transaction, ce qui les rend "
"intrinsèquement moins rares, et donc potentiellement moins précieux."

#: src\faq.md:181
msgid "_Inscriptions do not pretend to support on-chain royalties._"
msgstr ""
"_Les inscriptions ne prétendent pas prendre en charge les redevances sur la "
"blockchain._"

#: src\faq.md:183
msgid ""
"On-chain royalties are a good idea in theory but not in practice. Royalty "
"payment cannot be enforced on-chain without complex and invasive "
"restrictions. The Ethereum NFT ecosystem is currently grappling with "
"confusion around royalties, and is collectively coming to grips with the "
"reality that on-chain royalties, which were messaged to artists as an "
"advantage of NFTs, are not possible, while platforms race to the bottom and "
"remove royalty support."
msgstr ""
"En théorie, la mise en œuvre des redevances dans le cadre de la blockchain "
"semble être une bonne chose, mais sa mise en pratique pose des problèmes "
"importants. Les paiements de redevances ne peuvent pas être implémentés sur "
"la blockchain sans restrictions complexes et intrusives. À l’heure actuelle, "
"l’écosystème des NFTs sur Ethereum est confronté à des problèmes dus à la "
"confusion générée autour des redevances et réalise collectivement que les "
"redevances sur la blockchain, qui ont été présentées aux artistes comme un "
"avantage des NFTs, ne sont pas possibles, tandis que les plateformes sont "
"entraînées dans une course vers le bas et éliminent déjà la prise en charge "
"des redevances."

#: src\faq.md:190
msgid ""
"Inscriptions avoid this situation entirely by making no false promises of "
"supporting royalties on-chain, thus avoiding the confusion, chaos, and "
"negativity of the Ethereum NFT situation."
msgstr ""
"Les inscriptions évitent totalement cette situation en ne faisant aucune "
"fausse promesse de prise en charge des redevances sur la blockchain, évitant "
"ainsi la confusion, le chaos et la négativité de la situation qui survient "
"avec les NFTs sur Ethereum."

#: src\faq.md:194
msgid "_Inscriptions unlock new markets._"
msgstr "_Les inscriptions ouvrent la voie à de nouveaux marchés._"

#: src\faq.md:196
msgid ""
"Bitcoin's market capitalization and liquidity are greater than Ethereum's by "
"a large margin. Much of this liquidity is not available to Ethereum NFTs, "
"since many Bitcoiners prefer not to interact with the Ethereum ecosystem due "
"to concerns related to simplicity, security, and decentralization."
msgstr ""
"La capitalisation du marché de Bitcoin et sa liquidité sont nettement "
"supérieures à celles d’Ethereum. Une grande partie de cette liquidité n’est "
"pas accessible aux NFTs sur Ethereum, car de nombreux bitcoiners préfèrent "
"ne pas interagir avec l’écosystème Ethereum en raison de préoccupations "
"liées à la simplicité, la sécurité et la décentralisation."

#: src\faq.md:201
msgid ""
"Such Bitcoiners may be more interested in inscriptions than Ethereum NFTs, "
"unlocking new classes of collector."
msgstr ""
"Ces bitcoiners pourraient être plus intéressés par les inscriptions que par "
"les NFTs Ethereum, ce qui ouvrirait la porte à d’autres types de "
"collectionneurs."

#: src\faq.md:204
msgid "_Inscriptions have a richer data model._"
msgstr "_Les inscriptions disposent d’un modèle de données plus riche._"

#: src\faq.md:206
msgid ""
"Inscriptions consist of a content type, also known as a MIME type, and "
"content, which is an arbitrary byte string. This is the same data model used "
"by the web, and allows inscription content to evolve with the web, and come "
"to support any kind of content supported by web browsers, without requiring "
"changes to the underlying protocol."
msgstr ""
"Une entrée se compose du type de contenu, appelé type MIME, et d’une chaîne "
"d’octets arbitraire constituant le contenu. Il s’agit du même modèle de "
"données que celui utilisé par le web, qui permet au contenu de l’inscription "
"d’évoluer avec le web et de prendre en charge tout type de contenu supporté "
"par les navigateurs web, sans qu’il soit nécessaire de modifier le protocole "
"sous-jacent."

#: src\faq.md:212
msgid "RGB and Taro assets?"
msgstr "Aux actifs RGB et Taro ?"

#: src\faq.md:214
msgid ""
"RGB and Taro are both second-layer asset protocols built on Bitcoin. "
"Compared to inscriptions, they are much more complicated, but much more "
"featureful."
msgstr ""
"RGB et Taro sont tous deux des protocoles d’actifs de deuxième couche basés "
"sur Bitcoin. Comparés aux inscriptions, ils sont beaucoup plus compliqués, "
"mais offrent beaucoup plus de fonctionnalités."

#: src\faq.md:217
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas the primary use-case of RGB and Taro are fungible tokens, so the "
"user experience for inscriptions is likely to be simpler and more polished "
"than the user experience for RGB and Taro NFTs."
msgstr ""
"La théorie ordinale a été conçue dès le départ pour les artefacts "
"numériques, tandis que RGB et Taro se concentrent sur les jetons fongibles, "
"de sorte que l’expérience utilisateur des inscriptions est susceptible "
"d’être plus simple et plus raffinée que celle des NFTs de RGB et Taro."

#: src\faq.md:222
msgid ""
"RGB and Taro both store content off-chain, which requires additional "
"infrastructure, and which may be lost. By contrast, inscription content is "
"stored on-chain, and cannot be lost."
msgstr ""
"RGB et Taro stockent le contenu en dehors de la blockchain, ce qui nécessite "
"une infrastructure supplémentaire qui pourrait être perdue. En revanche, le "
"contenu des inscriptions est stocké sur la blockchain et ne peut pas être "
"perdu."

#: src\faq.md:226
msgid ""
"Ordinal theory, RGB, and Taro are all very early, so this is speculation, "
"but ordinal theory's focus may give it the edge in terms of features for "
"digital artifacts, including a better content model, and features like "
"globally unique symbols."
msgstr ""
"La théorie ordinale, RGB et Taro n’en sont qu’à leurs débuts, il ne s’agit "
"donc que de spéculations, mais l’approche de la théorie ordinale pourrait "
"lui donner un avantage en termes de fonctionnalité pour les artefacts "
"numériques, notamment un meilleur modèle de contenu et des fonctionnalités "
"telles que des symboles globalement uniques."

#: src\faq.md:231
msgid "Counterparty assets?"
msgstr "Aux actifs de Counterparty ?"

#: src\faq.md:233
msgid ""
"Counterparty has its own token, XCP, which is required for some "
"functionality, which makes most bitcoiners regard it as an altcoin, and not "
"an extension or second layer for bitcoin."
msgstr ""
"Counterparty possède son propre token, XCP, qui est nécessaire pour "
"certaines fonctionnalités, ce qui fait que la plupart des bitcoiners le "
"considèrent comme un altcoin, plutôt que comme une extension ou une seconde "
"couche de Bitcoin."

#: src\faq.md:237
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas Counterparty was primarily designed for financial token issuance."
msgstr ""
"La théorie ordinale a été conçue dès le départ pour les artefacts "
"numériques, alors que Counterparty a été principalement conçue pour "
"l’émission de tokens financiers."

#: src\faq.md:240
msgid "Inscriptions for…"
msgstr "Les inscriptions pour…"

#: src\faq.md:243
msgid "Artists"
msgstr "Les artistes"

#: src\faq.md:245
msgid ""
"_Inscriptions are on Bitcoin._ Bitcoin is the digital currency with the "
"highest status and greatest chance of long-term survival. If you want to "
"guarantee that your art survives into the future, there is no better way to "
"publish it than as inscriptions."
msgstr ""
"_Les inscriptions sont sur Bitcoin._ Le bitcoin est la monnaie numérique la "
"plus prestigieuse et la plus susceptible de survivre à long terme. Si vous "
"souhaitez garantir que votre art perdure dans le futur, il n’y a pas de "
"meilleur moyen de le publier que sous forme d’inscriptions."

#: src\faq.md:250
msgid ""
"_Cheaper on-chain storage._ At $20,000 per BTC and the minimum relay fee of "
"1 satoshi per vbyte, publishing inscription content costs $50 per 1 million "
"bytes."
msgstr ""
"_Le stockage sur la blockchain est moins coûteux._ Avec 20 000 dollars par "
"BTC et des frais de transaction minimum de 1 satoshi par vbyte, publier du "
"contenu d’inscriptions coûte 50 dollars pour 1 million d’octets."

#: src\faq.md:254
msgid ""
"_Inscriptions are early!_ Inscriptions are still in development, and have "
"not yet launched on mainnet. This gives you an opportunity to be an early "
"adopter, and explore the medium as it evolves."
msgstr ""
"_Les inscriptions n’en sont qu’à leurs débuts !_ Les inscriptions sont "
"encore en cours de développement et n’ont pas encore été lancées sur le "
"réseau principal. Cela vous donne l’occasion d’être un adopteur précoce et "
"d’explorer ce medium à mesure qu’il évolue."

#: src\faq.md:258
msgid ""
"_Inscriptions are simple._ Inscriptions do not require writing or "
"understanding smart contracts."
msgstr ""
"_Les inscriptions sont simples._ Il n’est pas nécessaire d’écrire ou de "
"comprendre les smart contracts."

#: src\faq.md:261
msgid ""
"_Inscriptions unlock new liquidity._ Inscriptions are more accessible and "
"appealing to bitcoin holders, unlocking an entirely new class of collector."
msgstr ""
"_Les inscriptions ouvrent la porte à de nouvelles sources de liquidités._ "
"Les inscriptions sont plus accessibles et plus attrayantes pour les "
"détenteurs de bitcoins, ce qui ouvre la porte à une toute nouvelle catégorie "
"de collectionneurs."

#: src\faq.md:264
msgid ""
"_Inscriptions are designed for digital artifacts._ Inscriptions are designed "
"from the ground up to support NFTs, and feature a better data model, and "
"features like globally unique symbols and enhanced provenance."
msgstr ""
"_Les inscriptions sont conçues pour les artefacts numériques._ Les "
"inscriptions sont conçues dès le départ pour prendre en charge les NFTs, et "
"présentent un meilleur modèle de données, avec des caractéristiques telles "
"que des symboles globalement uniques et une provenance améliorée."

#: src\faq.md:268
msgid ""
"_Inscriptions do not support on-chain royalties._ This is negative, but only "
"depending on how you look at it. On-chain royalties have been a boon for "
"creators, but have also created a huge amount of confusion in the Ethereum "
"NFT ecosystem. The ecosystem now grapples with this issue, and is engaged in "
"a race to the bottom, towards a royalties-optional future. Inscriptions have "
"no support for on-chain royalties, because they are technically infeasible. "
"If you choose to create inscriptions, there are many ways you can work "
"around this limitation: withhold a portion of your inscriptions for future "
"sale, to benefit from future appreciation, or perhaps offer perks for users "
"who respect optional royalties."
msgstr ""
"_Les inscriptions ne prennent pas en charge les redevances sur la blockchain."
"_ Cela peut être considéré comme un point négatif, mais cela dépend du point "
"de vue de chacun. Bien que les redevances sur la blockchain aient grandement "
"bénéficié aux créateurs, elles ont également généré une grande confusion "
"dans l’écosystème des NFTs sur Ethereum. À l’heure actuelle, l’écosystème "
"est aux prises avec ce problème et s’est engagé dans une course vers le bas, "
"vers un avenir où les redevances seront facultatives. Les inscriptions ne "
"prennent pas en charge les redevances sur la blockchain, car elles sont "
"techniquement irréalisables. Si vous décidez de créer des inscriptions, vous "
"pouvez contourner cette limitation de plusieurs manières : en retenant une "
"partie de vos inscriptions pour les vendre ultérieurement, afin de "
"bénéficier d’une appréciation future, ou en offrant peut-être des avantages "
"aux utilisateurs qui respectent les redevances facultatives."

#: src\faq.md:279
msgid "Collectors"
msgstr "Les collectionneurs"

#: src\faq.md:281
msgid ""
"_Inscriptions are simple, clear, and have no surprises._ They are always "
"immutable and on-chain, with no special due diligence required."
msgstr ""
"_Les inscriptions sont simples, claires et ne réservent aucune surprise._ "
"Elles sont toujours immuables et sur la blockchain Bitcoin, sans qu’aucune "
"diligence particulière ne soit requise."

#: src\faq.md:284
msgid ""
"_Inscriptions are on Bitcoin._ You can verify the location and properties of "
"inscriptions easily with Bitcoin full node that you control."
msgstr ""
"_Les inscriptions sont sur Bitcoin._ Vous pouvez facilement vérifier "
"l’emplacement et les propriétés des inscriptions à l’aide d’un nœud complet "
"(full node) Bitcoin que vous contrôlez."

#: src\faq.md:287
msgid "Bitcoiners"
msgstr "Les bitcoiners"

#: src\faq.md:289
msgid ""
"Let me begin this section by saying: the most important thing that the "
"Bitcoin network does is decentralize money. All other use-cases are "
"secondary, including ordinal theory. The developers of ordinal theory "
"understand and acknowledge this, and believe that ordinal theory helps, at "
"least in a small way, Bitcoin's primary mission."
msgstr ""
"Permettez-moi de commencer cette section en disant que la fonction "
"principale du réseau Bitcoin est la décentralisation de l’argent. Toutes les "
"autres utilisations sont secondaires, y compris la théorie ordinale. Les "
"développeurs de la théorie ordinale le comprennent très bien et considèrent "
"que leur travail contribue, ne serait-ce qu’un peu, à la mission première de "
"Bitcoin."

#: src\faq.md:295
msgid ""
"Unlike many other things in the altcoin space, digital artifacts have merit. "
"There are, of course, a great deal of NFTs that are ugly, stupid, and "
"fraudulent. However, there are many that are fantastically creative, and "
"creating and collecting art has been a part of the human story since its "
"inception, and predates even trade and money, which are also ancient "
"technologies."
msgstr ""
"Contrairement à beaucoup d’autres choses dans l’espace altcoin, les "
"artefacts numériques ont du mérite. Il est vrai que beaucoup de NFTs sont "
"laids, stupides et frauduleux. Cependant, il y en a aussi beaucoup qui se "
"distinguent par leur incroyable créativité. La création et la collection "
"d’œuvres d’art font partie de l’histoire de l’humanité depuis ses débuts, "
"précédant même le commerce et l’argent, qui sont également des technologies "
"anciennes."

#: src\faq.md:302
msgid ""
"Bitcoin provides an amazing platform for creating and collecting digital "
"artifacts in a secure, decentralized way, that protects users and artists in "
"the same way that it provides an amazing platform for sending and receiving "
"value, and for all the same reasons."
msgstr ""
"Bitcoin fournit une plateforme extraordinaire pour créer et collectionner "
"des artefacts numériques de manière sécurisée et décentralisée, protégeant à "
"la fois les utilisateurs et les artistes, et fournissant une plateforme "
"extraordinaire pour transmettre et recevoir de la valeur."

#: src\faq.md:307
msgid ""
"Ordinals and inscriptions increase demand for Bitcoin block space, which "
"increase Bitcoin's security budget, which is vital for safeguarding "
"Bitcoin's transition to a fee-dependent security model, as the block subsidy "
"is halved into insignificance."
msgstr ""
"Les ordinals et les inscriptions augmentent la demande d’espace de bloc "
"Bitcoin, ce qui accroît le budget de sécurité de Bitcoin – ce qui est vital "
"pour préserver la transition de Bitcoin vers un modèle de sécurité dépendant "
"des frais, car la subvention de bloc est réduite de moitié au point de "
"devenir insignifiante."

#: src\faq.md:312
msgid ""
"Inscription content is stored on-chain, and thus the demand for block space "
"for use in inscriptions is unlimited. This creates a buyer of last resort "
"for _all_ Bitcoin block space. This will help support a robust fee market, "
"which ensures that Bitcoin remains secure."
msgstr ""
"Le contenu des inscriptions est stocké sur la blockchain et, de ce fait, la "
"demande d’espace de bloc pour les inscriptions est donc illimitée. Cela crée "
"un acheteur de dernier recours pour _tout_ l’espace de bloc Bitcoin, "
"contribuant à soutenir un marché de frais robuste, ce qui aide à préserver "
"la sécurité de Bitcoin."

#: src\faq.md:317
msgid ""
"Inscriptions also counter the narrative that Bitcoin cannot be extended or "
"used for new use-cases. If you follow projects like DLCs, Fedimint, "
"Lightning, Taro, and RGB, you know that this narrative is false, but "
"inscriptions provide a counter argument which is easy to understand, and "
"which targets a popular and proven use case, NFTs, which makes it highly "
"legible."
msgstr ""
"Les inscriptions vont également à l’encontre de l’idée selon laquelle il "
"n’est pas possible de développer ou d’utiliser Bitcoin pour de nouveaux cas "
"d’utilisation. Si vous suivez des projets comme DLC, Fedimint, Lightning, "
"Taro et RGB, vous savez que ce discours est faux, mais les inscriptions "
"fournissent un contre-argument qui est facile à comprendre et qui cible un "
"cas d’utilisation populaire et éprouvé, les NFTs, ce qui le rend très "
"intelligible."

#: src\faq.md:323
msgid ""
"If inscriptions prove, as the authors hope, to be highly sought after "
"digital artifacts with a rich history, they will serve as a powerful hook "
"for Bitcoin adoption: come for the fun, rich art, stay for the decentralized "
"digital money."
msgstr ""
"Si les inscriptions s’avèrent, comme l’espèrent les auteurs, être des "
"artefacts numériques très recherchés et dotés d’une riche histoire, elles "
"serviront d’accroche puissante pour l’adoption de Bitcoin : venez pour l’art "
"amusant et riche, restez pour la monnaie numérique décentralisée."

#: src\faq.md:327
msgid ""
"Inscriptions are an extremely benign source of demand for block space. "
"Unlike, for example, stablecoins, which potentially give large stablecoin "
"issuers influence over the future of Bitcoin development, or DeFi, which "
"might centralize mining by introducing opportunities for MEV, digital art "
"and collectables on Bitcoin, are unlikely to produce individual entities "
"with enough power to corrupt Bitcoin. Art is decentralized."
msgstr ""
"Les inscriptions sont une source extrêmement bénigne de demande d’espace de "
"bloc. Contrairement, par exemple, aux stablecoins, qui peuvent donner aux "
"émetteurs de stablecoins une grande influence sur le développement futur de "
"Bitcoin, ou au DeFi, qui pourrait centraliser le minage en introduisant des "
"opportunités de MEV, il est peu probable que l’art numérique et les objets "
"de collection sur Bitcoin produisent des entités individuelles ayant "
"suffisamment de pouvoir pour corrompre Bitcoin. L’art est décentralisé."

#: src\faq.md:334
msgid ""
"Inscription users and service providers are incentivized to run Bitcoin full "
"nodes, to publish and track inscriptions, and thus throw their economic "
"weight behind the honest chain."
msgstr ""
"Les utilisateurs et les fournisseurs de services d’inscriptions sont incités "
"à exploiter des nœuds complets Bitcoin, à publier et à suivre les "
"inscriptions, contribuant ainsi à renforcer la blockchain légitime grâce à "
"leur poids économique."

#: src\faq.md:338
msgid ""
"Ordinal theory and inscriptions do not meaningfully affect Bitcoin's "
"fungibility. Bitcoin users can ignore both and be unaffected."
msgstr ""
"La théorie ordinale et les inscriptions n’affectent pas la fongibilité de "
"Bitcoin de manière significative. Les utilisateurs de Bitcoin peuvent "
"ignorer les deux et ne pas être affectés."

#: src\faq.md:341
msgid ""
"We hope that ordinal theory strengthens and enriches bitcoin, and gives it "
"another dimension of appeal and functionality, enabling it more effectively "
"serve its primary use case as humanity's decentralized store of value."
msgstr ""
"Nous espérons que la théorie ordinale renforcera et enrichira Bitcoin, et "
"lui donnera une autre dimension d’attrait et de fonctionnalité, lui "
"permettant de mieux servir son cas d’utilisation principal en tant que "
"réserve de valeur décentralisée de l’humanité."

#: src\contributing.md:1
msgid "Contributing to `ord`"
msgstr "Contribuer à `ord`"

#: src\contributing.md:4
msgid "Suggested Steps"
msgstr "Étapes suggérées"

#: src\contributing.md:7
msgid "Find an issue you want to work on."
msgstr "Trouvez un problème sur lequel vous voulez travailler."

#: src\contributing.md:8
msgid ""
"Figure out what would be a good first step towards resolving the issue. This "
"could be in the form of code, research, a proposal, or suggesting that it be "
"closed, if it's out of date or not a good idea in the first place."
msgstr ""
"Déterminez quelle pourrait être la première mesure à prendre pour résoudre "
"le problème. Cela pourrait se faire sous la forme d’un code, d’une "
"recherche, d’une proposition, ou en suggérant qu’il soit clos, si le "
"problème est obsolète ou s’il n’était pas une bonne idée dès le départ."

#: src\contributing.md:11
msgid ""
"Comment on the issue with an outline of your suggested first step, and "
"asking for feedback. Of course, you can dive in and start writing code or "
"tests immediately, but this avoids potentially wasted effort, if the issue "
"is out of date, not clearly specified, blocked on something else, or "
"otherwise not ready to implement."
msgstr ""
"Commentez le problème en décrivant les grandes lignes de la première étape "
"que vous suggérez et demandez un retour d’information. Bien sûr, vous "
"pourriez vous lancer immédiatement dans l’écriture du code ou de tests, mais "
"cela vous évitera de gaspiller des efforts si le problème n’est plus "
"d’actualité, s’il n’est pas clairement spécifié, s’il est bloqué ou s’il "
"n’est pas prêt à être implémenté."

#: src\contributing.md:16
msgid ""
"If the issue requires a code change or bugfix, open a draft PR with tests, "
"and ask for feedback. This makes sure that everyone is on the same page "
"about what needs to be done, or what the first step in solving the issue "
"should be. Also, since tests are required, writing the tests first makes it "
"easy to confirm that the change can be tested easily."
msgstr ""
"Si le problème nécessite une modification du code ou la correction d’un "
"bogue, ouvrez un brouillon de demande de tirage (pull request) avec des "
"tests et demandez des suggestions. Cela permet de s’assurer que tout le "
"monde est d’accord sur ce qui doit être fait, ou sur la première étape de la "
"résolution du problème. De plus, puisque des tests sont nécessaires, le fait "
"de les écrire en premier permet de confirmer que le changement peut être "
"testé facilement."

#: src\contributing.md:21
msgid ""
"Mash the keyboard randomly until the tests pass, and refactor until the code "
"is ready to submit."
msgstr ""
"Tapez de façon aléatoire sur le clavier jusqu’à ce que les tests passent, et "
"réusinez le code jusqu’à ce qu’il soit prêt à être soumis."

#: src\contributing.md:23
msgid "Mark the PR as ready to review."
msgstr "Marquez la demande de tirage comme prête à être révisée."

#: src\contributing.md:24
msgid "Revise the PR as needed."
msgstr "Révisez la demande de tirage si nécessaire."

#: src\contributing.md:25
msgid "And finally, mergies!"
msgstr "Et finalement, fusionnez-la !"

#: src\contributing.md:27
msgid "Start small"
msgstr "Commencez modestement"

#: src\contributing.md:30
msgid ""
"Small changes will allow you to make an impact quickly, and if you take the "
"wrong tack, you won't have wasted much time."
msgstr ""
"Des petits changements vous permettront d’avoir un impact rapide, et si vous "
"faites fausse route, vous n’aurez pas perdu beaucoup de temps."

#: src\contributing.md:33
msgid "Ideas for small issues:"
msgstr "Voici quelques idées pour des problèmes mineurs :"

#: src\contributing.md:34
msgid "Add a new test or test case that increases test coverage"
msgstr ""
"Ajoutez un nouveau test ou un cas de test qui augmente la couverture des "
"tests"

#: src\contributing.md:35
msgid "Add or improve documentation"
msgstr "Ajoutez ou améliorez la documentation"

#: src\contributing.md:36
msgid ""
"Find an issue that needs more research, and do that research and summarize "
"it in a comment"
msgstr ""
"Trouvez un problème nécessitant davantage de recherche, effectuez cette "
"recherche et résumez-la dans un commentaire"

#: src\contributing.md:38
msgid "Find an out-of-date issue and comment that it can be closed"
msgstr ""
"Trouvez un problème obsolète et indiquez qu’il peut être clos dans un "
"commentaire"

#: src\contributing.md:39
msgid ""
"Find an issue that shouldn't be done, and provide constructive feedback "
"detailing why you think that is the case"
msgstr ""
"Identifiez un problème qui ne devrait pas être traité, et fournissez des "
"commentaires constructifs expliquant pourquoi vous pensez que c’est le cas"

#: src\contributing.md:42
msgid "Merge early and often"
msgstr "Fusionnez tôt et souvent"

#: src\contributing.md:45
msgid ""
"Break up large tasks into multiple smaller steps that individually make "
"progress. If there's a bug, you can open a PR that adds a failing ignored "
"test. This can be merged, and the next step can be to fix the bug and "
"unignore the test. Do research or testing, and report on your results. Break "
"a feature into small sub-features, and implement them one at a time."
msgstr ""
"Divisez les tâches importantes en plusieurs étapes plus petites, qui "
"progressent individuellement. En cas de bogue, vous pouvez ouvrir une "
"demande de tirage qui ajoute un test ignoré défaillant. Cela peut être "
"fusionné, et l’étape suivante peut être de corriger le bogue et de "
"désactiver (unignore) l’option ignorant le test. Effectuez des recherches ou "
"des tests et faites un rapport sur vos résultats. Divisez une fonctionnalité "
"en de petites sous-fonctionnalités et implémentez-les une par une."

#: src\contributing.md:51
msgid ""
"Figuring out how to break down a larger PR into smaller PRs where each can "
"be merged is an art form well-worth practicing. The hard part is that each "
"PR must itself be an improvement."
msgstr ""
"Trouver un moyen de réduire la taille d’une demande de tirage volumineuse en "
"demandes de tirages plus petites, de façon à ce que chacune puisse être "
"fusionnée individuellement, est un art qui vaut la peine d’être pratiqué. Le "
"défi consiste à s’assurer que chaque demande de tirage représente une "
"amélioration en soi."

#: src\contributing.md:55
msgid ""
"I strive to follow this advice myself, and am always better off when I do."
msgstr ""
"Je m’efforce de suivre ce conseil moi-même et je m’en sors toujours mieux "
"quand je le fais."

#: src\contributing.md:57
msgid ""
"Small changes are fast to write, review, and merge, which is much more fun "
"than laboring over a single giant PR that takes forever to write, review, "
"and merge. Small changes don't take much time, so if you need to stop "
"working on a small change, you won't have wasted much time as compared to a "
"larger change that represents many hours of work. Getting a PR in quickly "
"improves the project a little bit immediately, instead of having to wait a "
"long time for larger improvement. Small changes are less likely to "
"accumulate merge conflict. As the Athenians said: _The fast commit what they "
"will, the slow merge what they must._"
msgstr ""
"Les petites modifications sont rapides à rédiger, à réviser et à fusionner, "
"ce qui est beaucoup plus amusant que de travailler sur une seule demande de "
"tirage géante qui prend une éternité à rédiger, à réviser et à fusionner. "
"Les petites modifications ne prennent pas beaucoup de temps, donc si vous "
"devez arrêter de travailler sur une petite modification, vous n’aurez pas "
"perdu beaucoup de temps par rapport à une modification plus importante qui "
"exige de nombreuses heures de travail. La soumission rapide d’une demande de "
"tirage améliore un peu le projet, mais de façon immédiate, au lieu de devoir "
"attendre longtemps pour obtenir une amélioration plus importante. Les "
"petites modifications sont moins susceptibles d’entraîner des conflits de "
"fusion. Comme le disaient les Athéniens : _Les plus rapides commettent ce "
"qu’ils veulent, tandis que les plus lents sont contraints de fusionner ce "
"qui est possible._"

#: src\contributing.md:67
msgid "Get help"
msgstr "Sollicitez de l’aide"

#: src\contributing.md:70
msgid ""
"If you're stuck for more than 15 minutes, ask for help, like a Rust Discord, "
"Stack Exchange, or in a project issue or discussion."
msgstr ""
"Si vous êtes bloqué pendant plus de 15 minutes, demandez de l’aide dans des "
"espaces tels que le Discord de Rust, sur Stack Exchange, ou dans le cadre "
"d’une question ou d’une discussion au sein du projet."

#: src\contributing.md:73
msgid "Practice hypothesis-driven debugging"
msgstr "Pratiquez le débogage par hypothèse"

#: src\contributing.md:76
msgid ""
"Formulate a hypothesis as to what is causing the problem. Figure out how to "
"test that hypothesis. Perform that tests. If it works, great, you fixed the "
"issue or now you know how to fix the issue. If not, repeat with a new "
"hypothesis."
msgstr ""
"Formulez une hypothèse sur la cause du problème. Trouvez comment vérifier "
"cette hypothèse et effectuez les tests appropriés. Si cela fonctionne, "
"parfait, vous avez résolu le problème, ou du moins vous savez maintenant "
"comment le faire. Dans le cas contraire, recommencez en formulant une "
"nouvelle hypothèse."

#: src\contributing.md:81
msgid "Pay attention to error messages"
msgstr "Prêtez attention aux messages d’erreur"

#: src\contributing.md:84
msgid "Read all error messages and don't tolerate warnings."
msgstr "Lisez tous les messages d’erreur et ne tolérez pas les avertissements."

#: src\donate.md:4
msgid ""
"Ordinals is open-source and community funded. The current lead maintainer of "
"`ord` is [raphjaph](https://github.com/raphjaph/). Raph's work on `ord` is "
"entirely funded by donations. If you can, please consider donating!"
msgstr ""
"Ordinals est un projet open-source financé par la communauté. Le responsable "
"actuel d’`ord` est [raphjaph](https://github.com/raphjaph/). Le travail de "
"Raph sur `ord` est entièrement financé par des dons. Si vous le pouvez, "
"pensez à faire un don !"

#: src\donate.md:8
msgid ""
"The donation address for Bitcoin is "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************). The "
"donation address for inscriptions is "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************)."
msgstr ""
"L’adresse de donation pour Bitcoin est "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************). L’adresse "
"de donation pour les inscriptions est "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************)."

#: src\donate.md:11
msgid ""
"Both addresses are in a 2 of 4 multisig wallet with keys held by [raphjaph]"
"(https://twitter.com/raphjaph), [erin](https://twitter.com/realizingerin), "
"[rodarmor](https://twitter.com/rodarmor), and [ordinally](https://twitter."
"com/veryordinally)."
msgstr ""
"Les deux adresses sont dans un portefeuille multisig 2-sur-4 dont les clés "
"sont détenues par [raphjaph](https://twitter.com/raphjaph), [erin](https://"
"twitter.com/realizingerin), [rodarmor](https://twitter.com/rodarmor), et "
"[ordinally](https://twitter.com/veryordinally)."

#: src\donate.md:17
msgid ""
"Donations received will go towards funding maintenance and development of "
"`ord`, as well as hosting costs for [ordinals.com](https://ordinals.com)."
msgstr ""
"Les dons reçus serviront à financer la maintenance et le développement "
"d’`ord`, ainsi que les coûts d’hébergement de [ordinals.com](https://"
"ordinals.com)."

#: src\donate.md:20
msgid "Thank you for donating!"
msgstr "Merci de faire un don !"

#: src\guides.md:1
msgid "Ordinal Theory Guides"
msgstr "Guides sur la théorie ordinale"

#: src\guides.md:4
msgid ""
"See the table of contents for a list of guides, including a guide to the "
"explorer, a guide for sat hunters, and a guide to inscriptions."
msgstr ""
"Consultez la table des matières pour une liste de guides, y compris un guide "
"de l’explorateur, un guide pour les chasseurs de sats et un guide sur les "
"inscriptions."

#: src\guides/explorer.md:1
msgid "Ordinal Explorer"
msgstr "Explorateur Ordinals"

#: src\guides/explorer.md:4
msgid ""
"The `ord` binary includes a block explorer. We host a instance of the block "
"explorer on mainnet at [ordinals.com](https://ordinals.com), and on signet "
"at [signet.ordinals.com](https://signet.ordinals.com)."
msgstr ""
"Le binaire `ord` comprend un explorateur de blocs. Nous hébergeons une "
"instance de l’explorateur de blocs sur mainnet à l’adresse [ordinals.com]"
"(https://ordinals.com),, et sur signet à l’adresse [signet.ordinals.com]"
"(https://signet.ordinals.com)."

#: src\guides/explorer.md:8
msgid "Running The Explorer"
msgstr "Exécuter l'explorateur"

#: src\guides/explorer.md:9
msgid "The server can be run locally with:"
msgstr "Le serveur peut être exécuté localement avec :"

#: src\guides/explorer.md:11
msgid "`ord server`"
msgstr "`ord server`"

#: src\guides/explorer.md:13
msgid "To specify a port add the `--http-port` flag:"
msgstr "Pour spécifier un port, ajoutez le drapeau `--http-port` :"

#: src\guides/explorer.md:15
msgid "`ord server --http-port 8080`"
msgstr "`ord server --http-port 8080`"

#: src\guides/explorer.md:17
msgid ""
"To enable the JSON-API endpoints add the `--enable-json-api` or `-j` flag "
"(see [here](#json-api) for more info):"
msgstr ""
"Pour activer les points de terminaisons JSON-API, ajoutez le drapeau `--"
"enable-json-api` ou `-j` (voir [ici](#json-api) pour plus d’informations) :"

#: src\guides/explorer.md:20
msgid "`ord --enable-json-api server`"
msgstr "`ord --enable-json-api server`"

#: src\guides/explorer.md:22
msgid "To test how your inscriptions will look you can run:"
msgstr ""
"Pour tester à quoi ressembleraient vos inscriptions, vous pouvez lancer :"

#: src\guides/explorer.md:24
msgid "`ord preview <FILE1> <FILE2> ...`"
msgstr "`ord preview <FILE1> <FILE2> ...`"

#: src\guides/explorer.md:26
msgid "Search"
msgstr "Recherche"

#: src\guides/explorer.md:29
msgid "The search box accepts a variety of object representations."
msgstr "La zone de recherche accepte une variété de représentations d’objets."

#: src\guides/explorer.md:31
msgid "Blocks"
msgstr "Blocs"

#: src\guides/explorer.md:33
msgid "Blocks can be searched by hash, for example, the genesis block:"
msgstr ""
"Les blocs peuvent être recherchés par hash, par exemple, le bloc genesis :"

#: src\guides/explorer.md:35
msgid ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://"
"ordinals.com/"
"search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"
msgstr ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://"
"ordinals.com/"
"search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"

#: src\guides/explorer.md:37
msgid "Transactions"
msgstr "Transactions"

#: src\guides/explorer.md:39
msgid ""
"Transactions can be searched by hash, for example, the genesis block "
"coinbase transaction:"
msgstr ""
"Les transactions peuvent être recherchées par hash, par exemple, la "
"transaction coinbase du bloc genesis :"

#: src\guides/explorer.md:42
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"

#: src\guides/explorer.md:44
msgid "Outputs"
msgstr "Sorties"

#: src\guides/explorer.md:46
msgid ""
"Transaction outputs can searched by outpoint, for example, the only output "
"of the genesis block coinbase transaction:"
msgstr ""
"Les sorties de transaction peuvent être recherchées par outpoint, par "
"exemple, la seule sortie de la transaction coinbase du bloc genesis :"

#: src\guides/explorer.md:49
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"

#: src\guides/explorer.md:51
msgid "Sats"
msgstr "Sats"

#: src\guides/explorer.md:53
msgid ""
"Sats can be searched by integer, their position within the entire bitcoin "
"supply:"
msgstr ""
"Les sats peuvent être recherchés par nombre entier, qui représente leur "
"position dans l’ensemble de l’offre de bitcoins :"

#: src\guides/explorer.md:56
msgid "[2099994106992659](https://ordinals.com/search/2099994106992659)"
msgstr "[2099994106992659](https://ordinals.com/search/2099994106992659)"

#: src\guides/explorer.md:58
msgid "By decimal, their block and offset within that block:"
msgstr "Par nombre décimal, leur bloc et leur déplacement dans ce bloc :"

#: src\guides/explorer.md:60
msgid "[481824.0](https://ordinals.com/search/481824.0)"
msgstr "[481824.0](https://ordinals.com/search/481824.0)"

#: src\guides/explorer.md:62
msgid ""
"By degree, their cycle, blocks since the last halving, blocks since the last "
"difficulty adjustment, and offset within their block:"
msgstr ""
"Par degré sexagésimal, leur cycle, blocs depuis le dernier halving, blocs "
"depuis le dernier ajustement de la difficulté, et déplacement dans leur "
"bloc :"

#: src\guides/explorer.md:65
msgid "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"
msgstr "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"

#: src\guides/explorer.md:67
msgid ""
"By name, their base 26 representation using the letters \"a\" through \"z\":"
msgstr ""
"Par leur nom, leur représentation à base 26 à l’aide des lettres de « a » à "
"« z » :"

#: src\guides/explorer.md:69
msgid "[ahistorical](https://ordinals.com/search/ahistorical)"
msgstr "[ahistorical](https://ordinals.com/search/ahistorical)"

#: src\guides/explorer.md:71
msgid ""
"Or by percentile, the percentage of bitcoin's supply that has been or will "
"have been issued when they are mined:"
msgstr ""
"Ou par percentile, le pourcentage de l’offre de bitcoins qui a été ou sera "
"émis une fois qu’ils auront été minés :"

#: src\guides/explorer.md:74
msgid "[100%](https://ordinals.com/search/100%)"
msgstr "[100%](https://ordinals.com/search/100%)"

#: src\guides/explorer.md:76
msgid "JSON-API"
msgstr "JSON-API"

#: src\guides/explorer.md:79
msgid ""
"You can run `ord` with the `--enable-json-api` flag to access endpoints that "
"return JSON instead of HTML if you set the HTTP `Accept: application/json` "
"header. The structure of theses objects closely follows what is shown in the "
"HTML. These endpoints are:"
msgstr ""
"Vous pouvez exécuter `ord` avec le drapeau `--enable-json-api` pour accéder "
"à des points de terminaison qui renvoient des réponses JSON au lieu de HTML "
"si vous définissez l’en-tête HTTP `Accept: application/json`. La structure "
"de ces objets suit de près ce qui est montré dans HTML. Ces points de "
"terminaison sont les suivants :"

#: src\guides/explorer.md:84
msgid "`/inscription/<INSCRIPTION_ID>`"
msgstr "`/inscription/<INSCRIPTION_ID>`"

#: src\guides/explorer.md:85
msgid "`/inscriptions`"
msgstr "`/inscriptions`"

#: src\guides/explorer.md:86
msgid "`/inscriptions/block/<BLOCK_HEIGHT>`"
msgstr "`/inscriptions/block/<BLOCK_HEIGHT>`"

#: src\guides/explorer.md:87
msgid "`/inscriptions/block/<BLOCK_HEIGHT>/<PAGE_INDEX>`"
msgstr "`/inscriptions/block/<BLOCK_HEIGHT>/<PAGE_INDEX>`"

#: src\guides/explorer.md:88
msgid "`/inscriptions/<FROM>`"
msgstr "`/inscriptions/<FROM>`"

#: src\guides/explorer.md:89
msgid "`/inscriptions/<FROM>/<N>`"
msgstr "`/inscriptions/<FROM>/<N>`"

#: src\guides/explorer.md:90 src\guides/explorer.md:91
msgid "`/output/<OUTPOINT>`"
msgstr "`/output/<OUTPOINT>`"

#: src\guides/explorer.md:92
msgid "`/sat/<SAT>`"
msgstr "`/sat/<SAT>`"

#: src\guides/explorer.md:94
msgid "To get a list of the latest 100 inscriptions you would do:"
msgstr ""
"Pour obtenir une liste des 100 dernières inscriptions, il faut procéder "
"comme suit :"

#: src\guides/explorer.md:96
msgid ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/inscriptions'\n"
"```"
msgstr ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/inscriptions'\n"
"```"

#: src\guides/explorer.md:100
msgid ""
"To see information about a UTXO, which includes inscriptions inside it, do:"
msgstr ""
"Pour obtenir des informations sur un UTXO qui contient des inscriptions, "
"exécutez :"

#: src\guides/explorer.md:102
msgid ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/output/"
"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed:0'\n"
"```"
msgstr ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/output/"
"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed:0'\n"
"```"

#: src\guides/explorer.md:106
msgid "Which returns:"
msgstr "Ce qui renvoie :"

#: src\guides/explorer.md:108
msgid ""
"```\n"
"{\n"
"  \"value\": 10000,\n"
"  \"script_pubkey\": \"OP_PUSHNUM_1 OP_PUSHBYTES_32 "
"156cc4878306157720607cdcb4b32afa4cc6853868458d7258b907112e5a434b\",\n"
"  \"address\": "
"\"bc1pz4kvfpurqc2hwgrq0nwtfve2lfxvdpfcdpzc6ujchyr3ztj6gd9sfr6ayf\",\n"
"  \"transaction\": "
"\"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed\",\n"
"  \"sat_ranges\": null,\n"
"  \"inscriptions\": [\n"
"    \"6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0\"\n"
"  ]\n"
"}\n"
"```"
msgstr ""
"```\n"
"{\n"
"  \"value\": 10000,\n"
"  \"script_pubkey\": \"OP_PUSHNUM_1 OP_PUSHBYTES_32 "
"156cc4878306157720607cdcb4b32afa4cc6853868458d7258b907112e5a434b\",\n"
"  \"address\": "
"\"bc1pz4kvfpurqc2hwgrq0nwtfve2lfxvdpfcdpzc6ujchyr3ztj6gd9sfr6ayf\",\n"
"  \"transaction\": "
"\"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed\",\n"
"  \"sat_ranges\": null,\n"
"  \"inscriptions\": [\n"
"    \"6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0\"\n"
"  ]\n"
"}\n"
"```"

#: src\guides/inscriptions.md:1
msgid "Ordinal Inscription Guide"
msgstr "Guide d’inscription ordinale"

#: src\guides/inscriptions.md:4
msgid ""
"Individual sats can be inscribed with arbitrary content, creating Bitcoin-"
"native digital artifacts that can be held in a Bitcoin wallet and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"Chaque sat peut être inscrit avec un contenu arbitraire, créant ainsi des "
"artefacts numériques natifs de Bitcoin qui peuvent être stockés dans un "
"portefeuille Bitcoin et transférés à l’aide de transactions Bitcoin. Les "
"inscriptions sont aussi durables, immuables, sûres et décentralisées que "
"Bitcoin lui-même."

#: src\guides/inscriptions.md:9
msgid ""
"Working with inscriptions requires a Bitcoin full node, to give you a view "
"of the current state of the Bitcoin blockchain, and a wallet that can create "
"inscriptions and perform sat control when constructing transactions to send "
"inscriptions to another wallet."
msgstr ""
"Travailler avec des inscriptions nécessite un nœud complet Bitcoin, pour "
"vous donner une vue d’ensemble de l’état actuel de la blockchain Bitcoin, et "
"un portefeuille qui peut créer des inscriptions et effectuer un contrôle de "
"sats lors de la préparation de transactions visant à envoyer des "
"inscriptions à un autre portefeuille."

#: src\guides/inscriptions.md:14
msgid ""
"Bitcoin Core provides both a Bitcoin full node and wallet. However, the "
"Bitcoin Core wallet cannot create inscriptions and does not perform sat "
"control."
msgstr ""
"Bitcoin Core fournit à la fois un nœud complet et un portefeuille Bitcoin. "
"Le portefeuille Bitcoin Core ne peut cependant pas créer d’inscriptions et "
"n’effectue pas de contrôle de sats."

#: src\guides/inscriptions.md:17
msgid ""
"This requires [`ord`](https://github.com/ordinals/ord), the ordinal utility. "
"`ord` doesn't implement its own wallet, so `ord wallet` subcommands interact "
"with Bitcoin Core wallets."
msgstr ""
"Pour cela, il faut recourir à [`ord`](https://github.com/ordinals/ord), "
"l’utilitaire ordinal. `ord` n’implémente pas son propre portefeuille, donc "
"les sous-commandes du portefeuille `ord wallet` interagissent avec les "
"portefeuilles Bitcoin Core."

#: src\guides/inscriptions.md:21
msgid "This guide covers:"
msgstr "Ce guide couvre :"

#: src\guides/inscriptions.md:23 src\guides/inscriptions.md:39
msgid "Installing Bitcoin Core"
msgstr "L’installation de Bitcoin Core"

#: src\guides/inscriptions.md:24
msgid "Syncing the Bitcoin blockchain"
msgstr "La synchronisation de la blockchain Bitcoin"

#: src\guides/inscriptions.md:25
msgid "Creating a Bitcoin Core wallet"
msgstr "La création d’un portefeuille Bitcoin Core"

#: src\guides/inscriptions.md:26
msgid "Using `ord wallet receive` to receive sats"
msgstr "L’utilisation de `ord wallet receive` pour recevoir des sats"

#: src\guides/inscriptions.md:27
msgid "Creating inscriptions with `ord wallet inscribe`"
msgstr "La création d’inscriptions à l’aide de `ord wallet inscribe`"

#: src\guides/inscriptions.md:28
msgid "Sending inscriptions with `ord wallet send`"
msgstr "L’envoi d’inscriptions à l’aide de `ord wallet send`"

#: src\guides/inscriptions.md:29
msgid "Receiving inscriptions with `ord wallet receive`"
msgstr "La réception d’inscriptions avec `ord wallet receive`"

#: src\guides/inscriptions.md:31
msgid "Getting Help"
msgstr "Obtenir de l’aide"

#: src\guides/inscriptions.md:34
msgid ""
"If you get stuck, try asking for help on the [Ordinals Discord Server]"
"(https://discord.com/invite/87cjuz4FYg), or checking GitHub for relevant "
"[issues](https://github.com/ordinals/ord/issues) and [discussions](https://"
"github.com/ordinals/ord/discussions)."
msgstr ""
"Si vous êtes coincé, essayez de demander de l’aide sur le [Serveur Discord "
"d’Ordinals](https://discord.com/invite/87cjuz4FYg), ou consultez GitHub pour "
"des [discussions](https://github.com/ordinals/ord/discussions) et des "
"[problèmes](https://github.com/ordinals/ord/issues) pertinents."

#: src\guides/inscriptions.md:42
msgid ""
"Bitcoin Core is available from [bitcoincore.org](https://bitcoincore.org/) "
"on the [download page](https://bitcoincore.org/en/download/)."
msgstr ""
"Bitcoin Core est disponible sur la [page de téléchargement](https://"
"bitcoincore.org/en/download/) de [bitcoincore.org](https://bitcoincore.org/)."

#: src\guides/inscriptions.md:45
msgid "Making inscriptions requires Bitcoin Core 24 or newer."
msgstr ""
"Créer des inscriptions requiert Bitcoin Core 24 ou une version plus récente."

#: src\guides/inscriptions.md:47
msgid ""
"This guide does not cover installing Bitcoin Core in detail. Once Bitcoin "
"Core is installed, you should be able to run `bitcoind -version` "
"successfully from the command line. Do _NOT_ use `bitcoin-qt`."
msgstr ""
"Ce guide ne couvre pas l’installation de Bitcoin Core en détail. Une fois "
"Bitcoin Core installé, vous devriez être en mesure d’exécuter `bitcoind -"
"version` avec succès depuis la ligne de commande. N’utilisez _PAS_ `bitcoin-"
"qt`."

#: src\guides/inscriptions.md:51
msgid "Configuring Bitcoin Core"
msgstr "Configurer Bitcoin Core"

#: src\guides/inscriptions.md:54
msgid "`ord` requires Bitcoin Core's transaction index and rest interface."
msgstr ""
"`ord` nécessite l’index de transactions et l’interface rest de Bitcoin Core."

#: src\guides/inscriptions.md:56
msgid ""
"To configure your Bitcoin Core node to maintain a transaction index, add the "
"following to your `bitcoin.conf`:"
msgstr ""
"Pour configurer votre nœud Bitcoin Core afin qu’il maintienne un index des "
"transactions, ajoutez ce qui suit à votre `bitcoin.conf`:"

#: src\guides/inscriptions.md:63
msgid "Or, run `bitcoind` with `-txindex`:"
msgstr "Ou bien, exécutez `bitcoind` avec `-txindex`:"

#: src\guides/inscriptions.md:69
msgid ""
"Details on creating or modifying your `bitcoin.conf` file can be found [here]"
"(https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md)."
msgstr ""
"Vous pouvez trouver des détails sur la création ou la modification de votre "
"fichier `bitcoin.conf` [ici](https://github.com/bitcoin/bitcoin/blob/master/"
"doc/bitcoin-conf.md)."

#: src\guides/inscriptions.md:72
msgid "Syncing the Bitcoin Blockchain"
msgstr "La synchronisation de la blockchain Bitcoin"

#: src\guides/inscriptions.md:75
msgid "To sync the chain, run:"
msgstr "Pour synchroniser la blockchain, exécutez :"

#: src\guides/inscriptions.md:81
msgid "…and leave it running until `getblockcount`:"
msgstr "...et laissez-la s’exécuter jusqu’à ce que `getblockcount`:"

#: src\guides/inscriptions.md:87
msgid ""
"agrees with the block count on a block explorer like [the mempool.space "
"block explorer](https://mempool.space/). `ord` interacts with `bitcoind`, so "
"you should leave `bitcoind` running in the background when you're using "
"`ord`."
msgstr ""
"corresponde au nombre de blocs sur un [explorateur de blocs tel que mempool."
"space](https://mempool.space/). `ord` interagit avec `bitcoind`, il faut "
"donc laisser `bitcoind` s’exécuter en arrière-plan lorsque vous utilisez "
"`ord`."

#: src\guides/inscriptions.md:91
msgid ""
"The blockchain takes about 600GB of disk space. If you have an external "
"drive you want to store blocks on, use the configuration option "
"`blocksdir=<external_drive_path>`. This is much simpler than using the "
"`datadir` option because the cookie file will still be in the default "
"location for `bitcoin-cli` and `ord` to find."
msgstr ""
"La blockchain occupe environ 600 Go d’espace disque. Si vous avez un disque "
"externe sur lequel vous souhaitez stocker des blocs, utilisez l’option de "
"configuration `blocksdir=<external_drive_path>`. C’est beaucoup plus simple "
"que d’utiliser l’option `datadir` car le fichier cookie sera toujours dans "
"l’emplacement par défaut afin que `bitcoin-cli` et `ord` puissent le trouver."

#: src\guides/inscriptions.md:97 src\guides/collecting/sparrow-wallet.md:173
msgid "Troubleshooting"
msgstr "Résolution des problèmes"

#: src\guides/inscriptions.md:100
msgid ""
"Make sure you can access `bitcoind` with `bitcoin-cli -getinfo` and that it "
"is fully synced."
msgstr ""
"Assurez-vous de pouvoir accéder à `bitcoind` avec `bitcoin-cli -getinfo` et "
"qu’il est entièrement synchronisé."

#: src\guides/inscriptions.md:103
msgid ""
"If `bitcoin-cli -getinfo` returns `Could not connect to the server`, "
"`bitcoind` is not running."
msgstr ""
"Si `bitcoin-cli -getinfo` renvoie `Could not connect to the server` (n’a pas "
"pu se connecter au serveur), `bitcoind` ne s’exécute pas."

#: src\guides/inscriptions.md:106
msgid ""
"Make sure `rpcuser`, `rpcpassword`, or `rpcauth` are _NOT_ set in your "
"`bitcoin.conf` file. `ord` requires using cookie authentication. Make sure "
"there is a file `.cookie` in your bitcoin data directory."
msgstr ""
"Assurez-vous que `rpcuser`, `rpcpassword` ou `rpcauth` ne sont _PAS_ "
"configurés dans votre fichier `bitcoin.conf`. `ord` nécessite l’utilisation "
"de l’authentification à l’aide de cookies. Assurez-vous qu’il y a un fichier "
"`.cookie` dans votre répertoire de données Bitcoin."

#: src\guides/inscriptions.md:110
msgid ""
"If `bitcoin-cli -getinfo` returns `Could not locate RPC credentials`, then "
"you must specify the cookie file location. If you are using a custom data "
"directory (specifying the `datadir` option), then you must specify the "
"cookie location like `bitcoin-cli -rpccookiefile=<your_bitcoin_datadir>/."
"cookie -getinfo`. When running `ord` you must specify the cookie file "
"location with `--cookie-file=<your_bitcoin_datadir>/.cookie`."
msgstr ""
"Si `bitcoin-cli -getinfo` renvoie `Could not locate RPC credentials` (n’a "
"pas pu localiser les informations d’authentification RPC), vous devez "
"spécifier l’emplacement du fichier cookie. Si vous utilisez un répertoire de "
"données personnalisé (en spécifiant l’option `datadir`), vous devez "
"spécifier l’emplacement du cookie comme suit : `bitcoin-cli -"
"rpccookiefile=<your_bitcoin_datadir>/.cookie -getinfo`. Lorsque vous "
"exécutez `ord`, vous devez spécifier l’emplacement du fichier cookie à "
"l’aide de `--cookie-file=<your_bitcoin_datadir>/.cookie`."

#: src\guides/inscriptions.md:118
msgid ""
"Make sure you do _NOT_ have `disablewallet=1` in your `bitcoin.conf` file. "
"If `bitcoin-cli listwallets` returns `Method not found` then the wallet is "
"disabled and you won't be able to use `ord`."
msgstr ""
"Assurez-vous de ne _PAS_ avoir `disablewallet=1` dans votre fichier `bitcoin."
"conf`. Si `bitcoin-cli listwallets` renvoie `Method not found` (méthode non "
"trouvée), le portefeuille est désactivé et vous ne pourrez pas utiliser "
"`ord`."

#: src\guides/inscriptions.md:122
msgid ""
"Make sure `txindex=1` is set. Run `bitcoin-cli getindexinfo` and it should "
"return something like"
msgstr ""
"Assurez-vous que `txindex=1` est configuré. Lancez `bitcoin-cli "
"getindexinfo` et cela devrait retourner quelque chose de ce genre"

#: src\guides/inscriptions.md:124
msgid ""
"```json\n"
"{\n"
"  \"txindex\": {\n"
"    \"synced\": true,\n"
"    \"best_block_height\": 776546\n"
"  }\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"txindex\": {\n"
"    \"synced\": true,\n"
"    \"best_block_height\": 776546\n"
"  }\n"
"}\n"
"```"

#: src\guides/inscriptions.md:132
msgid ""
"If it only returns `{}`, `txindex` is not set. If it returns `\"synced\": "
"false`, `bitcoind` is still creating the `txindex`. Wait until `\"synced\": "
"true` before using `ord`."
msgstr ""
"Si cela renvoie uniquement `{}`, `txindex` n’est pas défini. Si cela renvoie "
"`\"synced\": false`, `bitcoind` est encore en train de créer le `txindex`. "
"Attendez que `\"synced\": true` s’affiche avant d’utiliser `ord`."

#: src\guides/inscriptions.md:136
msgid ""
"If you have `maxuploadtarget` set it can interfere with fetching blocks for "
"`ord` index. Either remove it or set `whitebind=127.0.0.1:8333`."
msgstr ""
"Si vous avez configuré `maxuploadtarget`, cela peut interférer avec le fetch "
"de blocs pour l’index `ord`. Il faut soit le supprimer ou définir "
"`whitebind=127.0.0.1:8333`."

#: src\guides/inscriptions.md:139
msgid "Installing `ord`"
msgstr "L’installation de `ord`"

#: src\guides/inscriptions.md:142
msgid ""
"The `ord` utility is written in Rust and can be built from [source](https://"
"github.com/ordinals/ord). Pre-built binaries are available on the [releases "
"page](https://github.com/ordinals/ord/releases)."
msgstr ""
"L’utilitaire `ord` est écrit en Rust et peut être compilé à partir des "
"[sources](https://github.com/ordinals/ord). Des binaires préconstruits sont "
"disponibles sur la [page des versions](https://github.com/ordinals/ord/"
"releases)."

#: src\guides/inscriptions.md:146
msgid "You can install the latest pre-built binary from the command line with:"
msgstr ""
"Vous pouvez installer le dernier binaire préconstruit à partir de la ligne "
"de commande suivante :"

#: src\guides/inscriptions.md:148
msgid ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"
msgstr ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"

#: src\guides/inscriptions.md:152
msgid "Once `ord` is installed, you should be able to run:"
msgstr "Une fois `ord` installé, vous devriez être en mesure d’exécuter :"

#: src\guides/inscriptions.md:158
msgid "Which prints out `ord`'s version number."
msgstr "Ce qui indiquera le numéro de version de `ord`."

#: src\guides/inscriptions.md:160
msgid "Creating a Bitcoin Core Wallet"
msgstr "La création d’un portefeuille Bitcoin Core"

#: src\guides/inscriptions.md:163
msgid ""
"`ord` uses Bitcoin Core to manage private keys, sign transactions, and "
"broadcast transactions to the Bitcoin network."
msgstr ""
"`ord` utilise Bitcoin Core pour gérer les clés privées, signer et diffuser "
"des transactions sur le réseau Bitcoin."

#: src\guides/inscriptions.md:166
msgid "To create a Bitcoin Core wallet named `ord` for use with `ord`, run:"
msgstr ""
"Pour créer un portefeuille Bitcoin Core nommé `ord` à utiliser avec `ord`, "
"exécutez :"

#: src\guides/inscriptions.md:172
msgid "Receiving Sats"
msgstr "Recevoir des sats"

#: src\guides/inscriptions.md:175
msgid ""
"Inscriptions are made on individual sats, using normal Bitcoin transactions "
"that pay fees in sats, so your wallet will need some sats."
msgstr ""
"Les inscriptions sont créées sur des sats individuels, en utilisant des "
"transactions Bitcoin standard qui paient des frais en sats. Votre "
"portefeuille doit donc contenir quelques sats."

#: src\guides/inscriptions.md:178
msgid "Get a new address from your `ord` wallet by running:"
msgstr ""
"Obtenez une nouvelle adresse à partir de votre portefeuille `ord` en "
"exécutant :"

#: src\guides/inscriptions.md:184
msgid "And send it some funds."
msgstr "Et envoyez-y des fonds."

#: src\guides/inscriptions.md:186
msgid "You can see pending transactions with:"
msgstr "Vous pouvez voir les transactions en attente en exécutant :"

#: src\guides/inscriptions.md:192
msgid ""
"Once the transaction confirms, you should be able to see the transactions "
"outputs with `ord wallet outputs`."
msgstr ""
"Une fois la transaction confirmée, vous devriez être en mesure de voir les "
"sorties de transactions avec `ord wallet outputs`."

#: src\guides/inscriptions.md:195
msgid "Creating Inscription Content"
msgstr "Créer du contenu pour les inscriptions"

#: src\guides/inscriptions.md:198
msgid ""
"Sats can be inscribed with any kind of content, but the `ord` wallet only "
"supports content types that can be displayed by the `ord` block explorer."
msgstr ""
"Les sats peuvent être inscrits avec n’importe quel type de contenu, mais le "
"portefeuille `ord` ne prend en charge que les types de contenu qui peuvent "
"être affichés par l’explorateur de blocs `ord`."

#: src\guides/inscriptions.md:201
msgid ""
"Additionally, inscriptions are included in transactions, so the larger the "
"content, the higher the fee that the inscription transaction must pay."
msgstr ""
"En outre, les inscriptions sont incluses dans les transactions, donc plus le "
"contenu est volumineux, plus les frais à payer pour la transaction de "
"l’inscription sont élevés."

#: src\guides/inscriptions.md:204
msgid ""
"Inscription content is included in transaction witnesses, which receive the "
"witness discount. To calculate the approximate fee that an inscribe "
"transaction will pay, divide the content size by four and multiply by the "
"fee rate."
msgstr ""
"Le contenu des inscriptions est inclus dans les témoins de transaction, qui "
"bénéficient de la réduction pour témoins. Afin de calculer le montant "
"approximatif des frais de transaction d’une inscription, divisez la taille "
"du contenu par quatre et multipliez par le taux de frais."

#: src\guides/inscriptions.md:208
msgid ""
"Inscription transactions must be less than 400,000 weight units, or they "
"will not be relayed by Bitcoin Core. One byte of inscription content costs "
"one weight unit. Since an inscription transaction includes not just the "
"inscription content, limit inscription content to less than 400,000 weight "
"units. 390,000 weight units should be safe."
msgstr ""
"Les transactions d’inscription doivent être inférieures à 400 000 unités de "
"poids, sinon elles ne seront pas relayées par Bitcoin Core. Un octet de "
"contenu d’inscription coûte une unité de poids. Étant donné qu’une "
"transaction d’inscription ne contient pas seulement le contenu de "
"l’inscription, il faut limiter le contenu de l’inscription à moins de 400 "
"000 unités de poids. Il est donc recommandé de ne pas dépasser 390 000 "
"unités de poids, afin de maintenir une marge de sécurité."

#: src\guides/inscriptions.md:214
msgid "Creating Inscriptions"
msgstr "La création d’inscriptions"

#: src\guides/inscriptions.md:217
msgid "To create an inscription with the contents of `FILE`, run:"
msgstr "Pour créer une inscription avec le contenu de `FILE`, exécutez :"

#: src\guides/inscriptions.md:223
msgid ""
"Ord will output two transactions IDs, one for the commit transaction, and "
"one for the reveal transaction, and the inscription ID. Inscription IDs are "
"of the form `TXIDiN`, where `TXID` is the transaction ID of the reveal "
"transaction, and `N` is the index of the inscription in the reveal "
"transaction."
msgstr ""
"Ord affichera deux identifiants de transaction, l’un pour la transaction "
"d’engagement et l’autre pour la transaction de révélation, ainsi que "
"l’identifiant de l’inscription. Les identifiants d’inscription ont le format "
"`TXIDiN`, `TXID` étant l’identifiant de la transaction de révélation et `N` "
"l’index de l’inscription dans la transaction de révélation."

#: src\guides/inscriptions.md:228
msgid ""
"The commit transaction commits to a tapscript containing the content of the "
"inscription, and the reveal transaction spends from that tapscript, "
"revealing the content on chain and inscribing it on the first sat of the "
"input that contains the corresponding tapscript."
msgstr ""
"La transaction d’engagement s’engage à utiliser un tapscript qui héberge le "
"contenu de l’inscription, tandis que la transaction de révélation dépense ce "
"tapscript, révélant ainsi le contenu sur la chaîne et l’inscrivant sur le "
"premier sat d’entrée contenant le tapscript correspondant."

#: src\guides/inscriptions.md:233
msgid ""
"Wait for the reveal transaction to be mined. You can check the status of the "
"commit and reveal transactions using  [the mempool.space block explorer]"
"(https://mempool.space/)."
msgstr ""
"Attendez que la transaction de révélation soit minée. Vous pouvez vérifier "
"le statut des transactions d’engagement et de révélation en utilisant "
"[l’explorateur de blocs mempool.space](https://mempool.space/)."

#: src\guides/inscriptions.md:237
msgid ""
"Once the reveal transaction has been mined, the inscription ID should be "
"printed when you run:"
msgstr ""
"Une fois que la transaction de révélation a été minée, l’identifiant "
"d’inscription devrait apparaître lorsque vous exécutez :"

#: src\guides/inscriptions.md:244
msgid "Parent-Child Inscriptions"
msgstr "Inscriptions parent-enfant"

#: src\guides/inscriptions.md:247
msgid ""
"Parent-child inscriptions enable what is colloquially known as collections, "
"see [provenance](../inscriptions/provenance.md) for more information."
msgstr ""
"Les inscriptions parent-enfant permettent ce que l’on appelle des "
"collections en langage courant (voir [provenance](../inscriptions/provenance."
"md) pour plus d’informations)."

#: src\guides/inscriptions.md:250
msgid ""
"To make an inscription a child of another, the parent inscription has to be "
"inscribed and present in the wallet. To choose a parent run `ord wallet "
"inscriptions` and copy the inscription id (`<PARENT_INSCRIPTION_ID>`)."
msgstr ""
"Pour faire d’une inscription l’enfant d’une autre, l’inscription parent doit "
"être inscrite et présente dans le portefeuille. Pour choisir un parent, "
"lancez `ord wallet inscriptions` et copiez l’identifiant de l’inscription "
"(`<PARENT_INSCRIPTION_ID>`)."

#: src\guides/inscriptions.md:254
msgid "Now inscribe the child inscription and specify the parent like so:"
msgstr ""
"Inscrivez maintenant l’inscription enfant et spécifiez le parent comme suit :"

#: src\guides/inscriptions.md:260
msgid ""
"This relationship cannot be added retroactively, the parent has to be "
"present at inception of the child."
msgstr ""
"Cette relation ne peut pas être ajoutée rétroactivement, le parent doit être "
"présent lors de la création de l’enfant."

#: src\guides/inscriptions.md:263
msgid "Sending Inscriptions"
msgstr "L’envoi d’inscriptions"

#: src\guides/inscriptions.md:266
msgid "Ask the recipient to generate a new address by running:"
msgstr ""
"Demandez au destinataire de générer une nouvelle adresse en exécutant :"

#: src\guides/inscriptions.md:272
msgid "Send the inscription by running:"
msgstr "Envoyez l’inscription en exécutant :"

#: src\guides/inscriptions.md:278 src\guides/inscriptions.md:306
msgid "See the pending transaction with:"
msgstr "Voyez la transaction en attente avec la commande :"

#: src\guides/inscriptions.md:284
msgid ""
"Once the send transaction confirms, the recipient can confirm receipt by "
"running:"
msgstr ""
"Une fois que la transaction d’envoi est confirmée, le destinataire peut "
"confirmer la réception en exécutant :"

#: src\guides/inscriptions.md:291
msgid "Receiving Inscriptions"
msgstr "La réception d’inscriptions"

#: src\guides/inscriptions.md:294
msgid "Generate a new receive address using:"
msgstr "Générez une nouvelle adresse de réception en utilisant :"

#: src\guides/inscriptions.md:300
msgid "The sender can transfer the inscription to your address using:"
msgstr ""
"L’expéditeur peut transférer l’inscription à votre adresse en utilisant :"

#: src\guides/inscriptions.md:311
msgid ""
"Once the send transaction confirms, you can can confirm receipt by running:"
msgstr ""
"Une fois que la transaction d’envoi est confirmée, vous pouvez confirmer la "
"réception en exécutant :"

#: src\guides/sat-hunting.md:4
msgid ""
"_This guide is out of date. Since it was written, the `ord` binary was "
"changed to only build the full satoshi index when the `--index-sats` flag is "
"supplied. Additionally, `ord` now has a built-in wallet that wraps a Bitcoin "
"Core wallet. See `ord wallet --help`._"
msgstr ""
"_Ce guide n’est plus à jour. Depuis qu’il a été écrit, le binaire `ord` a "
"été modifié de façon à ne construire l’index complet des satoshis que "
"lorsque le drapeau `--index-sats` est fourni. De plus, `ord` a désormais un "
"portefeuille intégré qui enveloppe un portefeuille Bitcoin Core. Consultez "
"`ord wallet --help`._"

#: src\guides/sat-hunting.md:9
msgid ""
"Ordinal hunting is difficult but rewarding. The feeling of owning a wallet "
"full of UTXOs, redolent with the scent of rare and exotic sats, is beyond "
"compare."
msgstr ""
"La chasse d’Ordinals est difficile mais gratifiante. Le sentiment de "
"posséder un portefeuille rempli d’UTXOs, imprégné de l’odeur de sats rares "
"et exotiques, est incomparable."

#: src\guides/sat-hunting.md:12
msgid ""
"Ordinals are numbers for satoshis. Every satoshi has an ordinal number and "
"every ordinal number has a satoshi."
msgstr ""
"Les ordinals sont des nombres pour les satoshis. Chaque satoshi a un nombre "
"ordinal et chaque nombre ordinal a un satoshi."

#: src\guides/sat-hunting.md:15
msgid "Preparation"
msgstr "Préparation"

#: src\guides/sat-hunting.md:18
msgid "There are a few things you'll need before you start."
msgstr "Avant de commencer il vous faudra certaines choses."

#: src\guides/sat-hunting.md:20
msgid ""
"First, you'll need a synced Bitcoin Core node with a transaction index. To "
"turn on transaction indexing, pass `-txindex` on the command-line:"
msgstr ""
"Tout d’abord, vous aurez besoin d’un nœud Bitcoin Core synchronisé avec un "
"index de transaction. Pour activer l’indexation des transactions, exécutez `-"
"txindex` en ligne de commande :"

#: src\guides/sat-hunting.md:27
msgid ""
"Or put the following in your [Bitcoin configuration file](https://github.com/"
"bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"
msgstr ""
"Ou ajoutez ce qui suit dans votre [fichier de configuration Bitcoin](https://"
"github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-"
"file-path) :"

#: src\guides/sat-hunting.md:34
msgid ""
"Launch it and wait for it to catch up to the chain tip, at which point the "
"following command should print out the current block height:"
msgstr ""
"Exécutez cette commande et attendez qu’elle rattrape le bout de la chaîne. "
"Une fois cela fait, la commande suivante devrait vous indiquer la hauteur du "
"bloc actuel :"

#: src\guides/sat-hunting.md:41
msgid "Second, you'll need a synced `ord` index."
msgstr "Deuxièmement, vous aurez besoin d’un index `ord` synchronisé."

#: src\guides/sat-hunting.md:43
msgid "Get a copy of `ord` from [the repo](https://github.com/ordinals/ord/)."
msgstr ""
"Obtenez une copie d’`ord` depuis [le référentiel](https://github.com/"
"ordinals/ord/)."

#: src\guides/sat-hunting.md:45
msgid ""
"Run `RUST_LOG=info ord index`. It should connect to your bitcoin core node "
"and start indexing."
msgstr ""
"Exécutez `RUST_LOG=info ord index`. Il devrait se connecter à votre nœud "
"Bitcoin Core et démarrer avec le processus d’indexation."

#: src\guides/sat-hunting.md:48
msgid "Wait for it to finish indexing."
msgstr "Attendez que l’indexation soit terminée."

#: src\guides/sat-hunting.md:50
msgid "Third, you'll need a wallet with UTXOs that you want to search."
msgstr ""
"Troisièmement, vous aurez besoin d’un portefeuille avec les UTXOs que vous "
"souhaitez rechercher."

#: src\guides/sat-hunting.md:52
msgid "Searching for Rare Ordinals"
msgstr "Rechercher des ordinals rares"

#: src\guides/sat-hunting.md:55
msgid "Searching for Rare Ordinals in a Bitcoin Core Wallet"
msgstr "Rechercher des ordinals rares dans un portefeuille Bitcoin Core"

#: src\guides/sat-hunting.md:57
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"searching for rare ordinals in a Bitcoin Core wallet is Easy. Assuming your "
"wallet is named `foo`:"
msgstr ""
"La commande `ord wallet` n’est qu’une enveloppe autour de l’API RPC de "
"Bitcoin Core, donc la recherche d’ordinals rares dans un portefeuille "
"Bitcoin Core est facile. Supposons que votre portefeuille s’appelle `foo` :"

#: src\guides/sat-hunting.md:61
msgid "Load your wallet:"
msgstr "Chargez votre portefeuille :"

#: src\guides/sat-hunting.md:67
msgid "Display any rare ordinals wallet `foo`'s UTXOs:"
msgstr "Affichez tous les UTXOs rares du portefeuille d’ordinals `foo` :"

#: src\guides/sat-hunting.md:73
msgid "Searching for Rare Ordinals in a Non-Bitcoin Core Wallet"
msgstr ""
"Rechercher des ordinals rares dans un portefeuille autre que Bitcoin Core"

#: src\guides/sat-hunting.md:75
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"to search for rare ordinals in a non-Bitcoin Core wallet, you'll need to "
"import your wallet's descriptors into Bitcoin Core."
msgstr ""
"La commande `ord wallet` n’est qu’une enveloppe autour de l’API RPC de "
"Bitcoin Core, donc pour rechercher des ordinals rares dans un portefeuille "
"autre que Bitcoin Core, vous devrez importer les descripteurs de votre "
"portefeuille dans Bitcoin Core."

#: src\guides/sat-hunting.md:79
msgid ""
"[Descriptors](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors."
"md) describe the ways that wallets generate private keys and public keys."
msgstr ""
"[Les descripteurs](https://github.com/bitcoin/bitcoin/blob/master/doc/"
"descriptors.md) décrivent la façon dont les portefeuilles génèrent les clés "
"privées et les clés publiques."

#: src\guides/sat-hunting.md:82
msgid ""
"You should only import descriptors into Bitcoin Core for your wallet's "
"public keys, not its private keys."
msgstr ""
"Vous devrez uniquement importer les descripteurs des clés publiques de votre "
"portefeuille dans Bitcoin Core, pas ceux des clés privées."

#: src\guides/sat-hunting.md:85
msgid ""
"If your wallet's public key descriptor is compromised, an attacker will be "
"able to see your wallet's addresses, but your funds will be safe."
msgstr ""
"Si le descripteur de la clé publique de votre portefeuille est compromis, un "
"attaquant pourra voir les adresses de votre portefeuille, mais vos fonds "
"seront en sécurité."

#: src\guides/sat-hunting.md:88
msgid ""
"If your wallet's private key descriptor is compromised, an attacker can "
"drain your wallet of funds."
msgstr ""
"Si le descripteur de la clé privée de votre portefeuille est compromis, un "
"attaquant peut vider votre portefeuille de ses fonds."

#: src\guides/sat-hunting.md:91
msgid ""
"Get the wallet descriptor from the wallet whose UTXOs you want to search for "
"rare ordinals. It will look something like this:"
msgstr ""
"Obtenez le descripteur du portefeuille contenant les UTXOs que vous voulez "
"analyser pour identifier s’ils contiennent des ordinals rares. Il "
"ressemblera à quelque chose comme ceci :"

#: src\guides/sat-hunting.md:98
msgid "Create a watch-only wallet named `foo-watch-only`:"
msgstr "Créez un portefeuille en lecture seule nommé `foo-watch-only` :"

#: src\guides/sat-hunting.md:104
msgid "Feel free to give it a better name than `foo-watch-only`!"
msgstr "N’hésitez pas à lui donner un meilleur nom que `foo-watch-only` !"

#: src\guides/sat-hunting.md:106
msgid "Load the `foo-watch-only` wallet:"
msgstr "Chargez le Portefeuille `foo-watch-only` :"

#: src\guides/sat-hunting.md:112
msgid "Import your wallet descriptors into `foo-watch-only`:"
msgstr ""
"Importez les descripteurs de votre portefeuille dans `foo-watch-only` :"

#: src\guides/sat-hunting.md:114
msgid ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\", \"timestamp\":0 }]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\", \"timestamp\":0 }]'\n"
"```"

#: src\guides/sat-hunting.md:119
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of `\"timestamp\"` instead of "
"`0`. This will reduce the time it takes for Bitcoin Core to search for your "
"wallet's UTXOs."
msgstr ""
"Si vous connaissez l’heure Unix à laquelle votre portefeuille a commencé à "
"recevoir des transactions, vous pouvez l’utiliser comme valeur de "
"`\"timestamp\"` au lieu de 0. Cela réduira le temps que Bitcoin Core prendra "
"pour rechercher des UTXOs dans votre portefeuille."

#: src\guides/sat-hunting.md:124 src\guides/sat-hunting.md:225
msgid "Check that everything worked:"
msgstr "Vérifiez que tout a fonctionné correctement :"

#: src\guides/sat-hunting.md:130 src\guides/sat-hunting.md:231
msgid "Display your wallet's rare ordinals:"
msgstr "Affichez les ordinals rares qui se trouvent dans votre portefeuille :"

#: src\guides/sat-hunting.md:136
msgid ""
"Searching for Rare Ordinals in a Wallet that Exports Multi-path Descriptors"
msgstr ""
"Rechercher des ordinals rares dans un portefeuille qui exporte des "
"descripteurs à chemins multiples (multi-path)"

#: src\guides/sat-hunting.md:138
msgid ""
"Some descriptors describe multiple paths in one descriptor using angle "
"brackets, e.g., `<0;1>`. Multi-path descriptors are not yet supported by "
"Bitcoin Core, so you'll first need to convert them into multiple "
"descriptors, and then import those multiple descriptors into Bitcoin Core."
msgstr ""
"Certains descripteurs décrivent plusieurs chemins dans un seul descripteur "
"en utilisant des crochets angulaires, par exemple `<0;1>`. Les descripteurs "
"à chemins multiples ne sont pas encore pris en charge par Bitcoin Core, vous "
"devrez donc d’abord les convertir en descripteurs multiples, puis importer "
"ces descripteurs multiples dans Bitcoin Core."

#: src\guides/sat-hunting.md:143
msgid ""
"First get the multi-path descriptor from your wallet. It will look something "
"like this:"
msgstr ""
"Obtenez d’abord le descripteur à chemins multiples de votre portefeuille. Il "
"ressemblera à quelque chose comme ceci :"

#: src\guides/sat-hunting.md:150
msgid "Create a descriptor for the receive address path:"
msgstr "Créez un descripteur pour le chemin de l’adresse de réception :"

#: src\guides/sat-hunting.md:156
msgid "And the change address path:"
msgstr ""
"Et pour le chemin de l’adresse qui recevra la monnaie restante (change "
"address) :"

#: src\guides/sat-hunting.md:162
msgid ""
"Get and note the checksum for the receive address descriptor, in this case "
"`tpnxnxax`:"
msgstr ""
"Obtenez et notez la somme de contrôle du descripteur d’adresse de réception, "
"dans ce cas `tpnxnxax`:"

#: src\guides/sat-hunting.md:170
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src\guides/sat-hunting.md:180
msgid "And for the change address descriptor, in this case `64k8wnd7`:"
msgstr ""
"Et pour le descripteur de l’adresse qui recevra la monnaie restante (change "
"address), dans ce cas `64k8wnd7` :"

#: src\guides/sat-hunting.md:187
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src\guides/sat-hunting.md:197
msgid "Load the wallet you want to import the descriptors into:"
msgstr ""
"Chargez le portefeuille dans lequel vous souhaitez importer les "
"descripteurs :"

#: src\guides/sat-hunting.md:203
msgid ""
"Now import the descriptors, with the correct checksums, into Bitcoin Core."
msgstr ""
"Importez maintenant les descripteurs, avec les sommes de contrôle correctes, "
"dans Bitcoin Core."

#: src\guides/sat-hunting.md:205
msgid ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"

#: src\guides/sat-hunting.md:220
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of the `\"timestamp\"` fields "
"instead of `0`. This will reduce the time it takes for Bitcoin Core to "
"search for your wallet's UTXOs."
msgstr ""
"Si vous connaissez l’heure Unix à laquelle votre portefeuille a commencé à "
"recevoir des transactions, vous pouvez l’utiliser comme valeur de "
"`\"timestamp\"` au lieu de `0`. Cela réduira le temps que Bitcoin Core "
"prendra pour rechercher des UTXOs dans votre portefeuille."

#: src\guides/sat-hunting.md:237
msgid "Exporting Descriptors"
msgstr "Exporter des descripteurs"

#: src\guides/sat-hunting.md:241
msgid ""
"Navigate to the `Settings` tab, then to `Script Policy`, and press the edit "
"button to display the descriptor."
msgstr ""
"Naviguez jusqu’à l’onglet `Settings`, puis jusqu’à `Script Policy`, et "
"appuyez sur le bouton d’édition pour afficher le descripteur."

#: src\guides/sat-hunting.md:244
msgid "Transferring Ordinals"
msgstr "Transférer des ordinals"

#: src\guides/sat-hunting.md:246
msgid ""
"The `ord` wallet supports transferring specific satoshis. You can also use "
"`bitcoin-cli` commands `createrawtransaction`, "
"`signrawtransactionwithwallet`, and `sendrawtransaction`, how to do so is "
"complex and outside the scope of this guide."
msgstr ""
"Le portefeuille `ord` permet de transférer des satoshis spécifiques. Vous "
"pouvez également utiliser les commandes `bitcoin-cli` "
"`createrawtransaction`, `signrawtransactionwithwallet` et "
"`sendrawtransaction`, mais cette procédure est complexe et dépasse le cadre "
"de ce guide."

#: src\guides/collecting.md:4
msgid ""
"Currently, [ord](https://github.com/ordinals/ord/) is the only wallet "
"supporting sat-control and sat-selection, which are required to safely store "
"and send rare sats and inscriptions, hereafter ordinals."
msgstr ""
"Actuellement, [ord](https://github.com/ordinals/ord/) est le seul "
"portefeuille qui prend en charge le contrôle et la sélection des sats, ce "
"qui est indispensable pour stocker et envoyer en toute sécurité des sats et "
"des inscriptions rares, ci-après dénommés « ordinals »."

#: src\guides/collecting.md:8
msgid ""
"The recommended way to send, receive, and store ordinals is with `ord`, but "
"if you are careful, it is possible to safely store, and in some cases send, "
"ordinals with other wallets."
msgstr ""
"Il est recommandé d’envoyer, de recevoir et de stocker les ordinals avec "
"`ord`, mais si vous êtes prudent, il est possible de stocker, et dans "
"certains cas d’envoyer, des ordinals en toute sécurité au moyen d’autres "
"portefeuilles."

#: src\guides/collecting.md:12
msgid ""
"As a general note, receiving ordinals in an unsupported wallet is not "
"dangerous. Ordinals can be sent to any bitcoin address, and are safe as long "
"as the UTXO that contains them is not spent. However, if that wallet is then "
"used to send bitcoin, it may select the UTXO containing the ordinal as an "
"input, and send the inscription or spend it to fees."
msgstr ""
"D’une manière générale, recevoir des ordinals dans un portefeuille non pris "
"en charge n’est pas dangereux. Les ordinals peuvent être envoyés à n’importe "
"quelle adresse bitcoin et sont sûrs tant que l’UTXO qui les contient n’est "
"pas dépensé. En revanche, si ce portefeuille est ensuite utilisé pour "
"envoyer des bitcoins, il se peut que l’UTXO contenant l’ordinal soit "
"sélectionné comme entrée et que l’inscription soit envoyée par erreur ou "
"qu’elle soit dépensée en frais."

#: src\guides/collecting.md:18
msgid ""
"A [guide](./collecting/sparrow-wallet.md) to creating an `ord`\\-compatible "
"wallet with [Sparrow Wallet](https://sparrowwallet.com/), is available in "
"this handbook."
msgstr ""
"Un [guide](./collecting/sparrow-wallet.md) pour créer un portefeuille "
"compatible avec `ord` à l’aide d’un portefeuille [Sparrow](https://"
"sparrowwallet.com/), est disponible dans ce manuel."

#: src\guides/collecting.md:21
msgid ""
"Please note that if you follow this guide, you should not use the wallet you "
"create to send BTC, unless you perform manual coin-selection to avoid "
"sending ordinals."
msgstr ""
"Veuillez noter que si vous suivez ce guide, vous ne devriez pas utiliser le "
"portefeuille que vous avez créé pour envoyer des BTC, à moins que vous ne "
"procédiez à une sélection manuelle de monnaie pour éviter d’envoyer des "
"ordinals par erreur."

#: src\guides/collecting/sparrow-wallet.md:1
msgid "Collecting Inscriptions and Ordinals with Sparrow Wallet"
msgstr ""
"Collectionner des inscriptions et des ordinals avec le portefeuille Sparrow"

#: src\guides/collecting/sparrow-wallet.md:4
msgid ""
"Users who cannot or have not yet set up the [ord](https://github.com/"
"ordinals/ord) wallet can receive inscriptions and ordinals with alternative "
"bitcoin wallets, as long as they are _very_ careful about how they spend "
"from that wallet."
msgstr ""
"Les utilisateurs qui ne peuvent pas ou n’ont pas encore mis en place le "
"portefeuille [ord](https://github.com/ordinals/ord)peuvent recevoir des "
"inscriptions et des ordinals en utilisant d’autres portefeuilles Bitcoin, à "
"condition qu’ils soient _très_ prudents à la manière dont ils dépensent "
"l’argent de ce portefeuille."

#: src\guides/collecting/sparrow-wallet.md:6
msgid ""
"This guide gives some basic steps on how to create a wallet with [Sparrow "
"Wallet](https://sparrowwallet.com/) which is compatible with `ord` and can "
"be later imported into `ord`"
msgstr ""
"Ce guide fournit des instructions de base pour créer un portefeuille avec "
"[Sparrow Wallet](https://sparrowwallet.com/) qui soit compatible avec `ord` "
"et qui puisse être importé dans ord par la suite"

#: src\guides/collecting/sparrow-wallet.md:8
msgid "⚠️⚠️ Warning!! ⚠️⚠️"
msgstr "⚠️⚠️ Avertissement!! ⚠️⚠️"

#: src\guides/collecting/sparrow-wallet.md:9
msgid ""
"As a general rule if you take this approach, you should use this wallet with "
"the Sparrow software as a receive-only wallet."
msgstr ""
"En règle générale, si vous adoptez cette approche, vous devriez utiliser ce "
"portefeuille avec le logiciel Sparrow uniquement en tant que portefeuille de "
"réception."

#: src\guides/collecting/sparrow-wallet.md:11
msgid ""
"Do not spend any satoshis from this wallet unless you are sure you know what "
"you are doing. You could very easily inadvertently lose access to your "
"ordinals and inscriptions if you don't heed this warning."
msgstr ""
"Ne dépensez pas de satoshis à partir de ce portefeuille à moins d’être sûr "
"de savoir ce que vous faites. Vous pourriez très facilement perdre l’accès à "
"vos ordinals et à vos inscriptions par inadvertance si vous ne tenez pas "
"compte de cet avertissement."

#: src\guides/collecting/sparrow-wallet.md:13
msgid "Wallet Setup & Receiving"
msgstr "Configuration du portefeuille et réception"

#: src\guides/collecting/sparrow-wallet.md:15
msgid ""
"Download the Sparrow Wallet from the [releases page](https://sparrowwallet."
"com/download/) for your particular operating system."
msgstr ""
"Téléchargez Sparrow Wallet à partir de la [page de téléchargement](https://"
"sparrowwallet.com/download/) correspondant à votre système d’exploitation."

#: src\guides/collecting/sparrow-wallet.md:17
msgid "Select `File -> New Wallet` and create a new wallet called `ord`."
msgstr ""
"Sélectionnez `File -> New Wallet` et créez un nouveau portefeuille appelé "
"`ord`."

#: src\guides/collecting/sparrow-wallet.md:19
msgid "![](images/wallet_setup_01.png)"
msgstr "![](images/wallet_setup_01.png)"

#: src\guides/collecting/sparrow-wallet.md:21
msgid ""
"Change the `Script Type` to `Taproot (P2TR)` and select the `New or Imported "
"Software Wallet` option."
msgstr ""
"Modifiez le `Script Type` (type de script) en choisissant `Taproot (P2TR)` "
"et sélectionnez l’option `New or Imported Software Wallet` (Portefeuille de "
"logiciel nouveau ou importé)."

#: src\guides/collecting/sparrow-wallet.md:23
msgid "![](images/wallet_setup_02.png)"
msgstr "![](images/wallet_setup_02.png)"

#: src\guides/collecting/sparrow-wallet.md:25
msgid ""
"Select `Use 12 Words` and then click `Generate New`. Leave the passphrase "
"blank."
msgstr ""
"Sélectionnez `Use 12 Words` (utiliser 12 mots), puis cliquez sur `Generate "
"New` (Créer nouveau). Laissez la passphrase (phrase secrète) vide."

#: src\guides/collecting/sparrow-wallet.md:27
msgid "![](images/wallet_setup_03.png)"
msgstr "![](images/wallet_setup_03.png)"

#: src\guides/collecting/sparrow-wallet.md:29
msgid ""
"A new 12 word BIP39 seed phrase will be generated for you. Write this down "
"somewhere safe as this is your backup to get access to your wallet. NEVER "
"share or show this seed phrase to anyone else."
msgstr ""
"Une nouvelle phrase de récupération BIP39 contenant 12 mots sera générée "
"pour vous. Notez-la dans un endroit sûr, car elle vous servira de référence "
"pour accéder à votre portefeuille. Ne communiquez ou ne montrez JAMAIS cette "
"phrase de récupération à qui que ce soit."

#: src\guides/collecting/sparrow-wallet.md:31
msgid "Once you have written down the seed phrase click `Confirm Backup`."
msgstr ""
"Une fois que vous avez noté la phrase de récupération, cliquez sur `Confirm "
"Backup` (confirmer la sauvegarde)."

#: src\guides/collecting/sparrow-wallet.md:33
msgid "![](images/wallet_setup_04.png)"
msgstr "![](images/wallet_setup_04.png)"

#: src\guides/collecting/sparrow-wallet.md:35
msgid ""
"Re-enter the seed phrase which you wrote down, and then click `Create "
"Keystore`."
msgstr ""
"Saisissez à nouveau la phrase de récupération que vous avez notée, puis "
"cliquez sur `Create Keystore` (Créer le Keystore)."

#: src\guides/collecting/sparrow-wallet.md:37
msgid "![](images/wallet_setup_05.png)"
msgstr "![](images/wallet_setup_05.png)"

#: src\guides/collecting/sparrow-wallet.md:39
msgid "Click `Import Keystore`."
msgstr "Cliquez sur `Import Keystore`.(Importer le Keystore)."

#: src\guides/collecting/sparrow-wallet.md:41
msgid "![](images/wallet_setup_06.png)"
msgstr "![](images/wallet_setup_06.png)"

#: src\guides/collecting/sparrow-wallet.md:43
msgid "Click `Apply`. Add a password for the wallet if you want to."
msgstr ""
"Cliquez sur `Apply` (Appliquer). Ajoutez un mot de passe pour le "
"portefeuille si vous le souhaitez."

#: src\guides/collecting/sparrow-wallet.md:45
msgid "![](images/wallet_setup_07.png)"
msgstr "![](images/wallet_setup_07.png)"

#: src\guides/collecting/sparrow-wallet.md:47
msgid ""
"You now have a wallet which is compatible with `ord`, and can be imported "
"into `ord` using the BIP39 Seed Phrase. To receive ordinals or inscriptions, "
"click on the `Receive` tab and copy a new address."
msgstr ""
"Vous disposez maintenant d’un portefeuille compatible avec `ord`, qui peut "
"être importé dans `ord` à l’aide de la phrase de récupération BIP39. Pour "
"recevoir des ordinals ou des inscriptions, cliquez sur l’onglet `Receive` "
"(recevoir) et copiez une nouvelle adresse."

#: src\guides/collecting/sparrow-wallet.md:49
msgid ""
"Each time you want to receive you should use a brand-new address, and not re-"
"use existing addresses."
msgstr ""
"Chaque fois que vous voulez recevoir, vous devriez utiliser une toute "
"nouvelle adresse et ne pas réutiliser les adresses existantes."

#: src\guides/collecting/sparrow-wallet.md:51
msgid ""
"Note that bitcoin is different to some other blockchain wallets, in that "
"this wallet can generate an unlimited number of new addresses. You can "
"generate a new address by clicking on the `Get Next Address` button. You can "
"see all of your addresses in the `Addresses` tab of the app."
msgstr ""
"Veuillez noter que le portefeuille Bitcoin est différent de certains autres "
"portefeuilles de blockchain dans la mesure où il peut générer un nombre "
"illimité de nouvelles adresses. Vous pouvez générer une nouvelle adresse en "
"cliquant sur le bouton `Get Next Address` (Obtenir l’adresse suivante). Vous "
"pouvez voir toutes vos adresses dans l’onglet `Addresses` (Adresses) de "
"l’application."

#: src\guides/collecting/sparrow-wallet.md:53
msgid ""
"You can add a label to each address, so you can keep track of what it was "
"used for."
msgstr ""
"Vous pouvez ajouter une étiquette à chaque adresse, afin de garder une trace "
"de sa fonction ou de son utilisation."

#: src\guides/collecting/sparrow-wallet.md:55
msgid "![](images/wallet_setup_08.png)"
msgstr "![](images/wallet_setup_08.png)"

#: src\guides/collecting/sparrow-wallet.md:57
msgid "Validating / Viewing Received Inscriptions"
msgstr "Validation / visualisation des inscriptions reçues"

#: src\guides/collecting/sparrow-wallet.md:59
msgid ""
"Once you have received an inscription you will see a new transaction in the "
"`Transactions` tab of Sparrow, as well as a new UTXO in the `UTXOs` tab."
msgstr ""
"Une fois que vous avez reçu une inscription, vous verrez une nouvelle "
"transaction dans l’onglet `Transactions` de Sparrow, ainsi qu’un nouvel UTXO "
"dans l’onglet `UTXOs`."

#: src\guides/collecting/sparrow-wallet.md:61
msgid ""
"Initially this transaction may have an \"Unconfirmed\" status, and you will "
"need to wait for it to be mined into a bitcoin block before it is fully "
"received."
msgstr ""
"Au départ, cette transaction peut avoir un statut « non confirmé », et vous "
"devrez attendre qu’elle soit minée dans un bloc de bitcoins avant de la "
"recevoir dans son intégralité."

#: src\guides/collecting/sparrow-wallet.md:63
msgid "![](images/validating_viewing_01.png)"
msgstr "![](images/validating_viewing_01.png)"

#: src\guides/collecting/sparrow-wallet.md:65
msgid ""
"To track the status of your transaction you can right-click on it,  select "
"`Copy Transaction ID` and then paste that transaction id into [mempool.space]"
"(https://mempool.space)."
msgstr ""
"Pour suivre le statut de votre transaction, vous pouvez faire un clic droit "
"dessus, sélectionner `Copy Transaction ID` (Copier l’identifiant de la "
"transaction) et ensuite coller cet identifiant de transaction dans [mempool."
"space](https://mempool.space)."

#: src\guides/collecting/sparrow-wallet.md:67
msgid "![](images/validating_viewing_02.png)"
msgstr "![](images/validating_viewing_02.png)"

#: src\guides/collecting/sparrow-wallet.md:69
msgid ""
"Once the transaction has confirmed, you can validate and view your "
"inscription by heading over to the `UTXOs` tab, finding the UTXO you want to "
"check, right-clicking on the `Output` and selecting `Copy Transaction "
"Output`. This transaction output id can then be pasted into the [ordinals."
"com](https://ordinals.com) search."
msgstr ""
"Une fois la transaction confirmée, vous pouvez valider et visualiser votre "
"inscription en allant dans l’onglet `UTXOs`, en trouvant l’UTXO que vous "
"souhaitez vérifier, en faisant un clic droit sur `Output` (sortie) et en "
"sélectionnant `Copy Transaction Output` (Copier sortie de transaction). Cet "
"identifiant de sortie de transaction peut ensuite être collé dans le champ "
"de recherche sur ordinals.com](https://ordinals.com)."

#: src\guides/collecting/sparrow-wallet.md:72
msgid "Freezing UTXO's"
msgstr "Gel des UTXOs"

#: src\guides/collecting/sparrow-wallet.md:73
msgid ""
"As explained above, each of your inscriptions is stored in an Unspent "
"Transaction Output (UTXO). You want to be very careful not to accidentally "
"spend your inscriptions, and one way to make it harder for this to happen is "
"to freeze the UTXO."
msgstr ""
"Comme expliqué ci-dessus, chacune de vos inscriptions est stockée dans une "
"sortie de transaction non dépensée (UTXO). Vous devez faire très attention à "
"ne pas dépenser accidentellement vos inscriptions. Une façon d’éviter cela "
"est de geler l’UTXO correspondant."

#: src\guides/collecting/sparrow-wallet.md:75
msgid ""
"To do this, go to the `UTXOs` tab, find the UTXO you want to freeze, right-"
"click on the `Output` and select `Freeze UTXO`."
msgstr ""
"Pour ce faire, allez dans l’onglet `UTXOs`, trouvez l’UTXO que vous voulez "
"geler, faites un clic droit sur `Output` et sélectionnez `Freeze UTXO` "
"(congeler UTXO)."

#: src\guides/collecting/sparrow-wallet.md:77
msgid ""
"This UTXO (Inscription) is now un-spendable within the Sparrow Wallet until "
"you unfreeze it."
msgstr ""
"Cet UTXO (Inscription) dans le portefeuille Sparrow ne pourra pas être "
"dépensé jusqu’à ce que vous le dégeliez."

#: src\guides/collecting/sparrow-wallet.md:79
msgid "Importing into `ord` wallet"
msgstr "Importation dans le portefeuille `ord`"

#: src\guides/collecting/sparrow-wallet.md:81
msgid ""
"For details on setting up Bitcoin Core and the `ord` wallet check out the "
"[Inscriptions Guide](../inscriptions.md)"
msgstr ""
"Pour plus de détails sur la configuration de Bitcoin Core et du portefeuille "
"`ord`, consultez le [guide des inscriptions](../inscriptions.md."

#: src\guides/collecting/sparrow-wallet.md:83
msgid ""
"When setting up `ord`, instead of running `ord wallet create` to create a "
"brand-new wallet, you can import your existing wallet using `ord wallet "
"restore \"BIP39 SEED PHRASE\"` using the seed phrase you generated with "
"Sparrow Wallet."
msgstr ""
"Lors de la configuration d’`ord`, au lieu d’exécuter `ord wallet create` "
"pour créer un nouveau portefeuille, vous pouvez importer votre portefeuille "
"existant en utilisant `ord wallet restore \"BIP39 SEED PHRASE\"` avec la "
"phrase de récupération que vous avez générée dans le portefeuille Sparrow."

#: src\guides/collecting/sparrow-wallet.md:85
msgid ""
"There is currently a [bug](https://github.com/ordinals/ord/issues/1589) "
"which causes an imported wallet to not be automatically rescanned against "
"the blockchain. To work around this you will need to manually trigger a "
"rescan using the bitcoin core cli: `bitcoin-cli -rpcwallet=ord "
"rescanblockchain 767430`"
msgstr ""
"Il existe actuellement un [bug](https://github.com/ordinals/ord/issues/1589) "
"qui empêche un portefeuille importé d’être re-scanné automatiquement pour "
"trouver son contenu sur la blockchain. Pour contourner ce problème, vous "
"devrez manuellement déclencher un rescan à l’aide de bitcoin core cli: "
"`bitcoin-cli -rpcwallet=ord rescanblockchain 767430`"

#: src\guides/collecting/sparrow-wallet.md:88
msgid ""
"You can then check your wallet's inscriptions using `ord wallet inscriptions`"
msgstr ""
"Vous pouvez ensuite vérifier les inscriptions de votre portefeuille en "
"utilisant `ord wallet inscriptions`"

#: src\guides/collecting/sparrow-wallet.md:90
msgid ""
"Note that if you have previously created a wallet with `ord`, then you will "
"already have a wallet with the default name, and will need to give your "
"imported wallet a different name. You can use the `--wallet` parameter in "
"all `ord` commands to reference a different wallet, eg:"
msgstr ""
"Notez que si vous avez précédemment créé un portefeuille avec `ord`, vous "
"aurez alors déjà un portefeuille avec le nom par défaut, et vous devrez "
"donner un nom différent à votre portefeuille importé. Vous pouvez utiliser "
"le paramètre `--wallet` dans toutes les commandes `ord` pour référencer un "
"portefeuille différent, par exemple :"

#: src\guides/collecting/sparrow-wallet.md:92
msgid "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"
msgstr "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"

#: src\guides/collecting/sparrow-wallet.md:94
msgid "`ord --wallet ord_from_sparrow wallet inscriptions`"
msgstr "`ord --wallet ord_from_sparrow wallet inscriptions`"

#: src\guides/collecting/sparrow-wallet.md:96
msgid "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"
msgstr "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"

#: src\guides/collecting/sparrow-wallet.md:98
msgid "Sending inscriptions with Sparrow Wallet"
msgstr "Envoyer des inscriptions avec le portefeuille Sparrow"

#: src\guides/collecting/sparrow-wallet.md:100
msgid "⚠️⚠️ Warning ⚠️⚠️"
msgstr "⚠️⚠️ Avertissement ⚠️⚠️"

#: src\guides/collecting/sparrow-wallet.md:101
msgid ""
"While it is highly recommended that you set up a bitcoin core node and run "
"the `ord` software, there are certain limited ways you can send inscriptions "
"out of Sparrow Wallet in a safe way. Please note that this is not "
"recommended, and you should only do this if you fully understand what you "
"are doing."
msgstr ""
"Bien qu’il soit fortement recommandé de mettre en place un nœud Bitcoin Core "
"et d’exécuter le logiciel `ord`, il existe quelques moyens limités d’envoyer "
"des inscriptions à partir du portefeuille Sparrow en toute sécurité. "
"Veuillez noter que cela n’est pas recommandé et que vous ne devez le faire "
"que si vous comprenez parfaitement ce que vous faites."

#: src\guides/collecting/sparrow-wallet.md:103
msgid ""
"Using the `ord` software will remove much of the complexity we are "
"describing here, as it is able to automatically and safely handle sending "
"inscriptions in an easy way."
msgstr ""
"Le fait d’utiliser le logiciel `ord` supprimera une grande partie de la "
"complexité que nous décrivons ici, car il est capable de gérer "
"automatiquement et en toute sécurité l’envoi d’inscriptions de manière "
"simple."

#: src\guides/collecting/sparrow-wallet.md:105
msgid "⚠️⚠️ Additional Warning ⚠️⚠️"
msgstr "⚠️⚠️ Avertissement supplémentaire ⚠️⚠️"

#: src\guides/collecting/sparrow-wallet.md:106
msgid ""
"Don't use your sparrow inscriptions wallet to do general sends of non-"
"inscription bitcoin. You can setup a separate wallet in sparrow if you need "
"to do normal bitcoin transactions, and keep your inscriptions wallet "
"separate."
msgstr ""
"N’utilisez pas votre portefeuille d’inscriptions Sparrow pour effectuer des "
"transactions de bitcoins qui n’impliquent pas des inscriptions. Vous pouvez "
"créer un portefeuille séparé dans Sparrow pour gérer vos transactions "
"régulières de bitcoins, et maintenir votre portefeuille d’inscriptions "
"séparé."

#: src\guides/collecting/sparrow-wallet.md:108
msgid "Bitcoin's UTXO model"
msgstr "Le modèle UTXO de Bitcoin"

#: src\guides/collecting/sparrow-wallet.md:109
msgid ""
"Before sending any transaction it's important that you have a good mental "
"model for bitcoin's Unspent Transaction Output (UTXO) system. The way "
"Bitcoin works is fundamentally different to many other blockchains such as "
"Ethereum. In Ethereum generally you have a single address in which you store "
"ETH, and you cannot differentiate between any of the ETH -  it is just all a "
"single value of the total amount in that address. Bitcoin works very "
"differently in that we generate a new address in the wallet for each "
"receive, and every time you receive sats to an address in your wallet you "
"are creating a new UTXO. Each UTXO can be seen and managed individually. You "
"can select specific UTXO's which you want to spend, and you can choose not "
"to spend certain UTXO's."
msgstr ""
"Avant d’envoyer une transaction, il est important que vous ayez une bonne "
"compréhension du système UTXO (Unspent Transaction Output) de Bitcoin. Le "
"fonctionnement du Bitcoin est fondamentalement différent de celui de "
"nombreuses autres blockchains telles que Ethereum. Sur Ethereum, vous avez "
"généralement une seule adresse dans laquelle vous stockez l’ETH, et vous ne "
"pouvez pas faire de distinction entre chacun des ETH – il s’agit simplement "
"d’une valeur unique du montant total dans cette adresse. Bitcoin fonctionne "
"très différemment dans la mesure où nous générons une nouvelle adresse dans "
"le portefeuille pour chaque réception, et chaque fois que vous recevez des "
"sats à une adresse de votre portefeuille, vous créez un nouvel UTXO. Chaque "
"UTXO peut être consulté et géré individuellement. Vous pouvez sélectionner "
"des UTXOs spécifiques que vous souhaitez dépenser, et vous pouvez choisir de "
"ne pas dépenser certains UTXOs."

#: src\guides/collecting/sparrow-wallet.md:111
msgid ""
"Some Bitcoin wallets do not expose this level of detail, and they just show "
"you a single summed up value of all the bitcoin in your wallet. However, "
"when sending inscriptions it is important that you use a wallet like Sparrow "
"which allows for UTXO control."
msgstr ""
"Certains portefeuilles Bitcoin n’exposent pas ce niveau de détail et se "
"contentent d’afficher une valeur unique correspondant à l’ensemble des "
"bitcoins dans votre portefeuille. Cependant, lorsque vous envoyez des "
"inscriptions, il est important que vous utilisiez un portefeuille comme "
"Sparrow qui permet le contrôle d’UTXOs."

#: src\guides/collecting/sparrow-wallet.md:113
msgid "Inspecting your inscription before sending"
msgstr "Inspecter son inscription avant de l’envoyer"

#: src\guides/collecting/sparrow-wallet.md:114
msgid ""
"Like we have previously described inscriptions are inscribed onto sats, and "
"sats are stored within UTXOs. UTXO's are a collection of satoshis with some "
"particular value of the number of satoshis (the output value). Usually (but "
"not always) the inscription will be inscribed on the first satoshi in the "
"UTXO."
msgstr ""
"Comme décrit ci-dessus, les inscriptions sont inscrites sur des sats, et les "
"sats sont stockés dans des UTXOs. Les UTXOs sont une collection de satoshis "
"avec une valeur particulière du nombre de satoshis (la valeur de sortie). En "
"général (mais pas toujours), l’inscription est inscrite sur le premier "
"satoshi de l’UTXO."

#: src\guides/collecting/sparrow-wallet.md:116
msgid ""
"When inspecting your inscription before sending the main thing you will want "
"to check is which satoshi in the UTXO your inscription is inscribed on."
msgstr ""
"Lorsque vous inspectez votre inscription avant de l’envoyer, la principale "
"chose à vérifier est sur quel satoshi de l’UTXO votre inscription est "
"inscrite."

#: src\guides/collecting/sparrow-wallet.md:118
msgid ""
"To do this, you can follow the [Validating / Viewing Received Inscriptions]"
"(./sparrow-wallet.md#validating--viewing-received-inscriptions) described "
"above to find the inscription page for your inscription on ordinals.com"
msgstr ""
"Pour ce faire, vous pouvez suivre la procédure de [validation / "
"visualisation des inscriptions reçues](./sparrow-wallet.md#validating--"
"viewing-received-inscriptions) décrite ci-dessus pour trouver la page "
"d’inscription de votre inscription sur ordinals.com"

#: src\guides/collecting/sparrow-wallet.md:120
msgid ""
"There you will find some metadata about your inscription which looks like "
"the following:"
msgstr ""
"Vous y trouverez des métadonnées sur votre inscription qui ressemblent à ce "
"qui suit :"

#: src\guides/collecting/sparrow-wallet.md:122
msgid "![](images/sending_01.png)"
msgstr "![](images/sending_01.png)"

#: src\guides/collecting/sparrow-wallet.md:124
msgid "There is a few of important things to check here:"
msgstr "Il y a plusieurs points importants à vérifier à ce stade :"

#: src\guides/collecting/sparrow-wallet.md:125
msgid ""
"The `output` identifier matches the identifier of the UTXO you are going to "
"send"
msgstr ""
"L’identifiant `output` correspond à l’identifiant de l’UTXO que vous allez "
"envoyer."

#: src\guides/collecting/sparrow-wallet.md:126
msgid ""
"The `offset` of the inscription is `0` (this means that the inscription is "
"located on the first sat in the UTXO)"
msgstr ""
"Le `offset` (déplacement) de l’inscription correspond à `0` (cela signifie "
"que l’inscription est située sur le premier sat de l’UTXO)"

#: src\guides/collecting/sparrow-wallet.md:127
msgid ""
"the `output_value` has enough sats to cover the transaction fee (postage) "
"for sending the transaction. The exact amount you will need depends on the "
"fee rate you will select for the transaction"
msgstr ""
"la valeur `output_value` contient suffisamment de sats pour couvrir les "
"frais de transaction (postage) liés à l’envoi de la transaction. Le montant "
"exact dont vous aurez besoin dépend du taux de frais que vous choisirez pour "
"la transaction"

#: src\guides/collecting/sparrow-wallet.md:129
msgid ""
"If all of the above are true for your inscription, it should be safe for you "
"to send it using the method below."
msgstr ""
"Si tous les points ci-dessus s’appliquent à votre inscription, vous pouvez "
"l’envoyer en toute sécurité en utilisant la méthode ci-dessous."

#: src\guides/collecting/sparrow-wallet.md:131
msgid ""
"⚠️⚠️ Be very careful sending your inscription particularly if the `offset` "
"value is not `0`. It is not recommended to use this method if that is the "
"case, as doing so you could accidentally send your inscription to a bitcoin "
"miner unless you know what you are doing."
msgstr ""
"⚠️⚠️ Soyez très prudent lorsque vous envoyez votre inscription, en particulier "
"si la valeur `offset` est différente de 0. Il n’est pas recommandé "
"d’utiliser cette méthode si c’est le cas, car vous pourriez accidentellement "
"envoyer votre inscription à un mineur de bitcoins, à moins que vous ne "
"sachiez ce que vous faites."

#: src\guides/collecting/sparrow-wallet.md:133
msgid "Sending your inscription"
msgstr "Envoyer votre inscription"

#: src\guides/collecting/sparrow-wallet.md:134
msgid ""
"To send an inscription navigate to the `UTXOs` tab, and find the UTXO which "
"you previously validated contains your inscription."
msgstr ""
"Pour envoyer une inscription, allez dans l’onglet `UTXOs` et recherchez "
"l’UTXO que vous avez validé précédemment comme contenant votre inscription."

#: src\guides/collecting/sparrow-wallet.md:136
msgid ""
"If you previously froze the UXTO you will need to right-click on it and "
"unfreeze it."
msgstr ""
"Si vous avez précédemment gelé cet UTXO, vous devrez faire un clic droit "
"dessus et le dégeler."

#: src\guides/collecting/sparrow-wallet.md:138
msgid ""
"Select the UTXO you want to send, and ensure that is the _only_ UTXO is "
"selected. You should see `UTXOs 1/1` in the interface. Once you are sure "
"this is the case you can hit `Send Selected`."
msgstr ""
"Sélectionnez l’UTXO que vous souhaitez envoyer et assurez-vous que c’est le "
"_seul_ UTXO sélectionné. Vous devriez voir `UTXOs 1/1` dans l’interface. Une "
"fois que vous êtes absolument sûr d’avoir sélectionné le bon UTXO, vous "
"pouvez cliquer sur `Send Selected` (envoyer la sélection)."

#: src\guides/collecting/sparrow-wallet.md:140
msgid "![](images/sending_02.png)"
msgstr "![](images/sending_02.png)"

#: src\guides/collecting/sparrow-wallet.md:142
msgid ""
"You will then be presented with the transaction construction interface. "
"There is a few things you need to check here to make sure that this is a "
"safe send:"
msgstr ""
"Vous verrez alors apparaître l’interface de construction de transactions. Il "
"y a quelques points que vous devez vérifier ici pour vous assurer que cet "
"envoi est sûr :"

#: src\guides/collecting/sparrow-wallet.md:144
msgid ""
"The transaction should have only 1 input, and this should be the UTXO with "
"the label you want to send"
msgstr ""
"La transaction ne doit avoir que 1 input (entrée), et celle-ci doit être "
"l’UTXO avec l’étiquette que vous voulez envoyer."

#: src\guides/collecting/sparrow-wallet.md:145
msgid ""
"The transaction should have only 1 output, which is the address/label where "
"you want to send the inscription"
msgstr ""
"La transaction ne doit avoir que 1 output (sortie), qui est l’adresse/"
"l’étiquette où vous voulez envoyer l’inscription"

#: src\guides/collecting/sparrow-wallet.md:147
msgid ""
"If your transaction looks any different, for example you have multiple "
"inputs, or multiple outputs then this may not be a safe transfer of your "
"inscription, and you should abandon sending until you understand more, or "
"can import into the `ord` wallet."
msgstr ""
"Si votre transaction semble différente, par exemple si elle a plusieurs "
"entrées ou plusieurs sorties, il se peut que le transfert de votre "
"inscription ne soit pas sûr et que vous deviez renoncer à l’envoyer jusqu’à "
"ce que vous en sachiez plus ou jusqu’à ce que vous puissiez l’importer dans "
"le portefeuille `ord`."

#: src\guides/collecting/sparrow-wallet.md:149
msgid ""
"You should set an appropriate transaction fee, Sparrow will usually "
"recommend a reasonable one, but you can also check [mempool.space](https://"
"mempool.space) to see what the recommended fee rate is for sending a "
"transaction."
msgstr ""
"Vous devez fixer des frais de transaction appropriés. Sparrow en recommande "
"généralement des raisonnables, mais vous pouvez également consulter [mempool."
"space](https://mempool.space) pour voir quel taux de frais est recommandé "
"pour l’envoi d’une transaction."

#: src\guides/collecting/sparrow-wallet.md:151
msgid ""
"You should add a label for the recipient address, a label like `alice "
"address for inscription #123` would be ideal."
msgstr ""
"Vous devriez ajouter une étiquette pour l’adresse du destinataire ; une "
"étiquette telle que `alice address for inscription #123` (adresse Alice pour "
"inscription #123) serait idéal."

#: src\guides/collecting/sparrow-wallet.md:153
msgid ""
"Once you have checked the transaction is a safe transaction using the checks "
"above, and you are confident to send it you can click `Create Transaction`."
msgstr ""
"Une fois que vous avez vérifié que la transaction est sûre en utilisant les "
"critères mentionnés ci-dessus, et que vous êtes confiant pour l’envoyer, "
"vous pouvez cliquer sur `Create Transaction` (créer transaction)."

#: src\guides/collecting/sparrow-wallet.md:155
msgid "![](images/sending_03.png)"
msgstr "![](images/sending_03.png)"

#: src\guides/collecting/sparrow-wallet.md:157
msgid ""
"Here again you can double check that your transaction looks safe, and once "
"you are confident you can click `Finalize Transaction for Signing`."
msgstr ""
"Ici, vous pouvez à nouveau vérifier que votre transaction semble sûre et, "
"une fois que vous êtes sûr de vous, vous pouvez cliquer sur `Finalize "
"Transaction for Signing` (Finaliser la transaction pour la signer)."

#: src\guides/collecting/sparrow-wallet.md:159
msgid "![](images/sending_04.png)"
msgstr "![](images/sending_04.png)"

#: src\guides/collecting/sparrow-wallet.md:161
msgid "Here you can triple check everything before hitting `Sign`."
msgstr ""
"Ici, vous pouvez tout vérifier une troisième fois avant de cliquer sur "
"`Sign` (Signer)."

#: src\guides/collecting/sparrow-wallet.md:163
msgid "![](images/sending_05.png)"
msgstr "![](images/sending_05.png)"

#: src\guides/collecting/sparrow-wallet.md:165
msgid ""
"And then actually you get very very last chance to check everything before "
"hitting `Broadcast Transaction`. Once you broadcast the transaction it is "
"sent to the bitcoin network, and starts being propagated into the mempool."
msgstr ""
"Ensuite, vous avez en réalité une toute dernière chance de tout vérifier "
"avant de cliquer sur `Broadcast Transaction` (Diffuser la transaction). Une "
"fois la transaction diffusée, elle est envoyée au réseau Bitcoin et commence "
"à se propager dans le mempool."

#: src\guides/collecting/sparrow-wallet.md:167
msgid "![](images/sending_06.png)"
msgstr "![](images/sending_06.png)"

#: src\guides/collecting/sparrow-wallet.md:169
msgid ""
"If you want to track the status of your transaction you can copy the "
"`Transaction Id (Txid)` and paste that into [mempool.space](https://mempool."
"space)"
msgstr ""
"Si vous souhaitez suivre l’état de votre transaction, vous pouvez copier "
"`Transaction Id (Txid)` (l’identifiant de transaction) et le coller dans "
"[mempool.space](https://mempool.space)"

#: src\guides/collecting/sparrow-wallet.md:171
msgid ""
"Once the transaction has confirmed you can check the inscription page on "
"[ordinals.com](https://ordinals.com) to validate that it has moved to the "
"new output location and address."
msgstr ""
"Une fois la transaction confirmée, vous pouvez consulter la page "
"d’inscriptions sur [ordinals.com](https://ordinals.com) pour vérifier "
"qu’elle a bien été transférée vers le nouvel emplacement de sortie et la "
"nouvelle adresse."

#: src\guides/collecting/sparrow-wallet.md:175
msgid ""
"Sparrow wallet is not showing a transaction/UTXO, but I can see it on "
"mempool.space!"
msgstr ""
"Le portefeuille Sparrow n’affiche pas une transaction/UTXO, mais je peux la "
"voir sur mempool.space !"

#: src\guides/collecting/sparrow-wallet.md:177
msgid ""
"Make sure that your wallet is connected to a bitcoin node. To validate this, "
"head into the `Preferences`\\-> `Server` settings, and click `Edit Existing "
"Connection`."
msgstr ""
"Assurez-vous que votre portefeuille est connecté à un nœud Bitcoin. Pour "
"valider cela, allez dans les paramètres `Preferences`\\-> `Server`, et "
"cliquez sur `Edit Existing Connection` (Modifier connexion existante)."

#: src\guides/collecting/sparrow-wallet.md:179
msgid "![](images/troubleshooting_01.png)"
msgstr "![](images/troubleshooting_01.png)"

#: src\guides/collecting/sparrow-wallet.md:181
msgid ""
"From there you can select a node and click `Test Connection` to validate "
"that Sparrow is able to connect successfully."
msgstr ""
"De là, vous pouvez sélectionner un nœud et cliquer sur `Test Connection` "
"(Tester connexion) pour valider que Sparrow est capable de se connecter avec "
"succès."

#: src\guides/collecting/sparrow-wallet.md:183
msgid "![](images/troubleshooting_02.png)"
msgstr "![](images/troubleshooting_02.png)"

#: src\guides/testing.md:4
msgid ""
"Ord can be tested using the following flags to specify the test network. For "
"more information on running Bitcoin Core for testing, see [Bitcoin's "
"developer documentation](https://developer.bitcoin.org/examples/testing."
"html)."
msgstr ""
"Ord peut être testé en utilisant les drapeaux suivants pour spécifier le "
"réseau de test. Pour plus d'informations sur l'exécution de Bitcoin Core en "
"mode test, consultez la [documentation pour développeurs de Bitcoin](https://"
"developer.bitcoin.org/examples/testing.html)."

#: src\guides/testing.md:7
msgid ""
"Most `ord` commands in [inscriptions](inscriptions.md) and [explorer]"
"(explorer.md) can be run with the following network flags:"
msgstr ""
"La plupart des commandes `ord` dans les sections [inscriptions](inscriptions."
"md) et [explorateur](explorer.md) peuvent être exécutées avec les drapeaux "
"de réseau suivants :"

#: src\guides/testing.md:10
msgid "Network"
msgstr "Réseau"

#: src\guides/testing.md:10
msgid "Flag"
msgstr "Drapeau"

#: src\guides/testing.md:12
msgid "Testnet"
msgstr "Testnet"

#: src\guides/testing.md:12
msgid "`--testnet` or `-t`"
msgstr "`--testnet` ou `-t`"

#: src\guides/testing.md:13
msgid "Signet"
msgstr "Signet"

#: src\guides/testing.md:13
msgid "`--signet` or `-s`"
msgstr "`--signet` ou `-s`"

#: src\guides/testing.md:14
msgid "Regtest"
msgstr "Regtest"

#: src\guides/testing.md:14
msgid "`--regtest` or `-r`"
msgstr "`--regtest` ou `-r`"

#: src\guides/testing.md:16
msgid "Regtest doesn't require downloading the blockchain or indexing ord."
msgstr ""
"Regtest ne requiert pas le téléchargement de la blockchain ou l'indexation "
"d'ord."

#: src\guides/testing.md:21
msgid "Run bitcoind in regtest with:"
msgstr "Exécutez bitcoind sur regtest avec :"

#: src\guides/testing.md:25
msgid "Create a wallet in regtest with:"
msgstr "Créez un portefeuille sur regtest avec :"

#: src\guides/testing.md:29
msgid "Get a regtest receive address with:"
msgstr "Obtenez une adresse de réception sur regtest avec :"

#: src\guides/testing.md:33
msgid "Mine 101 blocks (to unlock the coinbase) with:"
msgstr "Minez 101 blocs (pour débloquer la récompense du coinbase) avec :"

#: src\guides/testing.md:37
msgid "Inscribe in regtest with:"
msgstr "Inscrivez sur regtest avec :"

#: src\guides/testing.md:41
msgid "Mine the inscription with:"
msgstr "Minez l’inscription avec :"

#: src\guides/testing.md:45
msgid "View the inscription in the regtest explorer:"
msgstr "Consultez l’inscription dans l’explorateur regtest :"

#: src\guides/testing.md:50
msgid "Testing Recursion"
msgstr "Test de récursion"

#: src\guides/testing.md:53
msgid ""
"When testing out [recursion](../inscriptions/recursion.md), inscribe the "
"dependencies first (example with [p5.js](https://p5js.org)):"
msgstr ""
"Lorsque vous testez la [récursion](../inscriptions/recursion.md), inscrivez "
"d'abord les dépendances (exemple avec [p5.js](https://p5js.org)) :"

#: src\guides/testing.md:58
msgid ""
"This should return a `inscription_id` which you can then reference in your "
"recursive inscription."
msgstr ""
"Cela devrait renvoyer un `inscription_id` (id d’inscription) que vous pouvez "
"ensuite référencer dans votre inscription récursive."

#: src\guides/testing.md:61
msgid ""
"ATTENTION: These ids will be different when inscribing on mainnet or signet, "
"so be sure to change those in your recursive inscription for each chain."
msgstr ""
"ATTENTION : Ces ids seront différents si vous inscrivez sur mainnet ou "
"signet, assurez-vous donc de les changer dans votre inscription récursive "
"pour chaque chaîne."

#: src\guides/testing.md:65
msgid "Then you can inscribe your recursive inscription with:"
msgstr "Ensuite, vous pouvez inscrire votre inscription récursive avec :"

#: src\guides/testing.md:69
msgid "Finally you will have to mine some blocks and start the server:"
msgstr "Enfin, vous devrez miner quelques blocs et démarrer le serveur :"

#: src\guides/moderation.md:4
msgid ""
"`ord` includes a block explorer, which you can run locally with `ord server`."
msgstr ""
"`ord` comprend un explorateur de blocs, que vous pouvez exécuter localement "
"avec `ord server`."

#: src\guides/moderation.md:6
msgid ""
"The block explorer allows viewing inscriptions. Inscriptions are user-"
"generated content, which may be objectionable or unlawful."
msgstr ""
"L'explorateur de blocs permet de visualiser les inscriptions. Les "
"inscriptions sont des contenus générés par les utilisateurs, qui peuvent "
"être répréhensibles ou illicites."

#: src\guides/moderation.md:9
msgid ""
"It is the responsibility of each individual who runs an ordinal block "
"explorer instance to understand their responsibilities with respect to "
"unlawful content, and decide what moderation policy is appropriate for their "
"instance."
msgstr ""
"Il incombe à chaque personne qui lance une instance de l'explorateur de "
"blocs ordinal de comprendre ses responsabilités en matière de contenu "
"illicite et de décider de la politique de modération appropriée pour son "
"instance."

#: src\guides/moderation.md:13
msgid ""
"In order to prevent particular inscriptions from being displayed on an `ord` "
"instance, they can be included in a YAML config file, which is loaded with "
"the `--config` option."
msgstr ""
"Afin d'empêcher que certaines inscriptions soient affichées sur une instance "
"`ord`, elles peuvent être incluses dans un fichier de configuration YAML, "
"qui est chargé avec l'option `--config`."

#: src\guides/moderation.md:17
msgid ""
"To hide inscriptions, first create a config file, with the inscription ID "
"you want to hide:"
msgstr ""
"Pour masquer des inscriptions, créez d'abord un fichier de configuration, "
"avec l'identifiant de l'inscription que vous souhaitez masquer :"

#: src\guides/moderation.md:25
msgid ""
"The suggested name for `ord` config files is `ord.yaml`, but any filename "
"can be used."
msgstr ""
"Le nom suggéré pour les fichiers de configuration `ord` est `ord.yaml`, mais "
"n'importe quel nom de fichier peut être utilisé."

#: src\guides/moderation.md:28
msgid "Then pass the file to `--config` when starting the server:"
msgstr ""
"Passez ensuite le fichier à `--config` lorsque vous démarrez le serveur :"

#: src\guides/moderation.md:30
msgid "`ord --config ord.yaml server`"
msgstr "`ord --config ord.yaml server`"

#: src\guides/moderation.md:32
msgid ""
"Note that the `--config` option comes after `ord` but before the `server` "
"subcommand."
msgstr ""
"Notez que l'option `--config` vient après `ord` mais avant la sous-commande "
"`server`."

#: src\guides/moderation.md:35
msgid "`ord` must be restarted in to load changes to the config file."
msgstr ""
"`ord` doit être redémarré pour que les modifications apportées au fichier de "
"configuration soient prises en compte."

#: src\guides/moderation.md:37
msgid "`ordinals.com`"
msgstr "`ordinals.com`"

#: src\guides/moderation.md:40
msgid ""
"The `ordinals.com` instances use `systemd` to run the `ord server` service, "
"which is called `ord`, with a config file located at `/var/lib/ord/ord.yaml`."
msgstr ""
"Les instances de `ordinals.com` utilisent `systemd` pour exécuter le service "
"`ord server`, appelé `ord`, avec un fichier de configuration situé dans `/"
"var/lib/ord/ord.yaml`."

#: src\guides/moderation.md:43
msgid "To hide an inscription on `ordinals.com`:"
msgstr "Pour masquer une inscription sur `ordinals.com` :"

#: src\guides/moderation.md:45
msgid "SSH into the server"
msgstr "Connectez-vous en SSH au serveur"

#: src\guides/moderation.md:46
msgid "Add the inscription ID to `/var/lib/ord/ord.yaml`"
msgstr "Ajoutez l'identifiant de l'inscription à `/var/lib/ord/ord.yaml`"

#: src\guides/moderation.md:47
msgid "Restart the service with `systemctl restart ord`"
msgstr "Redémarrez le service avec `systemctl restart ord`"

#: src\guides/moderation.md:48
msgid "Monitor the restart with `journalctl -u ord`"
msgstr "Surveillez le redémarrage avec `journalctl -u ord`"

#: src\guides/moderation.md:50
msgid ""
"Currently, `ord` is slow to restart, so the site will not come back online "
"immediately."
msgstr ""
"Actuellement, `ord` est lent à redémarrer, le site ne sera donc pas remis en "
"ligne immédiatement."

#: src\guides/reindexing.md:4
msgid ""
"Sometimes the `ord` database must be reindexed, which means deleting the "
"database and restarting the indexing process with either `ord index update` "
"or `ord server`. Reasons to reindex are:"
msgstr ""
"Parfois, la base de données `ord` doit être réindexée, ce qui signifie qu'il "
"faut supprimer la base de données et relancer le processus d'indexation, "
"soit avec `ord index update` , soit avec `ord server`. Les raisons qui "
"justifient la réindexation sont les suivantes :"

#: src\guides/reindexing.md:8
msgid "A new major release of ord, which changes the database scheme"
msgstr ""
"Il existe une nouvelle version importante d’ord, qui modifie le schéma de la "
"base de données"

#: src\guides/reindexing.md:9
msgid "The database got corrupted somehow"
msgstr "La base de données a été corrompue d'une certaine manière"

#: src\guides/reindexing.md:11
msgid ""
"The database `ord` uses is called [redb](https://github.com/cberner/redb), "
"so we give the index the default file name `index.redb`. By default we store "
"this file in different locations depending on your operating system."
msgstr ""
"La base de données utilisée par ord s'appelle [redb](https://github.com/"
"cberner/redb), et l'index se voit donc attribué le nom de fichier par défaut "
"`index.redb`. Ce fichier est sauvegardé par défaut à différents endroits en "
"fonction de votre système d'exploitation."

#: src\guides/reindexing.md:15
msgid "Platform"
msgstr "Plateforme"

#: src\guides/reindexing.md:15
msgid "Value"
msgstr "Valeur"

#: src\guides/reindexing.md:17
msgid "Linux"
msgstr "Linux"

#: src\guides/reindexing.md:17
msgid "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"
msgstr "`$XDG_DATA_HOME`/ord ou `$HOME`/.local/share/ord"

#: src\guides/reindexing.md:17
msgid "/home/<USER>/.local/share/ord"
msgstr "/home/<USER>/.local/share/ord"

#: src\guides/reindexing.md:18
msgid "macOS"
msgstr "macOS"

#: src\guides/reindexing.md:18
msgid "`$HOME`/Library/Application Support/ord"
msgstr "`$HOME`/Library/Application Support/ord"

#: src\guides/reindexing.md:18
msgid "/Users/<USER>/Library/Application Support/ord"
msgstr "/Users/<USER>/Library/Application Support/ord"

#: src\guides/reindexing.md:19
msgid "Windows"
msgstr "Windows"

#: src\guides/reindexing.md:19
msgid "`{FOLDERID_RoamingAppData}`\\\\ord"
msgstr "`{FOLDERID_RoamingAppData}`\\\\ord"

#: src\guides/reindexing.md:19
msgid "C:\\Users\\<USER>\\AppData\\Roaming\\ord"
msgstr "C:\\Users\\<USER>\\AppData\\Roaming\\ord"

#: src\guides/reindexing.md:21
msgid ""
"So to delete the database and reindex on MacOS you would have to run the "
"following commands in the terminal:"
msgstr ""
"Ainsi, pour supprimer la base de données et la réindexer sur MacOS, vous "
"devriez exécuter les commandes suivantes dans le terminal :"

#: src\guides/reindexing.md:29
msgid ""
"You can of course also set the location of the data directory yourself with "
"`ord --datadir <DIR> index update` or give it a specific filename and path "
"with `ord --index <FILENAME> index update`."
msgstr ""
"Vous pouvez bien sûr aussi définir `ord --datadir <DIR> index update` ou "
"lui donner un nom de fichier et un chemin d’accès spécifiques avec `ord --"
"index <FILENAME> index update`."

#: src\bounties.md:1
msgid "Ordinal Bounty Hunting Hints"
msgstr "Conseils pour la chasse aux récompenses d’Ordinals"

#: src\bounties.md:4
msgid ""
"The `ord` wallet can send and receive specific satoshis. Additionally, "
"ordinal theory is extremely simple. A clever hacker should be able to write "
"code from scratch to manipulate satoshis using ordinal theory in no time."
msgstr ""
"Le portefeuille `ord` peut envoyer et recevoir des satoshis spécifiques. De "
"plus, la théorie ordinale est extrêmement simple. Un hacker ingénieux "
"devrait être capable d'écrire du code à partir de zéro pour manipuler des "
"satoshis en utilisant la théorie ordinale en un rien de temps."

#: src\bounties.md:8
msgid ""
"For more information about ordinal theory, check out the [FAQ](./faq.md) for "
"an overview, the [BIP](https://github.com/ordinals/ord/blob/master/bip."
"mediawiki) for the technical details, and the [ord repo](https://github.com/"
"ordinals/ord) for the `ord` wallet and block explorer."
msgstr ""
"Pour plus d'informations sur la théorie ordinale, consultez la section [FAQ]"
"(./faq.md) pour une vue d'ensemble, le [BIP](https://github.com/ordinals/ord/"
"blob/master/bip.mediawiki) pour les détails techniques, et le [référentiel "
"ord](https://github.com/ordinals/ord) pour le portefeuille `ord` et "
"l'explorateur de blocs."

#: src\bounties.md:14
msgid ""
"Satoshi was the original developer of ordinal theory. However, he knew that "
"others would consider it heretical and dangerous, so he hid his knowledge, "
"and it was lost to the sands of time. This potent theory is only now being "
"rediscovered. You can help by researching rare satoshis."
msgstr ""
"Satoshi a été le premier à développer la théorie ordinale. Cependant, il "
"savait que d'autres la considéreraient comme hérétique et dangereuse, il a "
"donc dissimulé ses connaissances, qui ont fini par disparaître avec le "
"temps. Ce n’est que maintenant que cette théorie puissante est redécouverte. "
"Vous pouvez y contribuer en recherchant des satoshis rares."

#: src\bounties.md:19
msgid "Good luck and godspeed!"
msgstr "Bonne chance et que la réussite vous accompagne !"

#: src\bounty/0.md:1
msgid "Ordinal Bounty 0"
msgstr "Récompense Ordinal 0"

#: src\bounty/0.md:4 src\bounty/1.md:4 src\bounty/2.md:4 src\bounty/3.md:4
msgid "Criteria"
msgstr "Critères"

#: src\bounty/0.md:7
msgid ""
"Send a sat whose ordinal number ends with a zero to the submission address:"
msgstr ""
"Envoyez un sat dont le nombre ordinal se termine par un zéro à l'adresse de "
"soumission :"

#: src\bounty/0.md:9
msgid "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"
msgstr "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"

#: src\bounty/0.md:11
msgid "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"
msgstr "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"

#: src\bounty/0.md:13
msgid "The sat must be the first sat of the output you send."
msgstr "Le sat doit être le premier sat de la sortie que vous envoyez."

#: src\bounty/0.md:15 src\bounty/1.md:14 src\bounty/2.md:15 src\bounty/3.md:63
msgid "Reward"
msgstr "Récompense"

#: src\bounty/0.md:18
msgid "100,000 sats"
msgstr "100,000 sats"

#: src\bounty/0.md:20 src\bounty/1.md:19 src\bounty/2.md:20 src\bounty/3.md:70
msgid "Submission Address"
msgstr "Adresse de soumission"

#: src\bounty/0.md:23
msgid ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/"
"address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"
msgstr ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/"
"address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"

#: src\bounty/0.md:25 src\bounty/1.md:24 src\bounty/2.md:25 src\bounty/3.md:75
msgid "Status"
msgstr "État"

#: src\bounty/0.md:28
msgid ""
"Claimed by [@count_null](https://twitter.com/rodarmor/"
"status/1560793241473400833)!"
msgstr ""
"Réclamé par [@count_null](https://twitter.com/rodarmor/"
"status/1560793241473400833) !"

#: src\bounty/1.md:1
msgid "Ordinal Bounty 1"
msgstr "Récompense Ordinal 1"

#: src\bounty/1.md:7
msgid ""
"The transaction that submits a UTXO containing the oldest sat, i.e., that "
"with the lowest number, amongst all submitted UTXOs will be judged the "
"winner."
msgstr ""
"La transaction qui soumet un UTXO contenant le sat le plus ancien, c'est-à-"
"dire celui avec le numéro le plus bas parmi tous les UTXOs soumis, sera "
"jugée gagnant."

#: src\bounty/1.md:10
msgid ""
"The bounty is open for submissions until block 753984—the first block of "
"difficulty adjustment period 374. Submissions included in block 753984 or "
"later will not be considered."
msgstr ""
"L'appel à candidatures pour la récompense restera ouvert jusqu'au bloc "
"753984, le premier bloc de la période d'ajustement de la difficulté 374. Les "
"candidatures soumises à partir du bloc 753984 ou ultérieurement ne seront "
"pas prises en compte."

#: src\bounty/1.md:17
msgid "200,000 sats"
msgstr "200 000 sats"

#: src\bounty/1.md:22
msgid ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/"
"address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"
msgstr ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/"
"address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"

#: src\bounty/1.md:27
msgid ""
"Claimed by [@ordinalsindex](https://twitter.com/rodarmor/"
"status/1569883266508853251)!"
msgstr ""
"Réclamé par [@ordinalsindex](https://twitter.com/rodarmor/"
"status/1569883266508853251) !"

#: src\bounty/2.md:1
msgid "Ordinal Bounty 2"
msgstr "Récompense Ordinal 2"

#: src\bounty/2.md:7
msgid "Send an "
msgstr "Envoyez un "

#: src\bounty/2.md:7
msgid "uncommon"
msgstr "sat peu commun"

#: src\bounty/2.md:7
msgid " sat to the submission address:"
msgstr " à l'adresse de soumission :"

#: src\bounty/2.md:9
msgid "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"
msgstr "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"

#: src\bounty/2.md:11
msgid "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"
msgstr "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"

#: src\bounty/2.md:13
msgid ""
"Confirm that the submission address has not received transactions before "
"submitting your entry. Only the first successful submission will be rewarded."
msgstr ""
"Confirmez que l'adresse de soumission n'a pas reçu de transactions avant de "
"soumettre votre candidature. Seule la première soumission réussie sera "
"récompensée."

#: src\bounty/2.md:18
msgid "300,000 sats"
msgstr "300 000 sats"

#: src\bounty/2.md:23
msgid ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/"
"address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"
msgstr ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/"
"address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"

#: src\bounty/2.md:28
msgid ""
"Claimed by [@utxoset](https://twitter.com/rodarmor/"
"status/1582424455615172608)!"
msgstr ""
"Réclamé par [@utxoset](https://twitter.com/rodarmor/"
"status/1582424455615172608) !"

#: src\bounty/3.md:1
msgid "Ordinal Bounty 3"
msgstr "Récompense Ordinal 3"

#: src\bounty/3.md:7
msgid ""
"Ordinal bounty 3 has two parts, both of which are based on _ordinal names_. "
"Ordinal names are a modified base-26 encoding of ordinal numbers. To avoid "
"locking short names inside the unspendable genesis block coinbase reward, "
"ordinal names get _shorter_ as the ordinal number gets _longer_. The name of "
"sat 0, the first sat to be mined is `nvtdijuwxlp` and the name of sat "
"2,099,999,997,689,999, the last sat to be mined, is `a`."
msgstr ""
"La récompense Ordinal 3 se compose de deux parties, toutes deux basées sur "
"les _noms ordinaux_. Les noms ordinaux sont un encodage en base-26 modifié "
"des nombres ordinaux. Pour éviter que les noms courts restent piégés à "
"l'intérieur du bloc de genèse et ne puissent pas être utilisés, les noms "
"ordinaux deviennent _plus courts_ à mesure que le nombre ordinal devient "
"_plus long_. Le nom du sat 0, le premier sat à être miné, est `nvtdijuwxlp` "
"et le nom du sat 2,099,999,997,689,999, le dernier sat qui sera miné, est "
"`a`."

#: src\bounty/3.md:14
msgid ""
"The bounty is open for submissions until block 840000—the first block after "
"the fourth halvening. Submissions included in block 840000 or later will not "
"be considered."
msgstr ""
"L’appel à candidatures pour la récompense restera ouvert jusqu'au bloc "
"840000, le premier bloc après le quatrième halving. Les candidatures "
"soumises à partir du bloc 840000 ou ultérieurement ne seront pas prises en "
"compte."

#: src\bounty/3.md:18
msgid ""
"Both parts use [frequency.tsv](frequency.tsv), a list of words and the "
"number of times they occur in the [Google Books Ngram dataset](http://"
"storage.googleapis.com/books/ngrams/books/datasetsv2.html). filtered to only "
"include the names of sats which will have been mined by the end of the "
"submission period, that appear at least 5000 times in the corpus."
msgstr ""
"Les deux parties utilisent [frequency.tsv](frequency.tsv), une liste de mots "
"et le nombre de fois qu'ils apparaissent dans l'ensemble de données [Google "
"Books Ngram](http://storage.googleapis.com/books/ngrams/books/datasetsv2."
"html). Ce fichier a été filtré pour n'inclure que les noms des sats qui "
"auront été minés à la fin de la période de soumission et qui apparaissent au "
"moins 5 000 fois dans le corpus."

#: src\bounty/3.md:24
msgid ""
"`frequency.tsv` is a file of tab-separated values. The first column is the "
"word, and the second is the number of times it appears in the corpus. The "
"entries are sorted from least-frequently occurring to most-frequently "
"occurring."
msgstr ""
"`frequency.tsv` est un fichier de valeurs séparées par des tabulations. La "
"première colonne est le mot, et la seconde le nombre de fois qu'il apparaît "
"dans le corpus. Les données sont organisées de manière à ce que les mots les "
"moins fréquents apparaissent en premier, suivis des mots les plus fréquents."

#: src\bounty/3.md:29
msgid ""
"`frequency.tsv` was compiled using [this program](https://github.com/casey/"
"onegrams)."
msgstr ""
"`frequency.tsv` a été compilé à l'aide de [ce programme](https://github.com/"
"casey/onegrams)."

#: src\bounty/3.md:32
msgid ""
"To search an `ord` wallet for sats with a name in `frequency.tsv`, use the "
"following [`ord`](https://github.com/ordinals/ord) command:"
msgstr ""
"Pour rechercher des sats dans un portefeuille `ord` dont le nom figure dans "
"`frequency.tsv`, utilisez la commande [`ord`](https://github.com/ordinals/"
"ord) suivante :"

#: src\bounty/3.md:39
msgid ""
"This command requires the sat index, so `--index-sats` must be passed to ord "
"when first creating the index."
msgstr ""
"Cette commande nécessite l'index sat, vous devez donc inclure le paramètre "
"`--index-sats` dans ord lorsque vous créez l'index pour la première fois."

#: src\bounty/3.md:42
msgid "Part 0"
msgstr "Partie 0"

#: src\bounty/3.md:44
msgid "_Rare sats pair best with rare words._"
msgstr "_Les sats rares s'associent mieux aux mots rares._"

#: src\bounty/3.md:46
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the lowest number of occurrences in `frequency.tsv` shall be the winner "
"of part 0."
msgstr ""
"La transaction qui soumet l'UTXO contenant le sat dont le nom apparaît avec "
"le plus petit nombre d'occurrences dans `frequency.tsv` sera le gagnant de "
"la partie 0."

#: src\bounty/3.md:50
msgid "Part 1"
msgstr "Partie 1"

#: src\bounty/3.md:52
msgid "_Popularity is the font of value._"
msgstr "_La popularité est la source de la valeur._"

#: src\bounty/3.md:54
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the highest number of occurrences in `frequency.tsv` shall be the "
"winner of part 1."
msgstr ""
"La transaction qui soumet l'UTXO contenant le sat dont le nom apparaît avec "
"le plus grand nombre d'occurrences dans `frequency.tsv` sera le gagnant de "
"la partie 1."

#: src\bounty/3.md:58
msgid "Tie Breaking"
msgstr "Critères de départage"

#: src\bounty/3.md:60
msgid ""
"In the case of a tie, where two submissions occur with the same frequency, "
"the earlier submission shall be the winner."
msgstr ""
"En cas d'égalité, lorsque deux soumissions surviennent avec la même "
"fréquence, la soumission effectuée en premier sera déclarée gagnante."

#: src\bounty/3.md:66
msgid "Part 0: 200,000 sats"
msgstr "Partie 0 : 200 000 sats"

#: src\bounty/3.md:67
msgid "Part 1: 200,000 sats"
msgstr "Partie 1 : 200 000 sats"

#: src\bounty/3.md:68
msgid "Total: 400,000 sats"
msgstr "Total : 400 000 sats"

#: src\bounty/3.md:73
msgid ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/"
"address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"
msgstr ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/"
"address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"

#: src\bounty/3.md:78
msgid "Unclaimed!"
msgstr "Pas réclamé !"
