# .env file
DB_USER="postgres"
DB_HOST="localhost"
DB_PORT="5432"
DB_DATABASE="postgres"
DB_PASSWD=""
DB_SSL="true"
DB_MAX_CONNECTIONS=50

# Bitcoin Datadir
BITCOIN_CHAIN_FOLDER="~/.bitcoin/"
COOKIE_FILE=""

# leave these empty to use .cookie file
BITCOIN_RPC_USER=""
BITCOIN_RPC_PASSWD=""
# `--rpc-url` parameter for `ord`, example: `127.0.0.1:8332`
BITCOIN_RPC_URL=""

# change to ord.exe on Windows (without ./)
ORD_BINARY="./ord"

# leave default if repository folder structure hasn't been changed
ORD_FOLDER="ord-runes/target/release/"
# relative to ord folder
ORD_DATADIR="."

NETWORK_TYPE="signet"

## reporting system settings
REPORT_TO_INDEXER="true"
REPORT_URL="https://api.opi.network/report_block"
REPORT_RETRIES="10"
# set a name for report dashboard
REPORT_NAME="opi_runes_index"

