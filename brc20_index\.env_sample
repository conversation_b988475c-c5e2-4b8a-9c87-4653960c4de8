# .env
DB_USER="postgres"
DB_HOST="localhost"
DB_PORT="5432"
DB_DATABASE="postgres"
DB_PASSWD=""

## main indexer database settings
DB_METAPROTOCOL_USER="postgres"
DB_METAPROTOCOL_HOST="localhost"
DB_METAPROTOCOL_PORT="5432"
DB_METAPROTOCOL_DATABASE="postgres"
DB_METAPROTOCOL_PASSWD=""

NETWORK_TYPE="mainnet"

## reporting system settings
REPORT_TO_INDEXER="true"
REPORT_URL="https://api.opi.network/report_block"
REPORT_RETRIES="10"
# set a name for report dashboard
REPORT_NAME="opi_brc20_index"

# create brc20_current_balances and brc20_unused_tx_inscrs tables
CREATE_EXTRA_TABLES="true"

USE_PROXY=true
PROXY_API_URL=http://list.rola.info:8088/user_get_ip_list?token=TMKbersgVA1YVShv1670853893656&type=datacenter&qty=1&time=5&country=fi&format=txt&protocol=http&filter=1