<!doctype html>
<html lang=en>
  <head>
    <meta charset=utf-8>
    <meta name=format-detection content='telephone=no'>
    <meta name=viewport content='width=device-width,initial-scale=1.0'>
    <meta property=og:title content='{{ self.content.title() }}'>
    <meta property=og:image content='{{ self.og_image() }}'>
    <meta property=twitter:card content=summary>
    <title>{{ self.content.title() }}</title>
    <link rel=alternate href=/feed.xml type=application/rss+xml title='Inscription Feed'>
    <link rel=icon href=/static/favicon.png>
    <link rel=icon href=/static/favicon.svg>
    <link rel=stylesheet href=/static/index.css>
    <link rel=stylesheet href=/static/modern-normalize.css>
    <script src=/static/index.js defer></script>
  </head>
  <body>
  <header>
    <nav>
      <a href=/ title=home>Ordinals<sup>{{ self.superscript() }}</sup></a>
      <a href=/inscriptions title=inscriptions><img class=icon src=/static/images.svg></a>
      <a href=/runes title=runes><img class=icon src=/static/rune.svg></a>
      <a href=/collections title=collections><img class=icon src=/static/diagram-project.svg></a>
      <a href=/blocks title=blocks><img class=icon src=/static/cubes.svg></a>
      <a href=/clock title=clock><img class=icon src=/static/clock.svg></a>
%% if self.config.index_sats {
      <a href=/rare.txt title=rare><img class=icon src=/static/gem.svg></a>
%% }
      <a href=https://docs.ordinals.com/ title=handbook><img class=icon src=/static/book.svg></a>
      <a href=https://github.com/ordinals/ord title=github><img class=icon src=/static/github.svg></a>
      <a href=https://discord.com/invite/ordinals title=discord><img class=icon src=/static/discord.svg></a>
      <form action=/search method=get>
        <input type=text autocapitalize=off autocomplete=off autocorrect=off name=query spellcheck=false>
        <input class=icon type=image src=/static/magnifying-glass.svg alt=Search>
      </form>
    </nav>
  </header>
  <main>
$$ Trusted(&self.content)
  </main>
  </body>
</html>
