[Unit]
After=network-online.target
Description=Bitcoin daemon
Documentation=https://github.com/bitcoin/bitcoin/blob/master/doc/init.md
Wants=network-online.target

[Service]
ConfigurationDirectory=bitcoin
ConfigurationDirectoryMode=0710
ExecStart=/usr/local/bin/bitcoind \
  -conf=/etc/bitcoin/bitcoin.conf \
  -chain=${CHAIN}
ExecStartPre=/bin/chgrp bitcoin /etc/bitcoin
Group=bitcoin
MemoryDenyWriteExecute=true
NoNewPrivileges=true
PermissionsStartOnly=true
PrivateDevices=true
PrivateTmp=true
ProtectHome=true
ProtectSystem=full
Restart=on-failure
RuntimeDirectory=bitcoind
RuntimeDirectoryMode=0710
StateDirectory=bitcoind
StateDirectoryMode=0710
TimeoutStartSec=infinity
TimeoutStopSec=600
Type=simple
User=bitcoin

[Install]
WantedBy=multi-user.target
