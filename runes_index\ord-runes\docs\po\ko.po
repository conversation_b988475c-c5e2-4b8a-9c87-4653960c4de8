msgid ""
msgstr ""
"Project-Id-Version: \n"
"POT-Creation-Date: 2023-10-11T00:34:07Z\n"
"PO-Revision-Date: 2023-10-17 17:13-0700\n"
"Last-Translator: @Inch0at3 <<EMAIL>>\n"
"Language-Team: Korean\n"
"Language: ko\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Poedit 3.4\n"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:2
#: /workspaces/ord_ko/docs/src/introduction.md:1
msgid "Introduction"
msgstr "서론"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:3
msgid "Overview"
msgstr "개요"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:4
#: /workspaces/ord_ko/docs/src/digital-artifacts.md:1
msgid "Digital Artifacts"
msgstr "디지털 아티팩트"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:5
#: /workspaces/ord_ko/docs/src/SUMMARY.md:14
#: /workspaces/ord_ko/docs/src/overview.md:221
#: /workspaces/ord_ko/docs/src/inscriptions.md:1
msgid "Inscriptions"
msgstr "인스크립션"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:6
#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:1
msgid "Metadata"
msgstr "메타 데이터"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:7
#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:1
msgid "Provenance"
msgstr "기원"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:8
#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:1
msgid "Recursion"
msgstr "리커젼"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:9
msgid "FAQ"
msgstr "자주 묻는 질문"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:10
msgid "Contributing"
msgstr "기여하는 방법"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:11
#: /workspaces/ord_ko/docs/src/donate.md:1
msgid "Donate"
msgstr "기부하는 방법"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:12
msgid "Guides"
msgstr "안내서들"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:13
msgid "Explorer"
msgstr "탐색기"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:15
#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:1
msgid "Sat Hunting"
msgstr "SAT 헌팅"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:16
#: /workspaces/ord_ko/docs/src/guides/collecting.md:1
msgid "Collecting"
msgstr "수집"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:17
#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:239
msgid "Sparrow Wallet"
msgstr "스패로우 월렛"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:18
#: /workspaces/ord_ko/docs/src/guides/testing.md:1
msgid "Testing"
msgstr "테스팅"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:19
#: /workspaces/ord_ko/docs/src/guides/moderation.md:1
msgid "Moderation"
msgstr "중재"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:20
#: /workspaces/ord_ko/docs/src/guides/reindexing.md:1
msgid "Reindexing"
msgstr "재인덱싱"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:21
msgid "Bounties"
msgstr "현상금"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:22
msgid "Bounty 0: 100,000 sats Claimed!"
msgstr "현상금 0: 100,000 SAT 획득!"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:23
msgid "Bounty 1: 200,000 sats Claimed!"
msgstr "현상금 1: 200,000 SAT 획득!"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:24
msgid "Bounty 2: 300,000 sats Claimed!"
msgstr "현상금 2: 300,000 SAT 획득!"

#: /workspaces/ord_ko/docs/src/SUMMARY.md:25
msgid "Bounty 3: 400,000 sats"
msgstr "현상금 3: 400,000 SAT"

#: /workspaces/ord_ko/docs/src/introduction.md:4
msgid ""
"This handbook is a guide to ordinal theory. Ordinal theory concerns itself "
"with satoshis, giving them individual identities and allowing them to be "
"tracked, transferred, and imbued with meaning."
msgstr ""
"이 핸드북은 오디널 이론(Ordinal Theory)에 대한 안내서다. 오디널 이론은 사토시"
"(Satoshis)에 관한 것으로, 각 사토시에 개별적인 정체성을 부여함으로써 그것들"
"을 추적과 전송할 수 있으며 그들에게 의미를 부여할 수 있다."

#: /workspaces/ord_ko/docs/src/introduction.md:8
msgid ""
"Satoshis, not bitcoin, are the atomic, native currency of the Bitcoin "
"network. One bitcoin can be sub-divided into 100,000,000 satoshis, but no "
"further."
msgstr ""
"비트코인 네트워크의 원자적 자체 통화는 비트코인이 아닌 사토시다. 1 비트코인"
"을 100,000,000 사토시로 분할할 수 있지만 그 이상은 불가능하다."

#: /workspaces/ord_ko/docs/src/introduction.md:11
msgid ""
"Ordinal theory does not require a sidechain or token aside from Bitcoin, and "
"can be used without any changes to the Bitcoin network. It works right now."
msgstr ""
"오디널 이론은 비트코인 외에 사이드체인이나 토큰을 필요로하지 않으며, 비트코"
"인 네트워크를 변경하지 않고도 사용할 수 있다. 지금 바로 사용할 수 있다."

#: /workspaces/ord_ko/docs/src/introduction.md:14
msgid ""
"Ordinal theory imbues satoshis with numismatic value, allowing them to be "
"collected and traded as curios."
msgstr ""
"오디널 이론은 사토시에 화폐학적 가치를 부여하여 수집품처럼 모으고 거래할 수 "
"있도록 해준다."

#: /workspaces/ord_ko/docs/src/introduction.md:17
msgid ""
"Individual satoshis can be inscribed with arbitrary content, creating unique "
"Bitcoin-native digital artifacts that can be held in Bitcoin wallets and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"각 사토시에 임의의 콘텐츠를 새길 수 있으며, 이로써 비트코인 지갑에 보관하고 "
"비트코인 거래를 통해 전송할 수 있는 고유한 비트코인 자체 디지털 아티팩트를 만"
"들 수 있다. 인스크립션은 비트코인만큼이나 내구성이 있고, 불변하며, 안전하고, "
"탈중앙화되어 있다."

#: /workspaces/ord_ko/docs/src/introduction.md:22
msgid ""
"Other, more unusual use-cases are possible: off-chain colored-coins, public "
"key infrastructure with key rotation, a decentralized replacement for the "
"DNS. For now though, such use-cases are speculative, and exist only in the "
"minds of fringe ordinal theorists."
msgstr ""
"더 특이한 사용 사례도 가능하다: 오프체인 컬러드 코인, 키 로테이션이 가능한 공"
"개 키 인프라, DNS에 탈중앙화된 대체. 하지만 현재로서는 이러한 사용 사례는 사"
"색에 불과하며, 비주류 오디널 이론가들의 머릿속에만 존재한다."

#: /workspaces/ord_ko/docs/src/introduction.md:27
msgid "For more details on ordinal theory, see the [overview](overview.md)."
msgstr "오디널 이론에 대한 자세한 내용은 [개요](overview.md)를 참조하자."

#: /workspaces/ord_ko/docs/src/introduction.md:29
msgid "For more details on inscriptions, see [inscriptions](inscriptions.md)."
msgstr ""
"인스크립션에 대한 자세한 내용은 [인스크립션](inscriptions.md)을 참조하자."

#: /workspaces/ord_ko/docs/src/introduction.md:31
msgid ""
"When you're ready to get your hands dirty, a good place to start is with "
"[inscriptions](guides/inscriptions.md), a curious species of digital "
"artifact enabled by ordinal theory."
msgstr ""
"직접 시도할 준비가 되었다면 오디널 이론으로 가능해진 신기한 디지털 아티팩트"
"에 한 종류인 [인스크립션](guides/inscriptions.md)이 좋은 시작점이다."

#: /workspaces/ord_ko/docs/src/introduction.md:35
msgid "Links"
msgstr "링크 모음"

#: /workspaces/ord_ko/docs/src/introduction.md:38
msgid "[GitHub](https://github.com/ordinals/ord/)"
msgstr "[깃허브](https://github.com/ordinals/ord/)"

#: /workspaces/ord_ko/docs/src/introduction.md:39
msgid "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: /workspaces/ord_ko/docs/src/introduction.md:40
msgid "[Discord](https://discord.gg/ordinals)"
msgstr "[디스코드](https://discord.gg/ordinals)"

#: /workspaces/ord_ko/docs/src/introduction.md:41
msgid "[Open Ordinals Institute Website](https://ordinals.org/)"
msgstr "[오픈 오디널스 연구소 웹사이트](https://ordinals.org/)"

#: /workspaces/ord_ko/docs/src/introduction.md:42
msgid "[Open Ordinals Institute X](https://x.com/ordinalsorg)"
msgstr "[오픈 오디널스 인스티튜트 X](https://x.com/ordinalsorg)"

#: /workspaces/ord_ko/docs/src/introduction.md:43
msgid "[Mainnet Block Explorer](https://ordinals.com)"
msgstr "[메인넷 블록 탐색기](https://ordinals.com)"

#: /workspaces/ord_ko/docs/src/introduction.md:44
msgid "[Signet Block Explorer](https://signet.ordinals.com)"
msgstr "[시그넷 블록 탐색기](https://signet.ordinals.com)"

#: /workspaces/ord_ko/docs/src/introduction.md:46
msgid "Videos"
msgstr "비디오 모음"

#: /workspaces/ord_ko/docs/src/introduction.md:49
msgid ""
"[Ordinal Theory Explained: Satoshi Serial Numbers and NFTs on Bitcoin]"
"(https://www.youtube.com/watch?v=rSS0O2KQpsI)"
msgstr ""
"[오디널 이론 설명: 사토시 일련 번호와 비트코인 NFT](https://www.youtube.com/"
"watch?v=rSS0O2KQpsI)"

#: /workspaces/ord_ko/docs/src/introduction.md:50
msgid ""
"[Ordinals Workshop with Rodarmor](https://www.youtube.com/watch?"
"v=MC_haVa6N3I)"
msgstr ""
"[Rodarmor와 함께하는 오디널스 워크샵](https://www.youtube.com/watch?"
"v=MC_haVa6N3I)"

#: /workspaces/ord_ko/docs/src/introduction.md:51
msgid ""
"[Ordinal Art: Mint Your own NFTs on Bitcoin w/ @rodarmor](https://www."
"youtube.com/watch?v=j5V33kV3iqo)"
msgstr ""
"[@rodarmor와 함께하는 오디널 아트: 비트코인에서 당신의 NFT를 발행하는 방법]"
"(https://www.youtube.com/watch?v=j5V33kV3iqo)"

#: /workspaces/ord_ko/docs/src/overview.md:1
msgid "Ordinal Theory Overview"
msgstr "오디널 이론 개요"

#: /workspaces/ord_ko/docs/src/overview.md:4
msgid ""
"Ordinals are a numbering scheme for satoshis that allows tracking and "
"transferring individual sats. These numbers are called [ordinal numbers]"
"(https://ordinals.com). Satoshis are numbered in the order in which they're "
"mined, and transferred from transaction inputs to transaction outputs first-"
"in-first-out. Both the numbering scheme and the transfer scheme rely on "
"_order_, the numbering scheme on the _order_ in which satoshis are mined, "
"and the transfer scheme on the _order_ of transaction inputs and outputs. "
"Thus the name, _ordinals_."
msgstr ""
"오디널스는 개별 사토시(SAT)를 추적하고 전송할 수 있는 사토시를 위한 번호 체계"
"이다. 이러한 번호를 [서수](https://ordinals.com)라고 한다. 사토시는 채굴된 순"
"서대로 번호가 매겨지며, 트랜잭션 입력에서 트랜잭션 출력으로 선입선출 방식으"
"로 전송된다. 번호 체계와 전송 체계는 모두 _순서_ 에 의존한다. 번호 체계는 사"
"토시가 채굴되는 _순서_ 에, 그리고 전송 체계는 트랜잭션 입력과 출력의 _순서_ "
"에 의존한다. 따라서 _오디널_ 이라는 이름이 붙었다."

#: /workspaces/ord_ko/docs/src/overview.md:13
msgid ""
"Technical details are available in [the BIP](https://github.com/ordinals/ord/"
"blob/master/bip.mediawiki)."
msgstr ""
"기술적인 자세한 내용은 [BIP](https://github.com/ordinals/ord/blob/master/bip."
"mediawiki)에서 확인할 수 있다."

#: /workspaces/ord_ko/docs/src/overview.md:16
msgid ""
"Ordinal theory does not require a separate token, another blockchain, or any "
"changes to Bitcoin. It works right now."
msgstr ""
"오디널 이론은 별도의 토큰이나 다른 블록체인 또는 비트코인에 어떠한 변경도 요"
"구하지 않는다. 지금 바로 사용할 수 있다."

#: /workspaces/ord_ko/docs/src/overview.md:19
msgid "Ordinal numbers have a few different representations:"
msgstr "서수는 몇 가지 다른 방법으로 표현할 수 있다:"

#: /workspaces/ord_ko/docs/src/overview.md:21
msgid ""
"_Integer notation_: [`2099994106992659`](https://ordinals.com/"
"sat/2099994106992659) The ordinal number, assigned according to the order in "
"which the satoshi was mined."
msgstr ""
"_정수 표기법_ : [`2099994106992659`](https://ordinals.com/"
"sat/2099994106992659) 사토시가 채굴된 순서에 따라 할당된 서수이다."

#: /workspaces/ord_ko/docs/src/overview.md:26
msgid ""
"_Decimal notation_: [`3891094.16797`](https://ordinals.com/"
"sat/3891094.16797) The first number is the block height in which the satoshi "
"was mined, the second the offset of the satoshi within the block."
msgstr ""
"_소수 표기법_ : [`3891094.16797`](https://ordinals.com/sat/3891094.16797) 첫 "
"번째 숫자는 사토시가 채굴된 블록 높이(block height)이고, 두 번째 숫자는 블록 "
"내 사토시의 오프셋(offset)이다."

#: /workspaces/ord_ko/docs/src/overview.md:31
msgid ""
"_Degree notation_: [`3°111094′214″16797‴`](https://ordinals.com/"
"sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). We'll get to that in "
"a moment."
msgstr ""
"_도 표기법_ : [`3°111094′214″16797‴`](https://ordinals.com/"
"sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). 잠시 후에 설명하겠다."

#: /workspaces/ord_ko/docs/src/overview.md:35
msgid ""
"_Percentile notation_: [`99.99971949060254%`](https://ordinals.com/"
"sat/99.99971949060254%25) . The satoshi's position in Bitcoin's supply, "
"expressed as a percentage."
msgstr ""
"_백분의수 표기법_ : [`99.99971949060254%`](https://ordinals.com/"
"sat/99.99971949060254%25) . 비트코인 공급량에서 사토시가 차지하는 위치를 백분"
"율로 표시한다."

#: /workspaces/ord_ko/docs/src/overview.md:39
msgid ""
"_Name_: [`satoshi`](https://ordinals.com/sat/satoshi). An encoding of the "
"ordinal number using the characters `a` through `z`."
msgstr ""
"_이름_ : [`satoshi`](https://ordinals.com/sat/satoshi). 문자 `a`부터 `z`까지"
"를 사용하여 서수를 인코딩한다."

#: /workspaces/ord_ko/docs/src/overview.md:42
msgid ""
"Arbitrary assets, such as NFTs, security tokens, accounts, or stablecoins "
"can be attached to satoshis using ordinal numbers as stable identifiers."
msgstr ""
"서수를 안정적 식별자로 사용해 NFT, 증권형 토큰, 계정, 스테이블코인 등 임의의 "
"자산을 사토시에 첨부할 수 있다."

#: /workspaces/ord_ko/docs/src/overview.md:45
msgid ""
"Ordinals is an open-source project, developed [on GitHub](https://github.com/"
"ordinals/ord). The project consists of a BIP describing the ordinal scheme, "
"an index that communicates with a Bitcoin Core node to track the location of "
"all satoshis, a wallet that allows making ordinal-aware transactions, a "
"block explorer for interactive exploration of the blockchain, functionality "
"for inscribing satoshis with digital artifacts, and this manual."
msgstr ""
"오디널스는 [깃허브](https://github.com/ordinals/ord)에서 개발된 오픈 소스 프"
"로젝트다. 이 프로젝트는 오디널 체계를 설명하는 BIP, 비트코인 코어 노드와 통신"
"하여 모든 사토시의 위치를 추적하는 인덱스, 오디널를 인식하고 트랜잭션를 할 "
"수 있는 지갑, 블록체인의 대화형 탐색을 위한 블록 탐색기, 사토시에 디지털 아티"
"팩트를 새기는 기능, 그리고 이 매뉴얼로 구성되었다."

#: /workspaces/ord_ko/docs/src/overview.md:52
msgid "Rarity"
msgstr ""

#: /workspaces/ord_ko/docs/src/overview.md:55
msgid ""
"Humans are collectors, and since satoshis can now be tracked and "
"transferred, people will naturally want to collect them. Ordinal theorists "
"can decide for themselves which sats are rare and desirable, but there are "
"some hints…"
msgstr ""
"인간은 수집가이며, 이제 사토시를 추적하고 전송할 수 있기 때문에 사람들은 당연"
"히 사토시를 수집하고 싶어 할 것이다. 어떤 SAT가 희귀하고 바람직한지는 각 오디"
"널 이론가가 스스로 결정할 수 있지만, 몇 가지 힌트가 있다…"

#: /workspaces/ord_ko/docs/src/overview.md:59
msgid ""
"Bitcoin has periodic events, some frequent, some more uncommon, and these "
"naturally lend themselves to a system of rarity. These periodic events are:"
msgstr ""
"비트코인에는 주기적인 이벤트가 있으며, 일부는 빈번하고 일부는 드물게 발생하"
"며, 이들은 자연스럽게 희귀성 시스템에 적합하다. 이러한 주기적 이벤트는 다음"
"과 같다:"

#: /workspaces/ord_ko/docs/src/overview.md:62
msgid ""
"_Blocks_: A new block is mined approximately every 10 minutes, from now "
"until the end of time."
msgstr "_블록_ : 지금부터 영원히 약 10분마다 새로운 블록이 채굴된다."

#: /workspaces/ord_ko/docs/src/overview.md:65
msgid ""
"_Difficulty adjustments_: Every 2016 blocks, or approximately every two "
"weeks, the Bitcoin network responds to changes in hashrate by adjusting the "
"difficulty target which blocks must meet in order to be accepted."
msgstr ""
"_난이도 조정_ : 비트코인 네트워크는 2016블록마다, 즉 약 2주마다 블록이 승인되"
"기 위해 충족해야 하는 난이도 목표를 조정하여 해시레이트의 변화에 대응한다."

#: /workspaces/ord_ko/docs/src/overview.md:69
msgid ""
"_Halvings_: Every 210,000 blocks, or roughly every four years, the amount of "
"new sats created in every block is cut in half."
msgstr ""
"_반감기_ : 210,000 블록마다, 즉 약 4년마다 모든 블록에서 생성되는 새로운 SAT"
"의 양이 절반으로 줄어든다."

#: /workspaces/ord_ko/docs/src/overview.md:72
msgid ""
"_Cycles_: Every six halvings, something magical happens: the halving and the "
"difficulty adjustment coincide. This is called a conjunction, and the time "
"period between conjunctions a cycle. A conjunction occurs roughly every 24 "
"years. The first conjunction should happen sometime in 2032."
msgstr ""
"_사이클_ : 6번의 반감기마다 마법 같은 일이 일어난다. 바로 반감기와 난이도 조"
"정이 동시에 일어난다. 이를 합(conjunction)이라고 하며, 두 합 사이의 기간을 사"
"이클이라고 한다. 합은 대략 24년마다 발생한다. 첫 번째 합은 2032년에 일어날 예"
"정이다."

#: /workspaces/ord_ko/docs/src/overview.md:77
msgid "This gives us the following rarity levels:"
msgstr "이에 따라 다음과 같은 희귀도 레벨이 정해진다:"

#: /workspaces/ord_ko/docs/src/overview.md:79
msgid "`common`: Any sat that is not the first sat of its block"
msgstr "`common`: 해당 블록의 첫 번째 SAT가 아닌 모든 SAT"

#: /workspaces/ord_ko/docs/src/overview.md:80
msgid "`uncommon`: The first sat of each block"
msgstr "`uncommon`: 각 블록의 첫 번째 SAT"

#: /workspaces/ord_ko/docs/src/overview.md:81
msgid "`rare`: The first sat of each difficulty adjustment period"
msgstr "`rare`: 각 난이도 조정 기간의 첫 번째 SAT"

#: /workspaces/ord_ko/docs/src/overview.md:82
msgid "`epic`: The first sat of each halving epoch"
msgstr "`epic`: 각 반감기의 첫 번째 SAT"

#: /workspaces/ord_ko/docs/src/overview.md:83
msgid "`legendary`: The first sat of each cycle"
msgstr "`legendary`: 각 사이클의 첫 번째 SAT"

#: /workspaces/ord_ko/docs/src/overview.md:84
msgid "`mythic`: The first sat of the genesis block"
msgstr "`mythic`: 제네시스블록의 첫 번째 SAT"

#: /workspaces/ord_ko/docs/src/overview.md:86
msgid ""
"Which brings us to degree notation, which unambiguously represents an "
"ordinal number in a way that makes the rarity of a satoshi easy to see at a "
"glance:"
msgstr ""
"다음은 사토시의 희귀성을 한눈에 쉽게 알아볼 수 있도록 서수를 명확하게 표현하"
"는 도 표기법을 알아보자:"

#: /workspaces/ord_ko/docs/src/overview.md:97
msgid ""
"Ordinal theorists often use the terms \"hour\", \"minute\", \"second\", and "
"\"third\" for _A_, _B_, _C_, and _D_, respectively."
msgstr ""
"오디널 이론가들은 _A_, _B_, _C_, _D_ 에 대해 각각 \"시\", \"분\", \"초\", 그"
"리고 “third”라는 용어를 자주 사용한다."

#: /workspaces/ord_ko/docs/src/overview.md:100
msgid "Now for some examples. This satoshi is common:"
msgstr "이제 몇 가지 예를 보자. 이 사토시는 common등급이다:"

#: /workspaces/ord_ko/docs/src/overview.md:111
msgid "This satoshi is uncommon:"
msgstr "이 사토시는 uncommon등급이다:"

#: /workspaces/ord_ko/docs/src/overview.md:121
msgid "This satoshi is rare:"
msgstr "이 사토시는 rare등급이다:"

#: /workspaces/ord_ko/docs/src/overview.md:131
msgid "This satoshi is epic:"
msgstr "이 사토시는 epic등급이다:"

#: /workspaces/ord_ko/docs/src/overview.md:141
msgid "This satoshi is legendary:"
msgstr "이 사토시는 legendary등급이다:"

#: /workspaces/ord_ko/docs/src/overview.md:151
msgid "And this satoshi is mythic:"
msgstr "그리고 이 사토시는 mythic등급이다:"

#: /workspaces/ord_ko/docs/src/overview.md:161
msgid ""
"If the block offset is zero, it may be omitted. This is the uncommon satoshi "
"from above:"
msgstr ""
"블록 오프셋이 0이면 생략할 수 있다. 이 것은 위에서 본 uncommon등급 사토시이"
"다:"

#: /workspaces/ord_ko/docs/src/overview.md:171
msgid "Rare Satoshi Supply"
msgstr "희귀 사토시 공급"

#: /workspaces/ord_ko/docs/src/overview.md:174
msgid "Total Supply"
msgstr "총 공급량"

#: /workspaces/ord_ko/docs/src/overview.md:176
msgid "`common`: 2.1 quadrillion"
msgstr "`common`: 2.1조"

#: /workspaces/ord_ko/docs/src/overview.md:177
msgid "`uncommon`: 6,929,999"
msgstr "`uncommon`: 6,929,999"

#: /workspaces/ord_ko/docs/src/overview.md:178
msgid "`rare`: 3437"
msgstr "`rare`: 3437"

#: /workspaces/ord_ko/docs/src/overview.md:179
msgid "`epic`: 32"
msgstr "`epic`: 32"

#: /workspaces/ord_ko/docs/src/overview.md:180
msgid "`legendary`: 5"
msgstr "`legendary`: 5"

#: /workspaces/ord_ko/docs/src/overview.md:181
#: /workspaces/ord_ko/docs/src/overview.md:190
msgid "`mythic`: 1"
msgstr "`mythic`: 1"

#: /workspaces/ord_ko/docs/src/overview.md:183
msgid "Current Supply"
msgstr "현재 공급량"

#: /workspaces/ord_ko/docs/src/overview.md:185
msgid "`common`: 1.9 quadrillion"
msgstr "`common`: 1.9조"

#: /workspaces/ord_ko/docs/src/overview.md:186
msgid "`uncommon`: 808,262"
msgstr "`uncommon`: 808,262"

#: /workspaces/ord_ko/docs/src/overview.md:187
msgid "`rare`: 369"
msgstr "`rare`: 369"

#: /workspaces/ord_ko/docs/src/overview.md:188
msgid "`epic`: 3"
msgstr "`epic`: 3"

#: /workspaces/ord_ko/docs/src/overview.md:189
msgid "`legendary`: 0"
msgstr "`legendary`: 0"

#: /workspaces/ord_ko/docs/src/overview.md:192
msgid ""
"At the moment, even uncommon satoshis are quite rare. As of this writing, "
"745,855 uncommon satoshis have been mined - one per 25.6 bitcoin in "
"circulation."
msgstr ""
"현재로서는 uncommon 사토시도 매우 드물다. 이 글을 쓰는 현재, 745,855개의 "
"uncommon 사토시가 채굴되었다 - 유통되는 25.6비트코인당 1개."

#: /workspaces/ord_ko/docs/src/overview.md:196
msgid "Names"
msgstr "이름"

#: /workspaces/ord_ko/docs/src/overview.md:199
msgid ""
"Each satoshi has a name, consisting of the letters _A_ through _Z_, that get "
"shorter the further into the future the satoshi was mined. They could start "
"short and get longer, but then all the good, short names would be trapped in "
"the unspendable genesis block."
msgstr ""
"각 사토시는 _A_ 부터 _Z_ 까지의 문자로 구성된 이름이 있으며, 더 미래에서 채"
"굴 될수록 사토시에 이름은 짧아진다. 짧게 시작해서 점점 길어질 수도 있지만, 그"
"러면 좋고 짧은 이름들은 모두 소비가 불가능한 제네시스 블록에 갇히게 될 것이"
"다."

#: /workspaces/ord_ko/docs/src/overview.md:204
msgid ""
"As an example, 1905530482684727°'s name is \"iaiufjszmoba\". The name of the "
"last satoshi to be mined is \"a\". Every combination of 10 characters or "
"less is out there, or will be out there, someday."
msgstr ""
"예를 들어 1905530482684727°의 이름은 \"iaiufjszmoba”이다. 마지막으로 채굴될 "
"사토시의 이름은 “a”이다. 10자 이하의 모든 조합은 이미 존재하거나 언젠가는 존"
"재할 것이다."

#: /workspaces/ord_ko/docs/src/overview.md:208
msgid "Exotics"
msgstr "엑소틱"

#: /workspaces/ord_ko/docs/src/overview.md:211
msgid ""
"Satoshis may be prized for reasons other than their name or rarity. This "
"might be due to a quality of the number itself, like having an integer "
"square or cube root. Or it might be due to a connection to a historical "
"event, such as satoshis from block 477,120, the block in which SegWit "
"activated, or 2099999997689999°, the last satoshi that will ever be mined."
msgstr ""
"사토시는 이름이나 희귀성 이외의 이유로 소중히 여겨질 수 있다. 정수 제곱근이"
"나 세제곱근과 같은 숫자 자체의 특성 때문일 수도 있다. 또는 세그윗이 활성화된 "
"블록인 477,120 블록의 사토시나 마지막으로 채굴될 사토시인 2099999997689999°"
"와 같은 역사적 사건과의 연관성 때문일 수도 있다."

#: /workspaces/ord_ko/docs/src/overview.md:217
msgid ""
"Such satoshis are termed \"exotic\". Which satoshis are exotic and what "
"makes them so is subjective. Ordinal theorists are encouraged to seek out "
"exotics based on criteria of their own devising."
msgstr ""
"이러한 사토시를 “엑소틱”(이색품)이라고 한다. 어떤 사토시가 엑소틱인지, 그리"
"고 무엇 때문에 엑소틱인지는 주관적이다. 오디널 이론가들은 자신이 고안한 기준"
"에 따라 엑소틱을 찾도록 권장한다."

#: /workspaces/ord_ko/docs/src/overview.md:224
msgid ""
"Satoshis can be inscribed with arbitrary content, creating Bitcoin-native "
"digital artifacts. Inscribing is done by sending the satoshi to be inscribed "
"in a transaction that reveals the inscription content on-chain. This content "
"is then inextricably linked to that satoshi, turning it into an immutable "
"digital artifact that can be tracked, transferred, hoarded, bought, sold, "
"lost, and rediscovered."
msgstr ""
"사토시에 임의의 콘텐츠를 새겨 비트코인 고유의 디지털 아티팩트를 생성할 수 있"
"다. 새김(inscribing)은 새김할 사토시를 해당 인스크립션 콘텐츠를 온체인에 공개"
"해주는 트랜잭션안에 전송하는 방식으로 이루어진다. 이로써 이 콘텐츠는 해당 사"
"토시와 불가분으로 연결되며, 추적, 전송, 비축, 구매, 판매, 분실, 재발견이 가능"
"한 불변의 디지털 아티팩트로 전환된다."

#: /workspaces/ord_ko/docs/src/overview.md:231
msgid "Archaeology"
msgstr "고고학"

#: /workspaces/ord_ko/docs/src/overview.md:234
msgid ""
"A lively community of archaeologists devoted to cataloging and collecting "
"early NFTs has sprung up. [Here's a great summary of historical NFTs by "
"Chainleft.](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-"
"N29oF4iwCgX3lacrvaG9Kjko)"
msgstr ""
"초기 NFT를 목록화하고 수집하는 데 전념하는 고고학자들의 활발한 커뮤니티가 생"
"겨났다. [여기에 역사적 NFT에 대한 Chainleft의 훌륭한 요약을 확인할 수 있다.]"
"(https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-"
"N29oF4iwCgX3lacrvaG9Kjko)"

#: /workspaces/ord_ko/docs/src/overview.md:238
msgid ""
"A commonly accepted cut-off for early NFTs is March 19th, 2018, the date the "
"first ERC-721 contract, [SU SQUARES](https://tenthousandsu.com/), was "
"deployed on Ethereum."
msgstr ""
"일반적으로 인정되는 초기 NFT에 마감일은 2018년 3월 19일로, 이더리움에 첫 번"
"째 ERC-721 컨트랙트인 [SU SQUARES](https://tenthousandsu.com/)가 배포된 날이"
"다."

#: /workspaces/ord_ko/docs/src/overview.md:242
msgid ""
"Whether or not ordinals are of interest to NFT archaeologists is an open "
"question! In one sense, ordinals were created in early 2022, when the "
"Ordinals specification was finalized. In this sense, they are not of "
"historical interest."
msgstr ""
"오디널스가 NFT 고고학자들의 관심을 끌지 여부는 아직 미지수다! 어떤 의미에서 "
"오디널스는 오디널스 사양이 확정된 2022년 초에 만들어졌다. 이런한 점에서, 오디"
"널스는 역사적으로 흥미롭지 않다."

#: /workspaces/ord_ko/docs/src/overview.md:247
msgid ""
"In another sense though, ordinals were in fact created by Satoshi Nakamoto "
"in 2009 when he mined the Bitcoin genesis block. In this sense, ordinals, "
"and especially early ordinals, are certainly of historical interest."
msgstr ""
"하지만 다른 의미에서 오디널스는 사실 2009년 사토시 나카모토가 비트코인 제네시"
"스 블록을 채굴할 때 만들어졌다. 이런 관점에서 오디널스, 특히 초기 오디널스는 "
"분명 역사적으로 흥미로운 존재다."

#: /workspaces/ord_ko/docs/src/overview.md:251
msgid ""
"Many ordinal theorists favor the latter view. This is not least because the "
"ordinals were independently discovered on at least two separate occasions, "
"long before the era of modern NFTs began."
msgstr ""
"많은 오디널 이론가들은 후자의 견해를 선호한다. 이는 특히 오디널스가 현대적 "
"NFT 시대가 시작되기 훨씬 전에 적어도 두 차례에 걸쳐 독립적으로 발견되었기 때"
"문이다."

#: /workspaces/ord_ko/docs/src/overview.md:255
msgid ""
"On August 21st, 2012, Charlie Lee [posted a proposal to add proof-of-stake "
"to Bitcoin to the Bitcoin Talk forum](https://bitcointalk.org/index.php?"
"topic=102355.0). This wasn't an asset scheme, but did use the ordinal "
"algorithm, and was implemented but never deployed."
msgstr ""
"2012년 8월 21일, 찰리 리는 [비트코인 토크 포럼에 비트코인에 지분증명을 추가하"
"자는 제안을 게시했다](https://bitcointalk.org/index.php?topic=102355.0). 이"
"는 자산 체계는 아니었지만 오디널 알고리즘을 사용했으며, 시행은 되었지만 배포"
"되지는 않았다."

#: /workspaces/ord_ko/docs/src/overview.md:261
msgid ""
"On October 8th, 2012, jl2012 [posted a scheme to the same forum](https://"
"bitcointalk.org/index.php?topic=117224.0) which uses decimal notation and "
"has all the important properties of ordinals. The scheme was discussed but "
"never implemented."
msgstr ""
"2012년 10월 8일, jl2012는 [같은 포럼에](https://bitcointalk.org/index.php?"
"topic=117224.0) 소수 표기법을 사용하고 오디널의 모든 중요한 속성을 가진 계획"
"을 게시했다. 이 계획은 논의되었지만 시행되지 않았다."

#: /workspaces/ord_ko/docs/src/overview.md:266
msgid ""
"These independent inventions of ordinals indicate in some way that ordinals "
"were discovered, or rediscovered, and not invented. The ordinals are an "
"inevitability of the mathematics of Bitcoin, stemming not from their modern "
"documentation, but from their ancient genesis. They are the culmination of a "
"sequence of events set in motion with the mining of the first block, so many "
"years ago."
msgstr ""
"이러한 오디널의 독립적인 발명은 어떻게 보면 오디널이 발명된 것이 아니라 발견"
"되었거나 재발견되었음을 나타낸다. 오디널은 비트코인의 수학의 필연적인 결과"
"로, 현대의 기록이 아니라 고대의 기원에서 비롯된 것이다. 오디널은 수년 전 첫 "
"번째 블록의 채굴과 함께 시작된 일련의 사건의 정점이다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:4
msgid ""
"Imagine a physical artifact. A rare coin, say, held safe for untold years in "
"the dark, secret clutch of a Viking hoard, now dug from the earth by your "
"grasping hands. It…"
msgstr ""
"물리적 인공물을 상상해 보라. 예를 들어, 밝혀지지 않은 세월 동안 어둡고 은밀"
"한 바이킹 창고의 손아귀에 있던 희귀한 동전이 이제 여러분의 손으로 의해 땅속에"
"서 파헤쳐졌다. 이 동전은…"

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:8
msgid ""
"…has an owner. You. As long as you keep it safe, nobody can take it from you."
msgstr ""
"...주인이 있다. 바로 당신. 당신이 안전하게 보관하는 한 누구도 빼앗을 수 없다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:10
msgid "…is complete. It has no missing parts."
msgstr "...완전하다. 결여된 요소가 없다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:12
msgid ""
"…can only be changed by you. If you were a trader, and you made your way to "
"18th century China, none but you could stamp it with your chop-mark."
msgstr ""
"...당신에 의해서만 변화될 수 있다. 만약 당신이 상인이고 18세기 중국으로 갔었"
"다면, 그 동전에 각인(chop-mark)을 찍을 수 있는 사람은 당신밖에 없었을 것이다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:15
msgid ""
"…can only be disposed of by you. The sale, trade, or gift is yours to make, "
"to whomever you wish."
msgstr ""
"...당신에 의해서만 처분될 수 있다. 당신이 원하는 누구에게나 판매, 거래 또는 "
"선물 할 수 있다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:18
msgid ""
"What are digital artifacts? Simply put, they are the digital equivalent of "
"physical artifacts."
msgstr ""
"디지털 아티팩트(인공물)란 무엇인가? 간단히 말해, 이는 물리적 인공물의 디지털 "
"버전이다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:21
msgid ""
"For a digital thing to be a digital artifact, it must be like that coin of "
"yours:"
msgstr ""
"디지털 한 것이 디지털 아티팩트가 되려면 당신의 그 동전과 같은 것이어야 한다:"

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:24
msgid ""
"Digital artifacts can have owners. A number is not a digital artifact, "
"because nobody can own it."
msgstr ""
"디지털 아티팩트는 소유자가 있을 수 있다. 숫자는 누구도 소유할 수 없으므로 디"
"지털 아티팩트가 아니다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:27
msgid ""
"Digital artifacts are complete. An NFT that points to off-chain content on "
"IPFS or Arweave is incomplete, and thus not a digital artifact."
msgstr ""
"디지털 아티팩트는 완전하다. IPFS 또는 Arweave의 오프체인 콘텐츠를 가리키는 "
"NFT는 불완전하며, 따라서 디지털 아티팩트가 아니다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:30
msgid ""
"Digital artifacts are permissionless. An NFT which cannot be sold without "
"paying a royalty is not permissionless, and thus not a digital artifact."
msgstr ""
"디지털 아티팩트는 무허가성이라는 특성을 가지고 있다. 로열티를 지불하지 않고"
"는 판매할 수 없는 NFT는 무허가성을 지니지 않으며, 따라서 디지털 아티팩트가 아"
"니다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:33
msgid ""
"Digital artifacts are uncensorable. Perhaps you can change a database entry "
"on a centralized ledger today, but maybe not tomorrow, and thus one cannot "
"be a digital artifact."
msgstr ""
"디지털 아티팩트는 검열할 수 없다. 중앙 원장의 데이터베이스 항목을 오늘은 변경"
"할 수 있어도 내일은 변경할 수 없을 수도 있으므로 이는 디지털 아티팩트가 될 "
"수 없다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:37
msgid ""
"Digital artifacts are immutable. An NFT with an upgrade key is not a digital "
"artifact."
msgstr ""
"디지털 아티팩트는 불변한다. 업그레이드 키가 있는 NFT는 디지털 아티팩트가 아니"
"다."

#: /workspaces/ord_ko/docs/src/digital-artifacts.md:40
msgid ""
"The definition of a digital artifact is intended to reflect what NFTs "
"_should_ be, sometimes are, and what inscriptions _always_ are, by their "
"very nature."
msgstr ""
"디지털 아티팩트의 정의는 NFT가 _추구해야하는_, 그리고 간혹 부합하는, 그리고 "
"인스크립션이 본질적으로 _항상_ 부합하는 이상을 반영하기 위한 것이다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:4
msgid ""
"Inscriptions inscribe sats with arbitrary content, creating bitcoin-native "
"digital artifacts, more commonly known as NFTs. Inscriptions do not require "
"a sidechain or separate token."
msgstr ""
"인스크립션(새김)은 임의의 콘텐츠를 SAT에 새겨 넣어 비트코인 고유의 디지털 아"
"티팩트(일반적인 명칭은 NFT)를 생성한다. 인스크립션은 사이드체인이나 별도의 토"
"큰을 필요로 하지 않는다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:8
msgid ""
"These inscribed sats can then be transferred using bitcoin transactions, "
"sent to bitcoin addresses, and held in bitcoin UTXOs. These transactions, "
"addresses, and UTXOs are normal bitcoin transactions, addresses, and UTXOS "
"in all respects, with the exception that in order to send individual sats, "
"transactions must control the order and value of inputs and outputs "
"according to ordinal theory."
msgstr ""
"이렇게 새겨진 SAT는 비트코인 트랜잭션을 사용해 전송하고, 비트코인 주소로 보내"
"고, 비트코인 UTXO에 보관할 수 있다. 이러한 트랜잭션, 주소, UTXO는 모든 면에"
"서 일반적인 비트코인 트랜잭션, 주소, UTXOs와 동일다. 다만 개별 사토시를 전송"
"하기 위해서는 트랜잭션이 오디널 이론에 따라 입력과 출력의 순서와 값을 제어해"
"야 한다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:15
msgid ""
"The inscription content model is that of the web. An inscription consists of "
"a content type, also known as a MIME type, and the content itself, which is "
"a byte string. This allows inscription content to be returned from a web "
"server, and for creating HTML inscriptions that use and remix the content of "
"other inscriptions."
msgstr ""
"인스크립션 콘텐츠 모델은 웹의 콘텐츠 모델이다. 인스크립션은 MIME 유형이라고"
"도 하는 콘텐츠 유형과 바이트 문자열(string)인 콘텐츠 자체로 구성된다. 이를 통"
"해 웹 서버에서 인스크립션 콘텐츠를 반환하고 다른 인스크립션의 콘텐츠를 사용 "
"및 리믹스하는 HTML 인스크립션을 만들 수 있다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:21
msgid ""
"Inscription content is entirely on-chain, stored in taproot script-path "
"spend scripts. Taproot scripts have very few restrictions on their content, "
"and additionally receive the witness discount, making inscription content "
"storage relatively economical."
msgstr ""
"인스크립션 콘텐츠는 전적으로 온체인에 저장되며, 탭루트 스크립트 경로 지출 스"
"크립트에 저장된다. 탭루트 스크립트는 콘텐츠에 대한 제한이 거의 없으며, 추가"
"로 증인 할인(witness discount)을 받을 수 있어 인스크립션 콘텐츠 저장 비용이 "
"상대적으로 저렴하다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:26
msgid ""
"Since taproot script spends can only be made from existing taproot outputs, "
"inscriptions are made using a two-phase commit/reveal procedure. First, in "
"the commit transaction, a taproot output committing to a script containing "
"the inscription content is created. Second, in the reveal transaction, the "
"output created by the commit transaction is spent, revealing the inscription "
"content on-chain."
msgstr ""
"탭루트 스크립트 지출은 기존 탭루트 출력에서만 만들 수 있으므로, 인스크립션은 "
"2단계 커밋/리빌 절차를 사용하여 만들어진다. 먼저 커밋 트랜잭션에서 인스크립"
"션 내용이 포함된 스크립트에 커밋하는 탭루트 출력이 생성된다. 둘째, 리빌 트랜"
"잭션에서는 커밋 트랜잭션에서 생성된 출력이 소비되어 인스크립션 콘텐츠를 온체"
"인에 공개한다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:33
msgid ""
"Inscription content is serialized using data pushes within unexecuted "
"conditionals, called \"envelopes\". Envelopes consist of an `OP_FALSE OP_IF "
"… OP_ENDIF` wrapping any number of data pushes. Because envelopes are "
"effectively no-ops, they do not change the semantics of the script in which "
"they are included, and can be combined with any other locking script."
msgstr ""
"인스크립션 콘텐츠는 “엔벨롭”(envelope)이라고 하는 실행되지 않은 조건문 내의 "
"데이터 푸시를 사용하여 직렬화된다. 엔벨롭는 데이터 푸시를 원하는 수만큼 감싸"
"는 `OP_FALSE OP_IF ... OP_ENDIF`로 구성된다. 엔벨롭는 사실상 노옵(no-op)이므"
"로 엔벨롭이 포함된 스크립트의 의미를 변경하지 않으며 다른 로킹(locking) 스크"
"립트와 결합할 수 있다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:39
msgid ""
"A text inscription containing the string \"Hello, world!\" is serialized as "
"follows:"
msgstr ""
"“Hello, world!”이라는 문자열이 포함된 텍스트 인스크립션은 다음과 같이 직렬화"
"된다:"

#: /workspaces/ord_ko/docs/src/inscriptions.md:42
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH “ord”\n"
"  OP_PUSH 1\n"
"  OP_PUSH “text/plain;charset=utf-8”\n"
"  OP_PUSH 0\n"
"  OP_PUSH “Hello, world!”\n"
"OP_ENDIF\n"
"```"

#: /workspaces/ord_ko/docs/src/inscriptions.md:53
msgid ""
"First the string `ord` is pushed, to disambiguate inscriptions from other "
"uses of envelopes."
msgstr ""
"먼저 `ord` 문자열이 푸시되어 인스크립션을 엔벨롭의 다른 용도와 구분할 수 있"
"다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:56
msgid ""
"`OP_PUSH 1` indicates that the next push contains the content type, and "
"`OP_PUSH 0`indicates that subsequent data pushes contain the content itself. "
"Multiple data pushes must be used for large inscriptions, as one of "
"taproot's few restrictions is that individual data pushes may not be larger "
"than 520 bytes."
msgstr ""
"`OP_PUSH 1`은 다음 푸시에 콘텐츠 유형이 포함되어 있음을 나타내고, `OP_PUSH 0`"
"은 후속 데이터 푸시에 콘텐츠 자체가 포함되어 있음을 나타낸다. 탭루트의 몇 가"
"지 제한 사항 중 하나는 개별 데이터 푸시가 520바이트를 초과할 수 없다는 것이므"
"로, 큰 인스크립션에는 여러 개의 데이터 푸시를 사용해야 한다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:62
msgid ""
"The inscription content is contained within the input of a reveal "
"transaction, and the inscription is made on the first sat of its input. This "
"sat can then be tracked using the familiar rules of ordinal theory, allowing "
"it to be transferred, bought, sold, lost to fees, and recovered."
msgstr ""
"인스크립션 콘텐츠는 리빌 트랜잭션의 입력에 포함되며, 인스크립션은 입력의 첫 "
"번째 SAT에 만들어진다. 이 SAT는 익숙한 오디널 이론의 규칙을 사용하여 추적할 "
"수 있으며, 이를 통해 전송, 구매, 판매, 수수료에 분실, 그리고 복구가 가능하다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:67
msgid "Content"
msgstr "콘텐츠"

#: /workspaces/ord_ko/docs/src/inscriptions.md:70
msgid ""
"The data model of inscriptions is that of a HTTP response, allowing "
"inscription content to be served by a web server and viewed in a web browser."
msgstr ""
"인스크립션의 데이터 모델은 웹 서버에서 인스크립션 콘텐츠를 제공하고 웹 브라우"
"저에서 볼 수 있도록 하는 HTTP 응답의 데이터 모델이다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:73
msgid "Fields"
msgstr "필드"

#: /workspaces/ord_ko/docs/src/inscriptions.md:76
msgid ""
"Inscriptions may include fields before an optional body. Each field consists "
"of two data pushes, a tag and a value."
msgstr ""
"인스크립션은 optional body 앞에 필드를 포함할 수 있다. 각 필드는 두 개의 데이"
"터 푸시, 즉 태그 및 값으로 구성된다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:79
msgid ""
"Currently, the only defined field is `content-type`, with a tag of `1`, "
"whose value is the MIME type of the body."
msgstr ""
"현재 정의된 유일한 필드는 'content-type'이며, 태그는 '1'이며, 값은 body에 "
"MIME 유형이다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:82
msgid ""
"The beginning of the body and end of fields is indicated with an empty data "
"push."
msgstr "Body의 시작과 필드의 끝은 빈 데이터 푸시로 표시된다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:85
msgid ""
"Unrecognized tags are interpreted differently depending on whether they are "
"even or odd, following the \"it's okay to be odd\" rule used by the "
"Lightning Network."
msgstr ""
"인식되지 않는 태그는 라이트닝 네트워크에서 사용하는 \"홀수여도 괜찮아\" 규칙"
"에 따라 짝수인지 홀수인지에 따라 다르게 해석된다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:89
msgid ""
"Even tags are used for fields which may affect creation, initial assignment, "
"or transfer of an inscription. Thus, inscriptions with unrecognized even "
"fields must be displayed as \"unbound\", that is, without a location."
msgstr ""
"인스크립션 생성, 초기 할당 또는 전송에 영향을 줄 수 있는 필드에는 짝수 태그"
"가 사용된다. 따라서 인식할 수 없는 짝수 필드가 있는 인스크립션은 “unbound”, "
"즉 위치가 없는 상태로 표시되어야 한다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:93
msgid ""
"Odd tags are used for fields which do not affect creation, initial "
"assignment, or transfer, such as additional metadata, and thus are safe to "
"ignore."
msgstr ""
"홀수 태그는 추가 메타데이터와 같이 생성, 초기 할당 또는 전송에 영향을 주지 않"
"는 필드에 사용되므로 무시해도 안전하다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:96
msgid "Inscription IDs"
msgstr "인스크립션 ID"

#: /workspaces/ord_ko/docs/src/inscriptions.md:99
msgid ""
"The inscriptions are contained within the inputs of a reveal transaction. In "
"order to uniquely identify them they are assigned an ID of the form:"
msgstr ""
"인스크립션은 리빌 트랜잭션의 입력값에 포함되어 있다. 인스크립션을 고유하게 식"
"별하기 위해 인스크립션에는 이 형식의 ID가 할당된다:"

#: /workspaces/ord_ko/docs/src/inscriptions.md:102
msgid "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"
msgstr "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"

#: /workspaces/ord_ko/docs/src/inscriptions.md:104
msgid ""
"The part in front of the `i` is the transaction ID (`txid`) of the reveal "
"transaction. The number after the `i` defines the index (starting at 0) of "
"new inscriptions being inscribed in the reveal transaction."
msgstr ""
"`i` 앞 부분은 리빌 트랜잭션의 트랜잭션 ID(`txid`)이다. `i` 뒤의 숫자는 리빌 "
"트랜잭션에 새겨지는 새 인스크립션의 인덱스(0부터 시작)를 정의한다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:108
msgid ""
"Inscriptions can either be located in different inputs, within the same "
"input or a combination of both. In any case the ordering is clear, since a "
"parser would go through the inputs consecutively and look for all "
"inscription `envelopes`."
msgstr ""
"인스크립션은 다른 입력 아니면 동일한 입력 내에 위치하거나 둘 다 일수 있다. 어"
"떤 경우든 구문 분석기는 입력을 연속적으로 살펴보고 모든 인스크립션 ‘엔벨"
"롭’을 찾기 때문에 순서는 명확하다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:112
msgid "Input"
msgstr "입력"

#: /workspaces/ord_ko/docs/src/inscriptions.md:112
msgid "Inscription Count"
msgstr "인스크립션 개수"

#: /workspaces/ord_ko/docs/src/inscriptions.md:112
msgid "Indices"
msgstr "인덱스"

#: /workspaces/ord_ko/docs/src/inscriptions.md:114
#: /workspaces/ord_ko/docs/src/inscriptions.md:117
msgid "0"
msgstr "0"

#: /workspaces/ord_ko/docs/src/inscriptions.md:114
#: /workspaces/ord_ko/docs/src/inscriptions.md:116
msgid "2"
msgstr "2"

#: /workspaces/ord_ko/docs/src/inscriptions.md:114
msgid "i0, i1"
msgstr "i0, i1"

#: /workspaces/ord_ko/docs/src/inscriptions.md:115
#: /workspaces/ord_ko/docs/src/inscriptions.md:118
msgid "1"
msgstr "1"

#: /workspaces/ord_ko/docs/src/inscriptions.md:115
msgid "i2"
msgstr "i2"

#: /workspaces/ord_ko/docs/src/inscriptions.md:116
#: /workspaces/ord_ko/docs/src/inscriptions.md:117
msgid "3"
msgstr "3"

#: /workspaces/ord_ko/docs/src/inscriptions.md:116
msgid "i3, i4, i5"
msgstr "i3, i4, i5"

#: /workspaces/ord_ko/docs/src/inscriptions.md:118
msgid "4"
msgstr "4"

#: /workspaces/ord_ko/docs/src/inscriptions.md:118
msgid "i6"
msgstr "i6"

#: /workspaces/ord_ko/docs/src/inscriptions.md:120
msgid "Sandboxing"
msgstr "샌드박싱 (sandboxing)"

#: /workspaces/ord_ko/docs/src/inscriptions.md:123
msgid ""
"HTML and SVG inscriptions are sandboxed in order to prevent references to "
"off-chain content, thus keeping inscriptions immutable and self-contained."
msgstr ""
"오프체인 콘텐츠에 대한 참조를 방지하기 위해 HTML 및 SVG 인스크립션은 샌드박스"
"가 적용되어 인스크립션의 변경을 불가능하게 하고 독립적으로 유지될 수 있게 한"
"다."

#: /workspaces/ord_ko/docs/src/inscriptions.md:126
msgid ""
"This is accomplished by loading HTML and SVG inscriptions inside `iframes` "
"with the `sandbox` attribute, as well as serving inscription content with "
"`Content-Security-Policy` headers."
msgstr ""
"이는 HTML 및 SVG 인스크립션을 ‘sandbox’ 속성을 사용하여 'iframe' 내에 로드하"
"고 `Content-Security-Policy` 헤더를 사용하여 인스크립션 콘텐츠를 제공함으로"
"써 수행된다."

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:4
msgid ""
"Inscriptions may include [CBOR](https://cbor.io/) metadata, stored as data "
"pushes in fields with tag `5`. Since data pushes are limited to 520 bytes, "
"metadata longer than 520 bytes must be split into multiple tag `5` fields, "
"which will then be concatenated before decoding."
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:9
msgid ""
"Metadata is human readable, and all metadata will be displayed to the user "
"with its inscription. Inscribers are encouraged to consider how metadata "
"will be displayed, and make metadata concise and attractive."
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:13
msgid "Metadata is rendered to HTML for display as follows:"
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:15
msgid ""
"`null`, `true`, `false`, numbers, floats, and strings are rendered as plain "
"text."
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:17
msgid "Byte strings are rendered as uppercase hexadecimal."
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:18
msgid ""
"Arrays are rendered as `<ul>` tags, with every element wrapped in `<li>` "
"tags."
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:20
msgid ""
"Maps are rendered as `<dl>` tags, with every key wrapped in `<dt>` tags, and "
"every value wrapped in `<dd>` tags."
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:22
msgid ""
"Tags are rendered as the tag , enclosed in a `<sup>` tag, followed by the "
"value."
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:25
msgid ""
"CBOR is a complex spec with many different data types, and multiple ways of "
"representing the same data. Exotic data types, such as tags, floats, and "
"bignums, and encoding such as indefinite values, may fail to display "
"correctly or at all. Contributions to `ord` to remedy this are welcome."
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:30
#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:27
#: /workspaces/ord_ko/docs/src/guides/testing.md:18
#: /workspaces/ord_ko/docs/src/guides/reindexing.md:15
msgid "Example"
msgstr "예제"

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:33
msgid ""
"Since CBOR is not human readable, in these examples it is represented as "
"JSON. Keep in mind that this is _only_ for these examples, and JSON metadata "
"will _not_ be displayed correctly."
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:37
msgid ""
"The metadata `{\"foo\":\"bar\",\"baz\":[null,true,false,0]}` would be "
"included in an inscription as:"
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:39
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"foo\":\"bar\",\"baz\":[null,true,false,0]}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:48
msgid "And rendered as:"
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:73
msgid "Metadata longer than 520 bytes must be split into multiple fields:"
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:75
#| msgid ""
#| "```\n"
#| "OP_FALSE\n"
#| "OP_IF\n"
#| "  OP_PUSH \"ord\"\n"
#| "  OP_PUSH 1\n"
#| "  OP_PUSH \"text/plain;charset=utf-8\"\n"
#| "  OP_PUSH 0\n"
#| "  OP_PUSH \"Hello, world!\"\n"
#| "OP_ENDIF\n"
#| "```"
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"very\":\"long\",\"metadata\":'\n"
"    OP_PUSH 0x05 OP_PUSH '\"is\",\"finally\":\"done\"}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/metadata.md:85
msgid ""
"Which would then be concatinated into `{\"very\":\"long\",\"metadata\":"
"\"is\",\"finally\":\"done\"}`."
msgstr ""

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:4
msgid ""
"The owner of an inscription can create child inscriptions, trustlessly "
"establishing the provenance of those children on-chain as having been "
"created by the owner of the parent inscription. This can be used for "
"collections, with the children of a parent inscription being members of the "
"same collection."
msgstr ""
"인스크립션 소유자는 자식 인스크립션을 생성할 수 있으며, 해당 자식의 출처를 부"
"모 인스크립션 소유자가 생성한 것으로 온체인에서 신뢰 없이 입증할 수 있다. 이"
"는 부모 인스크립션의 자식이 동일한 컬렉션의 멤버가 되는 컬렉션에 사용할 수 있"
"다."

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:9
msgid ""
"Children can themselves have children, allowing for complex hierarchies. For "
"example, an artist might create an inscription representing themselves, with "
"sub inscriptions representing collections that they create, with the "
"children of those sub inscriptions being items in those collections."
msgstr ""
"자식이 자식을 가질 수 있으므로 복잡한 계층 구조가 가능하다. 예를 들어, 아티스"
"트가 자신을 나타내는 인스크립션을 만들고 하위 인스크립션은 자신이 만든 컬렉션"
"을 나타내며, 이 하위 인스크립션의 자식은 해당 컬렉션의 아이템이 될 수 있다."

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:14
msgid "Specification"
msgstr "설명서"

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:16
msgid "To create a child inscription C with parent inscription P:"
msgstr "부모 인스크립션 P로 자식 인스크립션 C를 생성하려면:"

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:18
msgid "Create an inscribe transaction T as usual for C."
msgstr "C에 대해 평소와 같이 인스크립션 트랜잭션 T를 생성한다."

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:19
msgid "Spend the parent P in one of the inputs of T."
msgstr "부모 P를 T의 입력 중 하나에 쓴다."

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:20
msgid ""
"Include tag `3`, i.e. `OP_PUSH 3`, in C, with the value of the serialized "
"binary inscription ID of P, serialized as the 32-byte `TXID`, followed by "
"the four-byte little-endian `INDEX`, with trailing zeroes omitted."
msgstr ""
"C에 태그 '3', 즉 'OP_PUSH 3'을 포함하고, 32바이트 'TXID'로 직렬화된 P의 바이"
"너리(이진수) 인스크립션 ID 값과 뒤에오는 0들을 생략한 4바이트 리틀엔디안 "
"'INDEX'를 포함시킨다."

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:24
msgid ""
"_NB_ The bytes of a bitcoin transaction ID are reversed in their text "
"representation, so the serialized transaction ID will be in the opposite "
"order."
msgstr ""
"_NB_ 비트코인 트랜잭션 ID의 바이트는 텍스트 표현이 역순으로되어 있으므로 직렬"
"화된 트랜잭션 ID는 반대 순서가 된다."

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:29
msgid ""
"An example of a child inscription of "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"
msgstr ""
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`의 자식 "
"인스크립션의 예시:"

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:32
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH “ord”\n"
"  OP_PUSH 1\n"
"  OP_PUSH “text/plain;charset=utf-8”\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH “Hello, world!”\n"
"OP_ENDIF\n"
"```"

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:45
msgid ""
"Note that the value of tag `3` is binary, not hex, and that for the child "
"inscription to be recognized as a child, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` must be "
"spent as one of the inputs of the inscribe transaction."
msgstr ""
"태그 `3`의 값은 헥스(16진수)가 아닌 바이너리(이진수)이며, 자식 비문이 자식으"
"로 인식되려면 "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`가 인스크"
"립션 트랜잭션의 입력 중 하나로 소비되어야 한다."

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:50
msgid ""
"Example encoding of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"
msgstr ""
"인스크립션 ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`의 인코"
"딩 예시:"

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:63
msgid ""
"And of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"
msgstr ""
"그리고 인스크립션 ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`의 경"
"우:"

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:75
msgid "Notes"
msgstr "메모"

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:77
msgid ""
"The tag `3` is used because it is the first available odd tag. Unrecognized "
"odd tags do not make an inscription unbound, so child inscriptions would be "
"recognized and tracked by old versions of `ord`."
msgstr ""
"태그 `3`은 사용 가능한 첫 번째 홀수 태그이기 때문에 사용된다. 인식되지 않는 "
"홀수 태그는 인스크립션을 unbound(바인딩 해제)하지 않으므로 하위 인스크립션은 "
"이전 버전의 `ord`에서 인식 및 추적된다."

#: /workspaces/ord_ko/docs/src/inscriptions/provenance.md:81
msgid ""
"A collection can be closed by burning the collection's parent inscription, "
"which guarantees that no more items in the collection can be issued."
msgstr ""
"컬렉션의 상위 인스크립션을 소각하여 컬렉션을 폐쇄할 수 있으며, 이 경우 컬렉션"
"의 아이템을 더 이상 발행할 수 없게 된다."

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:4
msgid ""
"An important exception to [sandboxing](../inscriptions.md#sandboxing) is "
"recursion: access to `ord`'s `/content` endpoint is permitted, allowing "
"inscriptions to access the content of other inscriptions by requesting `/"
"content/<INSCRIPTION_ID>`."
msgstr ""
"[샌드박싱](../inscriptions.md#sandboxing)의 중요한 예외는 리커젼(재귀)이다. "
"`ord`의 `/content` 엔드포인트에 대한 액세스가 허용되므로 어떤 인스크립션이 `/"
"content/<INSCRIPTION_ID>`를 요청하여 다른 인스크립션의 콘텐츠에 액세스할 수 "
"있다."

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:9
msgid "This has a number of interesting use-cases:"
msgstr "여기에는 여러 가지 흥미로운 사용 사례가 있다:"

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:11
msgid "Remixing the content of existing inscriptions."
msgstr "기존 인스크립션의 콘텐츠를 리믹스."

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:13
msgid ""
"Publishing snippets of code, images, audio, or stylesheets as shared public "
"resources."
msgstr ""
"코드, 이미지, 오디오 또는 스타일시트 스니펫을 공유된 공개 리소스로 발행."

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:16
msgid ""
"Generative art collections where an algorithm is inscribed as JavaScript, "
"and instantiated from multiple inscriptions with unique seeds."
msgstr ""
"알고리즘이 자바스크립트로 새겨져 있고 고유한 시드를 가진 여러 인스크립션에서 "
"인스턴스화되는 제너레이티브 아트 컬렉션."

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:19
msgid ""
"Generative profile picture collections where accessories and attributes are "
"inscribed as individual images, or in a shared texture atlas, and then "
"combined, collage-style, in unique combinations in multiple inscriptions."
msgstr ""
"액세서리와 속성을 개별 이미지 또는 공유된 텍스처 아틀라스(texture atlas) 안"
"에 인스크립션으로 새긴 다음 콜라주 스타일로 여러 인스크립션에서 고유한 조합으"
"로 결합하는 제네레이티브 프로필 사진 컬렉션."

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:23
msgid "A few other endpoints that inscriptions may access are the following:"
msgstr "인스크립션이 액세스할 수 있는 몇 가지 다른 엔드포인트는 다음과 같다:"

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:25
msgid "`/blockheight`: latest block height."
msgstr "`/blockheight`: 최신 블록 높이."

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:26
msgid "`/blockhash`: latest block hash."
msgstr "`/blockhash`: 최신 블록 해시."

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:27
msgid "`/blockhash/<HEIGHT>`: block hash at given block height."
msgstr "`/blockhash/<HEIGHT>`: 주어진 블록 높이에 블록 해시."

#: /workspaces/ord_ko/docs/src/inscriptions/recursion.md:28
msgid "`/blocktime`: UNIX time stamp of latest block."
msgstr "`/blocktime`: 최신 블록의 UNIX 타임스탬프."

#: /workspaces/ord_ko/docs/src/faq.md:1
msgid "Ordinal Theory FAQ"
msgstr "오디널 이론 자주 묻는 질문 (FAQ)"

#: /workspaces/ord_ko/docs/src/faq.md:4
msgid "What is ordinal theory?"
msgstr "오디널 이론이란 무엇인가?"

#: /workspaces/ord_ko/docs/src/faq.md:7
msgid ""
"Ordinal theory is a protocol for assigning serial numbers to satoshis, the "
"smallest subdivision of a bitcoin, and tracking those satoshis as they are "
"spent by transactions."
msgstr ""
"오디널 이론은 비트코인의 가장 작은 단위인 사토시에 일련번호를 할당하고 트랜잭"
"션에서 사토시가 사용될 때 이를 추적하는 프로토콜이다."

#: /workspaces/ord_ko/docs/src/faq.md:11
msgid ""
"These serial numbers are large numbers, like this 804766073970493. Every "
"satoshi, which is ¹⁄₁₀₀₀₀₀₀₀₀ of a bitcoin, has an ordinal number."
msgstr ""
"이 일련번호는 예를 들어 804766073970493같이 큰 숫자이다. 비트코인의 ¹⁄"
"₁₀₀₀₀₀₀₀₀₀인 모든 사토시에는 서수 번호가 있다."

#: /workspaces/ord_ko/docs/src/faq.md:14
msgid ""
"Does ordinal theory require a side chain, a separate token, or changes to "
"Bitcoin?"
msgstr ""
"오디널 이론은 사이드 체인, 별도의 토큰 또는 비트코인 변경을 필요로 하는가?"

#: /workspaces/ord_ko/docs/src/faq.md:17
msgid ""
"Nope! Ordinal theory works right now, without a side chain, and the only "
"token needed is bitcoin itself."
msgstr ""
"아니다! 오디널 이론은 현재 사이드 체인 없이도 작동하며, 필요한 유일한 토큰은 "
"비트코인 자체이다."

#: /workspaces/ord_ko/docs/src/faq.md:20
msgid "What is ordinal theory good for?"
msgstr "오디널 이론의 장점은 무엇인가?"

#: /workspaces/ord_ko/docs/src/faq.md:23
msgid ""
"Collecting, trading, and scheming. Ordinal theory assigns identities to "
"individual satoshis, allowing them to be individually tracked and traded, as "
"curios and for numismatic value."
msgstr ""
"수집, 거래, 그리고 계획. 오디널 이론은 개별 사토시에게 신원을 부여하여 개별적"
"으로 추적하고 수집품이나 화폐적 가치를 위해 거래할 수 있도록 한다."

#: /workspaces/ord_ko/docs/src/faq.md:27
msgid ""
"Ordinal theory also enables inscriptions, a protocol for attaching arbitrary "
"content to individual satoshis, turning them into bitcoin-native digital "
"artifacts."
msgstr ""
"오디널 이론은 또한 개별 사토시에 임의의 콘텐츠를 첨부하여 비트코인 고유의 디"
"지털 아티팩트로 전환하는 프로토콜인 인스크립션을 가능하게 한다."

#: /workspaces/ord_ko/docs/src/faq.md:31
msgid "How does ordinal theory work?"
msgstr "오디널 이론은 어떻게 작동하는가?"

#: /workspaces/ord_ko/docs/src/faq.md:34
msgid ""
"Ordinal numbers are assigned to satoshis in the order in which they are "
"mined. The first satoshi in the first block has ordinal number 0, the second "
"has ordinal number 1, and the last satoshi of the first block has ordinal "
"number 4,999,999,999."
msgstr ""
"서수 번호는 채굴된 순서대로 사토시에 할당된다. 첫 번째 블록의 첫 번째 사토시"
"는 서수 번호 0, 두 번째 사토시는 서수 번호 1, 첫 번째 블록의 마지막 사토시는 "
"서수 번호 4,999,999,999를 갖는다."

#: /workspaces/ord_ko/docs/src/faq.md:39
msgid ""
"Satoshis live in outputs, but transactions destroy outputs and create new "
"ones, so ordinal theory uses an algorithm to determine how satoshis hop from "
"the inputs of a transaction to its outputs."
msgstr ""
"사토시는 출력(output)에 존재하지만 트랜잭션은 출력을 파괴하고 새로운 출력을 "
"생성하므로 오디널 이론은 알고리즘을 사용해 사토시가 트랜잭션의 입력에서 출력"
"으로 이동하는 방식을 결정한다."

#: /workspaces/ord_ko/docs/src/faq.md:43
msgid "Fortunately, that algorithm is very simple."
msgstr "다행히도 이 알고리즘은 매우 간단하다."

#: /workspaces/ord_ko/docs/src/faq.md:45
msgid ""
"Satoshis transfer in first-in-first-out order. Think of the inputs to a "
"transaction as being a list of satoshis, and the outputs as a list of slots, "
"waiting to receive a satoshi. To assign input satoshis to slots, go through "
"each satoshi in the inputs in order, and assign each to the first available "
"slot in the outputs."
msgstr ""
"사토시는 선입선출 순서로 전송된다. 트랜잭션의 입력은 사토시에 대한 목록으로, "
"출력은 사토시 수신을 기다리는 슬롯 목록으로 생각해 보자. 입력 사토시를 슬롯"
"에 할당하려면 입력에 있는 각 사토시를 순서대로 가장 먼저 사용 가능한 출력 슬"
"롯에 할당한다."

#: /workspaces/ord_ko/docs/src/faq.md:51
msgid ""
"Let's imagine a transaction with three inputs and two outputs. The inputs "
"are on the left of the arrow and the outputs are on the right, all labeled "
"with their values:"
msgstr ""
"3개의 입력과 2개의 출력이 있는 트랜잭션을 가정해 보자. 입력은 화살표 왼쪽에 "
"있고 출력은 오른쪽에 있으며 모두 값으로 레이블이 지정되어 있다:"

#: /workspaces/ord_ko/docs/src/faq.md:57
msgid ""
"Now let's label the same transaction with the ordinal numbers of the "
"satoshis that each input contains, and question marks for each output slot. "
"Ordinal numbers are large, so let's use letters to represent them:"
msgstr ""
"이제 동일한 트랜잭션에 각 입력에 포함된 사토시의 서수 번호와 각 출력 슬롯에 "
"물음표로 레이블을 지정해 보자. 서수 번호는 크기가 크므로 문자로 표현해 보자:"

#: /workspaces/ord_ko/docs/src/faq.md:63
msgid ""
"To figure out which satoshi goes to which output, go through the input "
"satoshis in order and assign each to a question mark:"
msgstr ""
"어떤 사토시가 어떤 출력으로 이동하는지 파악하려면 입력된 사토시를 순서대로 살"
"펴보고 각각 물음표에 할당하면 된다:"

#: /workspaces/ord_ko/docs/src/faq.md:68
msgid ""
"What about fees, you might ask? Good question! Let's imagine the same "
"transaction, this time with a two satoshi fee. Transactions with fees send "
"more satoshis in the inputs than are received by the outputs, so to make our "
"transaction into one that pays fees, we'll remove the second output:"
msgstr ""
"수수료는 어떻게 되는가? 좋은 질문이다! 동일한 트랜잭션에 이번에는 2사토시 수"
"수료가 있다고 가정해 보자. 수수료가 있는 트랜잭션은 출력에서 받는 것보다 더 "
"많은 사토시를 입력으로 전송하므로, 수수료를 지불하는 트랜잭션으로 만들기 위"
"해 두 번째 출력을 제거하자:"

#: /workspaces/ord_ko/docs/src/faq.md:75
msgid "The satoshis "
msgstr "사토시 "

#: /workspaces/ord_ko/docs/src/faq.md:75
msgid "e"
msgstr "e"

#: /workspaces/ord_ko/docs/src/faq.md:75
msgid " and "
msgstr " 그리고 "

#: /workspaces/ord_ko/docs/src/faq.md:75
msgid "f"
msgstr "f"

#: /workspaces/ord_ko/docs/src/faq.md:75
msgid " now have nowhere to go in the outputs:"
msgstr " 는 이제 출력에 갈 곳이 없다:"

#: /workspaces/ord_ko/docs/src/faq.md:80
msgid ""
"So they go to the miner who mined the block as fees. [The BIP](https://"
"github.com/ordinals/ord/blob/master/bip.mediawiki) has the details, but in "
"short, fees paid by transactions are treated as extra inputs to the coinbase "
"transaction, and are ordered how their corresponding transactions are "
"ordered in the block. The coinbase transaction of the block might look like "
"this:"
msgstr ""
"따라서 블록을 채굴한 채굴자에게 수수료로 지급된다. [BIP](https://github.com/"
"ordinals/ord/blob/master/bip.mediawiki)에 자세한 내용이 나와 있지만, 간단히 "
"말해 트랜잭션이 지불한 수수료는 코인베이스 트랜잭션에 대한 추가 입력으로 취급"
"되며, 이의 순서는 해당 트랜잭션이 블록에서 어떻게 정렬되는지에 따라서 정해진"
"다. 해당 블록의 코인베이스 트랜잭션은 다음과 같이 보일 수 있다:"

#: /workspaces/ord_ko/docs/src/faq.md:89
msgid "Where can I find the nitty-gritty details?"
msgstr "핵심적인 세부 정보는 어디에서 찾을 수 있는가?"

#: /workspaces/ord_ko/docs/src/faq.md:92
msgid "[The BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr ""
"[BIP을 참조하자!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: /workspaces/ord_ko/docs/src/faq.md:94
msgid ""
"Why are sat inscriptions called \"digital artifacts\" instead of \"NFTs\"?"
msgstr ""
"SAT 인스크립션을 \"NFT\"가 아닌 \"디지털 아티팩트\"라고 부르는 이유는 무엇인"
"가?"

#: /workspaces/ord_ko/docs/src/faq.md:97
msgid ""
"An inscription is an NFT, but the term \"digital artifact\" is used instead, "
"because it's simple, suggestive, and familiar."
msgstr ""
"인스크립션은 NFT이지만, 단순하고 시사하는 바가 많으며 친숙하기 때문에 대신 "
"\"디지털 아티팩트\"라는 용어를 사용한다."

#: /workspaces/ord_ko/docs/src/faq.md:100
msgid ""
"The phrase \"digital artifact\" is highly suggestive, even to someone who "
"has never heard the term before. In comparison, NFT is an acronym, and "
"doesn't provide any indication of what it means if you haven't heard the "
"term before."
msgstr ""
"\"디지털 아티팩트\"라는 문구는 이 용어를 처음 들어본 사람에게도 매우 시사하"
"는 바가 많은 표현이다. 이에 비해 NFT는 약어이며, 이 용어를 처음 들어본 사람이"
"라면 무슨 뜻인지 알 수 없다."

#: /workspaces/ord_ko/docs/src/faq.md:104
msgid ""
"Additionally, \"NFT\" feels like financial terminology, and the both word "
"\"fungible\" and sense of the word \"token\" as used in \"NFT\" is uncommon "
"outside of financial contexts."
msgstr ""
"또한 “NFT”(대체 불가능 토큰)는 금융 용어처럼 느껴지며, 여기서 쓰여지는 “대체 "
"가능한”이라는 단어와 “토큰”이라는 단어의 의미 모두 금융적인 상황을 제외하고"
"는 그 쓰임이 흔하지 않다."

#: /workspaces/ord_ko/docs/src/faq.md:108
msgid "How do sat inscriptions compare to…"
msgstr "SAT 인스크립션을 다음과 비교하면…"

#: /workspaces/ord_ko/docs/src/faq.md:111
msgid "Ethereum NFTs?"
msgstr "이더리움 NFT?"

#: /workspaces/ord_ko/docs/src/faq.md:113
msgid "_Inscriptions are always immutable._"
msgstr "_인스크립션은 항상 불변한다._"

#: /workspaces/ord_ko/docs/src/faq.md:115
msgid ""
"There is simply no way to for the creator of an inscription, or the owner of "
"an inscription, to modify it after it has been created."
msgstr ""
"인스크립이 생성된 후에는 작성자나 소유자가 이를 수정할 수 있는 방법이 없다."

#: /workspaces/ord_ko/docs/src/faq.md:118
msgid ""
"Ethereum NFTs _can_ be immutable, but many are not, and can be changed or "
"deleted by the NFT contract owner."
msgstr ""
"이더리움 NFT는 불변하는 것이 _가능_ 하지만, 많은 경우 불변하지 않으며, 스마"
"트 컨트랙트 소유자가 변경하거나 삭제할 수 있다."

#: /workspaces/ord_ko/docs/src/faq.md:121
msgid ""
"In order to make sure that a particular Ethereum NFT is immutable, the "
"contract code must be audited, which requires detailed knowledge of the EVM "
"and Solidity semantics."
msgstr ""
"특정 이더리움 NFT가 변경 불가능한지 확인하려면 컨트랙트 코드를 감사해야 하"
"며, 이를 위해서는 EVM과 솔리디티 시맨틱에 대한 자세한 지식이 필요하다."

#: /workspaces/ord_ko/docs/src/faq.md:125
msgid ""
"It is very hard for a non-technical user to determine whether or not a given "
"Ethereum NFT is mutable or immutable, and Ethereum NFT platforms make no "
"effort to distinguish whether an NFT is mutable or immutable, and whether "
"the contract source code is available and has been audited."
msgstr ""
"기술 전문가가 아닌 사용자가 특정 이더리움 NFT가 변경 가능한지 또는 변경 불가"
"능한지 여부를 판단하는 것은 매우 어렵고, 이더리움 NFT 플랫폼은 NFT가 변경 가"
"능한지 또는 변경 불가능한지, 컨트랙트 소스 코드가 확인 가능하고 감사를 받았는"
"지 여부를 구분하기 위해 노력하지 않는다."

#: /workspaces/ord_ko/docs/src/faq.md:130
msgid "_Inscription content is always on-chain._"
msgstr "_인스크립션 콘텐츠는 항상 온체인에 있다._"

#: /workspaces/ord_ko/docs/src/faq.md:132
msgid ""
"There is no way for an inscription to refer to off-chain content. This makes "
"inscriptions more durable, because content cannot be lost, and scarcer, "
"because inscription creators must pay fees proportional to the size of the "
"content."
msgstr ""
"인스크립션은 오프체인 콘텐츠를 참조할 수 있는 방법이 없다. 따라서 콘텐츠가 손"
"실되지 않기 때문에 인스크립션의 내구성이 높아지고, 인스크립션 작성자가 콘텐"
"츠 크기에 비례하는 수수료를 지불해야 하기 때문에 희소성이 높아진다."

#: /workspaces/ord_ko/docs/src/faq.md:136
msgid ""
"Some Ethereum NFT content is on-chain, but much is off-chain, and is stored "
"on platforms like IPFS or Arweave, or on traditional, fully centralized web "
"servers. Content on IPFS is not guaranteed to continue to be available, and "
"some NFT content stored on IPFS has already been lost. Platforms like "
"Arweave rely on weak economic assumptions, and will likely fail "
"catastrophically when these economic assumptions are no longer met. "
"Centralized web servers may disappear at any time."
msgstr ""
"일부 이더리움 NFT 콘텐츠는 온체인에 있지만, 대부분은 오프체인에 있으며, IPFS"
"나 Arweave와 같은 플랫폼이나 기존의 완전히 중앙화된 웹 서버에 저장되어 있다. "
"IPFS에 저장된 콘텐츠는 지속적 사용이 보장되지 않으며, IPFS에 저장된 일부 NFT "
"콘텐츠는 이미 손실된 바 있다. Arweave와 같은 플랫폼은 취약한 경제적 가정에 의"
"존하고 있으며, 이러한 경제적 가정이 더 이상 충족되지 않으면 파국적으로 실패"
"할 가능성이 높다. 중앙화된 웹 서버는 언제든 사라질 수 있다."

#: /workspaces/ord_ko/docs/src/faq.md:144
msgid ""
"It is very hard for a non-technical user to determine where the content of a "
"given Ethereum NFT is stored."
msgstr ""
"평범한 사용자가 특정 이더리움 NFT의 콘텐츠가 어디에 저장되어 있는지 파악하는 "
"것은 매우 어렵다."

#: /workspaces/ord_ko/docs/src/faq.md:147
msgid "_Inscriptions are much simpler._"
msgstr "_인스크립션은 훨씬 더 간단하다._"

#: /workspaces/ord_ko/docs/src/faq.md:149
msgid ""
"Ethereum NFTs depend on the Ethereum network and virtual machine, which are "
"highly complex, constantly changing, and which introduce changes via "
"backwards-incompatible hard forks."
msgstr ""
"이더리움 NFT는 이더리움 네트워크와 가상 머신에 의존하며 이는 매우 복잡하고 끊"
"임없이 변화하며, 이전 버전과 호환되지 않는 하드포크를 통해 변경 사항을 도입한"
"다."

#: /workspaces/ord_ko/docs/src/faq.md:153
msgid ""
"Inscriptions, on the other hand, depend on the Bitcoin blockchain, which is "
"relatively simple and conservative, and which introduces changes via "
"backwards-compatible soft forks."
msgstr ""
"반면에 인스크립션은 비트코인 블록체인에 의존하며 이는 상대적으로 단순하고 보"
"수적이며 이전 버전과 호환되는 소프트 포크를 통해 변경 사항을 도입한다."

#: /workspaces/ord_ko/docs/src/faq.md:157
msgid "_Inscriptions are more secure._"
msgstr "_인스크립션이 더 안전하다._"

#: /workspaces/ord_ko/docs/src/faq.md:159
msgid ""
"Inscriptions inherit Bitcoin's transaction model, which allow a user to see "
"exactly which inscriptions are being transferred by a transaction before "
"they sign it. Inscriptions can be offered for sale using partially signed "
"transactions, which don't require allowing a third party, such as an "
"exchange or marketplace, to transfer them on the user's behalf."
msgstr ""
"인스크립션은 비트코인의 트랜잭션 모델을 이어받아 사용자가 서명하기 전에 트랜"
"잭션이 전송하는 인스크립션을 정확히 확인할 수 있다. 인스크립션은 부분적으로 "
"서명된 트랜잭션(PSBT)을 사용하여 판매할 수 있으며, 거래소나 마켓플레이스와 같"
"은 제3자가 사용자를 대신하여 전송하도록 허용할 필요가 없다."

#: /workspaces/ord_ko/docs/src/faq.md:165
msgid ""
"By comparison, Ethereum NFTs are plagued with end-user security "
"vulnerabilities. It is commonplace to blind-sign transactions, grant third-"
"party apps unlimited permissions over a user's NFTs, and interact with "
"complex and unpredictable smart contracts. This creates a minefield of "
"hazards for Ethereum NFT users which are simply not a concern for ordinal "
"theorists."
msgstr ""
"이에 비해 이더리움 NFT는 최종 사용자 보안 취약점으로 골머리를 앓고 있다. 트랜"
"잭션에 블라인드 서명을 하고, 제3자 앱에 사용자 NFT에 대한 무제한 권한을 부여"
"하고, 복잡하고 예측할 수 없는 스마트 콘트랙트와 상호작용하는 것은 흔한 일이"
"다. 이는 이더리움 NFT 사용자에게 위험의 지뢰밭을 만들며, 이는 오디널 이론가들"
"에게는 전혀 문제가 되지 않는다."

#: /workspaces/ord_ko/docs/src/faq.md:171
msgid "_Inscriptions are scarcer._"
msgstr "_인스크립션은 더 희귀하다._"

#: /workspaces/ord_ko/docs/src/faq.md:173
msgid ""
"Inscriptions require bitcoin to mint, transfer, and store. This seems like a "
"downside on the surface, but the raison d'etre of digital artifacts is to be "
"scarce and thus valuable."
msgstr ""
"인스크립션을 발행하고, 전송하고, 보관하려면 비트코인이 필요하다. 이는 겉으로 "
"보기에는 단점처럼 보이지만, 디지털 아티팩트의 존재 이유는 희소성이 높고 따라"
"서 가치 있는 것이 되는 것이다."

#: /workspaces/ord_ko/docs/src/faq.md:177
msgid ""
"Ethereum NFTs, on the other hand, can be minted in virtually unlimited "
"qualities with a single transaction, making them inherently less scarce, and "
"thus, potentially less valuable."
msgstr ""
"반면, 이더리움 NFT는 한 번의 트랜잭션으로 사실상 무제한으로 발행할 수 있으므"
"로 이는 본질적으로 그 희소성을 낮추고 따라서 잠재적으로 그 가치를 떨어뜨릴 "
"수 있다."

#: /workspaces/ord_ko/docs/src/faq.md:181
msgid "_Inscriptions do not pretend to support on-chain royalties._"
msgstr "_인스크립션은 온체인 로열티를 지원하는 것처럼 가장하지 않는다._"

#: /workspaces/ord_ko/docs/src/faq.md:183
msgid ""
"On-chain royalties are a good idea in theory but not in practice. Royalty "
"payment cannot be enforced on-chain without complex and invasive "
"restrictions. The Ethereum NFT ecosystem is currently grappling with "
"confusion around royalties, and is collectively coming to grips with the "
"reality that on-chain royalties, which were messaged to artists as an "
"advantage of NFTs, are not possible, while platforms race to the bottom and "
"remove royalty support."
msgstr ""
"온체인 로열티는 이론적으로는 좋은 아이디어이지만 실제로는 그렇지 않다. 복잡하"
"고 침해적 제한 없이는 온체인에서 로열티 지급을 시행할 수 없다. 현재 이더리움 "
"NFT 생태계는 로열티에 대한 혼란으로 어려움을 겪고 있으며, 플랫폼들이 경쟁적으"
"로 로열티 지원을 없애고 있는 가운데 아티스트들에게 NFT의 장점으로 얘기되었던 "
"온체인 로열티가 불가능하다는 현실을 공동으로 인식하고 있다."

#: /workspaces/ord_ko/docs/src/faq.md:190
msgid ""
"Inscriptions avoid this situation entirely by making no false promises of "
"supporting royalties on-chain, thus avoiding the confusion, chaos, and "
"negativity of the Ethereum NFT situation."
msgstr ""
"인스크립션은 온체인 로열티 지원에 대한 거짓 약속을 하지 않음으로써 이러한 상"
"황을 완전히 피하여 이더리움 NFT 상황의 혼란, 혼돈, 부정적 영향을 피할 수 있"
"다."

#: /workspaces/ord_ko/docs/src/faq.md:194
msgid "_Inscriptions unlock new markets._"
msgstr "_인스크립션은 새로운 시장을 열어준다._"

#: /workspaces/ord_ko/docs/src/faq.md:196
msgid ""
"Bitcoin's market capitalization and liquidity are greater than Ethereum's by "
"a large margin. Much of this liquidity is not available to Ethereum NFTs, "
"since many Bitcoiners prefer not to interact with the Ethereum ecosystem due "
"to concerns related to simplicity, security, and decentralization."
msgstr ""
"비트코인의 시가총액과 유동성은 이더리움보다 큰 폭으로 높다. 많은 비트코인 사"
"용자는 단순성, 보안, 탈중앙화와 관련된 우려로 인해 이더리움 생태계와 상호작용"
"하지 않는 것을 선호하기 때문에 이더리움 NFT에는 이러한 유동성의 상당 부분이 "
"제공되지 않는다."

#: /workspaces/ord_ko/docs/src/faq.md:201
msgid ""
"Such Bitcoiners may be more interested in inscriptions than Ethereum NFTs, "
"unlocking new classes of collector."
msgstr ""
"이러한 비트코인 사용자들은 이더리움 NFT보다 인스크립션에 더 관심을 가질 수 있"
"으며, 새로운 종류의 수집가가 생겨나게 할 수 있다."

#: /workspaces/ord_ko/docs/src/faq.md:204
msgid "_Inscriptions have a richer data model._"
msgstr "_인스크립션에는 더 풍부한 데이터 모델이 있다._"

#: /workspaces/ord_ko/docs/src/faq.md:206
msgid ""
"Inscriptions consist of a content type, also known as a MIME type, and "
"content, which is an arbitrary byte string. This is the same data model used "
"by the web, and allows inscription content to evolve with the web, and come "
"to support any kind of content supported by web browsers, without requiring "
"changes to the underlying protocol."
msgstr ""
"인스크립션은 MIME 유형이라고도 하는 콘텐츠 유형과 임의의 바이트 문자열"
"(string)인 콘텐츠로 구성된다. 이는 웹에서 사용되는 것과 동일한 데이터 모델이"
"며, 인스크립션 콘텐츠가 웹과 함께 진화하여 기본 프로토콜을 변경하지 않고도 "
"웹 브라우저에서 지원하는 모든 종류의 콘텐츠를 지원할 수 있도록 한다."

#: /workspaces/ord_ko/docs/src/faq.md:212
msgid "RGB and Taro assets?"
msgstr "RGB 및 타로(Taro) 자산?"

#: /workspaces/ord_ko/docs/src/faq.md:214
msgid ""
"RGB and Taro are both second-layer asset protocols built on Bitcoin. "
"Compared to inscriptions, they are much more complicated, but much more "
"featureful."
msgstr ""
"RGB와 타로는 모두 비트코인에 기반한 레이어2 자산 프로토콜이다. 인스크립션과 "
"비교하면 훨씬 더 복잡하지만 훨씬 더 많은 기능을 갖추고 있다."

#: /workspaces/ord_ko/docs/src/faq.md:217
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas the primary use-case of RGB and Taro are fungible tokens, so the "
"user experience for inscriptions is likely to be simpler and more polished "
"than the user experience for RGB and Taro NFTs."
msgstr ""
"오디널 이론은 처음부터 디지털 아티팩트를 위해 설계된 반면, RGB와 타로의 주요 "
"사용 사례는 대체 가능한 토큰이므로 인스크립션에 대한 사용자 경험은 RGB와 타"
"로 NFT의 사용자 경험보다 더 단순하고 세련될 가능성이 높다."

#: /workspaces/ord_ko/docs/src/faq.md:222
msgid ""
"RGB and Taro both store content off-chain, which requires additional "
"infrastructure, and which may be lost. By contrast, inscription content is "
"stored on-chain, and cannot be lost."
msgstr ""
"RGB와 타로는 모두 오프체인에 콘텐츠를 저장하기 때문에 추가 인프라가 필요하고 "
"손실될 수 있다. 반면, 인스크립션 콘텐츠는 온체인에 저장되므로 손실될 가능성"
"이 없다."

#: /workspaces/ord_ko/docs/src/faq.md:226
msgid ""
"Ordinal theory, RGB, and Taro are all very early, so this is speculation, "
"but ordinal theory's focus may give it the edge in terms of features for "
"digital artifacts, including a better content model, and features like "
"globally unique symbols."
msgstr ""
"오디널 이론, RGB, 타로는 모두 초기 단계이므로 추측에 불과하지만, 오디널 이론"
"의 초점이 더 나은 콘텐츠 모델 같은 디지털 아티팩트에 대한 기능 측면에서 그리"
"고 전 세계적으로 고유한 심볼과 같은 기능 측면에서 우위를 점하게 해줄수 있다."

#: /workspaces/ord_ko/docs/src/faq.md:231
msgid "Counterparty assets?"
msgstr "카운터파티 자산(Counterparty assets)?"

#: /workspaces/ord_ko/docs/src/faq.md:233
msgid ""
"Counterparty has its own token, XCP, which is required for some "
"functionality, which makes most bitcoiners regard it as an altcoin, and not "
"an extension or second layer for bitcoin."
msgstr ""
"카운터파티는 일부 기능에 필요한 자체 토큰인 XCP를 보유하고 있으며, 이로 인해 "
"대부분의 비트코인 사용자들은 이를 비트코인의 확장 또는 세컨드 레이어가 아닌 "
"알트코인으로 간주한다."

#: /workspaces/ord_ko/docs/src/faq.md:237
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas Counterparty was primarily designed for financial token issuance."
msgstr ""
"오디널 이론은 처음부터 디지털 아티팩트를 위해 설계된 반면, 카운터파티는 주로 "
"금융 토큰 발행을 위해 설계되었다."

#: /workspaces/ord_ko/docs/src/faq.md:240
msgid "Inscriptions for…"
msgstr "누굴 위한 인스크립션…"

#: /workspaces/ord_ko/docs/src/faq.md:243
msgid "Artists"
msgstr "예술가"

#: /workspaces/ord_ko/docs/src/faq.md:245
msgid ""
"_Inscriptions are on Bitcoin._ Bitcoin is the digital currency with the "
"highest status and greatest chance of long-term survival. If you want to "
"guarantee that your art survives into the future, there is no better way to "
"publish it than as inscriptions."
msgstr ""
"_인스크립션은 비트코인에 있다._ 비트코인은 가장 높은 지위와 장기적인 생존 가"
"능성을 가진 디지털 통화이다. 여러분의 작품이 미래에도 살아남을 수 있도록 보장"
"하고 싶다면 인스크립션으로 발행하는 것보다 더 좋은 방법은 없다."

#: /workspaces/ord_ko/docs/src/faq.md:250
msgid ""
"_Cheaper on-chain storage._ At $20,000 per BTC and the minimum relay fee of "
"1 satoshi per vbyte, publishing inscription content costs $50 per 1 million "
"bytes."
msgstr ""
"_더 저렴한 온체인 스토리지._ BTC당 20,000달러 그리고 최소 릴레이 수수료는 "
"vByte당 1 사토시 일 때, 인스크립션 콘텐츠를 발행하는 데 드는 비용은 100만 바"
"이트당 50달러이다."

#: /workspaces/ord_ko/docs/src/faq.md:254
msgid ""
"_Inscriptions are early!_ Inscriptions are still in development, and have "
"not yet launched on mainnet. This gives you an opportunity to be an early "
"adopter, and explore the medium as it evolves."
msgstr ""
"_인스크립션은 이르다!_ 인스크립션은 아직 개발 중이며 아직 메인넷에 출시되지 "
"않았다. 따라서 얼리어답터가 되어 매체가 발전해 나가는 과정을 살펴볼 수 있는 "
"기회이다."

#: /workspaces/ord_ko/docs/src/faq.md:258
msgid ""
"_Inscriptions are simple._ Inscriptions do not require writing or "
"understanding smart contracts."
msgstr ""
"_인스크립션은 간단하다._ 인스크립션은 스마트 컨트랙트를 작성하거나 이해할 필"
"요가 없다."

#: /workspaces/ord_ko/docs/src/faq.md:261
msgid ""
"_Inscriptions unlock new liquidity._ Inscriptions are more accessible and "
"appealing to bitcoin holders, unlocking an entirely new class of collector."
msgstr ""
"_인스크립션은 새로운 유동성을 열어준다._ 인스크립션은 비트코인 보유자가 더 쉽"
"게 접근할 수 있고 매력적으로 보일 수 있으며, 완전히 새로운 종류의 수집가들이 "
"등장하게 할 수 있다."

#: /workspaces/ord_ko/docs/src/faq.md:264
msgid ""
"_Inscriptions are designed for digital artifacts._ Inscriptions are designed "
"from the ground up to support NFTs, and feature a better data model, and "
"features like globally unique symbols and enhanced provenance."
msgstr ""
"_인스크립션은 디지털 아티팩트를 위해 설계되었다._ 인스크립션은 처음부터 NFT"
"를 지원하도록 설계되었으며, 더 나은 데이터 모델과 전 세계적으로 고유한 심볼, "
"향상된 출처 증명과 같은 기능을 갖추고 있다."

#: /workspaces/ord_ko/docs/src/faq.md:268
msgid ""
"_Inscriptions do not support on-chain royalties._ This is negative, but only "
"depending on how you look at it. On-chain royalties have been a boon for "
"creators, but have also created a huge amount of confusion in the Ethereum "
"NFT ecosystem. The ecosystem now grapples with this issue, and is engaged in "
"a race to the bottom, towards a royalties-optional future. Inscriptions have "
"no support for on-chain royalties, because they are technically infeasible. "
"If you choose to create inscriptions, there are many ways you can work "
"around this limitation: withhold a portion of your inscriptions for future "
"sale, to benefit from future appreciation, or perhaps offer perks for users "
"who respect optional royalties."
msgstr ""
"_인스크립션은 온체인 로열티를 지원하지 않는다._ 이는 부정적이지만, 어떻게 보"
"느냐에 따라 다르다. 온체인 로열티는 크리에이터에게 큰 혜택이었지만, 이더리움 "
"NFT 생태계에 엄청난 혼란을 야기하기도 했다. 이제 생태계는 이 문제와 씨름하고 "
"있으며, 로열티가 선택적인 미래를 향해 바닥을 향한 경쟁을 벌이고 있다. 인스크"
"립션은 기술적으로 실현 불가능하기 때문에 온체인 로열티를 지원하지 않는다. 인"
"스크립션을 생성하기로 결정한 경우, 향후 판매를 위해 인스크립션의 일부를 보류"
"하여 향후 가치 상승의 혜택을 받거나 선택적 로열티를 존중하는 사용자에게 혜택"
"을 제공하는 등 여러 가지 방법으로 이 제한을 해결할 수 있다."

#: /workspaces/ord_ko/docs/src/faq.md:279
msgid "Collectors"
msgstr "수집가"

#: /workspaces/ord_ko/docs/src/faq.md:281
msgid ""
"_Inscriptions are simple, clear, and have no surprises._ They are always "
"immutable and on-chain, with no special due diligence required."
msgstr ""
"_인스크립션은 간단하고 명확하며 놀랄 일이 없다._ 인스크립션은 항상 불변하고 "
"온체인이며 특별한 실사가 필요하지 않다."

#: /workspaces/ord_ko/docs/src/faq.md:284
msgid ""
"_Inscriptions are on Bitcoin._ You can verify the location and properties of "
"inscriptions easily with Bitcoin full node that you control."
msgstr ""
"_인스크립션은 비트코인에 있다._ 사용자가 제어하는 비트코인 풀 노드를 통해 인"
"스크립션의 위치와 속성을 쉽게 확인할 수 있다."

#: /workspaces/ord_ko/docs/src/faq.md:287
msgid "Bitcoiners"
msgstr "비트코인 사용자"

#: /workspaces/ord_ko/docs/src/faq.md:289
msgid ""
"Let me begin this section by saying: the most important thing that the "
"Bitcoin network does is decentralize money. All other use-cases are "
"secondary, including ordinal theory. The developers of ordinal theory "
"understand and acknowledge this, and believe that ordinal theory helps, at "
"least in a small way, Bitcoin's primary mission."
msgstr ""
"이 부분을 이렇게 시작하고자 한다: 비트코인 네트워크가 하는 가장 중요한 일은 "
"화폐를 탈중앙화하는 것이다. 오디널 이론을 포함한 다른 모든 사용 사례는 부차적"
"인 것이다. 오디널 이론의 개발자들은 이를 이해하고 인정하며, 오디널 이론이 비"
"트코인의 주요 사명에 조금이라도 도움이 된다고 믿는다."

#: /workspaces/ord_ko/docs/src/faq.md:295
msgid ""
"Unlike many other things in the altcoin space, digital artifacts have merit. "
"There are, of course, a great deal of NFTs that are ugly, stupid, and "
"fraudulent. However, there are many that are fantastically creative, and "
"creating and collecting art has been a part of the human story since its "
"inception, and predates even trade and money, which are also ancient "
"technologies."
msgstr ""
"알트코인 공간의 다른 많은 것들과 달리 디지털 아티팩트에는 장점이 있다. 물론 "
"추악하고 어리석고 사기적인 NFT도 많이 있다. 하지만 환상적으로 창의적인 예술품"
"도 많이 있으며, 예술품을 만들고 수집하는 것은 인류의 시작부터 이어져 온 일이"
"며, 고대 기술인 무역과 화폐보다 더 오래 전부터 존재해 왔다."

#: /workspaces/ord_ko/docs/src/faq.md:302
msgid ""
"Bitcoin provides an amazing platform for creating and collecting digital "
"artifacts in a secure, decentralized way, that protects users and artists in "
"the same way that it provides an amazing platform for sending and receiving "
"value, and for all the same reasons."
msgstr ""
"비트코인은 안전하고 탈중앙화된 방식으로 디지털 아티팩트를 만들고 수집할 수 있"
"는 놀라운 플랫폼을 제공하며, 같은 이유로 가치를 주고받을 수 있는 놀라운 플랫"
"폼을 제공하며 동시에 같은 방식으로 사용자와 예술가를 보호한다."

#: /workspaces/ord_ko/docs/src/faq.md:307
msgid ""
"Ordinals and inscriptions increase demand for Bitcoin block space, which "
"increase Bitcoin's security budget, which is vital for safeguarding "
"Bitcoin's transition to a fee-dependent security model, as the block subsidy "
"is halved into insignificance."
msgstr ""
"오디널과 인스크립션은 비트코인 블록 공간에 대한 수요를 증가시켜 비트코인의 보"
"안 예산을 증가시키며, 이는 블록 보조금이 무의미한 값으로 반감 되는 상황에서 "
"비트코인이 수수료 의존적 보안 모델로 전환하는 것을 보호하는 데 필수적이다."

#: /workspaces/ord_ko/docs/src/faq.md:312
msgid ""
"Inscription content is stored on-chain, and thus the demand for block space "
"for use in inscriptions is unlimited. This creates a buyer of last resort "
"for _all_ Bitcoin block space. This will help support a robust fee market, "
"which ensures that Bitcoin remains secure."
msgstr ""
"인스크립션 콘텐츠는 온체인에 저장되며, 따라서 인스크립션에 사용되는 블록 공간"
"에 대한 수요는 무제한이다. 이는 _모든_ 비트코인 블록 공간에 대한 최종구매자"
"를 만든다. 이는 비트코인이 안전하게 유지되는 것을 보장해 주는 강력한 수수료 "
"시장을 지원하는 데 도움이 될 것이다."

#: /workspaces/ord_ko/docs/src/faq.md:317
msgid ""
"Inscriptions also counter the narrative that Bitcoin cannot be extended or "
"used for new use-cases. If you follow projects like DLCs, Fedimint, "
"Lightning, Taro, and RGB, you know that this narrative is false, but "
"inscriptions provide a counter argument which is easy to understand, and "
"which targets a popular and proven use case, NFTs, which makes it highly "
"legible."
msgstr ""
"인스크립션은 또한 비트코인을 확장하거나 새로운 사용 사례에 사용할 수 없다는 "
"내러티브를 반박한다. DLC, 페디민트, 라이트닝, 타로, RGB와 같은 프로젝트를 팔"
"로우하신다면 이 내러티브가 거짓이라는 것을 알고 있겠지만, 인스크립션 제시하"
"는 반론은 이해하기 쉽고 이 반론은 대중적이고 검증된 사용 사례인 NFT를 대상으"
"로 하기 때문에 이를 판독하기 쉽게 한다."

#: /workspaces/ord_ko/docs/src/faq.md:323
msgid ""
"If inscriptions prove, as the authors hope, to be highly sought after "
"digital artifacts with a rich history, they will serve as a powerful hook "
"for Bitcoin adoption: come for the fun, rich art, stay for the decentralized "
"digital money."
msgstr ""
"저자들의 바람대로 인스크립션이 풍부한 역사를 지닌 인기가 높은 디지털 아티팩트"
"라는 것이 증명된다면, 인스크립션은 비트코인 채택의 강력한 밑밥 역할을 하게 "
"될 것이다: 재미있고 풍부한 예술 때문에 와보고 탈중앙화된 디지털 화폐 때문에 "
"머무르자."

#: /workspaces/ord_ko/docs/src/faq.md:327
msgid ""
"Inscriptions are an extremely benign source of demand for block space. "
"Unlike, for example, stablecoins, which potentially give large stablecoin "
"issuers influence over the future of Bitcoin development, or DeFi, which "
"might centralize mining by introducing opportunities for MEV, digital art "
"and collectables on Bitcoin, are unlikely to produce individual entities "
"with enough power to corrupt Bitcoin. Art is decentralized."
msgstr ""
"인스크립션은 블록 공간에 대한 매우 양성적인 수요의 원천이다. 예를 들어, 잠재"
"적으로 대규모 스테이블코인 발행자가 비트코인 개발의 미래에 영향력을 행사할 "
"수 있게 할수 있는 스테이블코인이나, 비트코인에 MEV를 적용할 기회를 도입하여 "
"채굴을 중앙화 시킬 수 있는 탈중앙 금융(DeFi)과 달리, 비트코인 디지털 아트와 "
"수집품이 비트코인을 손상시킬 수 있는 충분한 힘을 가진 개별 주체를 만들어 낼 "
"가능성은 희박하다. 예술은 탈중앙화되어 있다."

#: /workspaces/ord_ko/docs/src/faq.md:334
msgid ""
"Inscription users and service providers are incentivized to run Bitcoin full "
"nodes, to publish and track inscriptions, and thus throw their economic "
"weight behind the honest chain."
msgstr ""
"인스크립션 사용자와 서비스 제공자는 비트코인 풀 노드를 운영하고, 인스크립션"
"을 발행하고 추적하여 정직한 체인에 경제적 힘을 실어줄 동기부여를 받는다."

#: /workspaces/ord_ko/docs/src/faq.md:338
msgid ""
"Ordinal theory and inscriptions do not meaningfully affect Bitcoin's "
"fungibility. Bitcoin users can ignore both and be unaffected."
msgstr ""
"오디널 이론과 인스크립션은 비트코인의 대체 가능성에 의미 있는 영향을 미치지 "
"않는다. 비트코인 사용자는 두 가지를 모두 무시해도 아무런 영향을 받지 않을 것"
"이다."

#: /workspaces/ord_ko/docs/src/faq.md:341
msgid ""
"We hope that ordinal theory strengthens and enriches bitcoin, and gives it "
"another dimension of appeal and functionality, enabling it more effectively "
"serve its primary use case as humanity's decentralized store of value."
msgstr ""
"우리의 바람은 오디널 이론이 비트코인을 강화하고 풍성하게 하며, 비트코인의 매"
"력과 기능에 또 다른 차원을 부여하여 비트코인이 인류의 탈중앙화된 가치 저장소"
"로서 이 주요 사용 사례를 더욱 효과적으로 수행할 수 있게 해 주는 것이다."

#: /workspaces/ord_ko/docs/src/contributing.md:1
msgid "Contributing to `ord`"
msgstr "`ord`에 기여하기"

#: /workspaces/ord_ko/docs/src/contributing.md:4
msgid "Suggested Steps"
msgstr "권장 단계"

#: /workspaces/ord_ko/docs/src/contributing.md:7
msgid "Find an issue you want to work on."
msgstr "작업하고 싶은 이슈를 찾아보자."

#: /workspaces/ord_ko/docs/src/contributing.md:8
msgid ""
"Figure out what would be a good first step towards resolving the issue. This "
"could be in the form of code, research, a proposal, or suggesting that it be "
"closed, if it's out of date or not a good idea in the first place."
msgstr ""
"문제 해결을 위한 첫 번째 단계로 무엇이 좋을지 생각해 보자. 이는 코드, 연구, "
"제안의 형태가 될 수도 있고, 오래되었거나 애초에 좋은 아이디어가 아닌 경우 폐"
"쇄를 제안하는 것일 수도 있다."

#: /workspaces/ord_ko/docs/src/contributing.md:11
msgid ""
"Comment on the issue with an outline of your suggested first step, and "
"asking for feedback. Of course, you can dive in and start writing code or "
"tests immediately, but this avoids potentially wasted effort, if the issue "
"is out of date, not clearly specified, blocked on something else, or "
"otherwise not ready to implement."
msgstr ""
"제안한 첫 번째 단계의 개요와 함께 문제에 대해 댓글을 달고 피드백을 요청하자. "
"물론 바로 코드나 테스트 작성을 시작할 수도 있지만, 이렇게 하면 문제가 오래되"
"었거나, 명확하게 지정되지 않았거나, 다른 것에 의해 차단되었거나, 구현할 준비"
"가 되지 않은 경우 잠재적으로 낭비될 수 있는 노력을 피할 수 있다."

#: /workspaces/ord_ko/docs/src/contributing.md:16
msgid ""
"If the issue requires a code change or bugfix, open a draft PR with tests, "
"and ask for feedback. This makes sure that everyone is on the same page "
"about what needs to be done, or what the first step in solving the issue "
"should be. Also, since tests are required, writing the tests first makes it "
"easy to confirm that the change can be tested easily."
msgstr ""
"문제에 코드 변경이나 버그 수정이 필요한 경우 테스트가 포함된 PR 초안을 오픈하"
"고 피드백을 요청하자. 이렇게 하면 모든 사람이 수행해야 할 작업이나 문제 해결"
"의 첫 단계가 무엇인지에 대해 동일한 정보를 공유할 수 있다. 또한 테스트가 필요"
"하므로 테스트를 먼저 작성하면 변경 사항을 쉽게 테스트할 수 있는지 쉽게 확인"
"할 수 있다."

#: /workspaces/ord_ko/docs/src/contributing.md:21
msgid ""
"Mash the keyboard randomly until the tests pass, and refactor until the code "
"is ready to submit."
msgstr ""
"테스트가 통과될 때까지 키보드를 무작위로 매시하고 코드를 제출할 준비가 될 때"
"까지 리팩터링하자."

#: /workspaces/ord_ko/docs/src/contributing.md:23
msgid "Mark the PR as ready to review."
msgstr "PR을 리뷰할 준비가 된 것으로 표시한다."

#: /workspaces/ord_ko/docs/src/contributing.md:24
msgid "Revise the PR as needed."
msgstr "필요에 따라 PR을 수정한다."

#: /workspaces/ord_ko/docs/src/contributing.md:25
msgid "And finally, mergies!"
msgstr "마지막으로 병합(merge)하자!"

#: /workspaces/ord_ko/docs/src/contributing.md:27
msgid "Start small"
msgstr "작은 것에서부터 시작"

#: /workspaces/ord_ko/docs/src/contributing.md:30
msgid ""
"Small changes will allow you to make an impact quickly, and if you take the "
"wrong tack, you won't have wasted much time."
msgstr ""
"작은 변화는 빠르게 영향력을 발휘할 수 있게 해 주며, 잘못된 방법을 선택하더라"
"도 많은 시간을 낭비하지 않을 수 있다."

#: /workspaces/ord_ko/docs/src/contributing.md:33
msgid "Ideas for small issues:"
msgstr "작은 문제에 대한 몇 가지 아이디어:"

#: /workspaces/ord_ko/docs/src/contributing.md:34
msgid "Add a new test or test case that increases test coverage"
msgstr "테스트 커버리지를 늘리는 새 테스트 또는 테스트 케이스 추가하자"

#: /workspaces/ord_ko/docs/src/contributing.md:35
msgid "Add or improve documentation"
msgstr "문서를 추가 또는 개선하자"

#: /workspaces/ord_ko/docs/src/contributing.md:36
msgid ""
"Find an issue that needs more research, and do that research and summarize "
"it in a comment"
msgstr "더 많은 조사가 필요한 이슈를 찾아서 조사를 하고 댓글에 요약하자"

#: /workspaces/ord_ko/docs/src/contributing.md:38
msgid "Find an out-of-date issue and comment that it can be closed"
msgstr "오래된 이슈를 찾아서 닫을 수 있다고 댓글을 달자"

#: /workspaces/ord_ko/docs/src/contributing.md:39
msgid ""
"Find an issue that shouldn't be done, and provide constructive feedback "
"detailing why you think that is the case"
msgstr ""
"작업해서는 안 되는 이슈를 찾아서 왜 그렇게 생각하는지 자세히 설명하는 건설적"
"인 피드백을 제공하자"

#: /workspaces/ord_ko/docs/src/contributing.md:42
msgid "Merge early and often"
msgstr "일찍 그리고 자주 병합하자"

#: /workspaces/ord_ko/docs/src/contributing.md:45
msgid ""
"Break up large tasks into multiple smaller steps that individually make "
"progress. If there's a bug, you can open a PR that adds a failing ignored "
"test. This can be merged, and the next step can be to fix the bug and "
"unignore the test. Do research or testing, and report on your results. Break "
"a feature into small sub-features, and implement them one at a time."
msgstr ""
"큰 작업을 여러 개의 작은 단계로 나누어 개별적으로 진행하자. 버그가 있는 경우 "
"실패한 테스트 무시를 추가하는 PR을 열 수 있다. 이를 병합할 수 있고 다음 단계"
"로 버그를 수정하고 테스트를 무시 해제할 수 있다. 연구 또는 테스트를 수행하고 "
"결과를 보고하자. 기능을 작은 하위 기능으로 나누고 한 번에 하나씩 구현하자."

#: /workspaces/ord_ko/docs/src/contributing.md:51
msgid ""
"Figuring out how to break down a larger PR into smaller PRs where each can "
"be merged is an art form well-worth practicing. The hard part is that each "
"PR must itself be an improvement."
msgstr ""
"큰 PR을 병합할 수 있는 작은 PR로 세분화하는 방법을 찾는 것은 연습해 볼 만한 "
"가치가 있는 기술이다. 어려운 부분은 각 PR 자체가 프로잭트를 개선해야 한다는 "
"것이다."

#: /workspaces/ord_ko/docs/src/contributing.md:55
msgid ""
"I strive to follow this advice myself, and am always better off when I do."
msgstr ""
"필자도 이 조언을 따르기 위해 노력하고 있으며, 그렇게 할 때 항상 더 나은 결과"
"를 얻는다."

#: /workspaces/ord_ko/docs/src/contributing.md:57
msgid ""
"Small changes are fast to write, review, and merge, which is much more fun "
"than laboring over a single giant PR that takes forever to write, review, "
"and merge. Small changes don't take much time, so if you need to stop "
"working on a small change, you won't have wasted much time as compared to a "
"larger change that represents many hours of work. Getting a PR in quickly "
"improves the project a little bit immediately, instead of having to wait a "
"long time for larger improvement. Small changes are less likely to "
"accumulate merge conflict. As the Athenians said: _The fast commit what they "
"will, the slow merge what they must._"
msgstr ""
"작은 변경 사항은 작성, 검토 및 병합이 빠르기 때문에 작성, 검토 및 병합에 오"
"랜 시간이 걸리는 거대한 단일 PR을 작성하는 것보다 훨씬 더 재미있다. 작은 변경"
"은 시간이 많이 걸리지 않으므로 작은 변경으로 작업을 중단해야 하는 경우에도 많"
"은 시간이 소요되는 큰 변경에 비해 시간을 낭비하지 않을 수 있다. PR을 신속하"
"게 적용하면 더 큰 개선을 위해 오랜 시간을 기다릴 필요 없이 즉시 프로젝트가 조"
"금씩 개선된다. 작은 변경은 병합 갈등이 누적될 가능성이 적다. 아테네인들이 말"
"했듯이 _빠른 이는 하고싶은 것을 커밋하고, 느린 이는 해야할 것을 병합한다._"

#: /workspaces/ord_ko/docs/src/contributing.md:67
msgid "Get help"
msgstr "도움 받기"

#: /workspaces/ord_ko/docs/src/contributing.md:70
msgid ""
"If you're stuck for more than 15 minutes, ask for help, like a Rust Discord, "
"Stack Exchange, or in a project issue or discussion."
msgstr ""
"15분 이상 막혔을 경우 Rust 디스코드, 스택 익스체인지 (Stack Exchange), 프로젝"
"트 이슈 또는 토론에서 도움을 요청하자."

#: /workspaces/ord_ko/docs/src/contributing.md:73
msgid "Practice hypothesis-driven debugging"
msgstr "가설 중심 디버깅을 사용하자"

#: /workspaces/ord_ko/docs/src/contributing.md:76
msgid ""
"Formulate a hypothesis as to what is causing the problem. Figure out how to "
"test that hypothesis. Perform that tests. If it works, great, you fixed the "
"issue or now you know how to fix the issue. If not, repeat with a new "
"hypothesis."
msgstr ""
"문제의 원인에 대한 가설을 세운다. 그 가설을 테스트하는 방법을 알아낸다. 해당 "
"테스트를 수행한다. 테스트가 효과가 있다면 문제를 해결했거나 이제 문제를 해결"
"하는 방법을 알게 된 것이다. 그렇지 않다면 새로운 가설로 반복한다."

#: /workspaces/ord_ko/docs/src/contributing.md:81
msgid "Pay attention to error messages"
msgstr "오류 메시지에 주의를 기울이자"

#: /workspaces/ord_ko/docs/src/contributing.md:84
msgid "Read all error messages and don't tolerate warnings."
msgstr "모든 오류 메시지를 읽고 경고를 무시하지 말자."

#: /workspaces/ord_ko/docs/src/donate.md:4
msgid ""
"Ordinals is open-source and community funded. The current lead maintainer of "
"`ord` is [raphjaph](https://github.com/raphjaph/). Raph's work on `ord` is "
"entirely funded by donations. If you can, please consider donating!"
msgstr ""
"오디널스는 오픈소스이며 커뮤니티 펀딩을 받고 있다. 현재 `ord`의 리드 메인터너"
"는 [raphjaph](https://github.com/raphjaph/)이다. Raph의 `ord` 작업은 전적으"
"로 기부금으로 충당된다. 가능하다면 기부에 동참하자!"

#: /workspaces/ord_ko/docs/src/donate.md:8
msgid ""
"The donation address for Bitcoin is "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************). The "
"donation address for inscriptions is "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************)."
msgstr ""
"비트코인 관련 기부 주소는 "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************)이다. 인스크립"
"션 관련 기부 주소는 "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************)이다."

#: /workspaces/ord_ko/docs/src/donate.md:11
msgid ""
"Both addresses are in a 2 of 4 multisig wallet with keys held by [raphjaph]"
"(https://twitter.com/raphjaph), [erin](https://twitter.com/realizingerin), "
"[rodarmor](https://twitter.com/rodarmor), and [ordinally](https://twitter."
"com/veryordinally)."
msgstr ""
"두 주소는 [raphjaph](https://twitter.com/raphjaph), [erin](https://twitter."
"com/realizingerin), [rodarmor](https://twitter.com/rodarmor), [ordinally]"
"(https://twitter.com/veryordinally)가 키를 보유한 2-of-4 다중서명 지갑에 있"
"다."

#: /workspaces/ord_ko/docs/src/donate.md:17
msgid ""
"Donations received will go towards funding maintenance and development of "
"`ord`, as well as hosting costs for [ordinals.com](https://ordinals.com)."
msgstr ""
"받은 기부금은 'ord'의 유지 및 개발 자금과 [ordinals.com](https://ordinals."
"com)의 호스팅 비용으로 사용된다."

#: /workspaces/ord_ko/docs/src/donate.md:20
msgid "Thank you for donating!"
msgstr "기부해 주어서 감사하다!"

#: /workspaces/ord_ko/docs/src/guides.md:1
msgid "Ordinal Theory Guides"
msgstr "오디널 이론 설명서들"

#: /workspaces/ord_ko/docs/src/guides.md:4
msgid ""
"See the table of contents for a list of guides, including a guide to the "
"explorer, a guide for sat hunters, and a guide to inscriptions."
msgstr ""
"탐색기(익스플로러) 가이드, SAT 헌터 가이드, 인스크립션 가이드 등 설명서 목록"
"을 보려면 목차를 참조하자."

#: /workspaces/ord_ko/docs/src/guides/explorer.md:1
msgid "Ordinal Explorer"
msgstr "오디널 탐색기"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:4
msgid ""
"The `ord` binary includes a block explorer. We host a instance of the block "
"explorer on mainnet at [ordinals.com](https://ordinals.com), and on signet "
"at [signet.ordinals.com](https://signet.ordinals.com)."
msgstr ""
"`ord` 바이너리에는 블록 탐색기가 포함되어 있다. 블록 탐색기의 인스턴스는 메인"
"넷은 [ordinals.com](https://ordinals.com)에서, 시그넷은 [signet.ordinals.com]"
"(https://signet.ordinals.com)에서 호스팅된다."

#: /workspaces/ord_ko/docs/src/guides/explorer.md:8
msgid "Running The Explorer"
msgstr "탐색기 실행"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:9
msgid "The server can be run locally with:"
msgstr "서버는 이것으로 로컬에서 실행할 수 있다:"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:11
msgid "`ord server`"
msgstr "`ord server`"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:13
msgid "To specify a port add the `--http-port` flag:"
msgstr "포트를 지정하려면 `--http-port` 플래그를 추가한다:"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:15
msgid "`ord server --http-port 8080`"
msgstr "`ord server —http-port 8080`"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:17
msgid ""
"To enable the JSON-API endpoints add the `--enable-json-api` or `-j` flag "
"(see [here](#json-api) for more info):"
msgstr ""
"JSON-API 엔드포인트를 활성화하려면 `--enable-json-api` 또는 `-j` 플래그를 추"
"가한다:"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:20
msgid "`ord --enable-json-api server`"
msgstr "`ord --enable-json-api server`"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:22
msgid "To test how your inscriptions will look you can run:"
msgstr "인스크립션이 어떻게 나올지 테스트하려면 이것을 실행해 보라:"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:24
msgid "`ord preview <FILE1> <FILE2> ...`"
msgstr "`ord preview <FILE1> <FILE2> …`"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:26
msgid "Search"
msgstr "검색"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:29
msgid "The search box accepts a variety of object representations."
msgstr "검색 상자에는 다양한 개체 표현이 허용된다."

#: /workspaces/ord_ko/docs/src/guides/explorer.md:31
msgid "Blocks"
msgstr "블록"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:33
msgid "Blocks can be searched by hash, for example, the genesis block:"
msgstr "블록은 해시별로 검색할 수 있다 (예를 들어 제네시스 블록):"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:35
msgid ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://"
"ordinals.com/"
"search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"
msgstr ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://"
"ordinals.com/"
"search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:37
msgid "Transactions"
msgstr "트랜잭션"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:39
msgid ""
"Transactions can be searched by hash, for example, the genesis block "
"coinbase transaction:"
msgstr ""
"트랜잭션을 해시별로 검색할 수 있다 (예를 들어 제네시스 블록 코인베이스 트랜잭"
"션):"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:42
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:44
msgid "Outputs"
msgstr "출력"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:46
msgid ""
"Transaction outputs can searched by outpoint, for example, the only output "
"of the genesis block coinbase transaction:"
msgstr ""
"트랜잭션 출력을 아웃포인트별로 검색할 수 있다 (예를 들어 제네시스 블록 코인베"
"이스 트랜잭션의 유일한 출력):"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:49
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:51
msgid "Sats"
msgstr "Sats"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:53
msgid ""
"Sats can be searched by integer, their position within the entire bitcoin "
"supply:"
msgstr ""
"각 SAT은 정수로 검색할 수 있다. 이는 전체 비트코인 공급량 내에서 해당 SAT의 "
"위치다:"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:56
msgid "[2099994106992659](https://ordinals.com/search/2099994106992659)"
msgstr "[2099994106992659](https://ordinals.com/search/2099994106992659)"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:58
msgid "By decimal, their block and offset within that block:"
msgstr ""
"소수점 기준으로 검색할 수 있다. 이는 해당 블록과 해당 블록 내의 오프셋을 나타"
"낸다:"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:60
msgid "[481824.0](https://ordinals.com/search/481824.0)"
msgstr "[481824.0](https://ordinals.com/search/481824.0)"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:62
msgid ""
"By degree, their cycle, blocks since the last halving, blocks since the last "
"difficulty adjustment, and offset within their block:"
msgstr ""
"도표기법으로 검색할 수 있다. 이는 주기, 마지막 반감기 이후 블록, 마지막 난이"
"도 조정 이후 블록, 블록 내 오프셋을 나타낸다:"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:65
msgid "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"
msgstr "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:67
msgid ""
"By name, their base 26 representation using the letters \"a\" through \"z\":"
msgstr ""
"이름으로 검색할 수 있다. 이는 문자 “a”부터 “z”까지를 사용하여 SAT을 base26으"
"로 표기하는 법이다:"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:69
msgid "[ahistorical](https://ordinals.com/search/ahistorical)"
msgstr "[ahistorical](https://ordinals.com/search/ahistorical)"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:71
msgid ""
"Or by percentile, the percentage of bitcoin's supply that has been or will "
"have been issued when they are mined:"
msgstr ""
"또는 백분위수로 검색할 수 있다. 이는 해당 SAT가 채굴되었을 때 이미 발행되었거"
"나 앞으로 발행될 비트코인의 공급량의 비율이다:"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:74
msgid "[100%](https://ordinals.com/search/100%)"
msgstr "[100%](https://ordinals.com/search/100%)"

#: /workspaces/ord_ko/docs/src/guides/explorer.md:76
msgid "JSON-API"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:79
msgid ""
"You can run `ord` with the `--enable-json-api` flag to access endpoints that "
"return JSON instead of HTML if you set the HTTP `Accept: application/json` "
"header. The structure of theses objects closely follows what is shown in the "
"HTML. These endpoints are:"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:84
msgid "`/inscription/<INSCRIPTION_ID>`"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:85
#| msgid "Inscriptions"
msgid "`/inscriptions`"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:86
msgid "`/inscriptions/block/<BLOCK_HEIGHT>`"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:87
msgid "`/inscriptions/block/<BLOCK_HEIGHT>/<PAGE_INDEX>`"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:88
#| msgid "Inscriptions"
msgid "`/inscriptions/<FROM>`"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:89
msgid "`/inscriptions/<FROM>/<N>`"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:90
#: /workspaces/ord_ko/docs/src/guides/explorer.md:91
msgid "`/output/<OUTPOINT>`"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:92
msgid "`/sat/<SAT>`"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:94
msgid "To get a list of the latest 100 inscriptions you would do:"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:96
msgid ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/inscriptions'\n"
"```"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:100
msgid ""
"To see information about a UTXO, which includes inscriptions inside it, do:"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:102
msgid ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/output/"
"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed:0'\n"
"```"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:106
msgid "Which returns:"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/explorer.md:108
msgid ""
"```\n"
"{\n"
"  \"value\": 10000,\n"
"  \"script_pubkey\": \"OP_PUSHNUM_1 OP_PUSHBYTES_32 "
"156cc4878306157720607cdcb4b32afa4cc6853868458d7258b907112e5a434b\",\n"
"  \"address\": "
"\"bc1pz4kvfpurqc2hwgrq0nwtfve2lfxvdpfcdpzc6ujchyr3ztj6gd9sfr6ayf\",\n"
"  \"transaction\": "
"\"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed\",\n"
"  \"sat_ranges\": null,\n"
"  \"inscriptions\": [\n"
"    \"6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0\"\n"
"  ]\n"
"}\n"
"```"
msgstr ""

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:1
msgid "Ordinal Inscription Guide"
msgstr "오디널 인스크립션 가이드"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:4
msgid ""
"Individual sats can be inscribed with arbitrary content, creating Bitcoin-"
"native digital artifacts that can be held in a Bitcoin wallet and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"각 SAT에 임의의 콘텐츠를 새길 수 있으며, 이로써 비트코인 지갑에 보관하고 비트"
"코인 거래를 통해 전송할 수 있는 고유한 비트코인 자체 디지털 아티팩트를 만들 "
"수 있다. 인스크립션은 비트코인만큼이나 내구성이 있고, 불변하며, 안전하고, 탈"
"중앙화되어 있다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:9
msgid ""
"Working with inscriptions requires a Bitcoin full node, to give you a view "
"of the current state of the Bitcoin blockchain, and a wallet that can create "
"inscriptions and perform sat control when constructing transactions to send "
"inscriptions to another wallet."
msgstr ""
"인스크립션 작업에는 비트코인 블록체인의 현재 상태를 볼 수 있는 비트코인 풀 노"
"드와 인스크립션을 생성하고 다른 지갑으로 인스크립션을 전송하는 트랜잭션을 만"
"들 때 SAT 제어를 수행할 수 있는 지갑이 필요하다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:14
msgid ""
"Bitcoin Core provides both a Bitcoin full node and wallet. However, the "
"Bitcoin Core wallet cannot create inscriptions and does not perform sat "
"control."
msgstr ""
"Bitcoin Core는 비트코인 풀노드와 지갑을 모두 제공한다. 그러나 Bitcoin Core 지"
"갑은 인스크립션을 생성할 수 없으며, SAT 제어를 수행하지 않는다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:17
msgid ""
"This requires [`ord`](https://github.com/ordinals/ord), the ordinal utility. "
"`ord` doesn't implement its own wallet, so `ord wallet` subcommands interact "
"with Bitcoin Core wallets."
msgstr ""
"이를 위해서는 오디널 유틸리티인 [`ord`](https://github.com/ordinals/ord)가 필"
"요하다. `ord`는 자체 지갑을 구현하지 않으므로 `ord wallet` 하위 명령은 "
"Bitcoin Core 지갑과 상호작용한다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:21
msgid "This guide covers:"
msgstr "이 가이드에서 다룰 토픽:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:23
#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:39
msgid "Installing Bitcoin Core"
msgstr "Bitcoin Core 설치"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:24
msgid "Syncing the Bitcoin blockchain"
msgstr "비트코인 블록체인 동기화"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:25
msgid "Creating a Bitcoin Core wallet"
msgstr "Bitcoin Core 지갑 만들기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:26
msgid "Using `ord wallet receive` to receive sats"
msgstr "`ord wallet receive`을 사용하여 SAT 받기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:27
msgid "Creating inscriptions with `ord wallet inscribe`"
msgstr "`ord wallet inscribe`으로 인스크립션 만들기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:28
msgid "Sending inscriptions with `ord wallet send`"
msgstr "`ord wallet send`로 인스크립션 보내기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:29
msgid "Receiving inscriptions with `ord wallet receive`"
msgstr "`ord wallet receive`로 인스크립션 받기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:31
msgid "Getting Help"
msgstr "도움 받기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:34
msgid ""
"If you get stuck, try asking for help on the [Ordinals Discord Server]"
"(https://discord.com/invite/87cjuz4FYg), or checking GitHub for relevant "
"[issues](https://github.com/ordinals/ord/issues) and [discussions](https://"
"github.com/ordinals/ord/discussions)."
msgstr ""
"문제가 해결되지 않는다면, [오디널스 디스코드 서버](https://discord.com/"
"invite/87cjuz4FYg)에서 도움을 요청해 보거나 깃허브에서 관련 [이슈](https://"
"github.com/ordinals/ord/issues) 및 [토론](https://github.com/ordinals/ord/"
"discussions)을 확인해 보자."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:42
msgid ""
"Bitcoin Core is available from [bitcoincore.org](https://bitcoincore.org/) "
"on the [download page](https://bitcoincore.org/en/download/)."
msgstr ""
"Bitcoin Core는 [bitcoincore.org](https://bitcoincore.org/)의 [다운로드 페이"
"지](https://bitcoincore.org/en/download/)에서 이용할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:45
msgid "Making inscriptions requires Bitcoin Core 24 or newer."
msgstr "인스립션을 만들려면 Bitcoin Core 24 이상이 필요하다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:47
msgid ""
"This guide does not cover installing Bitcoin Core in detail. Once Bitcoin "
"Core is installed, you should be able to run `bitcoind -version` "
"successfully from the command line. Do _NOT_ use `bitcoin-qt`."
msgstr ""
"이 가이드에서는 Bitcoin Core 설치에 대해 자세히 다루지 않는다. Bitcoin Core"
"가 설치되면 명령줄에서 `bitcoind -version`을 성공적으로 실행할 수 있어야 한"
"다. `bitcoin-qt`는 사용하지 말라."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:51
msgid "Configuring Bitcoin Core"
msgstr "Bitcoin Core 설정하기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:54
msgid "`ord` requires Bitcoin Core's transaction index and rest interface."
msgstr "`ord`는 Bitcoin Core의 트랜잭션 인덱스와 rest 인터페이스가 필요하다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:56
msgid ""
"To configure your Bitcoin Core node to maintain a transaction index, add the "
"following to your `bitcoin.conf`:"
msgstr ""
"트랜잭션 인덱스를 유지하도록 Bitcoin Core 노드를 설정하려면, `bitcoin.conf`"
"에 다음을 추가한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:63
msgid "Or, run `bitcoind` with `-txindex`:"
msgstr "또는 `-txindex`와 함께 `bitcoind`를 실행한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:69
msgid ""
"Details on creating or modifying your `bitcoin.conf` file can be found [here]"
"(https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md)."
msgstr ""
"`bitcoin.conf` 파일 생성 또는 수정에 대한 자세한 내용은 [여기](https://"
"github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md)에서 확인할 수 있"
"다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:72
msgid "Syncing the Bitcoin Blockchain"
msgstr "비트코인 블록체인 동기화"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:75
msgid "To sync the chain, run:"
msgstr "체인을 동기화하려면 다음을 실행한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:81
msgid "…and leave it running until `getblockcount`:"
msgstr "...그리고 `getblockcount`가:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:87
msgid ""
"agrees with the block count on a block explorer like [the mempool.space "
"block explorer](https://mempool.space/). `ord` interacts with `bitcoind`, so "
"you should leave `bitcoind` running in the background when you're using "
"`ord`."
msgstr ""
"[mempool.space block explorer](https://mempool.space/)와 같은 블록 탐색기의 "
"블록 수와 일치할때까지 실행한다. `ord`는 `bitcoind`와 상호 작용하므로 `ord`"
"을 사용할 때는 `bitcoind`를 백그라운드에서 실행하도록 두어야 한다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:91
msgid ""
"The blockchain takes about 600GB of disk space. If you have an external "
"drive you want to store blocks on, use the configuration option "
"`blocksdir=<external_drive_path>`. This is much simpler than using the "
"`datadir` option because the cookie file will still be in the default "
"location for `bitcoin-cli` and `ord` to find."
msgstr ""
"블록체인은 약 600GB의 디스크 공간을 필요로 한다. 블록을 저장할 외장 드라이브"
"가 있는 경우, 설정 옵션 `blocksdir=<external_drive_path>`를 사용한다. 이렇게 "
"하면 쿠키 파일이 여전히 `bitcoin-cli`와 `ord`가 찾을 수 있는 기본 위치에 있"
"기 때문에 `datadir` 옵션을 사용하는 것보다 훨씬 간단하다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:97
#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:173
msgid "Troubleshooting"
msgstr "트러블 슈팅"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:100
msgid ""
"Make sure you can access `bitcoind` with `bitcoin-cli -getinfo` and that it "
"is fully synced."
msgstr ""
"`bitcoind`를 `bitcoin-cli -getinfo`로 액세스할 수 있는지 그리고 이것이 완전"
"히 동기화되었는지를 확인한다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:103
msgid ""
"If `bitcoin-cli -getinfo` returns `Could not connect to the server`, "
"`bitcoind` is not running."
msgstr ""
"`bitcoin-cli -getinfo`가 `Could not connect to the server`을 반환하면 "
"`bitcoind`가 실행되고 있지 않은 것이다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:106
msgid ""
"Make sure `rpcuser`, `rpcpassword`, or `rpcauth` are _NOT_ set in your "
"`bitcoin.conf` file. `ord` requires using cookie authentication. Make sure "
"there is a file `.cookie` in your bitcoin data directory."
msgstr ""
"`rpcuser`, `rpcpassword` 또는 `rpcauth`가 `bitcoin.conf` 파일에 설정되어 있"
"지 _않는_ 것을 확인하자. `ord`는 쿠키 인증에 사용을 요구한다. 비트코인 데이"
"터 디렉토리에 `.cookie` 파일이 있는지 확인하자."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:110
msgid ""
"If `bitcoin-cli -getinfo` returns `Could not locate RPC credentials`, then "
"you must specify the cookie file location. If you are using a custom data "
"directory (specifying the `datadir` option), then you must specify the "
"cookie location like `bitcoin-cli -rpccookiefile=<your_bitcoin_datadir>/."
"cookie -getinfo`. When running `ord` you must specify the cookie file "
"location with `--cookie-file=<your_bitcoin_datadir>/.cookie`."
msgstr ""
"`bitcoin-cli -getinfo`가 `Could not locate RPC credentials`을 반환하면 쿠키 "
"파일 위치를 지정해야 한다. 사용자 지정 데이터 디렉터리를 사용하는 경우"
"(`datadir` 옵션을 지정) `bitcoin-cli -rpccookiefile=<your_bitcoin_datadir>/."
"cookie -getinfo`와 같이 쿠키 위치를 지정해야 한다. `ord`을 실행할 때는 `--"
"cookie-file=<your_bitcoin_datadir>/.cookie`로 쿠키 파일 위치를 지정해야 한다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:118
msgid ""
"Make sure you do _NOT_ have `disablewallet=1` in your `bitcoin.conf` file. "
"If `bitcoin-cli listwallets` returns `Method not found` then the wallet is "
"disabled and you won't be able to use `ord`."
msgstr ""
"`bitcoin.conf` 파일에 `disablewallet=1`이 _없는_ 것을 확인하자. `bitcoin-cli "
"listwallets`가 `Method not found`을 반환하면 지갑이 비활성화된 것이며 `ord`"
"를 사용할 수 없다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:122
msgid ""
"Make sure `txindex=1` is set. Run `bitcoin-cli getindexinfo` and it should "
"return something like"
msgstr ""
"`txindex=1`이 설정되어 있는지 확인한다. `bitcoin-cli getindexinfo`를 실행하"
"면 다음과 같은 결과가 반환되어야 한다"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:124
msgid ""
"```json\n"
"{\n"
"  \"txindex\": {\n"
"    \"synced\": true,\n"
"    \"best_block_height\": 776546\n"
"  }\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"txindex\": {\n"
"    \"synced\": true,\n"
"    \"best_block_height\": 776546\n"
"  }\n"
"}\n"
"```"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:132
msgid ""
"If it only returns `{}`, `txindex` is not set. If it returns `\"synced\": "
"false`, `bitcoind` is still creating the `txindex`. Wait until `\"synced\": "
"true` before using `ord`."
msgstr ""
"만약 `{}`만 반환하면 `txindex`가 설정되지 않은 것이다. `”synced”: false`를 반"
"환하면 `bitcoind`가 여전히 `txindex`를 생성하고 있는 것이다. `”synced”: true`"
"가 될 때까지 기다렸다가 `ord`를 사용하라."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:136
msgid ""
"If you have `maxuploadtarget` set it can interfere with fetching blocks for "
"`ord` index. Either remove it or set `whitebind=127.0.0.1:8333`."
msgstr ""
"`maxuploadtarget`이 설정되어 있으면 `ord` 인덱스를 위해 블록을 가져오는 데 방"
"해가 될 수 있다. 이를 제거하거나 `whitebind=127.0.0.1:8333`을 설정하라."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:139
msgid "Installing `ord`"
msgstr "`ord` 설치"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:142
msgid ""
"The `ord` utility is written in Rust and can be built from [source](https://"
"github.com/ordinals/ord). Pre-built binaries are available on the [releases "
"page](https://github.com/ordinals/ord/releases)."
msgstr ""
"'ord` 유틸리티는 Rust로 작성되었으며 [소스](https://github.com/ordinals/ord)"
"에서 빌드할 수 있다. 미리 빌드된 바이너리는 [릴리스 페이지](https://github."
"com/ordinals/ord/releases)에서 사용할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:146
msgid "You can install the latest pre-built binary from the command line with:"
msgstr "명령줄에서 다음을 사용하여 미리 빌드된 최신 바이너리를 설치할 수 있다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:148
msgid ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"
msgstr ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:152
msgid "Once `ord` is installed, you should be able to run:"
msgstr "`ord`가 설치되면 다음을 실행할 수 있어야 한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:158
msgid "Which prints out `ord`'s version number."
msgstr "이것이 `ord`의 버전 번호를 출력한다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:160
msgid "Creating a Bitcoin Core Wallet"
msgstr "Bitcoin Core 지갑 만들기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:163
msgid ""
"`ord` uses Bitcoin Core to manage private keys, sign transactions, and "
"broadcast transactions to the Bitcoin network."
msgstr ""
"'ord'는 비트코인 코어를 사용해 개인 키를 관리하고, 트랜잭션에 서명하고, 비트"
"코인 네트워크로 트랜잭션을 브로드캐스트한다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:166
msgid "To create a Bitcoin Core wallet named `ord` for use with `ord`, run:"
msgstr ""
"`ord`와 함께 사용할 `ord`이라는 이름을 가진 Bitcoin Core 지갑을 만들려면 다음"
"을 실행한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:172
msgid "Receiving Sats"
msgstr "SAT 받기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:175
msgid ""
"Inscriptions are made on individual sats, using normal Bitcoin transactions "
"that pay fees in sats, so your wallet will need some sats."
msgstr ""
"인스크립션은 SAT로 수수료를 지불하는 일반 비트코인 거래를 사용하여 개별 SAT"
"에 새김 됨으로 지갑에 약간의 SAT가 필요하다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:178
msgid "Get a new address from your `ord` wallet by running:"
msgstr "다음을 실행하여 `ord` 지갑에서 새 주소를 가져온다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:184
msgid "And send it some funds."
msgstr "그리고 자금을 조금 보내자."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:186
msgid "You can see pending transactions with:"
msgstr "다음을 사용하여 펜딩 트랜잭션을 확인할 수 있다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:192
msgid ""
"Once the transaction confirms, you should be able to see the transactions "
"outputs with `ord wallet outputs`."
msgstr ""
"트랜잭션이 컨펌되면 `ord wallet outputs`으로 트랜잭션 출력을 확인할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:195
msgid "Creating Inscription Content"
msgstr "인스크립션 콘텐츠 만들기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:198
msgid ""
"Sats can be inscribed with any kind of content, but the `ord` wallet only "
"supports content types that can be displayed by the `ord` block explorer."
msgstr ""
"SAT에는 모든 종류의 콘텐츠를 새길 수 있지만, `ord` 지갑은 `ord` 블록 탐색기에"
"서 표시할 수 있는 콘텐츠 유형만 지원한다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:201
msgid ""
"Additionally, inscriptions are included in transactions, so the larger the "
"content, the higher the fee that the inscription transaction must pay."
msgstr ""
"또한 인스크립션은 트랜잭션에 포함되므로 콘텐츠가 클수록 인스크립션 트랜잭션에"
"서 지불해야 하는 수수료가 높아진다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:204
msgid ""
"Inscription content is included in transaction witnesses, which receive the "
"witness discount. To calculate the approximate fee that an inscribe "
"transaction will pay, divide the content size by four and multiply by the "
"fee rate."
msgstr ""
"인스크립션 콘텐츠는 트랜잭션 증인에 포함되며, 증인 할인(witness discount)을 "
"받는다. 인스크립트 트랜잭션이 지불할 대략의 수수료를 계산하려면 콘텐츠 크기"
"를 4로 나눈 다음 수수료율을 곱하면 된다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:208
msgid ""
"Inscription transactions must be less than 400,000 weight units, or they "
"will not be relayed by Bitcoin Core. One byte of inscription content costs "
"one weight unit. Since an inscription transaction includes not just the "
"inscription content, limit inscription content to less than 400,000 weight "
"units. 390,000 weight units should be safe."
msgstr ""
"인스크립션 트랜잭션은 400,000 가중치 단위(weight unit) 미만이어야 하며, 그렇"
"지 않으면 Bitcoin Core에서 릴레이 되지 않는다. 1바이트의 인스크립션 콘텐츠는 "
"1 가중치 단위의 비용이 든다. 인스크립션 트랜잭션에는 인스크립션 콘텐츠만 포함"
"되지 않으므로, 인스크립션 콘텐츠를 400,000 가중치 단위 미만으로 제한하라. "
"390,000 개의 가중치 단위미만은 안전할 것이다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:214
msgid "Creating Inscriptions"
msgstr "인스크립션 생성하기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:217
msgid "To create an inscription with the contents of `FILE`, run:"
msgstr "`FILE`의 내용으로 인스크립션을 만들려면 다음을 실행한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:223
msgid ""
"Ord will output two transactions IDs, one for the commit transaction, and "
"one for the reveal transaction, and the inscription ID. Inscription IDs are "
"of the form `TXIDiN`, where `TXID` is the transaction ID of the reveal "
"transaction, and `N` is the index of the inscription in the reveal "
"transaction."
msgstr ""
"Ord는 커밋 트랜잭션과 리빌 트랜잭션에 대한 두 개의 트랜잭션 ID와 인스크립션 "
"ID를 출력한다. 인스크립션 ID는 `TXIDiN` 형식이며, 여기서 `TXID`는 리빌 트랜잭"
"션의 트랜잭션 ID이고 `N`은 리빌 트랜잭션안에 있는 인스크립션의 인덱스이다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:228
msgid ""
"The commit transaction commits to a tapscript containing the content of the "
"inscription, and the reveal transaction spends from that tapscript, "
"revealing the content on chain and inscribing it on the first sat of the "
"input that contains the corresponding tapscript."
msgstr ""
"커밋 트랜잭션은 인스크립션 콘텐츠가 포함된 탭스크립트(tapscript)에 커밋하고, "
"리빌 트랜잭션은 해당 탭스크립트에서 지출하여 체인에서 콘텐츠를 공개하고 해당 "
"탭스크립트가 포함된 입력의 첫 번째 SAT에 인스크립션을 새긴다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:233
msgid ""
"Wait for the reveal transaction to be mined. You can check the status of the "
"commit and reveal transactions using  [the mempool.space block explorer]"
"(https://mempool.space/)."
msgstr ""
"리빌 트랜잭션이 채굴될 때까지 기다린다. [Mempool.space 블록 탐색기](https://"
"mempool.space/)를 사용하여 커밋 및 리빌 트랜잭션의 상태를 확인할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:237
msgid ""
"Once the reveal transaction has been mined, the inscription ID should be "
"printed when you run:"
msgstr ""
"리빌 트랜잭션이 채굴되면 다음을 실행할 때 인스크립션 ID가 출력되어야 한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:244
msgid "Parent-Child Inscriptions"
msgstr "부모-자식 인스크립션"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:247
msgid ""
"Parent-child inscriptions enable what is colloquially known as collections, "
"see [provenance](../inscriptions/provenance.md) for more information."
msgstr ""
"부모-자식 인스크립션은 구어체로 컬렉션이라고 하는 것을 가능하게 한다. 자세한 "
"내용은 [기원](../inscriptions/provenance.md)을 참조하자."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:250
msgid ""
"To make an inscription a child of another, the parent inscription has to be "
"inscribed and present in the wallet. To choose a parent run `ord wallet "
"inscriptions` and copy the inscription id (`<PARENT_INSCRIPTION_ID>`)."
msgstr ""
"인스크립션을 다른 인스크립션의 자식으로 만들려면 부모 인스크립션이 새김되어 "
"있고 지갑에 있어야 한다. 부모를 선택하려면 `ord wallet inscriptions`을 실행하"
"고 인스크립션 ID(`<PARENT_INSCRIPTION_ID>`)를 복사한다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:254
msgid "Now inscribe the child inscription and specify the parent like so:"
msgstr "이제 자식 인스크립션을 새기고 부모를 다음과 같이 지정한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:260
msgid ""
"This relationship cannot be added retroactively, the parent has to be "
"present at inception of the child."
msgstr ""
"이 관계는 소급하여 추가할 수 없으며, 부모는 자녀가 시작될 때 이미 존재해야 한"
"다."

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:263
msgid "Sending Inscriptions"
msgstr "인스크립션 보내기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:266
msgid "Ask the recipient to generate a new address by running:"
msgstr "수신자에게 다음을 실행해 새 주소를 생성하도록 요청한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:272
msgid "Send the inscription by running:"
msgstr "다음을 실행하여 인스크립션을 보낸다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:278
#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:306
msgid "See the pending transaction with:"
msgstr "다음을 실행하여 펜딩 트랜잭션을 확인한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:284
msgid ""
"Once the send transaction confirms, the recipient can confirm receipt by "
"running:"
msgstr ""
"전송 트랜잭션이 확인되면 수신자는 다음을 실행하여 수신을 확인할 수 있다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:291
msgid "Receiving Inscriptions"
msgstr "인스크립션 받기"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:294
msgid "Generate a new receive address using:"
msgstr "다음을 사용하여 새 수신 주소를 생성한다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:300
msgid "The sender can transfer the inscription to your address using:"
msgstr "발신자는 다음을 사용하여 인스크립션을 당신의 주소로 전송할 수 있다:"

#: /workspaces/ord_ko/docs/src/guides/inscriptions.md:311
msgid ""
"Once the send transaction confirms, you can can confirm receipt by running:"
msgstr "전송 트랜잭션이 확인되면 다음을 실행하여 수신을 확인할 수 있다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:4
msgid ""
"_This guide is out of date. Since it was written, the `ord` binary was "
"changed to only build the full satoshi index when the `--index-sats` flag is "
"supplied. Additionally, `ord` now has a built-in wallet that wraps a Bitcoin "
"Core wallet. See `ord wallet --help`._"
msgstr ""
"_이 가이드는 오래되었다. 이 가이드가 작성된 이후, `ord` 바이너리는 `--index-"
"sats` 플래그가 제공될 때만 풀 사토시 인덱스를 빌드하도록 변경되다. 또한 `ord`"
"에는 이제 Bitcoin Core 지갑을 감싸는 지갑이 내장되어 있다. `ord wallet —help`"
"를 참조하자._"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:9
msgid ""
"Ordinal hunting is difficult but rewarding. The feeling of owning a wallet "
"full of UTXOs, redolent with the scent of rare and exotic sats, is beyond "
"compare."
msgstr ""
"오디널 헌팅은 어렵지만 보람이 있다. 희귀하고 이색적인 SAT의 향기가 풍기는 "
"UTXO가 가득한 지갑을 소유했을 때의 기분은 그 무엇과도 비교할 수 없다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:12
msgid ""
"Ordinals are numbers for satoshis. Every satoshi has an ordinal number and "
"every ordinal number has a satoshi."
msgstr ""
"오디널은 사토시를 나타내는 숫자이다. 모든 사토시에는 서수가 있고 모든 서수에"
"는 사토시가 있다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:15
msgid "Preparation"
msgstr "준비"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:18
msgid "There are a few things you'll need before you start."
msgstr "시작하기 전에 몇 가지 준비해야 할 사항이 있다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:20
msgid ""
"First, you'll need a synced Bitcoin Core node with a transaction index. To "
"turn on transaction indexing, pass `-txindex` on the command-line:"
msgstr ""
"첫째, 트랜잭션 인덱스가 있는 동기화된 Bitcoin Core 노드가 필요하다. 트랜잭션 "
"인덱싱을 활성화하려면 명령줄에 `-txindex`를 입력한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:27
msgid ""
"Or put the following in your [Bitcoin configuration file](https://github.com/"
"bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"
msgstr ""
"또는 다음을 [Bitcoin configuration file](https://github.com/bitcoin/bitcoin/"
"blob/master/doc/bitcoin-conf.md#configuration-file-path)에 입력한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:34
msgid ""
"Launch it and wait for it to catch up to the chain tip, at which point the "
"following command should print out the current block height:"
msgstr ""
"이를 실행하고 체인 끝을 따라잡을 때까지 기다린 후 다음 명령을 입력하면 현재 "
"블록 높이를 출력할 것이다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:41
msgid "Second, you'll need a synced `ord` index."
msgstr "둘째, 동기화된 `ord` 인덱스가 필요하다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:43
msgid "Get a copy of `ord` from [the repo](https://github.com/ordinals/ord/)."
msgstr ""
"[the repo](https://github.com/ordinals/ord/)에서 `ord`의 사본을 가져오자."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:45
msgid ""
"Run `RUST_LOG=info ord index`. It should connect to your bitcoin core node "
"and start indexing."
msgstr ""
"`RUST_LOG=info ord index`를 실행한다. Bitcoin Core 노드에 연결되고 인덱싱을 "
"시작할 것이다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:48
msgid "Wait for it to finish indexing."
msgstr "인덱싱이 완료될 때까지 기다린다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:50
msgid "Third, you'll need a wallet with UTXOs that you want to search."
msgstr "셋째, 검색하려는 UTXO가 있는 지갑이 필요하다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:52
msgid "Searching for Rare Ordinals"
msgstr "희귀 오디널스 검색하기"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:55
msgid "Searching for Rare Ordinals in a Bitcoin Core Wallet"
msgstr "Bitcoin Core 지갑에서 희귀 오디널스 검색하기"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:57
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"searching for rare ordinals in a Bitcoin Core wallet is Easy. Assuming your "
"wallet is named `foo`:"
msgstr ""
"`ord wallet` 명령은 Bitcoin Core의 RPC API를 감싸는 래퍼일 뿐이므로 Bitcoin "
"Core 지갑에서 희귀한 오디널스를 검색하는 것은 쉽다. 지갑의 이름이 `foo`이라"
"는 가정하에:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:61
msgid "Load your wallet:"
msgstr "지갑을 로드한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:67
msgid "Display any rare ordinals wallet `foo`'s UTXOs:"
msgstr "희귀한 오디널스 지갑 `foo`의 UTXO를 표시한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:73
msgid "Searching for Rare Ordinals in a Non-Bitcoin Core Wallet"
msgstr "Bitcoin Core 지갑이 아닌 지갑에서 희귀 오디널스 검색하기"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:75
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"to search for rare ordinals in a non-Bitcoin Core wallet, you'll need to "
"import your wallet's descriptors into Bitcoin Core."
msgstr ""
"`ord wallet` 명령은 Bitcoin Core의 RPC API를 감싸는 래퍼일 뿐이므로, Bitcoin "
"Core가 아닌 지갑에서 희귀 오디널스를 검색하려면 지갑의 기술자(discriptors)를 "
"Bitcoin Core로 가져오기 해야 한다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:79
msgid ""
"[Descriptors](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors."
"md) describe the ways that wallets generate private keys and public keys."
msgstr ""
"[기술자](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors.md)"
"는 지갑이 개인 키와 공개 키를 생성하는 방법을 설명한다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:82
msgid ""
"You should only import descriptors into Bitcoin Core for your wallet's "
"public keys, not its private keys."
msgstr ""
"지갑의 공개 키에 대한 기술자만 Bitcoin Core로 가져와야 하며, 개인 키는 가져오"
"지 않아야 한다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:85
msgid ""
"If your wallet's public key descriptor is compromised, an attacker will be "
"able to see your wallet's addresses, but your funds will be safe."
msgstr ""
"지갑의 공개 키 기술자가 유출되면 공격자가 지갑 주소를 볼 수 있지만 자금은 안"
"전할 것이다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:88
msgid ""
"If your wallet's private key descriptor is compromised, an attacker can "
"drain your wallet of funds."
msgstr ""
"지갑의 개인 키 기술자가 유출되면 공격자가 지갑에서 자금을 탈취할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:91
msgid ""
"Get the wallet descriptor from the wallet whose UTXOs you want to search for "
"rare ordinals. It will look something like this:"
msgstr ""
"희귀 오디널스를 검색하려는 지갑에서 지갑 기술자를 가져온다. 다음과 같은 형태"
"일 것이다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:98
msgid "Create a watch-only wallet named `foo-watch-only`:"
msgstr "‘foo-watch-only’라는 이름으로 Watch-only 지갑을 생성한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:104
msgid "Feel free to give it a better name than `foo-watch-only`!"
msgstr "당신 마음대로 `foo-watch-only`보다 더 좋은 이름으로 지어주자!"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:106
msgid "Load the `foo-watch-only` wallet:"
msgstr "`foo-watch-only` 지갑을 로드한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:112
msgid "Import your wallet descriptors into `foo-watch-only`:"
msgstr "지갑 기술자를 'foo-watch-only'로 가져온다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:114
msgid ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\", \"timestamp\":0 }]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  ‘[{ “desc”: "
"“wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax”, “timestamp”:0 }]’\n"
"```"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:119
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of `\"timestamp\"` instead of "
"`0`. This will reduce the time it takes for Bitcoin Core to search for your "
"wallet's UTXOs."
msgstr ""
"지갑이 처음 트랜잭션을 받기 시작한 유닉스 타임스탬프를 알고 있다면, `0` 대신 "
"`”timestamp”` 값에 사용할 수 있다. 이렇게 하면 Bitcoin Core가 당신 지갑의 "
"UTXO를 검색하는 데 걸리는 시간을 줄일 수 있다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:124
#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:225
msgid "Check that everything worked:"
msgstr "모든 것이 제대로 되었는지 확인한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:130
#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:231
msgid "Display your wallet's rare ordinals:"
msgstr "당신 지갑의 희귀 오디널스를 표시한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:136
msgid ""
"Searching for Rare Ordinals in a Wallet that Exports Multi-path Descriptors"
msgstr "다중 경로 기술자를 내보내기 하는 지갑에서 희귀 오디널스 검색하기"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:138
msgid ""
"Some descriptors describe multiple paths in one descriptor using angle "
"brackets, e.g., `<0;1>`. Multi-path descriptors are not yet supported by "
"Bitcoin Core, so you'll first need to convert them into multiple "
"descriptors, and then import those multiple descriptors into Bitcoin Core."
msgstr ""
"일부 기술자는 꺾쇠 괄호(예: `<0;1>`)를 사용하여 하나의 기술자 안에 다수에 경"
"로를 기술한다. 다중 경로 기술자는 아직 Bitcoin Core에서 지원되지 않으므로, 먼"
"저 다수에 기술자들로 변환한 다음 Bitcoin Core로 해당 기술자들을 가져오기 해"
"야 한다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:143
msgid ""
"First get the multi-path descriptor from your wallet. It will look something "
"like this:"
msgstr ""
"먼저 지갑에서 다중 경로 기술자를 가져온다. 다음과 같은 형태가 될 것이다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:150
msgid "Create a descriptor for the receive address path:"
msgstr "수신 주소 경로에 대한 기술자를 만든다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:156
msgid "And the change address path:"
msgstr "그리고 주소 변경 경로에 대한 기술자를 만든다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:162
msgid ""
"Get and note the checksum for the receive address descriptor, in this case "
"`tpnxnxax`:"
msgstr "수신 주소 기술자의 체크섬(이 경우 `tpnxnxax`)을 가져와 기록한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:170
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  “descriptor”: "
"“wpkh([bf1dd55e/84’/0’/0’]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29”,\n"
"  “checksum”: “tpnxnxax”,\n"
"  “isrange”: true,\n"
"  “issolvable”: true,\n"
"  “hasprivatekeys”: false\n"
"}\n"
"```"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:180
msgid "And for the change address descriptor, in this case `64k8wnd7`:"
msgstr ""
"그리고 변경 주소 설명자의 체크섬(이 경우 `64k8wnd7`)를 가져와 기록한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:187
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  “descriptor”: "
"“wpkh([bf1dd55e/84’/0’/0’]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#fyfc5f6a”,\n"
"  “checksum”: “64k8wnd7”,\n"
"  “isrange”: true,\n"
"  “issolvable”: true,\n"
"  “hasprivatekeys”: false\n"
"}\n"
"```"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:197
msgid "Load the wallet you want to import the descriptors into:"
msgstr "기술자를 가져오기 할 지갑을 로드한다:"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:203
msgid ""
"Now import the descriptors, with the correct checksums, into Bitcoin Core."
msgstr "이제 올바른 체크섬과 기술자를 Bitcoin Core로 가져오기 한다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:205
msgid ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" ‘[\n"
"   {\n"
"     “desc”: "
"“wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax”\n"
"     “timestamp”:0\n"
"   },\n"
"   {\n"
"     “desc”: "
"“wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#64k8wnd7”,\n"
"     “timestamp”:0\n"
"   }\n"
" ]’\n"
"```"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:220
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of the `\"timestamp\"` fields "
"instead of `0`. This will reduce the time it takes for Bitcoin Core to "
"search for your wallet's UTXOs."
msgstr ""
"지갑이 처음 트랜잭션을 받기 시작한 유닉스 타임스탬프를 알고 있다면, `0` 대신 "
"`”timestamp”` 값에 사용할 수 있다. 이렇게 하면 Bitcoin Core가 당신 지갑의 "
"UTXO를 검색하는 데 걸리는 시간을 줄일 수 있다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:237
msgid "Exporting Descriptors"
msgstr "기술자 내보내기"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:241
msgid ""
"Navigate to the `Settings` tab, then to `Script Policy`, and press the edit "
"button to display the descriptor."
msgstr ""
"`Settings` 탭으로 이동한 다음 `Script Policy`로 이동한 후 편집 버튼을 눌러 기"
"술자를 표시한다."

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:244
msgid "Transferring Ordinals"
msgstr "오디널스 전송하기"

#: /workspaces/ord_ko/docs/src/guides/sat-hunting.md:246
msgid ""
"The `ord` wallet supports transferring specific satoshis. You can also use "
"`bitcoin-cli` commands `createrawtransaction`, "
"`signrawtransactionwithwallet`, and `sendrawtransaction`, how to do so is "
"complex and outside the scope of this guide."
msgstr ""
"`ord` 지갑은 특정 사토시 전송을 지원한다. `bitcoin-cli` 명령어인 "
"`createrawtransaction`, `signrawtransactionwithwallet`, `sendrawtransaction`"
"을 사용할 수도 있지만, 이 방법들은 복잡하며 이 가이드의 범위를 벗어난다."

#: /workspaces/ord_ko/docs/src/guides/collecting.md:4
msgid ""
"Currently, [ord](https://github.com/ordinals/ord/) is the only wallet "
"supporting sat-control and sat-selection, which are required to safely store "
"and send rare sats and inscriptions, hereafter ordinals."
msgstr ""
"현재 희귀한 SAT 및 인스크립션(이하 오디널)을 안전하게 보관하고 전송하는 데에 "
"필수인 SAT콘트롤 및 SAT선택을 지원하는 지갑은 [ord](https://github.com/"
"ordinals/ord/)가 유일하다."

#: /workspaces/ord_ko/docs/src/guides/collecting.md:8
msgid ""
"The recommended way to send, receive, and store ordinals is with `ord`, but "
"if you are careful, it is possible to safely store, and in some cases send, "
"ordinals with other wallets."
msgstr ""
"오디널을 주고받고 보관하는 권장된 방법은 `ord`를 사용하는 것이지만, 조금만 주"
"의하면 다른 지갑으로 오디널을 안전하게 보관하고 경우에 따라서는 전송하는 것"
"도 가능하다."

#: /workspaces/ord_ko/docs/src/guides/collecting.md:12
msgid ""
"As a general note, receiving ordinals in an unsupported wallet is not "
"dangerous. Ordinals can be sent to any bitcoin address, and are safe as long "
"as the UTXO that contains them is not spent. However, if that wallet is then "
"used to send bitcoin, it may select the UTXO containing the ordinal as an "
"input, and send the inscription or spend it to fees."
msgstr ""
"일반적으로, 지원되지 않는 지갑에서 오디널을 받는 것은 위험하지 않다. 오디널"
"은 모든 비트코인 주소로 전송할 수 있으며, 오디널이 포함된 UTXO가 사용되지 않"
"는 한 안전하다. 그러나 해당 지갑이 비트코인을 전송하는 데 사용되는 경우, 해"
"당 지갑은 오디널이 포함된 UTXO를 입력으로 선택해 인스크립션을 보내거나 수수료"
"로 사용할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting.md:18
msgid ""
"A [guide](./collecting/sparrow-wallet.md) to creating an `ord`\\-compatible "
"wallet with [Sparrow Wallet](https://sparrowwallet.com/), is available in "
"this handbook."
msgstr ""
"이 핸드북에는 [스패로우 월렛](https://sparrowwallet.com/)를 사용해 `ord`와 호"
"환되는 지갑을 만드는 [가이드](./collecting/sparrow-wallet.md)가 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting.md:21
msgid ""
"Please note that if you follow this guide, you should not use the wallet you "
"create to send BTC, unless you perform manual coin-selection to avoid "
"sending ordinals."
msgstr ""
"이 가이드를 따르는 경우, 오디널 전송을 피하기 위해 수동으로 코인선택을 하지 "
"않는 한, 생성한 지갑을 사용해 BTC를 전송해서는 안 된다는 점에 유의하기 바란"
"다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:1
msgid "Collecting Inscriptions and Ordinals with Sparrow Wallet"
msgstr "스패로우 월렛으로 인스크립션과 오디널 수집하기"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:4
msgid ""
"Users who cannot or have not yet set up the [ord](https://github.com/"
"ordinals/ord) wallet can receive inscriptions and ordinals with alternative "
"bitcoin wallets, as long as they are _very_ careful about how they spend "
"from that wallet."
msgstr ""
"[ord](https://github.com/ordinals/ord) 지갑을 설정할 수 없거나 아직 설정하지 "
"않은 사용자는 해당 지갑에서 지출하는 방식에 _매우_ 주의를 기울인다면 대체 비"
"트코인 지갑으로 인스크립션과 오디널을 받을 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:6
msgid ""
"This guide gives some basic steps on how to create a wallet with [Sparrow "
"Wallet](https://sparrowwallet.com/) which is compatible with `ord` and can "
"be later imported into `ord`"
msgstr ""
"이 가이드는 `ord`와 호환되며 나중에 `ord`로 가져오기 할 수 있는 [스패로우 월"
"렛](https://sparrowwallet.com/)을 사용해 지갑을 어떻게 생성하는지에 기본 단계"
"를 설명한다"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:8
msgid "⚠️⚠️ Warning!! ⚠️⚠️"
msgstr "⚠️⚠️ 경고!! ⚠️⚠️"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:9
msgid ""
"As a general rule if you take this approach, you should use this wallet with "
"the Sparrow software as a receive-only wallet."
msgstr ""
"일반적으로 이 방법을 사용하는 경우, 이 지갑을 스패로우 소프트웨어에 사용해 수"
"신 전용(receive-only) 지갑으로 사용해야 한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:11
msgid ""
"Do not spend any satoshis from this wallet unless you are sure you know what "
"you are doing. You could very easily inadvertently lose access to your "
"ordinals and inscriptions if you don't heed this warning."
msgstr ""
"어떻게 하는지 확실하지 않다면 이 지갑에서 사토시를 사용(spend) 하지 말자. 이 "
"경고에 주의하지 않으면 실수로 오디널과 인스크립션 대한 액세스를 아주 쉽게 잃"
"을 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:13
msgid "Wallet Setup & Receiving"
msgstr "지갑 설정 및 수신하기"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:15
msgid ""
"Download the Sparrow Wallet from the [releases page](https://sparrowwallet."
"com/download/) for your particular operating system."
msgstr ""
"[릴리즈 페이지](https://sparrowwallet.com/download/)에서 당신 운영체제의 맞"
"는 스패로우 월렛을 다운로드한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:17
msgid "Select `File -> New Wallet` and create a new wallet called `ord`."
msgstr "`File -> New Wallet`을 선택하고 `ord`라는 새 지갑을 생성한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:19
msgid "![](images/wallet_setup_01.png)"
msgstr "![](images/wallet_setup_01.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:21
msgid ""
"Change the `Script Type` to `Taproot (P2TR)` and select the `New or Imported "
"Software Wallet` option."
msgstr ""
"`Script Type`을 `Taproot (P2TR)`로 변경하고 `New or Imported Software "
"Wallet` 옵션을 선택한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:23
msgid "![](images/wallet_setup_02.png)"
msgstr "![](images/wallet_setup_02.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:25
msgid ""
"Select `Use 12 Words` and then click `Generate New`. Leave the passphrase "
"blank."
msgstr ""
"`Use 12 Words`을 선택한 다음 `Generate New`를 클릭한다. 비밀번호는 비워둔다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:27
msgid "![](images/wallet_setup_03.png)"
msgstr "![](images/wallet_setup_03.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:29
msgid ""
"A new 12 word BIP39 seed phrase will be generated for you. Write this down "
"somewhere safe as this is your backup to get access to your wallet. NEVER "
"share or show this seed phrase to anyone else."
msgstr ""
"새로운 12단어 BIP39 시드 문구가 생성된다. 지갑에 액세스하기 위한 백업용이므"
"로 안전한 곳에 적어두자. 절대로 이 시드 문구를 다른 사람에게 공유하거나 보여"
"주지 말자."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:31
msgid "Once you have written down the seed phrase click `Confirm Backup`."
msgstr "시드 문구를 적었으면 `Confirm Backup`을 클릭한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:33
msgid "![](images/wallet_setup_04.png)"
msgstr "![](images/wallet_setup_04.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:35
msgid ""
"Re-enter the seed phrase which you wrote down, and then click `Create "
"Keystore`."
msgstr "적어둔 시드 문구를 다시 입력한 다음 `Create Keystore`을 클릭한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:37
msgid "![](images/wallet_setup_05.png)"
msgstr "![](images/wallet_setup_05.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:39
msgid "Click `Import Keystore`."
msgstr "`Import Keystore`를 클릭한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:41
msgid "![](images/wallet_setup_06.png)"
msgstr "![](images/wallet_setup_06.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:43
msgid "Click `Apply`. Add a password for the wallet if you want to."
msgstr "`Apply`를 클릭한다. 원하는 경우 지갑의 비밀번호를 설정한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:45
msgid "![](images/wallet_setup_07.png)"
msgstr "![](images/wallet_setup_07.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:47
msgid ""
"You now have a wallet which is compatible with `ord`, and can be imported "
"into `ord` using the BIP39 Seed Phrase. To receive ordinals or inscriptions, "
"click on the `Receive` tab and copy a new address."
msgstr ""
"이제 `ord`와 호환되는 지갑을 갖게 되었으며, BIP39 시드 문구를 사용하여 `ord`"
"로 가져오기 할 수 있다. 오디널 또는 인스크립션을 받으려면 `Receive` 탭을 클릭"
"하고 새 주소를 복사한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:49
msgid ""
"Each time you want to receive you should use a brand-new address, and not re-"
"use existing addresses."
msgstr ""
"수신을 원할 때마다 기존 주소를 재사용하지 말고 새 주소를 사용해야 한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:51
msgid ""
"Note that bitcoin is different to some other blockchain wallets, in that "
"this wallet can generate an unlimited number of new addresses. You can "
"generate a new address by clicking on the `Get Next Address` button. You can "
"see all of your addresses in the `Addresses` tab of the app."
msgstr ""
"비트코인은 다른 블록체인 지갑들과 달리 새 주소를 무제한으로 생성할 수 있다는 "
"점을 유의하자. `Get Next Address` 버튼을 클릭하면 새 주소를 생성할 수 있다. "
"앱의 `Addresses` 탭에서 모든 주소를 확인할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:53
msgid ""
"You can add a label to each address, so you can keep track of what it was "
"used for."
msgstr "각 주소에 레이블(label)을 추가하여 사용 용도를 추적할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:55
msgid "![](images/wallet_setup_08.png)"
msgstr "![](images/wallet_setup_08.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:57
msgid "Validating / Viewing Received Inscriptions"
msgstr "수신된 인스크립션 확인하기/보기"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:59
msgid ""
"Once you have received an inscription you will see a new transaction in the "
"`Transactions` tab of Sparrow, as well as a new UTXO in the `UTXOs` tab."
msgstr ""
"인스크립션을 받으면 스패로우의 `Transactions` 탭에 새 트랜잭션이 표시되고, "
"`UTXOs` 탭에 새 UTXO가 표시된다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:61
msgid ""
"Initially this transaction may have an \"Unconfirmed\" status, and you will "
"need to wait for it to be mined into a bitcoin block before it is fully "
"received."
msgstr ""
"처음에 이 트랜잭션은 \"미확인”(“Unconfirmed”) 상태일 수 있으며, 완전히 수신하"
"려면 이 트랜잭션이 비트코인 블록으로 채굴될 때까지 기다려야 한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:63
msgid "![](images/validating_viewing_01.png)"
msgstr "![](images/validating_viewing_01.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:65
msgid ""
"To track the status of your transaction you can right-click on it,  select "
"`Copy Transaction ID` and then paste that transaction id into [mempool.space]"
"(https://mempool.space)."
msgstr ""
"트랜잭션의 상태를 추적하려면 트랜잭션을 마우스 오른쪽 버튼으로 클릭하고 "
"`Copy Transaction ID`를 선택한 다음 해당 트랜잭션 ID를 [mempool.space]"
"(https://mempool.space)에 붙여넣기 하면 된다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:67
msgid "![](images/validating_viewing_02.png)"
msgstr "![](images/validating_viewing_02.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:69
msgid ""
"Once the transaction has confirmed, you can validate and view your "
"inscription by heading over to the `UTXOs` tab, finding the UTXO you want to "
"check, right-clicking on the `Output` and selecting `Copy Transaction "
"Output`. This transaction output id can then be pasted into the [ordinals."
"com](https://ordinals.com) search."
msgstr ""
"트랜잭션이 확인되면 `UTXOs` 탭으로 이동하여 확인하려는 UTXO를 찾아 `Output`"
"을 마우스 오른쪽 버튼으로 클릭하고 `Copy Transaction Output`를 선택하여 인스"
"크립션의 유효성을 검사하고 확인할 수 있다. 그런 다음 이 트랜잭션 출력 ID를 "
"[ordinals.com](https://ordinals.com) 검색에 붙여넣을 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:72
msgid "Freezing UTXO's"
msgstr "UTXO 동결하기"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:73
msgid ""
"As explained above, each of your inscriptions is stored in an Unspent "
"Transaction Output (UTXO). You want to be very careful not to accidentally "
"spend your inscriptions, and one way to make it harder for this to happen is "
"to freeze the UTXO."
msgstr ""
"위에서 설명한 것처럼, 각 인스크립션은 미사용 트랜잭션 출력값(UTXO)에 저장된"
"다. 실수로 인스크립션을 사용하지 않도록 매우 주의해야 하며, 이런 일이 발생하"
"지 않도록 하는 한 가지 방법은 UTXO를 동결(freeze)하는 것이다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:75
msgid ""
"To do this, go to the `UTXOs` tab, find the UTXO you want to freeze, right-"
"click on the `Output` and select `Freeze UTXO`."
msgstr ""
"이렇게 하려면 `UTXOs` 탭으로 이동하여 동결하려는 UTXO를 찾은 다음 `Output`을 "
"마우스 오른쪽 버튼으로 클릭하고 `Freeze UTXO`을 선택한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:77
msgid ""
"This UTXO (Inscription) is now un-spendable within the Sparrow Wallet until "
"you unfreeze it."
msgstr ""
"이 UTXO(인스크립션)는 이제 동결을 해제할 때까지 스패로우 지갑 내에서 사용할 "
"수 없다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:79
msgid "Importing into `ord` wallet"
msgstr "`ord` 지갑으로 가져오기"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:81
msgid ""
"For details on setting up Bitcoin Core and the `ord` wallet check out the "
"[Inscriptions Guide](../inscriptions.md)"
msgstr ""
"Bitcoin Core 및 `ord` 지갑 설정에 대한 자세한 내용은 [인스크립션 가이드](../"
"inscriptions.md)를 확인하자"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:83
msgid ""
"When setting up `ord`, instead of running `ord wallet create` to create a "
"brand-new wallet, you can import your existing wallet using `ord wallet "
"restore \"BIP39 SEED PHRASE\"` using the seed phrase you generated with "
"Sparrow Wallet."
msgstr ""
"`ord`를 설정할 때 `ord wallet create`을 실행하여 새로운 지갑을 생성하는 대"
"신, 스패로우 월렛에서 생성한 시드 문구와 `ord wallet restore “BIP39 SEED "
"PHRASE”`를 사용하여 기존 지갑을 가져오기 할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:85
msgid ""
"There is currently a [bug](https://github.com/ordinals/ord/issues/1589) "
"which causes an imported wallet to not be automatically rescanned against "
"the blockchain. To work around this you will need to manually trigger a "
"rescan using the bitcoin core cli: `bitcoin-cli -rpcwallet=ord "
"rescanblockchain 767430`"
msgstr ""
"현재 가져오기 한 지갑이 블록체인에 대해 자동으로 재스캔되지 않는 [버그]"
"(https://github.com/ordinals/ord/issues/1589)가 있다. 이 문제를 해결하려면 "
"bitcoin core cli(`bitcoin-cli -rpcwallet=ord rescanblockchain 767430`)를 사용"
"하여 수동으로 재스캔을 트리거해야 한다"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:88
msgid ""
"You can then check your wallet's inscriptions using `ord wallet inscriptions`"
msgstr ""
"그런 다음 `ord wallet inscriptions`을 사용하여 지갑의 인스크립션을 확인할 수 "
"있다"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:90
msgid ""
"Note that if you have previously created a wallet with `ord`, then you will "
"already have a wallet with the default name, and will need to give your "
"imported wallet a different name. You can use the `--wallet` parameter in "
"all `ord` commands to reference a different wallet, eg:"
msgstr ""
"이전에 `ord`로 지갑을 생성한 적이 있다면 기본 이름의 지갑이 이미 있을 것이므"
"로 가져오기 한 지갑에 다른 이름을 지정해야 한다. 모든 `ord` 명령에 `--"
"wallet` 매개변수를 사용하여 다른 지갑을 참조할 수 있다. 예시:"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:92
msgid "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"
msgstr "`ord —wallet ord_from_sparrow wallet restore “BIP39 SEED PHRASE”`"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:94
msgid "`ord --wallet ord_from_sparrow wallet inscriptions`"
msgstr "`ord —wallet ord_from_sparrow wallet inscriptions`"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:96
msgid "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"
msgstr "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:98
msgid "Sending inscriptions with Sparrow Wallet"
msgstr "스패로우 월렛으로 인스크립션 보내기"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:100
msgid "⚠️⚠️ Warning ⚠️⚠️"
msgstr "⚠️⚠️ 경고 ⚠️⚠️"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:101
msgid ""
"While it is highly recommended that you set up a bitcoin core node and run "
"the `ord` software, there are certain limited ways you can send inscriptions "
"out of Sparrow Wallet in a safe way. Please note that this is not "
"recommended, and you should only do this if you fully understand what you "
"are doing."
msgstr ""
"Bitcoin Core 노드를 설정하고 `ord` 소프트웨어를 실행하는 것을 적극 권장하지"
"만, 스패로우 월렛에서 안전한 방법으로 인스크립션을 전송할 수 있는 몇 가지 제"
"한된 방법이 있다. 이 방법은 권장되지 않으며, 자신이 무엇을 하는지 완전히 이해"
"하는 경우에만 이 방법을 사용해야 한다는 점에 유의하기 바란다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:103
msgid ""
"Using the `ord` software will remove much of the complexity we are "
"describing here, as it is able to automatically and safely handle sending "
"inscriptions in an easy way."
msgstr ""
"`ord` 소프트웨어를 사용하면 인스크립션 전송을 자동으로 안전하게 처리할 수 있"
"으므로 여기서 설명하는 복잡성을 상당 부분 제거할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:105
msgid "⚠️⚠️ Additional Warning ⚠️⚠️"
msgstr "⚠️⚠️ 추가 경고 ⚠️⚠️"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:106
msgid ""
"Don't use your sparrow inscriptions wallet to do general sends of non-"
"inscription bitcoin. You can setup a separate wallet in sparrow if you need "
"to do normal bitcoin transactions, and keep your inscriptions wallet "
"separate."
msgstr ""
"스패로우 인스크립션 지갑을 사용해 인스크립션이 아닌 일반 비트코인을 송금하지 "
"말자. 일반적인 비트코인 거래를 해야 하는 경우 스패로우에서 별도의 지갑을 설정"
"하고 인스크립션 지갑을 따로 보관할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:108
msgid "Bitcoin's UTXO model"
msgstr "비트코인의 UTXO 모델"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:109
msgid ""
"Before sending any transaction it's important that you have a good mental "
"model for bitcoin's Unspent Transaction Output (UTXO) system. The way "
"Bitcoin works is fundamentally different to many other blockchains such as "
"Ethereum. In Ethereum generally you have a single address in which you store "
"ETH, and you cannot differentiate between any of the ETH -  it is just all a "
"single value of the total amount in that address. Bitcoin works very "
"differently in that we generate a new address in the wallet for each "
"receive, and every time you receive sats to an address in your wallet you "
"are creating a new UTXO. Each UTXO can be seen and managed individually. You "
"can select specific UTXO's which you want to spend, and you can choose not "
"to spend certain UTXO's."
msgstr ""
"트랜잭션을 전송하기 전에 비트코인의 미사용 트랜잭션 출력값(UTXO) 시스템에 대"
"한 올바른 멘털 모델을 갖추는 것이 중요하다. 비트코인의 작동 방식은 이더리움"
"과 같은 다른 블록체인과 근본적으로 다르다. 이더리움에서는 일반적으로 이더를 "
"저장하는 단일 주소가 있으며, 이더를 구분할 수 없으므로 이것은 단지 그 주소에 "
"있는 총금액의 단일 값일 뿐이다. 비트코인은 매번 수신 때마다 지갑에 새 주소를 "
"생성한다는 점에서 매우 다르게 작동하며, 지갑에 있는 주소로 SAT를 받을 때마다 "
"새로운 UTXO를 생성하게 된다. 각 UTXO는 개별적으로 보고 관리할 수 있다. 사용하"
"고자 하는 특정 UTXO를 선택할 수 있으며, 특정 UTXO를 사용하지 않도록 선택할 수"
"도 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:111
msgid ""
"Some Bitcoin wallets do not expose this level of detail, and they just show "
"you a single summed up value of all the bitcoin in your wallet. However, "
"when sending inscriptions it is important that you use a wallet like Sparrow "
"which allows for UTXO control."
msgstr ""
"일부 비트코인 지갑은 이러한 수준의 세부 정보를 노출하지 않으며, 지갑에 있는 "
"모든 비트코인의 합산된 가치만 보여준다. 하지만 인스크립션을 전송할 때는 UTXO "
"제어가 가능한 스패로우와 같은 지갑을 사용하는 것이 중요하다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:113
msgid "Inspecting your inscription before sending"
msgstr "보내기 전에 인스크립션 검사하기"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:114
msgid ""
"Like we have previously described inscriptions are inscribed onto sats, and "
"sats are stored within UTXOs. UTXO's are a collection of satoshis with some "
"particular value of the number of satoshis (the output value). Usually (but "
"not always) the inscription will be inscribed on the first satoshi in the "
"UTXO."
msgstr ""
"앞서 설명한 것처럼 인스크립션은 SAT에 새겨지며, SAT는 UTXO 내에 저장된다. "
"UTXO는 특정 값의 사토시 수(출력 값)를 가진 사토시의 모음이다. 일반적으로 (항"
"상 그런 것은 아니지만) 인스크립션은 UTXO의 첫 번째 사토시에 새겨진다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:116
msgid ""
"When inspecting your inscription before sending the main thing you will want "
"to check is which satoshi in the UTXO your inscription is inscribed on."
msgstr ""
"전송하기 전에 인스크립션을 검사할 때 가장 중요하게 확인해야 할 것은 UTXO의 사"
"토시 중 인스크립션이 새겨진 사토시가 어느 것인지다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:118
msgid ""
"To do this, you can follow the [Validating / Viewing Received Inscriptions]"
"(./sparrow-wallet.md#validating--viewing-received-inscriptions) described "
"above to find the inscription page for your inscription on ordinals.com"
msgstr ""
"이것을 하려면 위에서 설명한 [수신된 인스크립션 확인하기/보기](./sparrow-"
"wallet.md#validating--viewing-received-inscriptions)를 따라 ordinals.com에서 "
"해당 인스크립션에 대한 인스크립션 페이지를 찾을 수 있다"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:120
msgid ""
"There you will find some metadata about your inscription which looks like "
"the following:"
msgstr "여기에서 다음과 같은 해당 인스크립션에 대한 메타데이터를 찾을 수 있다:"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:122
msgid "![](images/sending_01.png)"
msgstr "![](images/sending_01.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:124
msgid "There is a few of important things to check here:"
msgstr "여기서 확인해야 할 몇 가지 중요한 사항이 있다:"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:125
msgid ""
"The `output` identifier matches the identifier of the UTXO you are going to "
"send"
msgstr "`output` 식별자가 전송하려는 UTXO의 식별자와 일치한"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:126
msgid ""
"The `offset` of the inscription is `0` (this means that the inscription is "
"located on the first sat in the UTXO)"
msgstr ""
"인스크립션의 `offset`은 `0`이다 (이는 인스크립션이 UTXO의 첫 번째 SAT에 위치"
"한다는 의미)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:127
msgid ""
"the `output_value` has enough sats to cover the transaction fee (postage) "
"for sending the transaction. The exact amount you will need depends on the "
"fee rate you will select for the transaction"
msgstr ""
"`output_value`에 트랜잭션 전송 수수료(우송료)를 충당할 수 있는 충분한 SAT가 "
"있는지 확인한다. 필요한 정확한 금액은 트랜잭션에 대해 선택할 수수료율에 따라 "
"다르다"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:129
msgid ""
"If all of the above are true for your inscription, it should be safe for you "
"to send it using the method below."
msgstr ""
"위의 모든 사항이 당신 인스크립션에 해당하면 아래 방법을 사용하여 인스크립션"
"을 보내도 안전할 것이다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:131
msgid ""
"⚠️⚠️ Be very careful sending your inscription particularly if the `offset` "
"value is not `0`. It is not recommended to use this method if that is the "
"case, as doing so you could accidentally send your inscription to a bitcoin "
"miner unless you know what you are doing."
msgstr ""
"⚠️⚠️ 특히 `offset` 값이 `0`이 아닌 경우 인스크립션을 보낼 때 매우 주의하라. 이 "
"경우 이 방법을 권장하지 않는다. 자신이 무엇을 하고 있는지 모르는 상태에서 이 "
"방법을 사용하면 실수로 비트코인 채굴자에게 인스크립션을 전송할 수 있기 때문이"
"다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:133
msgid "Sending your inscription"
msgstr "인스크립션 보내기"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:134
msgid ""
"To send an inscription navigate to the `UTXOs` tab, and find the UTXO which "
"you previously validated contains your inscription."
msgstr ""
"인스크립션을 보내려면 `UTXOs` 탭으로 이동하여 이전에 유효성을 확인한 인스크립"
"션이 포함된 UTXO를 찾는다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:136
msgid ""
"If you previously froze the UXTO you will need to right-click on it and "
"unfreeze it."
msgstr ""
"이전에 UXTO를 동결했다면 마우스 오른쪽 버튼으로 클릭한 후 동결 해제해야 한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:138
msgid ""
"Select the UTXO you want to send, and ensure that is the _only_ UTXO is "
"selected. You should see `UTXOs 1/1` in the interface. Once you are sure "
"this is the case you can hit `Send Selected`."
msgstr ""
"전송하려는 UTXO를 선택하고, 이 UTXO _만_ 이 선택되어 있는지 확인한다. 인터페"
"이스에 `UTXO 1/1`이 표시될 것이다. 이 것이 확인되면 `Send Selected`를 누르면 "
"된다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:140
msgid "![](images/sending_02.png)"
msgstr "![](images/sending_02.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:142
msgid ""
"You will then be presented with the transaction construction interface. "
"There is a few things you need to check here to make sure that this is a "
"safe send:"
msgstr ""
"그러면 트랜잭션 생성(create transaction) 인터페이스가 표시된다. 안전한 송금인"
"지 확인하기 위해 여기서 확인해야 할 몇 가지 사항이 있다:"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:144
msgid ""
"The transaction should have only 1 input, and this should be the UTXO with "
"the label you want to send"
msgstr ""
"트랜잭션에는 입력(input)이 1개만 있어야 하며, 이 입력은 전송하려는 레이블이 "
"있는 UTXO여야 한다"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:145
msgid ""
"The transaction should have only 1 output, which is the address/label where "
"you want to send the inscription"
msgstr ""
"트랜잭션에는 출력(output)이 1개만 있어야 하며 이는 인스크립션을 보낼 주소/레"
"이블이다"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:147
msgid ""
"If your transaction looks any different, for example you have multiple "
"inputs, or multiple outputs then this may not be a safe transfer of your "
"inscription, and you should abandon sending until you understand more, or "
"can import into the `ord` wallet."
msgstr ""
"예를 들어 입력이 여러 개이거나 출력이 여러 개인 등 거래가 조금이라도 다르게 "
"보인다면 안전한 송금이 아닐 수 있으니, 더 많은 정보를 파악하거나 `ord` 지갑으"
"로 가져오기 할 수 있을 때까지 전송을 중단해야 한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:149
msgid ""
"You should set an appropriate transaction fee, Sparrow will usually "
"recommend a reasonable one, but you can also check [mempool.space](https://"
"mempool.space) to see what the recommended fee rate is for sending a "
"transaction."
msgstr ""
"적절한 트랜잭션 수수료를 설정해야 하며, 보통 스패로우에서 합리적인 수수료를 "
"추천해 주지만, 트랜잭션 전송에 대한 권장 수수료율을 확인하려면 [mempool."
"space](https://mempool.space)에서 확인할 수도 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:151
msgid ""
"You should add a label for the recipient address, a label like `alice "
"address for inscription #123` would be ideal."
msgstr ""
"받는 사람 주소에 레이블을 추가해야 하는데, `alice address for inscription "
"#123`와 같은 레이블이 이상적이다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:153
msgid ""
"Once you have checked the transaction is a safe transaction using the checks "
"above, and you are confident to send it you can click `Create Transaction`."
msgstr ""
"위의 확인사항을 통해 안전한 트랜잭션인지 확인하고 송금에 확신이 들면 `Create "
"Transaction`을 클릭한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:155
msgid "![](images/sending_03.png)"
msgstr "![](images/sending_03.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:157
msgid ""
"Here again you can double check that your transaction looks safe, and once "
"you are confident you can click `Finalize Transaction for Signing`."
msgstr ""
"여기서도 거래가 안전한지 다시 한 번 확인할 수 있으며, 확신이 들면 `Finalize "
"Transaction for Signing`을 클릭한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:159
msgid "![](images/sending_04.png)"
msgstr "![](images/sending_04.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:161
msgid "Here you can triple check everything before hitting `Sign`."
msgstr "여기에서 `Sign`을 누르기 전에 모든 것을 다시 한 번 확인할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:163
msgid "![](images/sending_05.png)"
msgstr "![](images/sending_05.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:165
msgid ""
"And then actually you get very very last chance to check everything before "
"hitting `Broadcast Transaction`. Once you broadcast the transaction it is "
"sent to the bitcoin network, and starts being propagated into the mempool."
msgstr ""
"그리고 실제로 `Broadcast Transaction`을 누르기 전에 모든 것을 확인할 수 있는 "
"마지막 기회가 주어진다. 트랜잭션을 브로드캐스트하면 비트코인 네트워크로 전송"
"되고 멤풀로 전파되기 시작한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:167
msgid "![](images/sending_06.png)"
msgstr "![](images/sending_06.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:169
msgid ""
"If you want to track the status of your transaction you can copy the "
"`Transaction Id (Txid)` and paste that into [mempool.space](https://mempool."
"space)"
msgstr ""
"트랜잭션의 상태를 추적하려면 `Transaction Id (Txid)`를 복사하여 [mempool."
"space](https://mempool.space)에 붙여넣으면 된다"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:171
msgid ""
"Once the transaction has confirmed you can check the inscription page on "
"[ordinals.com](https://ordinals.com) to validate that it has moved to the "
"new output location and address."
msgstr ""
"트랜잭션이 확인되면 [ordinals.com](https://ordinals.com)에서 인스크립션 페이"
"지를 확인하여 새 출력 위치(output location) 및 주소로 이동했는지 확인할 수 있"
"다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:175
msgid ""
"Sparrow wallet is not showing a transaction/UTXO, but I can see it on "
"mempool.space!"
msgstr ""
"스패로우 지갑에 트랜잭션/UTXO가 표시되지 않지만, 나는 mempool.space에서는 볼 "
"수 있다!"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:177
msgid ""
"Make sure that your wallet is connected to a bitcoin node. To validate this, "
"head into the `Preferences`\\-> `Server` settings, and click `Edit Existing "
"Connection`."
msgstr ""
"지갑이 비트코인 노드에 연결되어 있는지 확인하자. 이를 확인하려면 "
"`Preferences`\\-> `Server` 설정으로 이동하여 `Edit Existing Connection`을 클"
"릭한다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:179
msgid "![](images/troubleshooting_01.png)"
msgstr "![](images/troubleshooting_01.png)"

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:181
msgid ""
"From there you can select a node and click `Test Connection` to validate "
"that Sparrow is able to connect successfully."
msgstr ""
"여기에서 노드를 선택하고 `Test Connection`를 클릭하여 스패로우가 성공적으로 "
"연결할 수 있는지 확인할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/collecting/sparrow-wallet.md:183
msgid "![](images/troubleshooting_02.png)"
msgstr "![](images/troubleshooting_02.png)"

#: /workspaces/ord_ko/docs/src/guides/testing.md:4
msgid ""
"Ord can be tested using the following flags to specify the test network. For "
"more information on running Bitcoin Core for testing, see [Bitcoin's "
"developer documentation](https://developer.bitcoin.org/examples/testing."
"html)."
msgstr ""
"Ord는 다음 플래그를 사용하여 테스트 네트워크를 지정하여 테스트할 수 있다. 테"
"스트를 위한 Bitcoin Core 실행에 대한 자세한 내용은 [비트코인 개발자 문서]"
"(https://developer.bitcoin.org/examples/testing.html)를 참조하자."

#: /workspaces/ord_ko/docs/src/guides/testing.md:7
msgid ""
"Most `ord` commands in [inscriptions](inscriptions.md) and [explorer]"
"(explorer.md) can be run with the following network flags:"
msgstr ""
"[인스크립션](inscriptions.md) 및 [탐색기](explorer.md)의 대부분의 `ord` 명령"
"은 다음 네트워크 플래그를 사용하여 실행할 수 있다:"

#: /workspaces/ord_ko/docs/src/guides/testing.md:10
msgid "Network"
msgstr "네트워크"

#: /workspaces/ord_ko/docs/src/guides/testing.md:10
msgid "Flag"
msgstr "플래그"

#: /workspaces/ord_ko/docs/src/guides/testing.md:12
msgid "Testnet"
msgstr "Testnet"

#: /workspaces/ord_ko/docs/src/guides/testing.md:12
msgid "`--testnet` or `-t`"
msgstr "`--testnet` 또는 `-t`"

#: /workspaces/ord_ko/docs/src/guides/testing.md:13
msgid "Signet"
msgstr "Signet"

#: /workspaces/ord_ko/docs/src/guides/testing.md:13
msgid "`--signet` or `-s`"
msgstr "`--signet` 또는 `-s`"

#: /workspaces/ord_ko/docs/src/guides/testing.md:14
msgid "Regtest"
msgstr "Regtest"

#: /workspaces/ord_ko/docs/src/guides/testing.md:14
msgid "`--regtest` or `-r`"
msgstr "`--regtest` 또는 `-r`"

#: /workspaces/ord_ko/docs/src/guides/testing.md:16
msgid "Regtest doesn't require downloading the blockchain or indexing ord."
msgstr "Regtest는 블록체인을 다운로드하거나 ord를 인덱싱할 필요가 없다."

#: /workspaces/ord_ko/docs/src/guides/testing.md:21
msgid "Run bitcoind in regtest with:"
msgstr "다음을 사용해 regtest에 bitcoind를 실행한다:"

#: /workspaces/ord_ko/docs/src/guides/testing.md:25
msgid "Create a wallet in regtest with:"
msgstr "다음을 사용해 regtest에 지갑을 만든다:"

#: /workspaces/ord_ko/docs/src/guides/testing.md:29
msgid "Get a regtest receive address with:"
msgstr "다음을 사용해 regtest 수신 주소를 얻는다:"

#: /workspaces/ord_ko/docs/src/guides/testing.md:33
msgid "Mine 101 blocks (to unlock the coinbase) with:"
msgstr "다음을 사용해 101 블록을 채굴한다(코인베이스 잠금 해제를 위해):"

#: /workspaces/ord_ko/docs/src/guides/testing.md:37
msgid "Inscribe in regtest with:"
msgstr "다음을 사용해 regtest에 인스립션을 새긴다:"

#: /workspaces/ord_ko/docs/src/guides/testing.md:41
msgid "Mine the inscription with:"
msgstr "다음을 사용해 인스크립션을 채굴한다:"

#: /workspaces/ord_ko/docs/src/guides/testing.md:45
msgid "View the inscription in the regtest explorer:"
msgstr "다음을 사용해 regtest 탐색기에서 인스크립션을 확인한다:"

#: /workspaces/ord_ko/docs/src/guides/testing.md:50
msgid "Testing Recursion"
msgstr "리커젼 테스트"

#: /workspaces/ord_ko/docs/src/guides/testing.md:53
msgid ""
"When testing out [recursion](../inscriptions/recursion.md), inscribe the "
"dependencies first (example with [p5.js](https://p5js.org)):"
msgstr ""
"[리커젼](../inscriptions/recursion.md)을 테스트할 때는 종속성을 먼저 새긴다 "
"(예: [p5.js](https://p5js.org)를 사용):"

#: /workspaces/ord_ko/docs/src/guides/testing.md:58
msgid ""
"This should return a `inscription_id` which you can then reference in your "
"recursive inscription."
msgstr ""
"그러면 리커젼 인스크립션에서 참조할 수 있는 `inscription_id`가 반환될 것이다."

#: /workspaces/ord_ko/docs/src/guides/testing.md:61
msgid ""
"ATTENTION: These ids will be different when inscribing on mainnet or signet, "
"so be sure to change those in your recursive inscription for each chain."
msgstr ""
"주의: 메인넷이나 시그넷에 새김 할 때 이 ID는 달라지므로 리커젼 인스크립션 안"
"에 ID를 각 체인 맞추어 변경하도록 하자."

#: /workspaces/ord_ko/docs/src/guides/testing.md:65
msgid "Then you can inscribe your recursive inscription with:"
msgstr "그런 다음을 사용해 리커젼 인스크립션을 새길 수 있다:"

#: /workspaces/ord_ko/docs/src/guides/testing.md:69
msgid "Finally you will have to mine some blocks and start the server:"
msgstr "마지막으로 블록을 어느 정도 채굴해야 하고 서버를 시작해야 한다:"

#: /workspaces/ord_ko/docs/src/guides/moderation.md:4
msgid ""
"`ord` includes a block explorer, which you can run locally with `ord server`."
msgstr ""
"`ord`에는 블록 탐색기가 포함되어 있으며, `ord server`로 로컬에서 실행할 수 있"
"다."

#: /workspaces/ord_ko/docs/src/guides/moderation.md:6
msgid ""
"The block explorer allows viewing inscriptions. Inscriptions are user-"
"generated content, which may be objectionable or unlawful."
msgstr ""
"블록 탐색기를 통해 인스크립션을 볼 수 있다. 인스크립션은 사용자가 생성한 콘텐"
"츠로, 그 콘텐츠가 불쾌감을 주거나 불법일 수 있다."

#: /workspaces/ord_ko/docs/src/guides/moderation.md:9
msgid ""
"It is the responsibility of each individual who runs an ordinal block "
"explorer instance to understand their responsibilities with respect to "
"unlawful content, and decide what moderation policy is appropriate for their "
"instance."
msgstr ""
"오디널스 블록 탐색기 인스턴스를 운영하는 각 개인은 불법 콘텐츠에 대한 자신의 "
"책임을 이해하고 자신의 인스턴스에 적합한 중재 정책을 결정할 책임이 있다."

#: /workspaces/ord_ko/docs/src/guides/moderation.md:13
msgid ""
"In order to prevent particular inscriptions from being displayed on an `ord` "
"instance, they can be included in a YAML config file, which is loaded with "
"the `--config` option."
msgstr ""
"특정 인스크립션이 `ord` 인스턴스에 표시되지 않도록 하려면 `--config` 옵션을 "
"사용해 로드되는 YAML 구성 파일에 포함할 수 있다."

#: /workspaces/ord_ko/docs/src/guides/moderation.md:17
msgid ""
"To hide inscriptions, first create a config file, with the inscription ID "
"you want to hide:"
msgstr ""
"인스크립션을 숨기려면 먼저 숨기려는 인스크립션 ID를 사용하여 구성 파일을 만든"
"다:"

#: /workspaces/ord_ko/docs/src/guides/moderation.md:25
msgid ""
"The suggested name for `ord` config files is `ord.yaml`, but any filename "
"can be used."
msgstr ""
"'ord' 구성 파일의 권장 이름은 'ord.yaml'이지만 어떤 파일 이름도 사용할 수 있"
"다."

#: /workspaces/ord_ko/docs/src/guides/moderation.md:28
msgid "Then pass the file to `--config` when starting the server:"
msgstr "그런 다음 서버를 시작할 때 `--config`에 파일을 전달한다:"

#: /workspaces/ord_ko/docs/src/guides/moderation.md:30
msgid "`ord --config ord.yaml server`"
msgstr "`ord --config ord.yaml server`"

#: /workspaces/ord_ko/docs/src/guides/moderation.md:32
msgid ""
"Note that the `--config` option comes after `ord` but before the `server` "
"subcommand."
msgstr ""
"`—config` 옵션은 `ord` 뒤에 오지만 `server` 하위 명령 앞에 온다는 점을 유의하"
"자."

#: /workspaces/ord_ko/docs/src/guides/moderation.md:35
msgid "`ord` must be restarted in to load changes to the config file."
msgstr "구성 파일의 변경 사항을 로드하려면 `ord`를 다시 시작해야 한다."

#: /workspaces/ord_ko/docs/src/guides/moderation.md:37
msgid "`ordinals.com`"
msgstr "`ordinals.com`"

#: /workspaces/ord_ko/docs/src/guides/moderation.md:40
msgid ""
"The `ordinals.com` instances use `systemd` to run the `ord server` service, "
"which is called `ord`, with a config file located at `/var/lib/ord/ord.yaml`."
msgstr ""
"`ordinals.com` 인스턴스는 `systemd`를 사용하여 `ord`라고 하는 `ord server` 서"
"비스를 실행하며, 구성 파일은 `/var/lib/ord/ord.yaml`에 있다."

#: /workspaces/ord_ko/docs/src/guides/moderation.md:43
msgid "To hide an inscription on `ordinals.com`:"
msgstr "`ordinals.com`에서 인스크립션을 숨기려면:"

#: /workspaces/ord_ko/docs/src/guides/moderation.md:45
msgid "SSH into the server"
msgstr "SSH 사용하여 서버에 접속한다"

#: /workspaces/ord_ko/docs/src/guides/moderation.md:46
msgid "Add the inscription ID to `/var/lib/ord/ord.yaml`"
msgstr "인스크립션 ID를 `/var/lib/ord/ord.yaml`에 추가한다"

#: /workspaces/ord_ko/docs/src/guides/moderation.md:47
msgid "Restart the service with `systemctl restart ord`"
msgstr "`systemctl restart ord`로 서비스를 다시 시작한다"

#: /workspaces/ord_ko/docs/src/guides/moderation.md:48
msgid "Monitor the restart with `journalctl -u ord`"
msgstr "`journalctl -u ord`로 재시작을 모니터링한다"

#: /workspaces/ord_ko/docs/src/guides/moderation.md:50
msgid ""
"Currently, `ord` is slow to restart, so the site will not come back online "
"immediately."
msgstr ""
"현재 `ord`는 재시작 속도가 느리기 때문에 사이트가 즉시 온라인 상태로 돌아오"
"지 않을 것이다."

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:4
msgid ""
"Sometimes the `ord` database must be reindexed, which means deleting the "
"database and restarting the indexing process with either `ord index update` "
"or `ord server`. Reasons to reindex are:"
msgstr ""
"때때로 `ord` 데이터베이스를 재인덱싱해야 하는 경우가 있는데, 이는 데이터베이"
"스를 삭제하고 `ord index update` 또는 `ord server`를 사용하여 인덱싱 프로세스"
"를 다시 시작해야 함을 의미한다. 재인덱싱해야 하는 이유는 다음과 같다:"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:8
msgid "A new major release of ord, which changes the database scheme"
msgstr "데이터베이스 체계를 변경하는 ord의 새로운 주요 릴리스"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:9
msgid "The database got corrupted somehow"
msgstr "데이터베이스가 어떻게든 손상되었을 때"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:11
msgid ""
"The database `ord` uses is called [redb](https://github.com/cberner/redb), "
"so we give the index the default file name `index.redb`. By default we store "
"this file in different locations depending on your operating system."
msgstr ""
"`ord`가 사용하는 데이터베이스는 [redb](https://github.com/cberner/redb)이므"
"로 인덱스에 `index.redb`로 기본 파일 이름을 지정한다. 기본적으로 이 파일은 운"
"영 체제에 따라 다른 위치에 저장된다."

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:15
msgid "Platform"
msgstr "Platform"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:15
msgid "Value"
msgstr "Value"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:17
msgid "Linux"
msgstr "Linux"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:17
msgid "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"
msgstr "`$XDG_DATA_HOME`/ord 또는 `$HOME`/.local/share/ord"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:17
msgid "/home/<USER>/.local/share/ord"
msgstr "/home/<USER>/.local/share/ord"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:18
msgid "macOS"
msgstr "macOS"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:18
msgid "`$HOME`/Library/Application Support/ord"
msgstr "`$HOME`/Library/Application Support/ord"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:18
msgid "/Users/<USER>/Library/Application Support/ord"
msgstr "/Users/<USER>/Library/Application Support/ord"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:19
msgid "Windows"
msgstr "Windows"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:19
msgid "`{FOLDERID_RoamingAppData}`\\\\ord"
msgstr "`{FOLDERID_RoamingAppData}`\\\\ord"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:19
msgid "C:\\Users\\<USER>\\AppData\\Roaming\\ord"
msgstr "C:\\Users\\<USER>\\AppData\\Roaming\\ord"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:21
msgid ""
"So to delete the database and reindex on MacOS you would have to run the "
"following commands in the terminal:"
msgstr ""
"따라서 MacOS에서 데이터베이스를 삭제하고 재인덱스하려면 터미널에서 다음 명령"
"을 실행해야 한다:"

#: /workspaces/ord_ko/docs/src/guides/reindexing.md:29
msgid ""
"You can of course also set the location of the data directory yourself with "
"`ord --datadir <DIR> index update` or give it a specific filename and path "
"with `ord --index <FILENAME> index update`."
msgstr ""
"물론 `ord --datadir <DIR> index update`로 데이터 디렉터리의 위치를 직접 설정"
"하거나 `ord --index <FILENAME> index update`로 특정 파일 이름과 경로를 지정"
"할 수도 있다."

#: /workspaces/ord_ko/docs/src/bounties.md:1
msgid "Ordinal Bounty Hunting Hints"
msgstr "오디널 현상금 사냥 힌트"

#: /workspaces/ord_ko/docs/src/bounties.md:4
msgid ""
"The `ord` wallet can send and receive specific satoshis. Additionally, "
"ordinal theory is extremely simple. A clever hacker should be able to write "
"code from scratch to manipulate satoshis using ordinal theory in no time."
msgstr ""
"`ord` 지갑은 특정 사토시를 주고받을 수 있다. 또한 오디널 이론은 매우 간단하"
"다. 영리한 해커라면 짧은 시간에 오디널 이론을 사용하여 사토시를 조작하는 코드"
"를 처음부터 작성할 수 있을 것이다."

#: /workspaces/ord_ko/docs/src/bounties.md:8
msgid ""
"For more information about ordinal theory, check out the [FAQ](./faq.md) for "
"an overview, the [BIP](https://github.com/ordinals/ord/blob/master/bip."
"mediawiki) for the technical details, and the [ord repo](https://github.com/"
"ordinals/ord) for the `ord` wallet and block explorer."
msgstr ""
"오디널 이론에 대한 자세한 내용은 [자주 묻는 질문](./faq.md)에서 개요를, [BIP]"
"(https://github.com/ordinals/ord/blob/master/bip.mediawiki)에서 기술적 세부 "
"사항을, [ord repo](https://github.com/ordinals/ord)에서 `ord` 지갑 및 블록 탐"
"색기를 확인하길 바란다."

#: /workspaces/ord_ko/docs/src/bounties.md:14
msgid ""
"Satoshi was the original developer of ordinal theory. However, he knew that "
"others would consider it heretical and dangerous, so he hid his knowledge, "
"and it was lost to the sands of time. This potent theory is only now being "
"rediscovered. You can help by researching rare satoshis."
msgstr ""
"사토시는 오디널 이론의 최초 개발자였다. 하지만 다른 사람들이 이 이론을 이단적"
"이고 위험하다고 생각할 것을 알았기 때문에 자신의 지식을 숨겼고, 이는 결국 시"
"간의 모래 속으로 사라져 버렸다. 이 강력한 이론은 이제야 재발견되고 있다. 당신"
"이 희귀한 사토시를 연구하면 이에 도움이 될 수 있다."

#: /workspaces/ord_ko/docs/src/bounties.md:19
msgid "Good luck and godspeed!"
msgstr "행운과 성공을 빈다!"

#: /workspaces/ord_ko/docs/src/bounty/0.md:1
msgid "Ordinal Bounty 0"
msgstr "오디널 현상금 0"

#: /workspaces/ord_ko/docs/src/bounty/0.md:4
#: /workspaces/ord_ko/docs/src/bounty/1.md:4
#: /workspaces/ord_ko/docs/src/bounty/2.md:4
#: /workspaces/ord_ko/docs/src/bounty/3.md:4
msgid "Criteria"
msgstr "기준"

#: /workspaces/ord_ko/docs/src/bounty/0.md:7
msgid ""
"Send a sat whose ordinal number ends with a zero to the submission address:"
msgstr "서수가 0으로 끝나는 SAT를 제출 주소로 보낸다:"

#: /workspaces/ord_ko/docs/src/bounty/0.md:9
msgid "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"
msgstr "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"

#: /workspaces/ord_ko/docs/src/bounty/0.md:11
msgid "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"
msgstr "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"

#: /workspaces/ord_ko/docs/src/bounty/0.md:13
msgid "The sat must be the first sat of the output you send."
msgstr "해당 SAT는 전송하는 출력(output)의 첫 번째 SAT여야 한다."

#: /workspaces/ord_ko/docs/src/bounty/0.md:15
#: /workspaces/ord_ko/docs/src/bounty/1.md:14
#: /workspaces/ord_ko/docs/src/bounty/2.md:15
#: /workspaces/ord_ko/docs/src/bounty/3.md:63
msgid "Reward"
msgstr "보상"

#: /workspaces/ord_ko/docs/src/bounty/0.md:18
msgid "100,000 sats"
msgstr "100,000 SAT"

#: /workspaces/ord_ko/docs/src/bounty/0.md:20
#: /workspaces/ord_ko/docs/src/bounty/1.md:19
#: /workspaces/ord_ko/docs/src/bounty/2.md:20
#: /workspaces/ord_ko/docs/src/bounty/3.md:70
msgid "Submission Address"
msgstr "제출 주소"

#: /workspaces/ord_ko/docs/src/bounty/0.md:23
msgid ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/"
"address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"
msgstr ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/"
"address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"

#: /workspaces/ord_ko/docs/src/bounty/0.md:25
#: /workspaces/ord_ko/docs/src/bounty/1.md:24
#: /workspaces/ord_ko/docs/src/bounty/2.md:25
#: /workspaces/ord_ko/docs/src/bounty/3.md:75
msgid "Status"
msgstr "상태"

#: /workspaces/ord_ko/docs/src/bounty/0.md:28
msgid ""
"Claimed by [@count_null](https://twitter.com/rodarmor/"
"status/1560793241473400833)!"
msgstr ""
"[@count_null](https://twitter.com/rodarmor/status/1560793241473400833)이 획득"
"했다!"

#: /workspaces/ord_ko/docs/src/bounty/1.md:1
msgid "Ordinal Bounty 1"
msgstr "오디널 현상금 1"

#: /workspaces/ord_ko/docs/src/bounty/1.md:7
msgid ""
"The transaction that submits a UTXO containing the oldest sat, i.e., that "
"with the lowest number, amongst all submitted UTXOs will be judged the "
"winner."
msgstr ""
"제출된 모든 UTXO 중에서 가장 오래된 SAT가 포함된, 즉 가장 낮은 숫자의 SAT를 "
"제출한 트랜잭션이 승자로 판단된다."

#: /workspaces/ord_ko/docs/src/bounty/1.md:10
msgid ""
"The bounty is open for submissions until block 753984—the first block of "
"difficulty adjustment period 374. Submissions included in block 753984 or "
"later will not be considered."
msgstr ""
"현상금은 난이도 조정 기간 374의 첫 번째 블록인 753984 블록까지 제출할 수 있"
"다. 블록 753984 이후에 포함된 제출물은 고려되지 않는다."

#: /workspaces/ord_ko/docs/src/bounty/1.md:17
msgid "200,000 sats"
msgstr "200,000 SAT"

#: /workspaces/ord_ko/docs/src/bounty/1.md:22
msgid ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/"
"address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"
msgstr ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/"
"address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"

#: /workspaces/ord_ko/docs/src/bounty/1.md:27
msgid ""
"Claimed by [@ordinalsindex](https://twitter.com/rodarmor/"
"status/1569883266508853251)!"
msgstr ""
"[@ordinalsindex](https://twitter.com/rodarmor/status/1569883266508853251)가 "
"획득했다!"

#: /workspaces/ord_ko/docs/src/bounty/2.md:1
msgid "Ordinal Bounty 2"
msgstr "오디널 현상금 2"

#: /workspaces/ord_ko/docs/src/bounty/2.md:7
msgid "Send an "
msgstr "제출 주소로 "

#: /workspaces/ord_ko/docs/src/bounty/2.md:7
msgid "uncommon"
msgstr "uncommon"

#: /workspaces/ord_ko/docs/src/bounty/2.md:7
msgid " sat to the submission address:"
msgstr " SAT를 보낸다:"

#: /workspaces/ord_ko/docs/src/bounty/2.md:9
msgid "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"
msgstr "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"

#: /workspaces/ord_ko/docs/src/bounty/2.md:11
msgid "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"
msgstr "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"

#: /workspaces/ord_ko/docs/src/bounty/2.md:13
msgid ""
"Confirm that the submission address has not received transactions before "
"submitting your entry. Only the first successful submission will be rewarded."
msgstr ""
"제출하기 전에 제출 주소에 트랜잭션이 수신되지 않았는지 확인하자. 첫 번째 성공"
"적인 제출에 대해서만 보상이 지급된다."

#: /workspaces/ord_ko/docs/src/bounty/2.md:18
msgid "300,000 sats"
msgstr "300,000 SAT"

#: /workspaces/ord_ko/docs/src/bounty/2.md:23
msgid ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/"
"address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"
msgstr ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/"
"address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"

#: /workspaces/ord_ko/docs/src/bounty/2.md:28
msgid ""
"Claimed by [@utxoset](https://twitter.com/rodarmor/"
"status/1582424455615172608)!"
msgstr ""
"[@utxoset](https://twitter.com/rodarmor/status/1582424455615172608)가 획득했"
"다!"

#: /workspaces/ord_ko/docs/src/bounty/3.md:1
msgid "Ordinal Bounty 3"
msgstr "오디널 현상금 3"

#: /workspaces/ord_ko/docs/src/bounty/3.md:7
msgid ""
"Ordinal bounty 3 has two parts, both of which are based on _ordinal names_. "
"Ordinal names are a modified base-26 encoding of ordinal numbers. To avoid "
"locking short names inside the unspendable genesis block coinbase reward, "
"ordinal names get _shorter_ as the ordinal number gets _longer_. The name of "
"sat 0, the first sat to be mined is `nvtdijuwxlp` and the name of sat "
"2,099,999,997,689,999, the last sat to be mined, is `a`."
msgstr ""
"오디널 현상금 3은 두 부분으로 나뉘며, 두 부분 모두 _오디널 이름_ 을 기반으로 "
"한다. 오디널 이름은 서수의 수정된 base26 인코딩이다. 사용할 수 없는 제네시스 "
"블록 코인베이스 보상 안에 짧은 이름이 고정되는 것을 방지하기 위해 서수가 _길"
"어질수록_ 오디널 이름은 _짧아진다_. 첫 번째 채굴되는 SAT 0의 이름은 "
"`nvtdijuwxlp`이고, 마지막 채굴되는 SAT 2,099,999,997,689,999의 이름은 `a`이"
"다."

#: /workspaces/ord_ko/docs/src/bounty/3.md:14
msgid ""
"The bounty is open for submissions until block 840000—the first block after "
"the fourth halvening. Submissions included in block 840000 or later will not "
"be considered."
msgstr ""
"현상금은 네 번째 반감기 이후 첫 번째 블록인 840000 블록까지 제출할 수 있다. "
"840000 블록 이후에 포함된 제출물은 고려되지 않는다."

#: /workspaces/ord_ko/docs/src/bounty/3.md:18
msgid ""
"Both parts use [frequency.tsv](frequency.tsv), a list of words and the "
"number of times they occur in the [Google Books Ngram dataset](http://"
"storage.googleapis.com/books/ngrams/books/datasetsv2.html). filtered to only "
"include the names of sats which will have been mined by the end of the "
"submission period, that appear at least 5000 times in the corpus."
msgstr ""
"두 부분 모두 [frequency.tsv](frequency.tsv)를 사용한다. 이는 [Google Books "
"Ngram dataset](http://storage.googleapis.com/books/ngrams/books/datasetsv2."
"html)에 있는 단어 목록 그리고 각 단어가 이 데이터셋에서 등장하는 횟수며 제출 "
"기간이 끝날 때까지 채굴될, 전체목록에 최소 5000번 이상 등장하는 SAT의 이름만 "
"포함하도록 필터링하였다."

#: /workspaces/ord_ko/docs/src/bounty/3.md:24
msgid ""
"`frequency.tsv` is a file of tab-separated values. The first column is the "
"word, and the second is the number of times it appears in the corpus. The "
"entries are sorted from least-frequently occurring to most-frequently "
"occurring."
msgstr ""
"`frequency.tsv`는 탭으로 구분된 값의 파일이다. 첫 번째 열은 단어이고 두 번째 "
"열은 이 단어가 전체목록에 나타나는 횟수이다. 항목은 가장 적게 나타나는 항목부"
"터 가장 많이 나타나는 항목까지 정렬된다."

#: /workspaces/ord_ko/docs/src/bounty/3.md:29
msgid ""
"`frequency.tsv` was compiled using [this program](https://github.com/casey/"
"onegrams)."
msgstr ""
"[이 프로그램](https://github.com/casey/onegrams)을 사용하여 `frequency.tsv`"
"를 컴파일했다."

#: /workspaces/ord_ko/docs/src/bounty/3.md:32
msgid ""
"To search an `ord` wallet for sats with a name in `frequency.tsv`, use the "
"following [`ord`](https://github.com/ordinals/ord) command:"
msgstr ""
"`frequency.tsv`에 이름이 있는 SAT을 `ord` 지갑에서 검색하려면 다음 [`ord`]"
"(https://github.com/ordinals/ord) 명령을 사용한다:"

#: /workspaces/ord_ko/docs/src/bounty/3.md:39
msgid ""
"This command requires the sat index, so `--index-sats` must be passed to ord "
"when first creating the index."
msgstr ""
"이 명령에는 SAT 인덱스가 필요하므로 인덱스를 처음 생성할 때 `--index-sats`를 "
"ord에 전달해야 한다."

#: /workspaces/ord_ko/docs/src/bounty/3.md:42
msgid "Part 0"
msgstr "파트 0"

#: /workspaces/ord_ko/docs/src/bounty/3.md:44
msgid "_Rare sats pair best with rare words._"
msgstr "_희귀 SAT는 희귀 단어와 가장 잘 어울린다._"

#: /workspaces/ord_ko/docs/src/bounty/3.md:46
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the lowest number of occurrences in `frequency.tsv` shall be the winner "
"of part 0."
msgstr ""
"`frequency.tsv`에서 가장 적은 횟수로 나타나는 SAT 이름을 포함하는 UTXO를 제출"
"한 트랜잭션이 파트 0의 승자가 된다."

#: /workspaces/ord_ko/docs/src/bounty/3.md:50
msgid "Part 1"
msgstr "파트 1"

#: /workspaces/ord_ko/docs/src/bounty/3.md:52
msgid "_Popularity is the font of value._"
msgstr "_인기는 가치의 글꼴이다._"

#: /workspaces/ord_ko/docs/src/bounty/3.md:54
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the highest number of occurrences in `frequency.tsv` shall be the "
"winner of part 1."
msgstr ""
"`frequency.tsv`에서 가장 많은 횟수로 나타나는 SAT 이름이 포함된 UTXO를 제출"
"한 트랜잭션이 파트 1의 승자가 된다."

#: /workspaces/ord_ko/docs/src/bounty/3.md:58
msgid "Tie Breaking"
msgstr "타이 브레이킹"

#: /workspaces/ord_ko/docs/src/bounty/3.md:60
msgid ""
"In the case of a tie, where two submissions occur with the same frequency, "
"the earlier submission shall be the winner."
msgstr ""
"동점인 경우, 즉 제출물의 빈도가 동일할 경우 먼저 제출된 것이 승자가 된다."

#: /workspaces/ord_ko/docs/src/bounty/3.md:66
msgid "Part 0: 200,000 sats"
msgstr "파트 0: 200,000 SAT"

#: /workspaces/ord_ko/docs/src/bounty/3.md:67
msgid "Part 1: 200,000 sats"
msgstr "파트 1: 200,000 SAT"

#: /workspaces/ord_ko/docs/src/bounty/3.md:68
msgid "Total: 400,000 sats"
msgstr "총: 400,000 SAT"

#: /workspaces/ord_ko/docs/src/bounty/3.md:73
msgid ""
"[`**********************************`](https://mempool.space/"
"address/**********************************)"
msgstr ""
"[`**********************************`](https://mempool.space/"
"address/**********************************)"

#: /workspaces/ord_ko/docs/src/bounty/3.md:78
msgid "Unclaimed!"
msgstr "미수령!"

#~ msgid "`uncommon`: 745,855"
#~ msgstr "`uncommon`: 745,855"

#, fuzzy
#~ msgid ""
#~ "get the parent inscription id `<PARENT_INSCRIPTION_ID>` from the output "
#~ "of `ord wallet inscriptions`"
#~ msgstr "지갑 비문`의 출력에서 부모 비문 ID `<부모_비문_ID>`를 가져옵니다"

#, fuzzy
#~ msgid ""
#~ "```\n"
#~ "ord wallet inscribe --fee-rate FEE_RATE --parent <PARENT_INSCRIPTION_ID> "
#~ "CHILD_FILE\"\n"
#~ "```"
#~ msgstr ""
#~ "```\n"
#~ "ord wallet inscribe --fee-rate FEE_RATE --parent <부모 <부모_인스크립션"
#~ "_ID> CHILD_FILE\"\n"
#~ "```"

#, fuzzy
#~ msgid ""
#~ "And when you visit [the ordinals explorer](https://ordinals.com/) at "
#~ "`ordinals.com/inscription/INSCRIPTION_ID`."
#~ msgstr ""
#~ "그리고 [오디널스 익스플로러](https://ordinals.com/)에서 `ordinals.com/"
#~ "inscription/INSCRIPTION_ID`를 방문하면 됩니다."
