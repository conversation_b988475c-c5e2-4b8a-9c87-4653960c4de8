#!/usr/bin/env bash

set -euxo pipefail

VERSION=${REF#"refs/tags/"}
DIST=`pwd`/dist

echo "Packaging ord $VERSION for $TARGET..."

test -f Cargo.lock || cargo generate-lockfile

echo "Building ord..."
RUSTFLAGS="--deny warnings $TARGET_RUSTFLAGS" \
  cargo build --bin ord --target $TARGET --release
EXECUTABLE=target/$TARGET/release/ord

if [[ $OS == windows-latest ]]; then
  EXECUTABLE=$EXECUTABLE.exe
fi

echo "Copying release files..."
mkdir -p dist/ord-$VERSION
cp \
  $EXECUTABLE \
  Cargo.lock \
  Cargo.toml \
  LICENSE \
  README.md \
  $DIST/ord-$VERSION

cd $DIST
echo "Creating release archive..."
case $OS in
  ubuntu-latest | macos-latest)
    ARCHIVE=$DIST/ord-$VERSION-$TARGET.tar.gz
    tar czf $ARCHIVE *
    echo "::set-output name=archive::$ARCHIVE"
    ;;
  windows-latest)
    ARCHIVE=$DIST/ord-$VERSION-$TARGET.zip
    7z a $ARCHIVE *
    echo "::set-output name=archive::`pwd -W`/ord-$VERSION-$TARGET.zip"
    ;;
esac
