require('dotenv').config();
const { Pool } = require('pg');

// 数据库连接配置
const db_pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_DATABASE || 'postgres',
  password: process.env.DB_PASSWD,
  port: parseInt(process.env.DB_PORT || "5432"),
  max: process.env.DB_MAX_CONNECTIONS || 10,
  ssl: process.env.DB_SSL == 'true' ? true : false
});

async function query_db(query, params = []) {
  return await db_pool.query(query, params);
}

async function syncCurrentBalances() {
  console.log('=== BRC20 当前余额同步脚本 ===\n');
  
  try {
    // 1. 检查环境变量
    console.log('1. 检查环境变量...');
    console.log('✓ DB_HOST:', process.env.DB_HOST || '默认: localhost');
    console.log('✓ DB_PORT:', process.env.DB_PORT || '默认: 5432');
    console.log('✓ DB_DATABASE:', process.env.DB_DATABASE || '默认: postgres');
    console.log('✓ DB_USER:', process.env.DB_USER || '默认: postgres');
    console.log('✓ USE_EXTRA_TABLES:', process.env.USE_EXTRA_TABLES || '默认: false');
    console.log();

    // 2. 检查当前状态
    console.log('2. 检查当前状态...');
    const blockHeightRes = await query_db('SELECT max(block_height) as max_block_height FROM brc20_block_hashes');
    const currentBlockHeight = blockHeightRes.rows[0].max_block_height;
    console.log('✓ 当前区块高度:', currentBlockHeight);

    const currentBalancesCount = await query_db('SELECT COUNT(*) as count FROM brc20_current_balances');
    console.log('✓ brc20_current_balances 当前记录数:', currentBalancesCount.rows[0].count);

    const historicBalancesCount = await query_db('SELECT COUNT(*) as count FROM brc20_historic_balances');
    console.log('✓ brc20_historic_balances 总记录数:', historicBalancesCount.rows[0].count);
    console.log();

    // 3. 检查是否需要同步
    if (parseInt(currentBalancesCount.rows[0].count) > 0) {
      console.log('⚠️  brc20_current_balances 表已有数据');
      console.log('是否要清空并重新同步？这将删除所有当前余额数据！');
      console.log('如果确定要继续，请手动运行以下SQL：');
      console.log('TRUNCATE TABLE brc20_current_balances;');
      console.log();
    }

    // 4. 生成同步SQL
    console.log('3. 生成同步SQL...');
    const syncSQL = `
-- 同步当前余额表的SQL脚本
-- 这将从历史余额表中提取每个 pkscript+tick 组合的最新余额

INSERT INTO brc20_current_balances (pkscript, wallet, tick, overall_balance, available_balance, block_height)
WITH latest_balances AS (
  SELECT 
    pkscript,
    wallet,
    tick,
    overall_balance,
    available_balance,
    block_height,
    ROW_NUMBER() OVER (PARTITION BY pkscript, tick ORDER BY id DESC) as rn
  FROM brc20_historic_balances
  WHERE block_height <= ${currentBlockHeight}
)
SELECT 
  pkscript,
  wallet,
  tick,
  overall_balance,
  available_balance,
  ${currentBlockHeight} as block_height
FROM latest_balances 
WHERE rn = 1 
  AND (overall_balance::numeric > 0 OR available_balance::numeric > 0);
`;

    console.log('✓ 同步SQL已生成');
    console.log();

    // 5. 执行同步（如果表为空）
    if (parseInt(currentBalancesCount.rows[0].count) === 0) {
      console.log('4. 执行同步...');
      console.log('开始同步当前余额数据...');
      
      const startTime = Date.now();
      const result = await query_db(`
        INSERT INTO brc20_current_balances (pkscript, wallet, tick, overall_balance, available_balance, block_height)
        WITH latest_balances AS (
          SELECT 
            pkscript,
            wallet,
            tick,
            overall_balance,
            available_balance,
            block_height,
            ROW_NUMBER() OVER (PARTITION BY pkscript, tick ORDER BY id DESC) as rn
          FROM brc20_historic_balances
          WHERE block_height <= $1
        )
        SELECT 
          pkscript,
          wallet,
          tick,
          overall_balance,
          available_balance,
          $1 as block_height
        FROM latest_balances 
        WHERE rn = 1 
          AND (overall_balance::numeric > 0 OR available_balance::numeric > 0)
      `, [currentBlockHeight]);
      
      const endTime = Date.now();
      console.log(`✓ 同步完成，耗时: ${(endTime - startTime) / 1000}秒`);
      console.log(`✓ 插入记录数: ${result.rowCount}`);
      console.log();

      // 6. 验证同步结果
      console.log('5. 验证同步结果...');
      const newCurrentBalancesCount = await query_db('SELECT COUNT(*) as count FROM brc20_current_balances');
      console.log('✓ 同步后 brc20_current_balances 记录数:', newCurrentBalancesCount.rows[0].count);

      // 检查 ordi
      const ordiCount = await query_db(`
        SELECT COUNT(*) as count 
        FROM brc20_current_balances 
        WHERE tick = 'ordi' AND overall_balance::numeric > 0
      `);
      console.log('✓ ordi 持有者数量:', ordiCount.rows[0].count);

      if (parseInt(ordiCount.rows[0].count) > 0) {
        const topOrdiHolders = await query_db(`
          SELECT wallet, overall_balance, available_balance
          FROM brc20_current_balances
          WHERE tick = 'ordi' AND overall_balance::numeric > 0
          ORDER BY overall_balance::numeric DESC
          LIMIT 3
        `);
        console.log('✓ ordi 前3名持有者:');
        topOrdiHolders.rows.forEach((row, index) => {
          console.log(`  ${index + 1}. ${row.wallet}: ${row.overall_balance} (可用: ${row.available_balance})`);
        });
      }
    }

    // 7. 更新配置建议
    console.log();
    console.log('6. 配置建议...');
    if (process.env.USE_EXTRA_TABLES !== 'true') {
      console.log('⚠️  建议在 .env 文件中设置 USE_EXTRA_TABLES="true"');
      console.log('这将启用 brc20_current_balances 表的使用，提高API性能');
    } else {
      console.log('✓ USE_EXTRA_TABLES 配置正确');
    }

  } catch (error) {
    console.error('❌ 同步过程中发生错误:', error);
  } finally {
    await db_pool.end();
    console.log('\n=== 同步完成 ===');
  }
}

// 运行同步脚本
syncCurrentBalances();
