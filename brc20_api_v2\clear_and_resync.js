require('dotenv').config();
const { Pool } = require('pg');

// 数据库连接配置
const db_pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_DATABASE || 'postgres',
  password: process.env.DB_PASSWD,
  port: parseInt(process.env.DB_PORT || "5432"),
  max: process.env.DB_MAX_CONNECTIONS || 10,
  ssl: process.env.DB_SSL == 'true' ? true : false
});

async function query_db(query, params = []) {
  return await db_pool.query(query, params);
}

async function clearAndResync() {
  console.log('=== BRC20 清空并重新同步脚本 ===\n');
  
  try {
    // 1. 检查当前状态
    console.log('1. 检查当前状态...');
    const blockHeightRes = await query_db('SELECT max(block_height) as max_block_height FROM brc20_block_hashes');
    const currentBlockHeight = blockHeightRes.rows[0].max_block_height;
    console.log('✓ 当前区块高度:', currentBlockHeight);

    const currentBalancesCount = await query_db('SELECT COUNT(*) as count FROM brc20_current_balances');
    console.log('✓ brc20_current_balances 当前记录数:', currentBalancesCount.rows[0].count);

    const historicBalancesCount = await query_db('SELECT COUNT(*) as count FROM brc20_historic_balances');
    console.log('✓ brc20_historic_balances 总记录数:', historicBalancesCount.rows[0].count);

    // 检查 ordi 数据
    const ordiHistoricCount = await query_db(`
      SELECT COUNT(*) as count 
      FROM brc20_historic_balances 
      WHERE tick = 'ordi'
    `);
    console.log('✓ 历史表中 ordi 记录数:', ordiHistoricCount.rows[0].count);
    console.log();

    // 2. 清空当前余额表
    console.log('2. 清空 brc20_current_balances 表...');
    console.log('⚠️  即将删除所有当前余额数据！');
    
    const truncateResult = await query_db('TRUNCATE TABLE brc20_current_balances;');
    console.log('✓ brc20_current_balances 表已清空');
    
    // 验证清空结果
    const afterTruncateCount = await query_db('SELECT COUNT(*) as count FROM brc20_current_balances');
    console.log('✓ 清空后记录数:', afterTruncateCount.rows[0].count);
    console.log();

    // 3. 重新同步数据
    console.log('3. 开始重新同步数据...');
    console.log('正在从历史余额表同步最新余额数据...');
    
    const startTime = Date.now();
    
    // 使用优化的同步查询
    const syncResult = await query_db(`
      INSERT INTO brc20_current_balances (pkscript, wallet, tick, overall_balance, available_balance, block_height)
      WITH latest_balances AS (
        SELECT 
          pkscript,
          wallet,
          tick,
          overall_balance,
          available_balance,
          block_height,
          ROW_NUMBER() OVER (PARTITION BY pkscript, tick ORDER BY id DESC) as rn
        FROM brc20_historic_balances
        WHERE block_height <= $1
      )
      SELECT 
        pkscript,
        wallet,
        tick,
        overall_balance,
        available_balance,
        $1 as block_height
      FROM latest_balances 
      WHERE rn = 1 
        AND (overall_balance::numeric > 0 OR available_balance::numeric > 0)
    `, [currentBlockHeight]);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`✓ 同步完成！`);
    console.log(`✓ 耗时: ${duration.toFixed(2)} 秒`);
    console.log(`✓ 插入记录数: ${syncResult.rowCount}`);
    console.log();

    // 4. 验证同步结果
    console.log('4. 验证同步结果...');
    
    const newCurrentBalancesCount = await query_db('SELECT COUNT(*) as count FROM brc20_current_balances');
    console.log('✓ 同步后总记录数:', newCurrentBalancesCount.rows[0].count);

    // 检查不同 ticker 数量
    const tickerCount = await query_db('SELECT COUNT(DISTINCT tick) as count FROM brc20_current_balances');
    console.log('✓ 不同 ticker 数量:', tickerCount.rows[0].count);

    // 专门检查 ordi
    const ordiCurrentCount = await query_db(`
      SELECT COUNT(*) as count 
      FROM brc20_current_balances 
      WHERE tick = 'ordi' AND overall_balance::numeric > 0
    `);
    console.log('✓ ordi 持有者数量:', ordiCurrentCount.rows[0].count);

    if (parseInt(ordiCurrentCount.rows[0].count) > 0) {
      // 显示 ordi 统计信息
      const ordiStats = await query_db(`
        SELECT 
          COUNT(*) as holder_count,
          SUM(overall_balance::numeric) as total_supply,
          MAX(overall_balance::numeric) as max_balance,
          MIN(overall_balance::numeric) as min_balance
        FROM brc20_current_balances
        WHERE tick = 'ordi' AND overall_balance::numeric > 0
      `);
      
      console.log('✓ ordi 统计信息:');
      console.log(`  - 持有者数量: ${ordiStats.rows[0].holder_count}`);
      console.log(`  - 总供应量: ${ordiStats.rows[0].total_supply}`);
      console.log(`  - 最大余额: ${ordiStats.rows[0].max_balance}`);
      console.log(`  - 最小余额: ${ordiStats.rows[0].min_balance}`);

      // 显示前5名持有者
      const topOrdiHolders = await query_db(`
        SELECT wallet, pkscript, overall_balance, available_balance
        FROM brc20_current_balances
        WHERE tick = 'ordi' AND overall_balance::numeric > 0
        ORDER BY overall_balance::numeric DESC
        LIMIT 5
      `);
      
      console.log('✓ ordi 前5名持有者:');
      topOrdiHolders.rows.forEach((row, index) => {
        const wallet = row.wallet || row.pkscript.substring(0, 20) + '...';
        console.log(`  ${index + 1}. ${wallet}`);
        console.log(`     总余额: ${row.overall_balance}`);
        console.log(`     可用余额: ${row.available_balance}`);
      });
    } else {
      console.log('❌ 警告: 同步后仍然没有找到 ordi 持有者数据');
      
      // 检查历史表中最新的 ordi 数据
      const latestOrdiHistoric = await query_db(`
        SELECT pkscript, wallet, overall_balance, available_balance, block_height
        FROM brc20_historic_balances
        WHERE tick = 'ordi' AND overall_balance::numeric > 0
        ORDER BY id DESC
        LIMIT 3
      `);
      
      if (latestOrdiHistoric.rows.length > 0) {
        console.log('✓ 历史表中最新的 ordi 记录:');
        latestOrdiHistoric.rows.forEach((row, index) => {
          console.log(`  ${index + 1}. 区块: ${row.block_height}, 余额: ${row.overall_balance}`);
        });
      }
    }
    console.log();

    // 5. 显示前10个 ticker
    console.log('5. 显示前10个活跃 ticker...');
    const topTickers = await query_db(`
      SELECT 
        tick, 
        COUNT(*) as holder_count,
        SUM(overall_balance::numeric) as total_supply
      FROM brc20_current_balances 
      WHERE overall_balance::numeric > 0
      GROUP BY tick 
      ORDER BY holder_count DESC 
      LIMIT 10
    `);
    
    if (topTickers.rows.length > 0) {
      console.log('✓ 前10个活跃 ticker:');
      topTickers.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.tick}: ${row.holder_count} 持有者, 总供应量: ${row.total_supply}`);
      });
    } else {
      console.log('❌ 没有找到任何活跃的 ticker');
    }

    console.log();
    console.log('🎉 清空并重新同步完成！');
    console.log('现在可以测试 API: curl "http://65.108.102.41:8180/v1/brc20/holders?ticker=ordi"');

  } catch (error) {
    console.error('❌ 操作过程中发生错误:', error);
    console.error('错误详情:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
  } finally {
    await db_pool.end();
    console.log('\n=== 脚本执行完成 ===');
  }
}

// 运行脚本
clearAndResync();
