<h1>Sat {{ self.sat.n() }}</h1>
<dl>
  <dt>decimal</dt><dd>{{ self.sat.decimal() }}</dd>
  <dt>degree</dt><dd>{{ self.sat.degree() }}</dd>
  <dt>percentile</dt><dd>{{ self.sat.percentile() }}</dd>
  <dt>name</dt><dd>{{ self.sat.name() }}</dd>
  <dt>cycle</dt><dd>{{ self.sat.cycle() }}</dd>
  <dt>epoch</dt><dd>{{ self.sat.epoch() }}</dd>
  <dt>period</dt><dd>{{ self.sat.period() }}</dd>
  <dt>block</dt><dd><a href=/block/{{self.sat.height()}}>{{ self.sat.height() }}</a></dd>
  <dt>offset</dt><dd>{{ self.sat.third() }}</dd>
  <dt>timestamp</dt><dd><time>{{self.blocktime.timestamp()}}</time>{{self.blocktime.suffix()}}</dd>
  <dt>rarity</dt><dd><span class={{self.sat.rarity()}}>{{ self.sat.rarity() }}</span></dd>
%% let charms = self.sat.charms();
%% if charms != 0 {
  <dt>charms</dt>
  <dd>
%% for charm in Charm::ALL {
%%   if charm.is_set(charms) {
    <span title={{charm}}>{{charm.icon()}}</span>
%%   }
%% }
  </dd>
%% }
%% if !self.inscriptions.is_empty() {
  <dt>inscriptions</dt>
  <dd class=thumbnails>
%% for inscription in &self.inscriptions {
    {{Iframe::thumbnail(*inscription)}}
%% }
  </dd>
%% }
%% if let Some(satpoint) = self.satpoint {
  <dt>location</dt><dd class=monospace>{{ satpoint }}</dd>
%% }
</dl>
<div class=center>
%% if self.sat.n() > 0 {
<a class=prev href=/sat/{{self.sat.n() - 1}}>prev</a>
%% } else {
prev
%% }
%% if self.sat < Sat::LAST {
<a class=next href=/sat/{{self.sat.n() + 1}}>next</a>
%% } else {
next
%% }
</div>
