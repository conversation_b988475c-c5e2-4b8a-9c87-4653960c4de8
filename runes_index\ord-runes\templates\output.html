<h1>Output <span class=monospace>{{self.outpoint}}</span></h1>
<dl>
%% if !self.inscriptions.is_empty() {
  <dt>inscriptions</dt>
  <dd class=thumbnails>
%% for inscription in &self.inscriptions {
    {{Iframe::thumbnail(*inscription)}}
%% }
  </dd>
%% }
%% if !self.runes.is_empty() {
  <dt>runes</dt>
  <dd>
    <table>
      <tr>
        <th>rune</th>
        <th>balance</th>
      </tr>
%% for (rune, balance) in &self.runes {
      <tr>
        <td><a href=/rune/{{ rune }}>{{ rune }}</a></td>
        <td>{{ balance }}</td>
      </tr>
%% }
    </table>
  </dd>
%% }
  <dt>value</dt><dd>{{ self.output.value }}</dd>
  <dt>script pubkey</dt><dd class=monospace>{{ self.output.script_pubkey.to_asm_string() }}</dd>
%% if let Ok(address) = self.chain.address_from_script(&self.output.script_pubkey ) {
  <dt>address</dt><dd class=monospace>{{ address }}</dd>
%% }
  <dt>transaction</dt><dd><a class=monospace href=/tx/{{ self.outpoint.txid }}>{{ self.outpoint.txid }}</a></dd>
  <dt>spent</dt><dd>{{ self.spent }}</dd>
</dl>
%% if let Some(sat_ranges) = &self.sat_ranges {
<h2>{{"Sat Range".tally(sat_ranges.len())}}</h2>
<ul class=monospace>
%% for (start, end) in sat_ranges {
%% if end - start == 1 {
  <li><a href=/sat/{{start}} class={{Sat(*start).rarity()}}>{{start}}</a></li>
%% } else {
  <li><a href=/range/{{start}}/{{end}} class={{Sat(*start).rarity()}}>{{start}}–{{end}}</a></li>
%% }
%% }
</ul>
%% }
