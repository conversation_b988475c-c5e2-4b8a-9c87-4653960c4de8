msgid ""
msgstr ""
"Project-Id-Version: Manual de la Teoria Ordinal\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: 2023-09-12 08:03-0600\n"
"Last-Translator: Zerone @0xZerone <<EMAIL>>\n"
"Language-Team: Spanish\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: src/SUMMARY.md:2
#: src/introduction.md:1
msgid "Introduction"
msgstr "Introducción"

#: src/SUMMARY.md:3
msgid "Overview"
msgstr "Descripción General"

#: src/SUMMARY.md:4
#: src/digital-artifacts.md:1
msgid "Digital Artifacts"
msgstr "Artefactos Digitales"

#: src/SUMMARY.md:5
#: src/SUMMARY.md:13
#: src/overview.md:221
#: src/inscriptions.md:1
msgid "Inscriptions"
msgstr "Inscripciones"

#: src/SUMMARY.md:6
#: src/inscriptions/provenance.md:1
msgid "Provenance"
msgstr "Procedencia"

#: src/SUMMARY.md:7
#: src/inscriptions/recursion.md:1
msgid "Recursion"
msgstr "Recursión"

#: src/SUMMARY.md:8
msgid "FAQ"
msgstr "Preguntas Frecuentes"

#: src/SUMMARY.md:9
msgid "Contributing"
msgstr "Contribuir"

#: src/SUMMARY.md:10
#: src/donate.md:1
msgid "Donate"
msgstr "Donaciones"

#: src/SUMMARY.md:11
msgid "Guides"
msgstr "Guías"

#: src/SUMMARY.md:12
msgid "Explorer"
msgstr "Explorador"

#: src/SUMMARY.md:14
#: src/guides/sat-hunting.md:1
msgid "Sat Hunting"
msgstr "Caza de Sats"

#: src/SUMMARY.md:15
#: src/guides/collecting.md:1
msgid "Collecting"
msgstr "Coleccionar"

#: src/SUMMARY.md:16
#: src/guides/sat-hunting.md:239
msgid "Sparrow Wallet"
msgstr "Monedero Sparrow"

#: src/SUMMARY.md:17
#: src/guides/testing.md:1
msgid "Testing"
msgstr "Pruebas"

#: src/SUMMARY.md:18
#: src/guides/moderation.md:1
msgid "Moderation"
msgstr "Moderación"

#: src/SUMMARY.md:19
#: src/guides/reindexing.md:1
msgid "Reindexing"
msgstr "Reindexación"

#: src/SUMMARY.md:20
msgid "Bounties"
msgstr "Recompensas"

#: src/SUMMARY.md:21
msgid "Bounty 0: 100,000 sats Claimed!"
msgstr "Recompensa Ordinal 0: 100,000 sats reclamados"

#: src/SUMMARY.md:22
msgid "Bounty 1: 200,000 sats Claimed!"
msgstr "Recompensa Ordinal 1: 200,000 sats reclamados"

#: src/SUMMARY.md:23
msgid "Bounty 2: 300,000 sats Claimed!"
msgstr "Recompensa Ordinal 2: 300,000 sats reclamados"

#: src/SUMMARY.md:24
msgid "Bounty 3: 400,000 sats"
msgstr "Recompensa Ordinal 3: 400,000 sats"

#: src/introduction.md:4
msgid ""
"This handbook is a guide to ordinal theory. Ordinal theory concerns itself "
"with satoshis, giving them individual identities and allowing them to be "
"tracked, transferred, and imbued with meaning."
msgstr ""
"Este manual sirve como una guía sobre la teoría ordinal. La teoría Ordinal "
"se encarga de proporcionar a cada Satoshi un identificador único, posibilitando "
"que cada uno pueda ser rastreado, transferido, e imbuido de significado. "

#: src/introduction.md:8
msgid ""
"Satoshis, not bitcoin, are the atomic, native currency of the Bitcoin "
"network. One bitcoin can be sub-divided into 100,000,000 satoshis, but no "
"further."
msgstr ""
"Los Satoshis y no el Bitcoin, son la divisa atómica y nativa de "
"la red de Bitcoin. Un Bitcoin se subdivide en exactamente 100,000,000 Satoshis, "
"ni más ni menos."

#: src/introduction.md:11
msgid ""
"Ordinal theory does not require a sidechain or token aside from Bitcoin, and "
"can be used without any changes to the Bitcoin network. It works right now."
msgstr ""
"La teoría Ordinal opera sin la necesidad de una cadena lateral o token "
"distinto a Bitcoin, y puede utilizarse sin ningún cambio en la red Bitcoin. "
"Funciona ahora mismo."

#: src/introduction.md:14
msgid ""
"Ordinal theory imbues satoshis with numismatic value, allowing them to be "
"collected and traded as curios."
msgstr ""
"La teoría Ordinal otorga a los Satoshis un valor numismático, permitiendo "
"que sean coleccionados e intercambiados como coleccionables."

#: src/introduction.md:17
msgid ""
"Individual satoshis can be inscribed with arbitrary content, creating unique "
"Bitcoin-native digital artifacts that can be held in Bitcoin wallets and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"En cada Satoshi se puede inscribir diferentes tipos de contenido, permitiendo "
"la creación de artefactos digitales únicos nativos en Bitcoin. Estos artefactos "
"digitales pueden almacenarse en monederos de Bitcoin y transferirse mediante "
"transacciones de Bitcoin. Las inscripciones son tan duraderas, inmutables, seguras "
"y descentralizadas como el propio Bitcoin."

#: src/introduction.md:22
msgid ""
"Other, more unusual use-cases are possible: off-chain colored-coins, public "
"key infrastructure with key rotation, a decentralized replacement for the "
"DNS. For now though, such use-cases are speculative, and exist only in the "
"minds of fringe ordinal theorists."
msgstr ""
"Otros casos de uso más inusuales son posibles: monedas por fuera de la cadena "
"y coloreadas (colored-coins), infraestructura de clave pública con rotación de clave, "
"un reemplazo descentralizado para el DNS. No obstante, en la actualidad, tales casos de uso "
"son especulativos y existen solo en las mentes de los teóricos de ordinals."

#: src/introduction.md:27
msgid "For more details on ordinal theory, see the [overview](overview.md)."
msgstr "Para obtener más detalles sobre la teoría Ordinal, consulte la [descripción general](overview.md)."

#: src/introduction.md:29
msgid "For more details on inscriptions, see [inscriptions](inscriptions.md)."
msgstr "Para obtener más detalles sobre las inscripciones, ver [inscripciones](inscriptions.md)."

#: src/introduction.md:31
msgid ""
"When you're ready to get your hands dirty, a good place to start is with "
"[inscriptions](guides/inscriptions.md), a curious species of digital "
"artifact enabled by ordinal theory."
msgstr ""
"Cuando estés listo para ponerte manos a la obra, un buen lugar para "
"comenzar es en la página de [inscripciones](guides/inscriptions.md), un tipo de "
"artefacto digital posibilitado por la teoría ordinal."

#: src/introduction.md:35
msgid "Links"
msgstr "Enlaces"

#: src/introduction.md:38
msgid "[GitHub](https://github.com/ordinals/ord/)"
msgstr "[GitHub](https://github.com/ordinals/ord/)"

#: src/introduction.md:39
msgid "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src/introduction.md:40
msgid "[Discord](https://discord.gg/ordinals)"
msgstr "[Discord](https://discord.gg/ordinals)"

#: src/introduction.md:41
msgid "[Open Ordinals Institute Website](https://ordinals.org/)"
msgstr "[Sitio Web del Instituto Open Ordinals](https://ordinals.org/)"

#: src/introduction.md:42
msgid "[Open Ordinals Institute X](https://x.com/ordinalsorg)"
msgstr "[X del Instituto Open Ordinals](https://x.com/ordinalsorg)"

#: src/introduction.md:43
msgid "[Mainnet Block Explorer](https://ordinals.com)"
msgstr "[Explorador de Bloques en Mainnet](https://ordinals.com)"

#: src/introduction.md:44
msgid "[Signet Block Explorer](https://signet.ordinals.com)"
msgstr "[Explorador de Bloques en Signet](https://signet.ordinals.com)"

#: src/introduction.md:46
msgid "Videos"
msgstr "Videos"

#: src/introduction.md:49
msgid ""
"[Ordinal Theory Explained: Satoshi Serial Numbers and NFTs on "
"Bitcoin](https://www.youtube.com/watch?v=rSS0O2KQpsI)"
msgstr ""
"[Explicación de la teoría Ordinal: Números de serie de Satoshis "
"y NFTs en Bitcoin](https://www.youtube.com/watch?v=rSS0O2KQpsI)"

#: src/introduction.md:50
msgid ""
"[Ordinals Workshop with "
"Rodarmor](https://www.youtube.com/watch?v=MC_haVa6N3I)"
msgstr "[Taller de Ordinals con "
"Rodarmor](https://www.youtube.com/watch?v=MC_haVa6N3I&ab_channel=PlebLab)"

#: src/introduction.md:51
msgid ""
"[Ordinal Art: Mint Your own NFTs on Bitcoin w/ "
"@rodarmor](https://www.youtube.com/watch?v=j5V33kV3iqo)"
msgstr ""
"[Arte Ordinal: Crea tus propios NFTs en Bitcoin con "
"@rodarmor](https://www.youtube.com/watch?v=j5V33kV3iqo)"

#: src/overview.md:1
msgid "Ordinal Theory Overview"
msgstr "Descripción General de la Teoría Ordinal"

#: src/overview.md:4
msgid ""
"Ordinals are a numbering scheme for satoshis that allows tracking and "
"transferring individual sats. These numbers are called [ordinal "
"numbers](https://ordinals.com). Satoshis are numbered in the order in which "
"they're mined, and transferred from transaction inputs to transaction "
"outputs first-in-first-out. Both the numbering scheme and the transfer "
"scheme rely on _order_, the numbering scheme on the _order_ in which "
"satoshis are mined, and the transfer scheme on the _order_ of transaction "
"inputs and outputs. Thus the name, _ordinals_."
msgstr ""
"Los Ordinals son un sistema de numeración para los satoshis con el "
"cual podemos hacerles seguimiento y transferirlos de manera individual. "
"Dichos números se denominan [números ordinales](https://ordinals.com/). "
"A cada Satoshi se le otorga un número de serie que se establece según el orden "
"en el que fueron minados y se transfieren basándose en la secuencia de las "
"transacciones entrantes y salientes, siguiendo el proceso "
"FIFO (First In, First Out). Tanto el esquema de numeración como el de transferencia "
"se basan en el _orden secuencial_; el esquema de numeración en el _orden_ en que se minan "
"los satoshis, mientras que el esquema de transferencia se basa en el _orden_ "
"de las entradas y salidas de las transacciones. De allí se deriva el nombre, _ordinals_."

#: src/overview.md:13
msgid ""
"Technical details are available in [the "
"BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)."
msgstr ""
"Los detalles técnicos están disponibles "
"en [el BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)."

#: src/overview.md:16
msgid ""
"Ordinal theory does not require a separate token, another blockchain, or any "
"changes to Bitcoin. It works right now."
msgstr "La teoría Ordinal funciona en este momento sin hacer cambios a Bitcoin "
"y no requiere de un token aparte u otra blockchain."

#: src/overview.md:19
msgid "Ordinal numbers have a few different representations:"
msgstr "Los números Ordinales tienen varias representaciones:"

#: src/overview.md:21
msgid ""
"_Integer notation_: "
"[`2099994106992659`](https://ordinals.com/sat/2099994106992659) The ordinal "
"number, assigned according to the order in which the satoshi was mined."
msgstr "_Notación entera_: "
"[`2099994106992659`](https://ordinals.com/sat/2099994106992659). El número"
"ordinal, asignado según el orden en que se minó el Satoshi"

#: src/overview.md:26
msgid ""
"_Decimal notation_: "
"[`3891094.16797`](https://ordinals.com/sat/3891094.16797) The first number "
"is the block height in which the satoshi was mined, the second the offset of "
"the satoshi within the block."
msgstr ""
"_Notación decimal_: "
"[` 3891094.16797`](https://ordinals.com/sat/3891094.16797) El primer número "
"es la altura del bloque en el cual se minó el Satoshi, el segundo es el desplazamiento "
"del Satoshi dentro del bloque."

#: src/overview.md:31
msgid ""
"_Degree notation_: "
"[`3°111094′214″16797‴`](https://ordinals.com/sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). "
"We'll get to that in a moment."
msgstr ""
"_Notación sexagesimal_: "
"[`3°111094′214″16797‴`](https://ordinals.com/sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). "
"Hablaremos de eso más adelante."


#: src/overview.md:35
msgid ""
"_Percentile notation_: "
"[`99.99************%`](https://ordinals.com/sat/99.99************%25) . The "
"satoshi's position in Bitcoin's supply, expressed as a percentage."
msgstr ""
"_Notación porcentual_: "
"[`99.99************%`](https://ordinals.com/sat/99.99************%25) . La "
"posición del Satoshi en el suministro de Bitcoin, expresado como un porcentaje."

#: src/overview.md:39
msgid ""
"_Name_: [`satoshi`](https://ordinals.com/sat/satoshi). An encoding of the "
"ordinal number using the characters `a` through `z`."
msgstr ""
"_Nombre_: "
"[`Satoshi`](https://ordinals.com/sat/satoshi). Una codificación del "
"número ordinal utilizando los caracteres de la `a` a la `z`."

#: src/overview.md:42
msgid ""
"Arbitrary assets, such as NFTs, security tokens, accounts, or stablecoins "
"can be attached to satoshis using ordinal numbers as stable identifiers."
msgstr ""
"Activos arbitrarios como los NFTs, tokens de seguridad, cuentas o stablecoins "
"se pude adjuntar a los satoshis usando números ordinales como identificadores."

#: src/overview.md:45
msgid ""
"Ordinals is an open-source project, developed [on "
"GitHub](https://github.com/ordinals/ord). The project consists of a BIP "
"describing the ordinal scheme, an index that communicates with a Bitcoin "
"Core node to track the location of all satoshis, a wallet that allows making "
"ordinal-aware transactions, a block explorer for interactive exploration of "
"the blockchain, functionality for inscribing satoshis with digital "
"artifacts, and this manual."
msgstr ""
"Ordinals es un proyecto de código abierto, desarrollado "
"[en GitHub](https://github.com/ordinals/ord). El proyecto consta de un BIP "
"que describe el esquema de Ordinals, un índice que se comunica con un nodo de Bitcoin "
"Core para rastrear la ubicación de todos los satoshis, un monedero que permite "
"realizar transacciones de Ordinals, un explorador de bloques para la exploración interactiva "
"de la blockchain, funcionalidad para inscribir artefactos digitales "
"en los satoshis y este manual."

#: src/overview.md:52
msgid "Rarity"
msgstr "Rareza"

#: src/overview.md:55
msgid ""
"Humans are collectors, and since satoshis can now be tracked and "
"transferred, people will naturally want to collect them. Ordinal theorists "
"can decide for themselves which sats are rare and desirable, but there are "
"some hints…"
msgstr ""
"Los humanos son coleccionistas, y ahora que los satoshis se pueden "
"rastrear y transferir, las personas querrán coleccionarlos. Los teóricos de "
"Ordinals pueden decidir por sí mismos cuáles sats son raros y deseables, pero "
"aquí hay algunas pistas..."

#: src/overview.md:59
msgid ""
"Bitcoin has periodic events, some frequent, some more uncommon, and these "
"naturally lend themselves to a system of rarity. These periodic events are:"
msgstr ""
"Bitcoin tiene eventos periódicos, algunos frecuentes y algunos menos comunes, y "
"estos se prestan para obtener un sistema de rareza. Estos eventos periódicos son:"

#: src/overview.md:62
msgid ""
"_Blocks_: A new block is mined approximately every 10 minutes, from now "
"until the end of time."
msgstr ""
"_Bloques_: Se mina un bloque nuevo aproximadamente cada 10 minutos, desde "
"ahora hasta el fin de los tiempos."

#: src/overview.md:65
msgid ""
"_Difficulty adjustments_: Every 2016 blocks, or approximately every two "
"weeks, the Bitcoin network responds to changes in hashrate by adjusting the "
"difficulty target which blocks must meet in order to be accepted."
msgstr ""
"_Ajustes de dificultad_: Cada 2016 bloques, o aproximadamente cada dos "
"semanas, la red de Bitcoin responde a cambios en la tasa de hash ajustando "
"la dificultad que los bloques deben cumplir para ser aceptados."

#: src/overview.md:69
msgid ""
"_Halvings_: Every 210,000 blocks, or roughly every four years, the amount of "
"new sats created in every block is cut in half."
msgstr ""
"_Halvings_: Cada 210,000 bloques, o aproximadamente cada cuatro años, la cantidad "
"de nuevos Satoshis creados en cada bloque se reduce a la mitad."

#: src/overview.md:72
msgid ""
"_Cycles_: Every six halvings, something magical happens: the halving and the "
"difficulty adjustment coincide. This is called a conjunction, and the time "
"period between conjunctions a cycle. A conjunction occurs roughly every 24 "
"years. The first conjunction should happen sometime in 2032."
msgstr ""
"_Ciclos_: Cada seis halvings, sucede algo mágico: el halving y el ajuste de "
"dificultad suceden al mismo tiempo. Esto se llama conjunción, y el período "
"de tiempo entre conjunciones es un ciclo. Una conjunción ocurre aproximadamente "
"cada 24 años. La primera conjunción debería ocurrir en el 2032."

#: src/overview.md:77
msgid "This gives us the following rarity levels:"
msgstr "Esto nos conlleva a los siguientes niveles de rareza:"

#: src/overview.md:79
msgid "`common`: Any sat that is not the first sat of its block"
msgstr "`común`: Cualquier sat que no es el primero en su bloque"

#: src/overview.md:80
msgid "`uncommon`: The first sat of each block"
msgstr "`poco común`: El primer sat de cada bloque"

#: src/overview.md:81
msgid "`rare`: The first sat of each difficulty adjustment period"
msgstr "`raro`: El primer sat en un periodo de ajuste de dificultad"

#: src/overview.md:82
msgid "`epic`: The first sat of each halving epoch"
msgstr "`épico`: El primer satoshi después de un halving"

#: src/overview.md:83
msgid "`legendary`: The first sat of each cycle"
msgstr "`legendario`: El primer Satoshi de cada ciclo"

#: src/overview.md:84
msgid "`mythic`: The first sat of the genesis block"
msgstr "`mítico`: El primer Sat del bloque génesis "

#: src/overview.md:86
msgid ""
"Which brings us to degree notation, which unambiguously represents an "
"ordinal number in a way that makes the rarity of a satoshi easy to see at a "
"glance:"
msgstr ""
"Lo cual nos trae a la notación sexagesimal esta representa un numero ordinal "
"de tal manera que nos facilita ver la rareza de un satoshi. "

#: src/overview.md:89
msgid ""
"```\n"
"A°B′C″D‴\n"
"│ │ │ ╰─ Index of sat in the block\n"
"│ │ ╰─── Index of block in difficulty adjustment period\n"
"│ ╰───── Index of block in halving epoch\n"
"╰─────── Cycle, numbered starting from 0\n"
"```"
msgstr ""
"```\n"
"A°B′C″D‴\n"
"│ │ │ ╰─ Índice del Satoshi dentro del bloque\n"
"│ │ ╰─── Índice del bloque en el periodo de ajuste de dificultad\n"
"│ ╰───── Índice del bloque en la época de halving\n"
"╰─────── Ciclo, numerados comenzando desde 0\n"
"```"

#: src/overview.md:97
msgid ""
"Ordinal theorists often use the terms \"hour\", \"minute\", \"second\", and "
"\"third\" for _A_, _B_, _C_, and _D_, respectively."
msgstr ""
"Los teóricos de Ordinals suelen usar los términos \"hora\", \"minuto\", \"segundo\" y "
"\"tercero\" en referencia a _A_, _B_, _C_, y _D_."

#: src/overview.md:100
msgid "Now for some examples. This satoshi is common:"
msgstr "Por ejemplo, este Satoshi es común:"

#: src/overview.md:102
msgid ""
"```\n"
"1°1′1″1‴\n"
"│ │ │ ╰─ Not first sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── Not first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″1‴\n"
"│ │ │ ╰─ No es el primer sat del bloque\n"
"│ │ ╰─── No es el primer bloque en el periodo de ajuste de dificultad\n"
"│ ╰───── No es el primer bloque en la época de halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:111
msgid "This satoshi is uncommon:"
msgstr "Este Satoshi es poco común:"

#: src/overview.md:113
msgid ""
"```\n"
"1°1′1″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── Not first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″0‴\n"
"│ │ │ ╰─ Primer sat del bloque\n"
"│ │ ╰─── No es el primer bloque en el periodo de ajuste de dificultad\n"
"│ ╰───── No es el primer bloque en un periodo de halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:121
msgid "This satoshi is rare:"
msgstr "Este Satoshi es raro:"

#: src/overview.md:123
msgid ""
"```\n"
"1°1′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── Not the first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′0″0‴\n"
"│ │ │ ╰─ Primer sat del bloque\n"
"│ │ ╰─── Primer bloque en el periodo de ajuste de dificultad\n"
"│ ╰───── No es el primer bloque en un periodo de halving\n"
"╰───────Segundo ciclo\n"
"```"

#: src/overview.md:131
msgid "This satoshi is epic:"
msgstr "Este Satoshi es épico:"

#: src/overview.md:133
msgid ""
"```\n"
"1°0′1″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°0′1″0‴\n"
"│ │ │ ╰─ Primer sat del bloque\n"
"│ │ ╰─── No es el primer bloque en el periodo de ajuste de dificultad\n"
"│ ╰───── Primer bloque en la época de halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:141
msgid "This satoshi is legendary:"
msgstr "Este Satoshi es legendario:"

#: src/overview.md:143
msgid ""
"```\n"
"1°0′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°0′0″0‴\n"
"│ │ │ ╰─ Primer sat del bloque\n"
"│ │ ╰─── Primer bloque en el periodo de ajuste de dificultad\n"
"│ ╰───── Primer bloque en la época de halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:151
msgid "And this satoshi is mythic:"
msgstr "Este Satoshi es mítico:"

#: src/overview.md:153
msgid ""
"```\n"
"0°0′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── First cycle\n"
"```"
msgstr ""
"```\n"
"0°0′0″0‴\n"
"│ │ │ ╰─ Primer sat del bloque\n"
"│ │ ╰─── Primer bloque en el periodo de ajuste de dificultad\n"
"│ ╰───── Primer bloque en la época de halving\n"
"╰─────── Primer ciclo\n"
"```"


#: src/overview.md:161
msgid ""
"If the block offset is zero, it may be omitted. This is the uncommon satoshi "
"from above:"
msgstr ""
"Si el Satoshi es el primero en el bloque, el cero puede ser omitido. "
"Este es el ejemplo del Satoshi poco común que explicamos previamente:"

#: src/overview.md:164
msgid ""
"```\n"
"1°1′1″\n"
"│ │ ╰─ Not first block in difficulty adjustment period\n"
"│ ╰─── Not first block in halving epoch\n"
"╰───── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″\n"
"│ │ ╰─── No es el primer bloque en el periodo de ajuste de dificultad\n"
"│ ╰───── No es el primer bloque en un periodo de halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:171
msgid "Rare Satoshi Supply"
msgstr "Suministro de Satoshi Raros"

#: src/overview.md:174
msgid "Total Supply"
msgstr "Suministro Total"

#: src/overview.md:176
msgid "`common`: 2.1 quadrillion"
msgstr "`común`: 2.1 mil billones"

#: src/overview.md:177
msgid "`uncommon`: 6,929,999"
msgstr "`poco común`: 6,929,999"

#: src/overview.md:178
msgid "`rare`: 3437"
msgstr "`raro`: 3437"

#: src/overview.md:179
msgid "`epic`: 32"
msgstr "`épico`: 32"

#: src/overview.md:180
msgid "`legendary`: 5"
msgstr "`legendario`: 5"

#: src/overview.md:181
#: src/overview.md:190
msgid "`mythic`: 1"
msgstr "`mítico`: 1"

#: src/overview.md:183
msgid "Current Supply"
msgstr "Suministro actual:"

#: src/overview.md:185
msgid "`common`: 1.9 quadrillion"
msgstr "`común`: 1.9 mil billones"

#: src/overview.md:186
msgid "`uncommon`: 745,855"
msgstr "`poco común`: 745,855"

#: src/overview.md:187
msgid "`rare`: 369"
msgstr "`raro`: 369"

#: src/overview.md:188
msgid "`epic`: 3"
msgstr "`épico`: 3"

#: src/overview.md:189
msgid "`legendary`: 0"
msgstr "`legendario`: 0"

#: src/overview.md:192
msgid ""
"At the moment, even uncommon satoshis are quite rare. As of this writing, "
"745,855 uncommon satoshis have been mined - one per 25.6 bitcoin in "
"circulation."
msgstr ""
"Por el momento, incluso los Satoshis poco comunes son bastante escasos. "
"Hasta la fecha de este escrito, se han minado 745,855 Satoshis poco comunes - "
"uno por cada 25.6 bitcoin en circulación."

#: src/overview.md:196
msgid "Names"
msgstr "Nombres "

#: src/overview.md:199
msgid ""
"Each satoshi has a name, consisting of the letters _A_ through _Z_, that get "
"shorter the further into the future the satoshi was mined. They could start "
"short and get longer, but then all the good, short names would be trapped in "
"the unspendable genesis block."
msgstr ""
"Cada Satoshi tiene un nombre compuesto por las letras _A_ hasta la _Z_. "
"Este nombre se va haciendo más corto cuanto más lejos en el futuro se mine el "
"Satoshi. Podrían comenzar con nombres cortos e irse alargando, pero esto causaría "
"que todos los nombres cortos quedaran atrapados en el bloque génesis el cual "
"no se puede gastar."

#: src/overview.md:204
msgid ""
"As an example, 1905530482684727°'s name is \"iaiufjszmoba\". The name of the "
"last satoshi to be mined is \"a\". Every combination of 10 characters or "
"less is out there, or will be out there, someday."
msgstr ""
"Por ejemplo, el nombre de 1905530482684727°' es \"iaiufjszmoba\". El nombre "
"del último satoshi que será minado es \"a\". Cada combinación de 10 caracteres "
"o menos ya existe, o existirá, algún día."

#: src/overview.md:208
msgid "Exotics"
msgstr "Exóticos"

#: src/overview.md:211
msgid ""
"Satoshis may be prized for reasons other than their name or rarity. This "
"might be due to a quality of the number itself, like having an integer "
"square or cube root. Or it might be due to a connection to a historical "
"event, such as satoshis from block 477,120, the block in which SegWit "
"activated, or 2099999997689999°, the last satoshi that will ever be mined."
msgstr ""
"Los satoshis pueden ser valorados por otras características distintas a su "
"nombre o rareza. Esto podría ser alguna cualidad intrínseca con el número, como "
"tener una raíz cuadrada o cúbica. También podría ser debido a una conexión con "
"un evento histórico, tales como los Satoshis del bloque 477,120, el bloque "
"en el que se activó SegWit, o 2099999997689999°, el último satoshi que será minado."

#: src/overview.md:217
msgid ""
"Such satoshis are termed \"exotic\". Which satoshis are exotic and what "
"makes them so is subjective. Ordinal theorists are encouraged to seek out "
"exotics based on criteria of their own devising."
msgstr "A estos satoshis se les denomina \"exóticos\". Cuales Satoshis son "
"exóticos y lo que los convierte en ello es subjetivo. A los teóricos de "
"Ordinals se les incentiva a buscar sats exóticos basados en sus propios "
"criterios."

#: src/overview.md:224
msgid ""
"Satoshis can be inscribed with arbitrary content, creating Bitcoin-native "
"digital artifacts. Inscribing is done by sending the satoshi to be inscribed "
"in a transaction that reveals the inscription content on-chain. This content "
"is then inextricably linked to that satoshi, turning it into an immutable "
"digital artifact that can be tracked, transferred, hoarded, bought, sold, "
"lost, and rediscovered."
msgstr ""
"Los satoshis pueden ser inscritos con contenido arbitrario, de este modo "
"creando artefactos digitales nativos en Bitcoin. La inscripción se realiza "
"enviando el satoshi que se desea inscribir en una transacción que revela "
"el contenido adjuntado a la inscripción en la cadena. Este contenido queda "
"inextricablemente vinculado a ese satoshi, convirtiéndolo en un artefacto "
"digital inmutable que puede ser rastreado, transferido, guardado, comprado, "
"vendido, perdido y redescubierto."

#: src/overview.md:231
msgid "Archaeology"
msgstr "Arqueología"

#: src/overview.md:234
msgid ""
"A lively community of archaeologists devoted to cataloging and collecting "
"early NFTs has sprung up. [Here's a great summary of historical NFTs by "
"Chainleft.](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)"
msgstr ""
"Ha surgido una gran comunidad de arqueólogos que se dedican a catalogar y "
"coleccionar los primeros NFTs. [Aquí hay un gran resumen de los NFTs históricos "
"por Chainleft](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)."

#: src/overview.md:238
msgid ""
"A commonly accepted cut-off for early NFTs is March 19th, 2018, the date the "
"first ERC-721 contract, [SU SQUARES](https://tenthousandsu.com/), was "
"deployed on Ethereum."
msgstr ""
"El 19 de marzo del 2018, por lo general es la fecha límite para referirnos a "
"los primeros NFT debido a que este día se implementó en Ethereum el primer "
"contrato ERC-721, [SU SQUARES](https://tenthousandsu.com/)."

#: src/overview.md:242
msgid ""
"Whether or not ordinals are of interest to NFT archaeologists is an open "
"question! In one sense, ordinals were created in early 2022, when the "
"Ordinals specification was finalized. In this sense, they are not of "
"historical interest."
msgstr ""
"¡Si los Ordinals son de interés o no para los arqueólogos de NFT siempre "
"será  una pregunta abierta! Los ordinals fueron creados a principios del "
"2022, cuando se finalizaron las especificaciones de Ordinals. En este sentido, "
"no son de interés histórico."

#: src/overview.md:247
msgid ""
"In another sense though, ordinals were in fact created by Satoshi Nakamoto "
"in 2009 when he mined the Bitcoin genesis block. In this sense, ordinals, "
"and especially early ordinals, are certainly of historical interest."
msgstr ""
"Pero si miramos desde otra perspectiva, los ordinals fueron creados por "
"Satoshi Nakamoto en el 2009 cuando minó el bloque génesis de Bitcoin. "
"Desde este punto de vista, los ordinals, y especialmente los primeros ordinals, "
"ciertamente son de interés histórico."

#: src/overview.md:251
msgid ""
"Many ordinal theorists favor the latter view. This is not least because the "
"ordinals were independently discovered on at least two separate occasions, "
"long before the era of modern NFTs began."
msgstr ""
"Muchos teóricos de ordinals favorecen este concepto. Esto se debe en parte "
"a que los ordinals fueron descubiertos en otras dos ocasiones, mucho antes "
"de que comenzara la era de los NFTs modernos."

#: src/overview.md:255
msgid ""
"On August 21st, 2012, Charlie Lee [posted a proposal to add proof-of-stake "
"to Bitcoin to the Bitcoin Talk "
"forum](https://bitcointalk.org/index.php?topic=102355.0). This wasn't an "
"asset scheme, but did use the ordinal algorithm, and was implemented but "
"never deployed."
msgstr ""
"El 21 de agosto de 2012, Charlie Lee [publicó una propuesta en el foro de "
"Bitcoin Talk para añadir el Proof of Stake (PoS) o prueba de participación "
"a Bitcoin](https://bitcointalk.org/index.php?topic=102355.0). Esto no era "
"un esquema de activos, pero sí utilizaba el algoritmo de ordinals, y fue "
"implementado, pero nunca se utilizó."

#: src/overview.md:261
msgid ""
"On October 8th, 2012, jl2012 [posted a scheme to the same "
"forum](https://bitcointalk.org/index.php?topic=117224.0) which uses decimal "
"notation and has all the important properties of ordinals. The scheme was "
"discussed but never implemented."
msgstr ""
"El 8 de octubre de 2012, jl2012 [publicó una propuesta en el mismo "
"foro](https://bitcointalk.org/index.php?topic=117224.0) que utiliza notación "
"decimal y tiene todas las propiedades importantes de los ordinals. "
"Se discutió el esquema, pero nunca fue utilizado."

#: src/overview.md:266
msgid ""
"These independent inventions of ordinals indicate in some way that ordinals "
"were discovered, or rediscovered, and not invented. The ordinals are an "
"inevitability of the mathematics of Bitcoin, stemming not from their modern "
"documentation, but from their ancient genesis. They are the culmination of a "
"sequence of events set in motion with the mining of the first block, so many "
"years ago."
msgstr ""
"Estos inventos que son independientes a los ordinals indican de alguna manera "
"que los ordinals fueron descubiertos, o redescubiertos, y no inventados. Los "
"ordinals son algo inevitable que nace debido a la logística matemática de "
"Bitcoin, sus raíces no provienen de su documentación moderna, sino de su "
"génesis. Son la culminación de una secuencia de eventos que se han ido "
"desarrollando a través de los años y comenzó cuando se minó el primer bloque."

#: src/digital-artifacts.md:4
msgid ""
"Imagine a physical artifact. A rare coin, say, held safe for untold years in "
"the dark, secret clutch of a Viking hoard, now dug from the earth by your "
"grasping hands. It…"
msgstr ""
"Imagina un artefacto físico. Digamos una moneda rara, guardada de forma segura "
"durante muchísimos años en el oscuro y secreto escondite de un tesoro vikingo, "
"ahora desenterrado por tus propias manos. Esta moneda..."

#: src/digital-artifacts.md:8
msgid ""
"…has an owner. You. As long as you keep it safe, nobody can take it from you."
msgstr ""
"...tiene un dueño. Tú. Mientras la mantengas a salvo, nadie puede quitártela."

#: src/digital-artifacts.md:10
msgid "…is complete. It has no missing parts."
msgstr "...está completa. No le falta ninguna pieza."

#: src/digital-artifacts.md:12
msgid ""
"…can only be changed by you. If you were a trader, and you made your way to "
"18th century China, none but you could stamp it with your chop-mark."
msgstr ""
"...solo puede ser modificada por ti. Si fueras comerciante y llegaras a la "
"China del siglo XVIII, solo tu podrías marcarla con tu sello personal."

#: src/digital-artifacts.md:15
msgid ""
"…can only be disposed of by you. The sale, trade, or gift is yours to make, "
"to whomever you wish."
msgstr ""
"...solo puede ser desechada por ti. La decisión de vender, intercambiar o "
"regalarla es tuya, y a quien tú desees."

#: src/digital-artifacts.md:18
msgid ""
"What are digital artifacts? Simply put, they are the digital equivalent of "
"physical artifacts."
msgstr ""
"¿Qué son los artefactos digitales? Pues simplemente, son el equivalente digital "
"de los artefactos físicos."

#: src/digital-artifacts.md:21
msgid ""
"For a digital thing to be a digital artifact, it must be like that coin of "
"yours:"
msgstr ""
"Para que algo digital sea un artefacto digital, debe ser como esa moneda tuya:"

#: src/digital-artifacts.md:24
msgid ""
"Digital artifacts can have owners. A number is not a digital artifact, "
"because nobody can own it."
msgstr ""
"Los artefactos digitales pueden tener dueño. Un número no es un artefacto "
"digital, porque nadie puede ser dueño de él."

#: src/digital-artifacts.md:27
msgid ""
"Digital artifacts are complete. An NFT that points to off-chain content on "
"IPFS or Arweave is incomplete, and thus not a digital artifact."
msgstr ""
"Los artefactos digitales están completos. Un NFT que apunta a contenido fuera "
"de cadena alojado en IPFS o Arweave está incompleto, y por lo tanto no es un "
"artefacto digital."

#: src/digital-artifacts.md:30
msgid ""
"Digital artifacts are permissionless. An NFT which cannot be sold without "
"paying a royalty is not permissionless, and thus not a digital artifact."
msgstr ""
"Los artefactos digitales son sin permiso. Un NFT que no pueda ser vendido "
"sin tener que pagar regalías no es sin permisos, y por lo tanto no es un "
"artefacto digital."

#: src/digital-artifacts.md:33
msgid ""
"Digital artifacts are uncensorable. Perhaps you can change a database entry "
"on a centralized ledger today, but maybe not tomorrow, and thus one cannot "
"be a digital artifact."
msgstr ""
"Los artefactos digitales no pueden ser censurados. Puede que hoy en día se "
"permita cambiar la información en una base de datos centralizada, pero tal "
"vez mañana no sea posible, por lo tanto, no puede ser un artefacto digital."

#: src/digital-artifacts.md:37
msgid ""
"Digital artifacts are immutable. An NFT with an upgrade key is not a digital "
"artifact."
msgstr ""
"Los artefactos digitales son inmutables. Un NFT con una llave de actualización "
"o dinámico no es un artefacto digital."

#: src/digital-artifacts.md:40
msgid ""
"The definition of a digital artifact is intended to reflect what NFTs "
"_should_ be, sometimes are, and what inscriptions _always_ are, by their "
"very nature."
msgstr ""
"La definición de un artefacto digital tiene la intención de reflejar lo "
"que deberían ser los NFTs, y lo que _siempre_ serán las inscripciones debido "
"a su naturaleza."

#: src/inscriptions.md:4
msgid ""
"Inscriptions inscribe sats with arbitrary content, creating bitcoin-native "
"digital artifacts, more commonly known as NFTs. Inscriptions do not require "
"a sidechain or separate token."
msgstr ""
"En una inscripción inscribimos contenido arbitrario en un sat, con este proceso "
"creamos artefactos digitales nativos de Bitcoin, comúnmente conocidos como NFTs. "
"Las inscripciones no requieren una cadena lateral ni un token aparte."

#: src/inscriptions.md:8
msgid ""
"These inscribed sats can then be transferred using bitcoin transactions, "
"sent to bitcoin addresses, and held in bitcoin UTXOs. These transactions, "
"addresses, and UTXOs are normal bitcoin transactions, addresses, and UTXOS "
"in all respects, with the exception that in order to send individual sats, "
"transactions must control the order and value of inputs and outputs "
"according to ordinal theory."
msgstr ""
"Los sats que se han inscrito pueden ser transferidos en una transacción "
"de bitcoin, ser enviados a direcciones Bitcoin y ser contenidos en "
"UTXOs (transacción de salida no gastada) de Bitcoin. Todos estos procesos se "
"llevan a cabo como se han hecho normalmente en Bitcoin, con la excepción de "
"que, para enviar cada sat, las transacciones deben controlar el orden y el valor "
"de las entradas y salidas según la teoría Ordinal."

#: src/inscriptions.md:15
msgid ""
"The inscription content model is that of the web. An inscription consists of "
"a content type, also known as a MIME type, and the content itself, which is "
"a byte string. This allows inscription content to be returned from a web "
"server, and for creating HTML inscriptions that use and remix the content of "
"other inscriptions."
msgstr ""
"El modelo de contenido de las inscripciones funciona similar al de la web."
"Una inscripción está conformada por el tipo de contenido, conocido como el "
"tipo MIME, y el contenido que es una cadena de bytes. Esto permite que el "
"contenido de la inscripción se pueda obtener de un servidor web y tener la "
"posibilidad de crear inscripciones HTML que usen el contenido de otras "
"inscripciones."

#: src/inscriptions.md:21
msgid ""
"Inscription content is entirely on-chain, stored in taproot script-path "
"spend scripts. Taproot scripts have very few restrictions on their content, "
"and additionally receive the witness discount, making inscription content "
"storage relatively economical."
msgstr ""
"El contenido de la inscripción está completamente en la cadena de bloques "
"o blockchain, almacenado en scripts de taproot. Los scripts de taproot tienen "
"muy pocas restricciones en cuanto a lo que pueden contener, y además reciben "
"el descuento de testigo, lo que hace que el almacenamiento de contenido de las "
"inscripciones sea relativamente económico."

#: src/inscriptions.md:26
msgid ""
"Since taproot script spends can only be made from existing taproot outputs, "
"inscriptions are made using a two-phase commit/reveal procedure. First, in "
"the commit transaction, a taproot output committing to a script containing "
"the inscription content is created. Second, in the reveal transaction, the "
"output created by the commit transaction is spent, revealing the inscription "
"content on-chain."
msgstr ""
"Dado que los gastos de script de taproot (taproot script spends) sólo pueden "
"hacerse desde salidas de taproot existentes, las inscripciones se hacen en dos "
"fases de compromiso/revelación. Primero, en la transacción de compromiso, se "
"crea una salida de taproot que se compromete a un script que contiene el "
"contenido de inscripción. Segundo, en la transacción de revelación, la salida "
"creada por la transacción de compromiso se gasta, revelando el contenido de la "
"inscripción en la cadena."

#: src/inscriptions.md:33
msgid ""
"Inscription content is serialized using data pushes within unexecuted "
"conditionals, called \"envelopes\". Envelopes consist of an `OP_FALSE OP_IF "
"… OP_ENDIF` wrapping any number of data pushes. Because envelopes are "
"effectively no-ops, they do not change the semantics of the script in which "
"they are included, and can be combined with any other locking script."
msgstr ""
"El contenido de la inscripción se serializa utilizando push de datos "
"dentro de condicionales que no han sido ejecutados, a estos se les llama \"sobres\". "
"Los sobres consisten en un `OP_FALSE OP_IF ... OP_ENDIF` envolviendo los push de datos. "
"Dado que los sobres son operaciones nulas, no cambian la semántica del script "
"en el que están incluidos y pueden combinarse con cualquier otro script de "
"bloqueo."

#: src/inscriptions.md:39
msgid ""
"A text inscription containing the string \"Hello, world!\" is serialized as "
"follows:"
msgstr ""
"Una inscripción de texto que contiene la cadena \"¡Hola, Mundo!\" se serializa "
"de la siguiente manera:"

#: src/inscriptions.md:42
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"¡Hola, Mundo!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions.md:53
msgid ""
"First the string `ord` is pushed, to disambiguate inscriptions from other "
"uses of envelopes."
msgstr ""
"Primero, se hace un push con el string `ord` para diferenciar que sobre va "
"a utilizar la inscripción."

#: src/inscriptions.md:56
msgid ""
"`OP_PUSH 1` indicates that the next push contains the content type, and "
"`OP_PUSH 0`indicates that subsequent data pushes contain the content itself. "
"Multiple data pushes must be used for large inscriptions, as one of "
"taproot's few restrictions is that individual data pushes may not be larger "
"than 520 bytes."
msgstr ""
"`OP_PUSH 1` indica que el próximo push es el tipo de contenido y `OP_PUSH 0` "
"indica que los siguientes datos en el push contienen el contenido que se va a "
"anexar. Múltiples push de datos deben ser utilizados para inscripciones de gran "
"tamaño ya que una de las pocas restricciones de Taproot es que un push de datos "
"no puede ser mayor a 520 bytes."

#: src/inscriptions.md:62
msgid ""
"The inscription content is contained within the input of a reveal "
"transaction, and the inscription is made on the first sat of its input. This "
"sat can then be tracked using the familiar rules of ordinal theory, allowing "
"it to be transferred, bought, sold, lost to fees, and recovered."
msgstr ""
"El modelo de datos de las inscripciones es el de una respuesta HTTP, "
"permitiendo que el contenido de la inscripción sea obtenido a través de un "
"servidor web y visualizado en un navegador web."

#: src/inscriptions.md:67
msgid "Content"
msgstr "Contenido"

#: src/inscriptions.md:70
msgid ""
"The data model of inscriptions is that of a HTTP response, allowing "
"inscription content to be served by a web server and viewed in a web browser."
msgstr ""
"El modelo de datos de las inscripciones es el de una respuesta HTTP, "
"permitiendo que el contenido de la inscripción sea obtenido a través de un servidor "
"web y visualizado en un navegador web."

#: src/inscriptions.md:73
msgid "Fields"
msgstr "Campos"

#: src/inscriptions.md:76
msgid ""
"Inscriptions may include fields before an optional body. Each field consists "
"of two data pushes, a tag and a value."
msgstr ""
"Las inscripciones pueden incluir campos antes de un cuerpo opcional. "
"Cada campo consta de dos push de datos, una etiqueta y un valor."

#: src/inscriptions.md:79
msgid ""
"Currently, the only defined field is `content-type`, with a tag of `1`, "
"whose value is the MIME type of the body."
msgstr ""
"Actualmente, el único campo definido es `content-type`, con una "
"etiqueta de `1`, cuyo valor es el tipo MIME del cuerpo."

#: src/inscriptions.md:82
msgid ""
"The beginning of the body and end of fields is indicated with an empty data "
"push."
msgstr ""
"Para indicar el principio del cuerpo y el final de los campos se hace "
"un push de datos vacío."

#: src/inscriptions.md:85
msgid ""
"Unrecognized tags are interpreted differently depending on whether they are "
"even or odd, following the \"it's okay to be odd\" rule used by the "
"Lightning Network."
msgstr ""
"Las etiquetas no reconocidas se interpretan de forma diferente según sean pares "
"o impares, siguiendo la regla \"está bien que sean impares\" utilizada por la "
"Lightning Network."

#: src/inscriptions.md:89
msgid ""
"Even tags are used for fields which may affect creation, initial assignment, "
"or transfer of an inscription. Thus, inscriptions with unrecognized even "
"fields must be displayed as \"unbound\", that is, without a location."
msgstr ""
"Las etiquetas pares se utilizan para campos que pueden afectar a la creación, "
"asignación inicial o transferencia de una inscripción. Por esto, las inscripciones "
"con campos pares no reconocidos deben mostrarse como \"no vinculadas\", es decir, "
"sin ubicación."

#: src/inscriptions.md:93
msgid ""
"Odd tags are used for fields which do not affect creation, initial "
"assignment, or transfer, such as additional metadata, and thus are safe to "
"ignore."
msgstr ""
"Las etiquetas impares se utilizan para campos que no afectan a la creación,"
"asignación inicial o transferencia, tales como los metadatos adicionales, y "
"por lo tanto se pueden ignorar."

#: src/inscriptions.md:96
msgid "Inscription IDs"
msgstr "IDs de las Inscripciones"

#: src/inscriptions.md:99
msgid ""
"The inscriptions are contained within the inputs of a reveal transaction. In "
"order to uniquely identify them they are assigned an ID of the form:"
msgstr ""
"Las inscripciones están alojadas en las entradas de una transacción de "
"revelación. Para identificarlas se les asigna un ID como este:"

#: src/inscriptions.md:102
msgid "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"
msgstr "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"

#: src/inscriptions.md:104
msgid ""
"The part in front of the `i` is the transaction ID (`txid`) of the reveal "
"transaction. The number after the `i` defines the index (starting at 0) of "
"new inscriptions being inscribed in the reveal transaction."
msgstr ""
"La parte delante de la `i` es el ID de transacción (`txid`) de la "
"transacción de revelación. El número después la `i` es el índice (comenzando por 0) "
"de las nuevas inscripciones que se están inscribiendo en la transacción "
"de revelación."

#: src/inscriptions.md:108
msgid ""
"Inscriptions can either be located in different inputs, within the same "
"input or a combination of both. In any case the ordering is clear, since a "
"parser would go through the inputs consecutively and look for all "
"inscription `envelopes`."
msgstr ""
"Las inscripciones pueden estar en diferentes entradas, dentro de la misma "
"entrada o en una combinación de ambas. En ambos de estos casos, el orden es "
"claro, ya que un analizador sintáctico (parser) recorrería las entradas "
"consecutivamente buscando los `sobres` de las inscripciones."

#: src/inscriptions.md:112
msgid "Input"
msgstr "Entrada"

#: src/inscriptions.md:112
msgid "Inscription Count"
msgstr "Conteo de Inscripciones"

#: src/inscriptions.md:112
msgid "Indices"
msgstr "Índice"

#: src/inscriptions.md:114
#: src/inscriptions.md:117
msgid "0"
msgstr "0"

#: src/inscriptions.md:114
#: src/inscriptions.md:116
msgid "2"
msgstr "2"

#: src/inscriptions.md:114
msgid "i0, i1"
msgstr "i0, il"

#: src/inscriptions.md:115
#: src/inscriptions.md:115
#: src/inscriptions.md:118
msgid "1"
msgstr "1"

#: src/inscriptions.md:115
msgid "i2"
msgstr "i2"

#: src/inscriptions.md:116
#: src/inscriptions.md:117
msgid "3"
msgstr "3"

#: src/inscriptions.md:116
msgid "i3, i4, i5"
msgstr "i3. i4, i5"

#: src/inscriptions.md:118
msgid "4"
msgstr "4"

#: src/inscriptions.md:118
msgid "i6"
msgstr "i6"

#: src/inscriptions.md:120
msgid "Sandboxing"
msgstr "Sandboxing o Aislamiento"

#: src/inscriptions.md:123
msgid ""
"HTML and SVG inscriptions are sandboxed in order to prevent references to "
"off-chain content, thus keeping inscriptions immutable and self-contained."
msgstr ""
"Las inscripciones en HTML y SVG están restringidas en un entorno aislado "
"llamado sandboxing para evitar referencias a contenido fuera de la cadena, "
"manteniendo así las inscripciones inmutables y contenidas dentro del entorno."

#: src/inscriptions.md:126
msgid ""
"This is accomplished by loading HTML and SVG inscriptions inside `iframes` "
"with the `sandbox` attribute, as well as serving inscription content with "
"`Content-Security-Policy` headers."
msgstr ""
"Esto se logra cargando las inscripciones en HTML y SVG dentro de `iframes` "
"con el atributo `sandbox` y agregando `Content-Security-Policy` a los encabezados."

#: src/inscriptions/provenance.md:4
msgid ""
"The owner of an inscription can create child inscriptions, trustlessly "
"establishing the provenance of those children on-chain as having been "
"created by the owner of the parent inscription. This can be used for "
"collections, with the children of a parent inscription being members of the "
"same collection."
msgstr ""
"El propietario de una inscripción puede crear inscripciones hijas, esto "
"establece una procedencia sin permiso de que esos hijos en la cadena fueron "
"creados por el dueño de la inscripción padre. Esto puede utilizarse para "
"colecciones, en las que los hijos de una inscripción son miembros de la "
"misma colección."

#: src/inscriptions/provenance.md:9
msgid ""
"Children can themselves have children, allowing for complex hierarchies. For "
"example, an artist might create an inscription representing themselves, with "
"sub inscriptions representing collections that they create, with the "
"children of those sub inscriptions being items in those collections."
msgstr ""
"Los hijos pueden tener a su vez hijos, lo que permite crear jerarquías "
"complejas. Por ejemplo, un artista puede crear una inscripción que lo represente "
"a él mismo, con sub-inscripciones que representen colecciones creadas por él, "
"siendo los hijos de esas sub-inscripciones elementos de esas colecciones."

#: src/inscriptions/provenance.md:14
msgid "Specification"
msgstr "Especificaciónes"

#: src/inscriptions/provenance.md:16
msgid "To create a child inscription C with parent inscription P:"
msgstr "Para crear el hijo de una inscripción C con una inscripción padre P:"

#: src/inscriptions/provenance.md:18
msgid "Create an inscribe transaction T as usual for C."
msgstr "Crear una transacción de inscripción T como de costumbre para C."

#: src/inscriptions/provenance.md:19
msgid "Spend the parent P in one of the inputs of T."
msgstr "Gasta el padre P en una de las entradas de T."

#: src/inscriptions/provenance.md:20
msgid ""
"Include tag `3`, i.e. `OP_PUSH 3`, in C, with the value of the serialized "
"binary inscription ID of P, serialized as the 32-byte `TXID`, followed by "
"the four-byte little-endian `INDEX`, with trailing zeroes omitted."
msgstr ""
"Incluye la etiqueta `3`, es decir, `OP_PUSH 3`, en C, con el valor del "
"ID binario serializado de la inscripción P, serializado con el `TXID` de "
"32 bytes, seguido por el `INDEX` en formato little-endian de cuatro bytes, "
"omitiendo los ceros al final."

#: src/inscriptions/provenance.md:24
msgid ""
"_NB_ The bytes of a bitcoin transaction ID are reversed in their text "
"representation, so the serialized transaction ID will be in the opposite "
"order."
msgstr ""
"_NB_ Los bytes del ID de una transacción de bitcoin se invierten en su "
"representación textual, por lo que el ID de transacción serializado será "
"en el orden inverso."

#: src/inscriptions/provenance.md:27
#: src/guides/testing.md:18
#: src/guides/reindexing.md:15
msgid "Example"
msgstr "Ejemplo"

#: src/inscriptions/provenance.md:29
msgid ""
"An example of a child inscription of "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"
msgstr ""
"Ejemplo de una inscripción hija de "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"

#: src/inscriptions/provenance.md:32
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH 0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"¡Hola, mundo!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:45
msgid ""
"Note that the value of tag `3` is binary, not hex, and that for the child "
"inscription to be recognized as a child, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` must be "
"spent as one of the inputs of the inscribe transaction."
msgstr ""
"Ten en cuenta que el valor de la etiqueta `3` es en binario, no hexadecimal,"
"y para que la inscripción hija sea reconocida como tal, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` debe "
"gastarse como una de las entradas de la transacción de inscripción."

#: src/inscriptions/provenance.md:50
msgid ""
"Example encoding of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"
msgstr "Ejemplo de codificación de la inscripción que contiene el ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"

#: src/inscriptions/provenance.md:53
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100ff\n"
"  …\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100ff\n"
"  …\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:63
msgid ""
"And of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"
msgstr ""
"Y del ID de inscripción `000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"

#: src/inscriptions/provenance.md:65
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a090807060504030201000001\n"
"  …\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a090807060504030201000001\n"
"  …\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:75
msgid "Notes"
msgstr "Notas"

#: src/inscriptions/provenance.md:77
msgid ""
"The tag `3` is used because it is the first available odd tag. Unrecognized "
"odd tags do not make an inscription unbound, so child inscriptions would be "
"recognized and tracked by old versions of `ord`."
msgstr ""
"La etiqueta `3` se utiliza porque es la primera etiqueta impar disponible. "
"Las etiquetas impares desconocidas no desvinculan las inscripciones, por lo "
"que las inscripciones hijas serían reconocidas y rastreadas por las versiones "
"anteriores de ord."

#: src/inscriptions/provenance.md:81
msgid ""
"A collection can be closed by burning the collection's parent inscription, "
"which guarantees that no more items in the collection can be issued."
msgstr ""
"Se puede cerrar una colección quemando la inscripción principal de la "
"colección, lo que garantiza que no se pueden emitir más ítems en la colección."

#: src/inscriptions/recursion.md:4
msgid ""
"An important exception to [sandboxing](../inscriptions.md#sandboxing) is "
"recursion: access to `ord`'s `/content` endpoint is permitted, allowing "
"inscriptions to access the content of other inscriptions by requesting "
"`/content/<INSCRIPTION_ID>`."
msgstr ""
"Una excepción importante a las restricciones del [sandboxing]"
"(../inscriptions.md#sandboxing-o-aislamiento) es la recursión. Se permite "
"el acceso al punto final o endpoint `/content` de `ord`, esto permite que "
"las inscripciones accedan al contenido de otras inscripciones solicitando "
"`/content/<ID_DE_INSCRIPCION>`."

#: src/inscriptions/recursion.md:9
msgid "This has a number of interesting use-cases:"
msgstr "Esto tiene varios casos de uso interesantes:"

#: src/inscriptions/recursion.md:11
msgid "Remixing the content of existing inscriptions."
msgstr "Combinar el contenido de inscripciones existentes."

#: src/inscriptions/recursion.md:13
msgid ""
"Publishing snippets of code, images, audio, or stylesheets as shared public "
"resources."
msgstr  ""
"Publicar fragmentos de código, imágenes, audio y hojas de cálculo como "
"recursos públicos."

#: src/inscriptions/recursion.md:16
msgid ""
"Generative art collections where an algorithm is inscribed as JavaScript, "
"and instantiated from multiple inscriptions with unique seeds."
msgstr ""
"Colecciones de arte generativo en donde un algoritmo está inscrito en "
"JavaScript, y se instancia desde múltiples inscripciones con semillas únicas."

#: src/inscriptions/recursion.md:19
msgid ""
"Generative profile picture collections where accessories and attributes are "
"inscribed as individual images, or in a shared texture atlas, and then "
"combined, collage-style, in unique combinations in multiple inscriptions."
msgstr ""
"Colecciones generativas de imágenes de perfil donde los accesorios y atributos "
"están inscritos como imágenes individuales, o en un atlas de texturas, y luego se "
"combinan, al estilo collage, en combinaciones únicas en múltiples inscripciones."

#: src/inscriptions/recursion.md:23
msgid "A few other endpoints that inscriptions may access are the following:"
msgstr "Algunos otros puntos finales a los que pueden acceder las inscripciones son los siguientes:"

#: src/inscriptions/recursion.md:25
msgid "`/blockheight`: latest block height."
msgstr "`/blockheight`: altura del bloque más reciente."

#: src/inscriptions/recursion.md:26
msgid "`/blockhash`: latest block hash."
msgstr "`/blockhash`: hash del bloque más reciente."

#: src/inscriptions/recursion.md:27
msgid "`/blockhash/<HEIGHT>`: block hash at given block height."
msgstr "`/blockhash/<ALTURA>`: hash del bloque a la altura de bloque dada."

#: src/inscriptions/recursion.md:28
msgid "`/blocktime`: UNIX time stamp of latest block."
msgstr "`/blocktime`: marca de tiempo UNIX del bloque más reciente."

#: src/faq.md:1
msgid "Ordinal Theory FAQ"
msgstr "Preguntas frecuentes sobre la teoría Ordinal"

#: src/faq.md:4
msgid "What is ordinal theory?"
msgstr "¿Qué es la teoría ordinal?"

#: src/faq.md:7
msgid ""
"Ordinal theory is a protocol for assigning serial numbers to satoshis, the "
"smallest subdivision of a bitcoin, and tracking those satoshis as they are "
"spent by transactions."
msgstr ""
"La teoría ordinal es un protocolo para asignar números de serie a los "
"satoshis, la denominación más pequeña de un bitcoin, y rastrear esos satoshis "
"a medida que son gastados en transacciones."

#: src/faq.md:11
msgid ""
"These serial numbers are large numbers, like this 804766073970493. Every "
"satoshi, which is ¹⁄₁₀₀₀₀₀₀₀₀ of a bitcoin, has an ordinal number."
msgstr ""
"Estos números de serie son números grandes, un ejemplo es el número "
"804766073970493. Cada satoshi, el cual es ¹⁄₁₀₀₀₀₀₀₀₀ de un bitcoin, tiene "
"un número ordinal."

#: src/faq.md:14
msgid ""
"Does ordinal theory require a side chain, a separate token, or changes to "
"Bitcoin?"
msgstr "¿La teoría ordinal requiere una cadena lateral, un token separado o "
"cambios en Bitcoin?"

#: src/faq.md:17
msgid ""
"Nope! Ordinal theory works right now, without a side chain, and the only "
"token needed is bitcoin itself."
msgstr ""
"¡No! La teoría ordinal funciona en este momento, sin una cadena lateral, "
"y el único token necesario es simplemente bitcoin."

#: src/faq.md:20
msgid "What is ordinal theory good for?"
msgstr "¿Para qué sirve la teoría Ordinals?"

#: src/faq.md:23
msgid ""
"Collecting, trading, and scheming. Ordinal theory assigns identities to "
"individual satoshis, allowing them to be individually tracked and traded, as "
"curios and for numismatic value."
msgstr ""
"Para coleccionar, comerciar e innovar. La teoría ordinal asigna una identidad "
"a los satoshis, permitiendo que se rastreen e intercambien, como curiosidades "
"y por su valor numismático."

#: src/faq.md:27
msgid ""
"Ordinal theory also enables inscriptions, a protocol for attaching arbitrary "
"content to individual satoshis, turning them into bitcoin-native digital "
"artifacts."
msgstr ""
"La teoría ordinal también habilita las inscripciones, un protocolo para adjuntar "
"contenido arbitrario a los satoshis, convirtiéndolos en artefactos digitales nativos "
"en bitcoin."

#: src/faq.md:31
msgid "How does ordinal theory work?"
msgstr "¿Cómo funciona la teoría ordinal?"

#: src/faq.md:34
msgid ""
"Ordinal numbers are assigned to satoshis in the order in which they are "
"mined. The first satoshi in the first block has ordinal number 0, the second "
"has ordinal number 1, and the last satoshi of the first block has ordinal "
"number 4,999,999,999."
msgstr ""
"Los números ordinales se asignan a los satoshis en el orden en que se minan. "
"El primer satoshi en el primer bloque tiene el número ordinal 0, el segundo tiene el "
"número ordinal 1 y el último satoshi del primer bloque tiene el número ordinal 4,999,999,999."

#: src/faq.md:39
msgid ""
"Satoshis live in outputs, but transactions destroy outputs and create new "
"ones, so ordinal theory uses an algorithm to determine how satoshis hop from "
"the inputs of a transaction to its outputs."
msgstr ""
"Los satoshis se hallan en las transacciones salientes, pero las transacciones "
"se destruyen cuando son emitidas y se crean nuevas transacciones, lo que la teoría de "
"Ordinals hace es utilizar un algoritmo para determinar cómo los satoshis se mueven entre "
"las transacciones que salen y entran."

#: src/faq.md:43
msgid "Fortunately, that algorithm is very simple."
msgstr "Afortunadamente, ese algoritmo es bastante sencillo."

#: src/faq.md:45
msgid ""
"Satoshis transfer in first-in-first-out order. Think of the inputs to a "
"transaction as being a list of satoshis, and the outputs as a list of slots, "
"waiting to receive a satoshi. To assign input satoshis to slots, go through "
"each satoshi in the inputs in order, and assign each to the first available "
"slot in the outputs."
msgstr ""
"Los satoshis se transfieren en orden, el primero que entra, es el primero "
"en salir. Simplemente hay que pensar en las entradas a una transacción como una "
"lista de satoshis, y las salidas como una lista con espacios libres, esperando para "
"recibir un satoshi. Para asignar satoshis a un espacio libre, se asigna uno por uno "
"al próximo espacio que esté disponible. "

#: src/faq.md:51
msgid ""
"Let's imagine a transaction with three inputs and two outputs. The inputs "
"are on the left of the arrow and the outputs are on the right, all labeled "
"with their values:"
msgstr ""
"Imaginemos una transacción con tres entradas y dos salidas. Las entradas "
"están a la izquierda de la flecha y las salidas a la derecha, todas con sus "
"respectivos valores:"

#: src/faq.md:55
msgid ""
"```\n"
"[2] [1] [3] → [4] [2]\n"
"```"
msgstr ""
"```\n"
"[2] [1] [3] → [4] [2]\n"
"```"

#: src/faq.md:57
msgid ""
"Now let's label the same transaction with the ordinal numbers of the "
"satoshis that each input contains, and question marks for each output slot. "
"Ordinal numbers are large, so let's use letters to represent them:"
msgstr ""
"Ahora miremos la misma transacción con los números ordinales de los satoshis "
"que contiene cada entrada. Pondremos signos de interrogantes para cada espacio "
"de salida que está libre. Ya que los números ordinales son grandes utilizaremos "
"letras para representarlos:"

#: src/faq.md:61
msgid ""
"```\n"
"[a b] [c] [d e f] → [? ? ? ?] [? ?]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [? ? ? ?] [? ?]\n"
"```"

#: src/faq.md:63
msgid ""
"To figure out which satoshi goes to which output, go through the input "
"satoshis in order and assign each to a question mark:"
msgstr ""
"Para saber dónde el satoshi quedara ubicado en la transacción de salida, "
"revisa los satoshis en la transacción de entrada y asigna cada uno a un "
"signo de interrogación:"

#: src/faq.md:66
msgid ""
"```\n"
"[a b] [c] [d e f] → [a b c d] [e f]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [a b c d] [e f]\n"
"```"

#: src/faq.md:68
msgid ""
"What about fees, you might ask? Good question! Let's imagine the same "
"transaction, this time with a two satoshi fee. Transactions with fees send "
"more satoshis in the inputs than are received by the outputs, so to make our "
"transaction into one that pays fees, we'll remove the second output:"
msgstr ""
"¿En este momento te estarás preguntando qué pasara con las comisiones? "
"¡Buena pregunta! Imaginemos la misma transacción, esta vez con una comisión "
"de dos satoshis. Las transacciones con comisiones contienen más satoshis en "
"las transacciones de entrada que las que reciben las transacciones de salida, "
"por lo que para convertir nuestra transacción en una que pague comisiones, "
"eliminaremos la segunda salida:"

#: src/faq.md:73
msgid ""
"```\n"
"[2] [1] [3] → [4]\n"
"```"
msgstr ""
"```\n"
"[2] [1] [3] → [4]\n"
"```"

#: src/faq.md:75
msgid "The satoshis "
msgstr "Los satoshis"

#: src/faq.md:75
msgid "e"
msgstr "e"

#: src/faq.md:75
msgid " and "
msgstr "y"

#: src/faq.md:75
msgid "f"
msgstr "f"

#: src/faq.md:75
msgid " now have nowhere to go in the outputs:"
msgstr "ahora no tienen donde ir en las salidas"

#: src/faq.md:78
msgid ""
"```\n"
"[a b] [c] [d e f] → [a b c d]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [a b c d]\n"
"```"

#: src/faq.md:80
msgid ""
"So they go to the miner who mined the block as fees. [The "
"BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) has the "
"details, but in short, fees paid by transactions are treated as extra inputs "
"to the coinbase transaction, and are ordered how their corresponding "
"transactions are ordered in the block. The coinbase transaction of the block "
"might look like this:"
msgstr ""
"Así que van al minero que minó el bloque como comisión. [El BIP]"
"(https://github.com/ordinals/ord/blob/master/bip.mediawiki) tiene los "
"detalles, pero, en resumen, las comisiones pagadas por las transacciones "
"se tratan como entradas adicionales a la transacción coinbase y se ordenan "
"según el orden en el que están en el bloque. La transacción coinbase del "
"bloque podría verse así:"

#: src/faq.md:87
msgid ""
"```\n"
"[SUBSIDY] [e f] → [SUBSIDY e f]\n"
"```"
msgstr ""
"```\n"
"[SUBSIDIO] [e f] → [SUBSIDIO e f]"
"```"

#: src/faq.md:89
msgid "Where can I find the nitty-gritty details?"
msgstr "¿Dónde puedo encontrar los detalles técnicos?"

#: src/faq.md:92
msgid "[The BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[¡En el BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src/faq.md:94
msgid ""
"Why are sat inscriptions called \"digital artifacts\" instead of \"NFTs\"?"
msgstr "¿Por qué a las inscripciones de Satoshis se les llama \"artefactos digitales\" en lugar de \"NFT\"?"

#: src/faq.md:97
msgid ""
"An inscription is an NFT, but the term \"digital artifact\" is used instead, "
"because it's simple, suggestive, and familiar."
msgstr "Una inscripción es un NFT, pero se utiliza el término \"artefacto digital\", "
"porque es fácil, sugerente y familiar."

#: src/faq.md:100
msgid ""
"The phrase \"digital artifact\" is highly suggestive, even to someone who "
"has never heard the term before. In comparison, NFT is an acronym, and "
"doesn't provide any indication of what it means if you haven't heard the "
"term before."
msgstr ""
"La frase \"artefacto digital\" es muy sugerente, incluso para alguien "
"que nunca ha oído el término. En comparación, NFT es un acrónimo y no proporciona "
"ninguna indicación de lo que significa si no has escuchado el término."

#: src/faq.md:104
msgid ""
"Additionally, \"NFT\" feels like financial terminology, and the both word "
"\"fungible\" and sense of the word \"token\" as used in \"NFT\" is uncommon "
"outside of financial contexts."
msgstr ""
"Además, la palabra, \"NFT\" suena como terminología financiera, y tanto "
"la palabra \"fungible\" como el sentido de la palabra \"token\" como se usa en los "
"\"NFT\" no son comunes fuera de los contextos financieros."

#: src/faq.md:108
msgid "How do sat inscriptions compare to…"
msgstr "¿Cómo se comparan las inscripciones con..."

#: src/faq.md:111
msgid "Ethereum NFTs?"
msgstr "¿Los NFT de Ethereum?"

#: src/faq.md:113
msgid "_Inscriptions are always immutable._"
msgstr "_Las inscripciones siempre son inmutables._"

#: src/faq.md:115
msgid ""
"There is simply no way to for the creator of an inscription, or the owner of "
"an inscription, to modify it after it has been created."
msgstr ""
"No hay forma de que el creador de una inscripción, o el propietario de "
"una inscripción, la modifique después de haber sido creada."

#: src/faq.md:118
msgid ""
"Ethereum NFTs _can_ be immutable, but many are not, and can be changed or "
"deleted by the NFT contract owner."
msgstr ""
"Los NFTs en Ethereum _pueden_ ser inmutables, pero muchos no lo son y pueden "
"ser cambiados o eliminados por el propietario del contrato del NFT."

#: src/faq.md:121
msgid ""
"In order to make sure that a particular Ethereum NFT is immutable, the "
"contract code must be audited, which requires detailed knowledge of the EVM "
"and Solidity semantics."
msgstr ""
"Para asegurarse de que un NFT de Ethereum sea inmutable, se debe "
"analizar el código del contrato, lo cual requiere un conocimiento a profundidad "
"de la EVM y la semántica de Solidity."

#: src/faq.md:125
msgid ""
"It is very hard for a non-technical user to determine whether or not a given "
"Ethereum NFT is mutable or immutable, and Ethereum NFT platforms make no "
"effort to distinguish whether an NFT is mutable or immutable, and whether "
"the contract source code is available and has been audited."
msgstr ""
"Para un usuario que no tiene habilidades técnicas es bastante difícil "
"determinar si un NFT de Ethereum es mutable o inmutable, y las plataformas "
"Ethereum NFT no se esfuerzan para ayudar a distinguir si un NFT es mutable o "
"inmutable y si el código fuente del contrato está disponible y ha sido auditado."

#: src/faq.md:130
msgid "_Inscription content is always on-chain._"
msgstr "_El contenido de la inscripción siempre está en la cadena de Bitcoin._"

#: src/faq.md:132
msgid ""
"There is no way for an inscription to refer to off-chain content. This makes "
"inscriptions more durable, because content cannot be lost, and scarcer, "
"because inscription creators must pay fees proportional to the size of the "
"content."
msgstr ""
"No hay forma de que una inscripción haga referencia a contenido que esta "
"por fuera de la cadena de Bitcoin. Debido a esto las inscripciones son más "
"duraderas porque el contenido no puede perderse y también las hace más "
"escasas porque los creadores de inscripciones deben pagar comisiones "
"proporcionales al tamaño del contenido."

#: src/faq.md:136
msgid ""
"Some Ethereum NFT content is on-chain, but much is off-chain, and is stored "
"on platforms like IPFS or Arweave, or on traditional, fully centralized web "
"servers. Content on IPFS is not guaranteed to continue to be available, and "
"some NFT content stored on IPFS has already been lost. Platforms like "
"Arweave rely on weak economic assumptions, and will likely fail "
"catastrophically when these economic assumptions are no longer met. "
"Centralized web servers may disappear at any time."
msgstr ""
"Hay algunos NFTs en Ethereum que están en la cadena, pero muchos no lo "
"están y se almacenan en plataformas como IPFS o Arweave, o en servidores web "
"centralizados. No se garantiza que el contenido que esta almacenado en IPFS "
"continue estando disponible, de hecho, ya se ha perdido contenido de NFTs que "
"fueron almacenado en IPFS. Plataformas como Arweave dependen de suposiciones "
"económicas y probablemente fallarán catastróficamente cuando estas suposiciones "
"ya no se cumplan. Los servidores web centralizados pueden desaparecer en "
"cualquier momento."

#: src/faq.md:144
msgid ""
"It is very hard for a non-technical user to determine where the content of a "
"given Ethereum NFT is stored."
msgstr ""
"Es difícil para un usuario que no tienes habilidades técnicas determinar "
"donde el contenido de un NFT de Ethereum esta alojado "

#: src/faq.md:147
msgid "_Inscriptions are much simpler._"
msgstr "_Las inscripciones son mucho más sencillas._"

#: src/faq.md:149
msgid ""
"Ethereum NFTs depend on the Ethereum network and virtual machine, which are "
"highly complex, constantly changing, and which introduce changes via "
"backwards-incompatible hard forks."
msgstr ""
"Los NFT de Ethereum dependen de la red y la máquina virtual de Ethereum, "
"las cuales son altamente complejas, cambian constantemente e introducen cambios "
"mediante bifurcaciones incompatibles con versiones anteriores."

#: src/faq.md:153
msgid ""
"Inscriptions, on the other hand, depend on the Bitcoin blockchain, which is "
"relatively simple and conservative, and which introduces changes via "
"backwards-compatible soft forks."
msgstr ""
"Por otro lado, las inscripciones dependen de la blockchain de Bitcoin que es "
"relativamente simple y conservadora, e introduce cambios mediante bifurcaciones "
"suaves compatibles con versiones anteriores."

#: src/faq.md:157
msgid "_Inscriptions are more secure._"
msgstr "_Las inscripciones son más seguras._"

#: src/faq.md:159
msgid ""
"Inscriptions inherit Bitcoin's transaction model, which allow a user to see "
"exactly which inscriptions are being transferred by a transaction before "
"they sign it. Inscriptions can be offered for sale using partially signed "
"transactions, which don't require allowing a third party, such as an "
"exchange or marketplace, to transfer them on the user's behalf."
msgstr ""
"Las inscripciones heredan el modelo de transacción de Bitcoin, lo "
"que permite al usuario ver exactamente qué inscripciones se están transfiriendo "
"en una transacción antes de firmarla. Las inscripciones pueden ofrecerse a la "
"venta mediante transacciones parcialmente firmadas, lo que no requiere el permiso "
"un tercero, como un mercado o plataforma para la transferencia."

#: src/faq.md:165
msgid ""
"By comparison, Ethereum NFTs are plagued with end-user security "
"vulnerabilities. It is commonplace to blind-sign transactions, grant "
"third-party apps unlimited permissions over a user's NFTs, and interact with "
"complex and unpredictable smart contracts. This creates a minefield of "
"hazards for Ethereum NFT users which are simply not a concern for ordinal "
"theorists."
msgstr ""
"En comparación, los NFT de Ethereum están plagados de vulnerabilidades de "
"seguridad para el usuario final. Es común firmar transacciones a ciegas, "
"otorgar permisos ilimitados a aplicaciones de terceros sobre los NFTs e "
"interactuar con contratos inteligentes complejos e impredecibles. Estos "
"problemas que tienen los NFTs de Ethereum simplemente no son una preocupación "
"para los teóricos de Ordinals."

#: src/faq.md:171
msgid "_Inscriptions are scarcer._"
msgstr "_Las inscripciones son más escasas._"

#: src/faq.md:173
msgid ""
"Inscriptions require bitcoin to mint, transfer, and store. This seems like a "
"downside on the surface, but the raison d'etre of digital artifacts is to be "
"scarce and thus valuable."
msgstr ""
"Para mintear, transferir y almacenar inscripciones, se requiere bitcoin. "
"Superficialmente esto parece un inconveniente, pero la razón de ser de los "
"artefactos digitales es ser escasos y, por lo tanto, valiosos."

#: src/faq.md:177
msgid ""
"Ethereum NFTs, on the other hand, can be minted in virtually unlimited "
"qualities with a single transaction, making them inherently less scarce, and "
"thus, potentially less valuable."
msgstr ""
"Los NFT de Ethereum, por otro lado, pueden ser minteados en cantidades "
"prácticamente ilimitadas con una sola transacción, lo que los hace intrínsecamente "
"menos escasos y, por lo tanto, potencialmente menos valiosos."

#: src/faq.md:181
msgid "_Inscriptions do not pretend to support on-chain royalties._"
msgstr "_Las inscripciones no pretenden respaldar regalías en la cadena._"

#: src/faq.md:183
msgid ""
"On-chain royalties are a good idea in theory but not in practice. Royalty "
"payment cannot be enforced on-chain without complex and invasive "
"restrictions. The Ethereum NFT ecosystem is currently grappling with "
"confusion around royalties, and is collectively coming to grips with the "
"reality that on-chain royalties, which were messaged to artists as an "
"advantage of NFTs, are not possible, while platforms race to the bottom and "
"remove royalty support."
msgstr ""
"En teoría, la implementación de regalías dentro de la blockchain suena bien, "
"pero su puesta en práctica presenta problemas significativos. El pago de regalías "
"no puede ser aplicado en la cadena sin restricciones complejas e invasivas. "
"En este momento, el ecosistema de NFT en Ethereum está enfrentando problemas "
"debido a la confusión generada por las regalías, y colectivamente se está "
"llegando a la conclusión de que las regalías en la cadena las cuales fueron "
"una ventaja que se planteó a favor de los NFTs para los artistas, no son "
"posibles. Algunas plataformas ya están eliminando el soporte de regalías."

#: src/faq.md:190
msgid ""
"Inscriptions avoid this situation entirely by making no false promises of "
"supporting royalties on-chain, thus avoiding the confusion, chaos, and "
"negativity of the Ethereum NFT situation."
msgstr ""
"Las inscripciones evitan completamente esta situación al no hacer falsas "
"promesas de respaldar regalías en la cadena, evitando así la confusión, "
"el caos y la negatividad de la situación que ocurre con los NFT en Ethereum."

#: src/faq.md:194
msgid "_Inscriptions unlock new markets._"
msgstr "_Las inscripciones abren las puertas a nuevos mercados._"

#: src/faq.md:196
msgid ""
"Bitcoin's market capitalization and liquidity are greater than Ethereum's by "
"a large margin. Much of this liquidity is not available to Ethereum NFTs, "
"since many Bitcoiners prefer not to interact with the Ethereum ecosystem due "
"to concerns related to simplicity, security, and decentralization."
msgstr ""
"La valoración de mercado y el flujo de capital en Bitcoin exceden "
"considerablemente a los de Ethereum. Gran parte de esta liquidez no está "
"disponible para los NFT en Ethereum, ya que muchos bitcoiners prefieren no "
"interactuar con el ecosistema de Ethereum debido a las preocupaciones "
"relacionadas con la simplicidad, seguridad y descentralización."

#: src/faq.md:201
msgid ""
"Such Bitcoiners may be more interested in inscriptions than Ethereum NFTs, "
"unlocking new classes of collector."
msgstr ""
"Estos bitcoiners podrían estar más interesados en las inscripciones "
"que en los NFT de Ethereum, abriendo la puerta a otros tipos de coleccionistas."

#: src/faq.md:204
msgid "_Inscriptions have a richer data model._"
msgstr "_Las inscripciones tienen un modelo de datos más robusto._"

#: src/faq.md:206
msgid ""
"Inscriptions consist of a content type, also known as a MIME type, and "
"content, which is an arbitrary byte string. This is the same data model used "
"by the web, and allows inscription content to evolve with the web, and come "
"to support any kind of content supported by web browsers, without requiring "
"changes to the underlying protocol."
msgstr ""
"Una inscripción está conformada por el tipo de contenido, conocido como el "
"tipo MIME, y una cadena (string) de bytes que sería el contenido. Este es "
"el mismo modelo de datos utilizado por la web, este permite que el contenido "
"de la inscripción evolucione con la web, y llegue a admitir cualquier tipo de "
"contenido soportado por los navegadores web, sin requerir cambios en el protocolo "
"subyacente."

#: src/faq.md:212
msgid "RGB and Taro assets?"
msgstr "¿Activos de RGB y Taro?"

#: src/faq.md:214
msgid ""
"RGB and Taro are both second-layer asset protocols built on Bitcoin. "
"Compared to inscriptions, they are much more complicated, but much more "
"featureful."
msgstr ""
"RGB y Taro son protocolos de activos construidos en una capa segundaria "
"sobre Bitcoin. En comparación con las inscripciones, son mucho más complicados, "
"pero con muchas más funcionalidades."

#: src/faq.md:217
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas the primary use-case of RGB and Taro are fungible tokens, so the "
"user experience for inscriptions is likely to be simpler and more polished "
"than the user experience for RGB and Taro NFTs."
msgstr ""
"La teoría ordinal ha sido diseñada desde cero para los artefactos "
"digitales, mientras que el enfoque de RGB y Taro son los tokens fungibles, "
"por lo que la experiencia de un usuario con las inscripciones probablemente "
"sea más sencilla y refinada que la experiencia de alguien utilizando NFTs de "
"RGB y Taro."

#: src/faq.md:222
msgid ""
"RGB and Taro both store content off-chain, which requires additional "
"infrastructure, and which may be lost. By contrast, inscription content is "
"stored on-chain, and cannot be lost."
msgstr ""
"RGB y Taro almacenan contenido fuera de la cadena, lo que requiere una "
"infraestructura adicional que podría perderse. En cambio, el contenido de las "
"inscripciones se almacena en la cadena y no puede perderse."

#: src/faq.md:226
msgid ""
"Ordinal theory, RGB, and Taro are all very early, so this is speculation, "
"but ordinal theory's focus may give it the edge in terms of features for "
"digital artifacts, including a better content model, and features like "
"globally unique symbols."
msgstr ""
"La teoría ordinal, RGB y Taro están en sus primeras etapas, por lo que "
"todo esto es especulativo, pero el enfoque de la teoría ordinal podría "
"darle la ventaja en términos de funcionalidad para artefactos digitales, "
"incluyendo un mejor modelo de contenido y características como símbolos "
"globalmente únicos."

#: src/faq.md:231
msgid "Counterparty assets?"
msgstr "¿Activos de Counterparty?"

#: src/faq.md:233
msgid ""
"Counterparty has its own token, XCP, which is required for some "
"functionality, which makes most bitcoiners regard it as an altcoin, and not "
"an extension or second layer for bitcoin."
msgstr ""
"Counterparty tiene su propio token, XCP, que es necesario para algunas "
"funcionalidades, lo que hace que la mayoría de los bitcoiners lo consideren "
"como una altcoin, y no como una extensión o segunda capa de bitcoin."

#: src/faq.md:237
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas Counterparty was primarily designed for financial token issuance."
msgstr ""
"La teoría ordinal fue diseñada específicamente para tratar con "
"artefactos digitales, a diferencia de Counterparty, que se diseñó principalmente "
"enfocado en la emisión de tokens financieros."

#: src/faq.md:240
msgid "Inscriptions for…"
msgstr "Inscripciones para..."

#: src/faq.md:243
msgid "Artists"
msgstr "Artistas"

#: src/faq.md:245
msgid ""
"_Inscriptions are on Bitcoin._ Bitcoin is the digital currency with the "
"highest status and greatest chance of long-term survival. If you want to "
"guarantee that your art survives into the future, there is no better way to "
"publish it than as inscriptions."
msgstr ""
"_Las inscripciones están en Bitcoin_. Bitcoin es la moneda digital con el mayor "
"prestigio y la mayor probabilidad de supervivencia a largo plazo. Si deseas garantizar "
"que tu arte perdure en el futuro, no hay mejor forma de publicarlo que mediante "
"inscripciones."

#: src/faq.md:250
msgid ""
"_Cheaper on-chain storage._ At $20,000 per BTC and the minimum relay fee of "
"1 satoshi per vbyte, publishing inscription content costs $50 per 1 million "
"bytes."
msgstr ""
"_Almacenamiento en cadena es menos costoso_. "
"A $20,000 por BTC y una comisión mínima de 1 satoshi por vbyte, el costo de "
"publicar contenido en una inscripción es de $50 por 1 millón de bytes."

#: src/faq.md:254
msgid ""
"_Inscriptions are early!_ Inscriptions are still in development, and have "
"not yet launched on mainnet. This gives you an opportunity to be an early "
"adopter, and explore the medium as it evolves."
msgstr ""
"_¡Las inscripciones están en sus inicios!_ Las inscripciones todavía "
"están en desarrollo y aún no se han lanzado en la red principal. Esto te brinda "
"la oportunidad de ser un pionero y explorar el medio a medida que evoluciona."

#: src/faq.md:258
msgid ""
"_Inscriptions are simple._ Inscriptions do not require writing or "
"understanding smart contracts."
msgstr ""
"_Las inscripciones son simples_. No es necesario escribir ni "
"comprender contratos inteligentes."

#: src/faq.md:261
msgid ""
"_Inscriptions unlock new liquidity._ Inscriptions are more accessible and "
"appealing to bitcoin holders, unlocking an entirely new class of collector."
msgstr ""
"_Las inscripciones abren las puertas a nuevas fuentes de liquidez_. "
"Las inscripciones resultan más accesibles y atractivas para los poseedores "
"de bitcoin, dando paso a una clase completamente nueva de coleccionistas."

#: src/faq.md:264
msgid ""
"_Inscriptions are designed for digital artifacts._ Inscriptions are designed "
"from the ground up to support NFTs, and feature a better data model, and "
"features like globally unique symbols and enhanced provenance."
msgstr ""
"_Las inscripciones están diseñadas para los artefactos digitales_. "
"Se diseñan desde cero enfocándose en los NFTs y ofrecen un modelo de datos "
"superior, con características como símbolos globalmente únicos y una procedencia "
"mejorada."

#: src/faq.md:268
msgid ""
"_Inscriptions do not support on-chain royalties._ This is negative, but only "
"depending on how you look at it. On-chain royalties have been a boon for "
"creators, but have also created a huge amount of confusion in the Ethereum "
"NFT ecosystem. The ecosystem now grapples with this issue, and is engaged in "
"a race to the bottom, towards a royalties-optional future. Inscriptions have "
"no support for on-chain royalties, because they are technically infeasible. "
"If you choose to create inscriptions, there are many ways you can work "
"around this limitation: withhold a portion of your inscriptions for future "
"sale, to benefit from future appreciation, or perhaps offer perks for users "
"who respect optional royalties."
msgstr ""
"_Las inscripciones no admiten regalías en cadena_. Esto puede verse como "
"algo negativo, pero realmente depende de la perspectiva de cada uno. Si bien "
"las regalías en cadena han beneficiado enormemente a los creadores, también han "
"generado una gran cantidad de confusión en el ecosistema NFT de Ethereum. En este"
" momento, el ecosistema está lidiando con este problema, encaminándose hacia un "
"futuro donde las regalías serán opcionales. Las inscripciones no admiten regalías "
"en cadena debido a que técnica y prácticamente no son viables. Sin embargo, si "
"decides crear inscripciones, existen varias estrategias para superar esta restricción: "
"puedes retener una porción de tus inscripciones para futuras ventas, aprovechando "
"así la valorización que puedan tener con el tiempo; o incluso ofrecer incentivos "
"a los usuarios que opten por respetar las regalías opcionales."

#: src/faq.md:279
msgid "Collectors"
msgstr "Coleccionistas "

#: src/faq.md:281
msgid ""
"_Inscriptions are simple, clear, and have no surprises._ They are always "
"immutable and on-chain, with no special due diligence required."
msgstr ""
"_Las inscripciones son sencillas, claras y sin sorpresas inesperadas_. "
"Son siempre inmutables y residen en la cadena de Bitcoin, lo que elimina la necesidad de gestiones adicionales."

#: src/faq.md:284
msgid ""
"_Inscriptions are on Bitcoin._ You can verify the location and properties of "
"inscriptions easily with Bitcoin full node that you control."
msgstr ""
"_Las inscripciones están en Bitcoin_. Puedes verificar fácilmente la "
"ubicación y propiedades de las inscripciones con un nodo de Bitcoin que tu controlas."

#: src/faq.md:287
msgid "Bitcoiners"
msgstr "Bitcoiners"

#: src/faq.md:289
msgid ""
"Let me begin this section by saying: the most important thing that the "
"Bitcoin network does is decentralize money. All other use-cases are "
"secondary, including ordinal theory. The developers of ordinal theory "
"understand and acknowledge this, and believe that ordinal theory helps, at "
"least in a small way, Bitcoin's primary mission."
msgstr ""
"Permíteme iniciar esta sección señalando que: la principal función de la "
"red de Bitcoin es la descentralización del dinero. Todos los otros usos "
"que se le pueden dar son secundarios, y eso incluye a la teoría ordinal. "
"Los desarrolladores detrás de esta teoría comprenden muy bien este aspecto "
"y consideran que su trabajo contribuye, aunque sea mínimamente, a la misión principal de Bitcoin."

#: src/faq.md:295
msgid ""
"Unlike many other things in the altcoin space, digital artifacts have merit. "
"There are, of course, a great deal of NFTs that are ugly, stupid, and "
"fraudulent. However, there are many that are fantastically creative, and "
"creating and collecting art has been a part of the human story since its "
"inception, and predates even trade and money, which are also ancient "
"technologies."
msgstr "A diferencia de muchas otras cosas en el espacio altcoin, los artefactos digitales tienen mérito. Es cierto que existen una gran cantidad de NFTs que son feos, estúpidos y fraudulentos. No obstante, también hay muchos que destacan por su increíble creatividad. La creación y colección de arte ha sido parte de la historia humana desde sus inicios, precediendo incluso al comercio y al dinero, que también son tecnologías ancestrales."

#: src/faq.md:302
msgid ""
"Bitcoin provides an amazing platform for creating and collecting digital "
"artifacts in a secure, decentralized way, that protects users and artists in "
"the same way that it provides an amazing platform for sending and receiving "
"value, and for all the same reasons."
msgstr ""
"Bitcoin ofrece una plataforma increíble para la creación y colección de artefactos "
"digitales de manera segura y descentralizada, protegiendo tanto a usuarios como a artistas, al igual que facilita una plataforma confiable para transmitir y recibir valor."

#: src/faq.md:307
msgid ""
"Ordinals and inscriptions increase demand for Bitcoin block space, which "
"increase Bitcoin's security budget, which is vital for safeguarding "
"Bitcoin's transition to a fee-dependent security model, as the block subsidy "
"is halved into insignificance."
msgstr ""
"Los Ordinals y las inscripciones aumentan la demanda de espacio en los bloques "
"de Bitcoin, lo que aumenta el presupuesto de seguridad de Bitcoin esto es vital "
"para salvaguardar la transición de Bitcoin a un modelo de seguridad dependiente de comisiones, a medida que el subsidio de bloque se reduce a una cantidad insignificante."

#: src/faq.md:312
msgid ""
"Inscription content is stored on-chain, and thus the demand for block space "
"for use in inscriptions is unlimited. This creates a buyer of last resort "
"for _all_ Bitcoin block space. This will help support a robust fee market, "
"which ensures that Bitcoin remains secure."
msgstr ""
"El contenido de la inscripción se guarda en la cadena de bloques de Bitcoin "
"y, debido a esto, la necesidad de espacio en los bloques para alojar inscripciones "
"es ilimitada. Esta dinámica establece una demanda constante por el espacio disponible "
"en los bloques de Bitcoin, favoreciendo la sustentación de un mercado de comisiones saludable, lo que a su vez contribuye a preservar la seguridad de Bitcoin."

#: src/faq.md:317
msgid ""
"Inscriptions also counter the narrative that Bitcoin cannot be extended or "
"used for new use-cases. If you follow projects like DLCs, Fedimint, "
"Lightning, Taro, and RGB, you know that this narrative is false, but "
"inscriptions provide a counter argument which is easy to understand, and "
"which targets a popular and proven use case, NFTs, which makes it highly "
"legible."
msgstr ""
"Las inscripciones también contrarrestan la narrativa de que no se puede ampliar o usar "
"Bitcoin para nuevos casos de uso. Si sigues proyectos como DLCs, Fedimint, "
"Lightning, Taro y RGB, sabes que esta narrativa es falsa, pero las "
"inscripciones proporcionan un contraargumento que es fácil de entender y que apunta a un caso de uso popular y que ha sido utilizado, NFTs, lo que lo hace muy atractivo."

#: src/faq.md:323
msgid ""
"If inscriptions prove, as the authors hope, to be highly sought after "
"digital artifacts with a rich history, they will serve as a powerful hook "
"for Bitcoin adoption: come for the fun, rich art, stay for the decentralized "
"digital money."
msgstr ""
"Si las inscripciones se demuestran, como esperan los autores, ser artefactos "
"digitales muy buscados con una historia rica, servirán como un poderoso gancho para la adopción de Bitcoin: ven por el arte divertido, quédate por el dinero digital descentralizado."

#: src/faq.md:327
msgid ""
"Inscriptions are an extremely benign source of demand for block space. "
"Unlike, for example, stablecoins, which potentially give large stablecoin "
"issuers influence over the future of Bitcoin development, or DeFi, which "
"might centralize mining by introducing opportunities for MEV, digital art "
"and collectables on Bitcoin, are unlikely to produce individual entities "
"with enough power to corrupt Bitcoin. Art is decentralized."
msgstr ""
"Las inscripciones son una fuente extremadamente benigna de demanda de espacio"
"en el bloque. A diferencia de, por ejemplo, las stablecoins, que potencialmente "
"dan a los emisores de stablecoins grandes influencia sobre el futuro desarrollo "
"de Bitcoin, o DeFi, que podría centralizar la minería introduciendo oportunidades "
"para MEV, el arte digital y coleccionables en Bitcoin, es poco probable "
"que produzcan entidades individuales con suficiente poder para corromper "
"Bitcoin. El arte es descentralizado."

#: src/faq.md:334
msgid ""
"Inscription users and service providers are incentivized to run Bitcoin full "
"nodes, to publish and track inscriptions, and thus throw their economic "
"weight behind the honest chain."
msgstr ""
"Se les incentiva a los usuarios de inscripciones y a los proveedores de servicios ejecutar nodos de Bitcoin, para publicar y rastrear inscripciones, y así apoyar respaldar la cadena."

#: src/faq.md:338
msgid ""
"Ordinal theory and inscriptions do not meaningfully affect Bitcoin's "
"fungibility. Bitcoin users can ignore both and be unaffected."
msgstr ""
"La teoría ordinal y las inscripciones no afectan en alguna escala significante la fungibilidad de Bitcoin. Los usuarios de Bitcoin pueden ignorar ambos y no verse afectados."

#: src/faq.md:341
msgid ""
"We hope that ordinal theory strengthens and enriches bitcoin, and gives it "
"another dimension of appeal and functionality, enabling it more effectively "
"serve its primary use case as humanity's decentralized store of value."
msgstr ""
"Esperamos que la teoría ordinal fortalezca y enriquezca a bitcoin, y le dé "
"otra dimensión de atractividad y funcionalidad, permitiéndole servir de manera más efectiva a su caso de uso principal como el almacenamiento descentralizado de valor de para la humanidad."

#: src/contributing.md:1
msgid "Contributing to `ord`"
msgstr "Contribuir a `ord`"

#: src/contributing.md:4
msgid "Suggested Steps"
msgstr "Pasos Sugeridos"

#: src/contributing.md:7
msgid "Find an issue you want to work on."
msgstr "Encuentra un problema en el que quieras trabajar."

#: src/contributing.md:8
msgid ""
"Figure out what would be a good first step towards resolving the issue. This "
"could be in the form of code, research, a proposal, or suggesting that it be "
"closed, if it's out of date or not a good idea in the first place."
msgstr ""
"Determina cuál debería ser el primer paso para abordar el problema. "
"Esto podría involucrar código, investigación, la elaboración de una propuesta o incluso sugerir su cierre si está desactualizado o evaluar si es una buena idea desde un inicio."

#: src/contributing.md:11
msgid ""
"Comment on the issue with an outline of your suggested first step, and "
"asking for feedback. Of course, you can dive in and start writing code or "
"tests immediately, but this avoids potentially wasted effort, if the issue "
"is out of date, not clearly specified, blocked on something else, or "
"otherwise not ready to implement."
msgstr ""
"Comenta sobre el problema con un esquema de tu primer paso sugerido y pide opiniones. Podrías comenzar a escribir código o hacer pruebas de inmediato, pero esto evita que se haga un esfuerzo potencialmente desperdiciado."

#: src/contributing.md:16
msgid ""
"If the issue requires a code change or bugfix, open a draft PR with tests, "
"and ask for feedback. This makes sure that everyone is on the same page "
"about what needs to be done, or what the first step in solving the issue "
"should be. Also, since tests are required, writing the tests first makes it "
"easy to confirm that the change can be tested easily."
msgstr ""
"Si el problema requiere un cambio de código o corrección de bugs, "
"abre un PR preliminar con pruebas y pide sugerencias. Esto asegura que todos esten"
"de acuerdo acerca de lo que se debe de hacer, o el primer paso en solucionar el"
"problema. Como se requieren pruebas escribirlas y probarlas confirma que se pueden "
"llevar a cabo fácilmente"

#: src/contributing.md:21
msgid ""
"Mash the keyboard randomly until the tests pass, and refactor until the code "
"is ready to submit."
msgstr "Escribe código hasta que las pruebas pasen y refactorizar hasta que el código esté listo para enviar."

#: src/contributing.md:23
msgid "Mark the PR as ready to review."
msgstr "Marca el PR como listo para revisar."

#: src/contributing.md:24
msgid "Revise the PR as needed."
msgstr "Revisa el PR según sea necesario."

#: src/contributing.md:25
msgid "And finally, mergies!"
msgstr "¡Por último, fusiónalo!"

#: src/contributing.md:27
msgid "Start small"
msgstr "Comienza por las cosas pequeñas "

#: src/contributing.md:30
msgid ""
"Small changes will allow you to make an impact quickly, and if you take the "
"wrong tack, you won't have wasted much time."
msgstr "Los cambios pequeños te permitirán tener un impacto rápidamente, y si eliges el enfoque equivocado, no habrás perdido mucho tiempo."

#: src/contributing.md:33
msgid "Ideas for small issues:"
msgstr "Ideas para problemas pequeños:"

#: src/contributing.md:34
msgid "Add a new test or test case that increases test coverage"
msgstr "Añadir una prueba nueva o un caso de prueba que amplíe la cobertura de las pruebas."

#: src/contributing.md:35
msgid "Add or improve documentation"
msgstr "Mejorar o añadir a la documentación existente."

#: src/contributing.md:36
msgid ""
"Find an issue that needs more research, and do that research and summarize "
"it in a comment"
msgstr ""
"Identificar un problema que requiera más investigación, realizarla y resumir los hallazgos en un comentario."

#: src/contributing.md:38
msgid "Find an out-of-date issue and comment that it can be closed"
msgstr "Encontrar un problema que ha quedado desactualizado y sugerir su cierre a través de un comentario."

#: src/contributing.md:39
msgid ""
"Find an issue that shouldn't be done, and provide constructive feedback "
"detailing why you think that is the case"
msgstr ""
"Encuentra un caso que no consideras un problema y proporciona tu opinión explicando por qué crees que ese el caso."

#: src/contributing.md:42
msgid "Merge early and often"
msgstr "Fusiona pronto y a menudo"

#: src/contributing.md:45
msgid ""
"Break up large tasks into multiple smaller steps that individually make "
"progress. If there's a bug, you can open a PR that adds a failing ignored "
"test. This can be merged, and the next step can be to fix the bug and "
"unignore the test. Do research or testing, and report on your results. Break "
"a feature into small sub-features, and implement them one at a time."
msgstr ""
"Divide las tareas grandes en partes con las cuales se puede progresar por separado. "
"Si hay un error, puedes abrir un PR que agregue una prueba fallida ignorada. Esto se "
"puede fusionar, y el siguiente paso puede ser corregir el error y desactivar la prueba. "
"Realiza investigaciones o pruebas y reporta tus resultados. Divide una característica en subcategorías pequeñas e impleméntalas una a la vez."

#: src/contributing.md:51
msgid ""
"Figuring out how to break down a larger PR into smaller PRs where each can "
"be merged is an art form well-worth practicing. The hard part is that each "
"PR must itself be an improvement."
msgstr ""
"Descomponer un PR grande en PRs más pequeños que puedan fusionarse individualmente es un arte que vale la pena practicar. El desafío radica en asegurar que cada PR represente una mejora por sí mismo."

#: src/contributing.md:55
msgid ""
"I strive to follow this advice myself, and am always better off when I do."
msgstr ""
"Me esfuerzo por aplicar este consejo yo mismo, y siempre obtengo beneficios cuando lo hago."

#: src/contributing.md:57
msgid ""
"Small changes are fast to write, review, and merge, which is much more fun "
"than laboring over a single giant PR that takes forever to write, review, "
"and merge. Small changes don't take much time, so if you need to stop "
"working on a small change, you won't have wasted much time as compared to a "
"larger change that represents many hours of work. Getting a PR in quickly "
"improves the project a little bit immediately, instead of having to wait a "
"long time for larger improvement. Small changes are less likely to "
"accumulate merge conflict. As the Athenians said: _The fast commit what they "
"will, the slow merge what they must._"
msgstr ""
"Los cambios pequeños son rápidos de escribir, revisar y fusionar, lo que es mucho "
"más divertido que trabajar en un solo PR gigante que tarda una eternidad en escribirse, "
"revisarse y fusionarse. Los cambios pequeños no toman mucho tiempo, así que, si necesitas "
"dejar de trabajar en un cambio pequeño, no habrás perdido mucho tiempo a comparación "
"de un cambio más grande en las que se invirtieron muchas horas de trabajo. Conseguir "
"que un PR se fusione rápidamente mejora el proyecto de inmediato, en lugar de tener "
"que esperar mucho tiempo para una mejora más grande. Los cambios pequeños tienen "
"menos probabilidades de acumular conflictos de fusión. _Como decían los atenienses: "
"_Los rápidos hacen lo que quieren, los lentos hacen lo que deben._"

#: src/contributing.md:67
msgid "Get help"
msgstr "Busca ayuda"

#: src/contributing.md:70
msgid ""
"If you're stuck for more than 15 minutes, ask for help, like a Rust Discord, "
"Stack Exchange, or in a project issue or discussion."
msgstr ""
"Si te ves atascado por más de 15 minutos, busca ayuda en espacios como el "
"Discord de Rust, en Stack Exchange, o en una discusión sobre el problema dentro del proyecto."

#: src/contributing.md:73
msgid "Practice hypothesis-driven debugging"
msgstr "Practica la depuración basada en hipótesis"

#: src/contributing.md:76
msgid ""
"Formulate a hypothesis as to what is causing the problem. Figure out how to "
"test that hypothesis. Perform that tests. If it works, great, you fixed the "
"issue or now you know how to fix the issue. If not, repeat with a new "
"hypothesis."
msgstr ""
"Formula una hipótesis sobre la causa del problema. Define cómo podrías verificar "
"esa hipótesis. Ejecuta las pruebas correspondientes. Si funciona, genial, has "
"solucionado el problema o, al menos, ahora sabes cómo hacerlo. Si no, vuelve a "
"empezar con una nueva hipótesis."

#: src/contributing.md:81
msgid "Pay attention to error messages"
msgstr "Presta atención a los mensajes de error"

#: src/contributing.md:84
msgid "Read all error messages and don't tolerate warnings."
msgstr "Lee todos los mensajes de error y no toleres las advertencias."

#: src/donate.md:4
msgid ""
"Ordinals is open-source and community funded. The current lead maintainer of "
"`ord` is [raphjaph](https://github.com/raphjaph/). Raph's work on `ord` is "
"entirely funded by donations. If you can, please consider donating!"
msgstr ""
"Ordinals es de código abierto y financiado por la comunidad. El encargado "
"principal de `ord` actualmente es [raphjaph](https://github.com/raphjaph/). "
"El trabajo de Raph en `ord` está financiado íntegramente por donaciones. "
"¡Si puedes, considera hacer una donación!"

#: src/donate.md:8
msgid ""
"The donation address for Bitcoin is "
"[**************************************************************](https://mempool.space/address/**************************************************************). "
"The donation address for inscriptions is "
"[**************************************************************](https://mempool.space/address/**************************************************************)."
msgstr ""
"La dirección de donaciones en Bitcoin es "
"[**************************************************************](https://mempool.space/address/**************************************************************). "
"La dirección de donaciones de inscripciones es "
"[**************************************************************](https://mempool.space/address/**************************************************************)."

#: src/donate.md:11
msgid ""
"Both addresses are in a 2 of 4 multisig wallet with keys held by "
"[raphjaph](https://twitter.com/raphjaph), "
"[erin](https://twitter.com/realizingerin), "
"[rodarmor](https://twitter.com/rodarmor), and "
"[ordinally](https://twitter.com/veryordinally)."
msgstr ""
"Ambas direcciones están en un monedero multisig 2 de 4 con las llaves en manos de "
"[raphjaph](https://twitter.com/raphjaph), "
"[erin](https://twitter.com/realizingerin), "
"[rodarmor](https://twitter.com/rodarmor) y "
"[ordinally](https://twitter.com/veryordinally)."

#: src/donate.md:17
msgid ""
"Donations received will go towards funding maintenance and development of "
"`ord`, as well as hosting costs for [ordinals.com](https://ordinals.com)."
msgstr ""
"Las donaciones recibidas se utilizarán para financiar el mantenimiento y "
"desarrollo de `ord`, así como para cubrir los costos asociados con el alojamiento de [ordinals.com]( https://ordinals.com/)."

#: src/donate.md:20
msgid "Thank you for donating!"
msgstr "¡Gracias por donar!"

#: src/guides.md:1
msgid "Ordinal Theory Guides"
msgstr "Guías de la Teoría Ordinal"

#: src/guides.md:4
msgid ""
"See the table of contents for a list of guides, including a guide to the "
"explorer, a guide for sat hunters, and a guide to inscriptions."
msgstr ""
"Consulta la tabla de contenido para ver una lista de guías, incluyendo "
"una guía para el explorador, una guía para cazadores de sats y una guía de las inscripciones."

#: src/guides/explorer.md:1
msgid "Ordinal Explorer"
msgstr "Explorador Ordinal"

#: src/guides/explorer.md:4
msgid ""
"The `ord` binary includes a block explorer. We host a instance of the block "
"explorer on mainnet at [ordinals.com](https://ordinals.com), and on signet "
"at [signet.ordinals.com](https://signet.ordinals.com)."
msgstr ""
"El binario `ord` incluye un explorador de bloques. Alojamos una instancia "
"del explorador de bloques en la mainnet en [ordinals.com](https://ordinals.com/) "
"y en signet en [signet.ordinals.com](https://signet.ordinals.com/)."

#: src/guides/explorer.md:8
msgid "Running The Explorer"
msgstr "Ejecutando El Explorador"

#: src/guides/explorer.md:9
msgid "The server can be run locally with:"
msgstr "El servidor puede ser ejecutado localmente utilizando:"

#: src/guides/explorer.md:11
msgid "`ord server`"
msgstr "`ord server`"

#: src/guides/explorer.md:13
msgid "To specify a port add the `--http-port` flag:"
msgstr "Para especificar un puerto agrega la bandera (flag) `--http-port`:"

#: src/guides/explorer.md:15
msgid "`ord server --http-port 8080`"
msgstr "` ord server --http-port 8080`"

#: src/guides/explorer.md:17
msgid "To test how your inscriptions will look you can run:"
msgstr "Para ver como se verían tus inscripciones puedes ejecutar esto:"

#: src/guides/explorer.md:19
msgid "`ord preview <FILE1> <FILE2> ...`"
msgstr "`ord preview <ARCHIVO1> <ARCHIVO2> ...`"

#: src/guides/explorer.md:21
msgid "Search"
msgstr "Búsqueda"

#: src/guides/explorer.md:24
msgid "The search box accepts a variety of object representations."
msgstr "El cuadro de búsqueda acepta una variedad de representaciones de objetos."

#: src/guides/explorer.md:26
msgid "Blocks"
msgstr "Bloques"

#: src/guides/explorer.md:28
msgid "Blocks can be searched by hash, for example, the genesis block:"
msgstr "Los bloques pueden buscarse por hash, por ejemplo, este es el bloque génesis:"

#: src/guides/explorer.md:30
msgid ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://ordinals.com/search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"
msgstr ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://ordinals.com/search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"

#: src/guides/explorer.md:32
msgid "Transactions"
msgstr "Transacciones"

#: src/guides/explorer.md:34
msgid ""
"Transactions can be searched by hash, for example, the genesis block "
"coinbase transaction:"
msgstr ""
"Las transacciones pueden buscarse por su hash, por ejemplo, esta es la "
"transacción coinbase del bloque génesis:"

#: src/guides/explorer.md:37
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"

#: src/guides/explorer.md:39
msgid "Outputs"
msgstr "Salidas"

#: src/guides/explorer.md:41
msgid ""
"Transaction outputs can searched by outpoint, for example, the only output "
"of the genesis block coinbase transaction:"
msgstr ""
"Las salidas de las transacciones se pueden buscar por outpoint, por ejemplo, "
"esta la única salida de la transacción coinbase del bloque génesis:"

#: src/guides/explorer.md:44
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"
msgstr "[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"

#: src/guides/explorer.md:46
msgid "Sats"
msgstr "Sats"

#: src/guides/explorer.md:48
msgid ""
"Sats can be searched by integer, their position within the entire bitcoin "
"supply:"
msgstr ""
"Los sats se pueden buscar por número entero (integer), que representa su posición dentro del suministro total de bitcoin:"

#: src/guides/explorer.md:51
msgid "[2099994106992659](https://ordinals.com/search/2099994106992659)"
msgstr "[2099994106992659](https://ordinals.com/search/2099994106992659)"

#: src/guides/explorer.md:53
msgid "By decimal, their block and offset within that block:"
msgstr "Por decimal, su bloque y desplazamiento dentro de ese bloque:"

#: src/guides/explorer.md:55
msgid "[481824.0](https://ordinals.com/search/481824.0)"
msgstr "[481824.0](https://ordinals.com/search/481824.0)"

#: src/guides/explorer.md:57
msgid ""
"By degree, their cycle, blocks since the last halving, blocks since the last "
"difficulty adjustment, and offset within their block:"
msgstr "Por grado sexagesimal, su ciclo, bloques desde el ultimo halving, bloques desde el último ajuste de dificultad y desplazamiento dentro de su bloque:"

#: src/guides/explorer.md:60
msgid "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"
msgstr "[1°0′0″0‴](https://ordinals.com/search/1%C2%B00%E2%80%B20%E2%80%B30%E2%80%B4)"

#: src/guides/explorer.md:62
msgid ""
"By name, their base 26 representation using the letters \"a\" through \"z\":"
msgstr ""
"Por nombre, utilizando su representación en base 26 con letras de la \"a\" hasta la \"z\":"

#: src/guides/explorer.md:64
msgid "[ahistorical](https://ordinals.com/search/ahistorical)"
msgstr "[ahistorical](https://ordinals.com/search/ahistorical)"

#: src/guides/explorer.md:66
msgid ""
"Or by percentile, the percentage of bitcoin's supply that has been or will "
"have been issued when they are mined:"
msgstr ""
"O por percentil, el cual representa el porcentaje del suministro total de bitcoin que ha sido o será emitido una vez sean minados:"

#: src/guides/explorer.md:69
msgid "[100%](https://ordinals.com/search/100%)"
msgstr "[100%](https://ordinals.com/search/100%)"

#: src/guides/inscriptions.md:1
msgid "Ordinal Inscription Guide"
msgstr "Guía de inscripciones Ordinal"

#: src/guides/inscriptions.md:4
msgid ""
"Individual sats can be inscribed with arbitrary content, creating "
"Bitcoin-native digital artifacts that can be held in a Bitcoin wallet and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"Cada sat puede ser inscrito con contenido arbitrario, permitiendo la creación "
"de artefactos digitales únicos nativos en Bitcoin. Estos artefactos digitales "
"pueden almacenarse en monederos de Bitcoin y transferirse mediante transacciones "
"de Bitcoin. Las inscripciones son tan duraderas, inmutables, seguras y "
"descentralizadas como el propio Bitcoin."

#: src/guides/inscriptions.md:9
msgid ""
"Working with inscriptions requires a Bitcoin full node, to give you a view "
"of the current state of the Bitcoin blockchain, and a wallet that can create "
"inscriptions and perform sat control when constructing transactions to send "
"inscriptions to another wallet."
msgstr ""
"Trabajar con inscripciones requiere de un nodo de Bitcoin para darte una visión "
"del estado actual de la blockchain de Bitcoin, además de un monedero capaz de "
"crear inscripciones y realizar control de sats a la hora de construir transacciones "
"para enviar inscripciones a otro monedero."

#: src/guides/inscriptions.md:14
msgid ""
"Bitcoin Core provides both a Bitcoin full node and wallet. However, the "
"Bitcoin Core wallet cannot create inscriptions and does not perform sat "
"control."
msgstr ""
"Bitcoin Core proporciona un nodo completo de Bitcoin y un monedero. "
"Sin embargo, el monedero de Bitcoin Core no puede crear inscripciones y no realiza control de sats."

#: src/guides/inscriptions.md:17
msgid ""
"This requires [`ord`](https://github.com/ordinals/ord), the ordinal utility. "
"`ord` doesn't implement its own wallet, so `ord wallet` subcommands interact "
"with Bitcoin Core wallets."
msgstr ""
"Para esto se requiere [`ord`](https://github.com/ordinals/ord), "
"la utilidad de ordinals. `ord` no implementa su propio monedero, por lo "
"que los subcomandos del `monedero ord` interactúan con los monederos de Bitcoin Core."

#: src/guides/inscriptions.md:21
msgid "This guide covers:"
msgstr "Esta guía cubre:"

#: src/guides/inscriptions.md:23
#: src/guides/inscriptions.md:39
msgid "Installing Bitcoin Core"
msgstr "La instalación de Bitcoin Core"

#: src/guides/inscriptions.md:24
msgid "Syncing the Bitcoin blockchain"
msgstr "Sincronización de la blockchain de Bitcoin"

#: src/guides/inscriptions.md:25
msgid "Creating a Bitcoin Core wallet"
msgstr "Creación de un monedero de Bitcoin Core"

#: src/guides/inscriptions.md:26
msgid "Using `ord wallet receive` to receive sats"
msgstr "Utilizando `ord wallet receive` para recibir sats"

#: src/guides/inscriptions.md:27
msgid "Creating inscriptions with `ord wallet inscribe`"
msgstr "Creación de inscripciones usando `ord wallet inscribe`"

#: src/guides/inscriptions.md:28
msgid "Sending inscriptions with `ord wallet send`"
msgstr "Enviar inscripciones usando `ord wallet send`"

#: src/guides/inscriptions.md:29
msgid "Receiving inscriptions with `ord wallet receive`"
msgstr "Recibir inscripciones con `ord wallet receive`"

#: src/guides/inscriptions.md:31
msgid "Getting Help"
msgstr "Obtener Ayuda"

#: src/guides/inscriptions.md:34
msgid ""
"If you get stuck, try asking for help on the [Ordinals Discord "
"Server](https://discord.com/invite/87cjuz4FYg), or checking GitHub for "
"relevant [issues](https://github.com/ordinals/ord/issues) and "
"[discussions](https://github.com/ordinals/ord/discussions)."
msgstr ""
"Si te atascas, intenta pedir ayuda en el "
"[Servidor de Discord de Ordinals](https://discord.com/invite/87cjuz4FYg), "
"o consulta el GitHub por [problemas](https://github.com/ordinals/ord/issues) "
"y [discusiones](https://github.com/ordinals/ord/discussions) relevantes."

#: src/guides/inscriptions.md:42
msgid ""
"Bitcoin Core is available from [bitcoincore.org](https://bitcoincore.org/) "
"on the [download page](https://bitcoincore.org/en/download/)."
msgstr "Bitcoin Core está disponible en la "
"[página de descargas](https://bitcoincore.org/en/download/) de "
"[bitcoincore.org](https://bitcoincore.org/)."

#: src/guides/inscriptions.md:45
msgid "Making inscriptions requires Bitcoin Core 24 or newer."
msgstr "Para crear inscripciones es necesario tener la versión 24 de Bitcoin Core o una más reciente."

#: src/guides/inscriptions.md:47
msgid ""
"This guide does not cover installing Bitcoin Core in detail. Once Bitcoin "
"Core is installed, you should be able to run `bitcoind -version` "
"successfully from the command line."
msgstr ""
"Esta guía no cubre los detalles de la instalación de Bitcoin Core. "
"Una vez que se ha instalado Bitcoin Core, deberías ser capaz de "
"ejecutar el comando `bitcoind -version` con éxito desde la línea de comandos."

#: src/guides/inscriptions.md:51
msgid "Configuring Bitcoin Core"
msgstr "Configuración de Bitcoin Core"

#: src/guides/inscriptions.md:54
msgid "`ord` requires Bitcoin Core's transaction index."
msgstr "`ord` requiere el índice de transacciones de Bitcoin Core."

#: src/guides/inscriptions.md:56
msgid ""
"To configure your Bitcoin Core node to maintain a transaction index, add the "
"following to your `bitcoin.conf`:"
msgstr ""
"Debes configurar tu nodo de Bitcoin Core para que mantenga un índice de transacciones. Agrega lo siguiente a tu `bitcoin.conf`:"

#: src/guides/inscriptions.md:59
#: src/guides/sat-hunting.md:30
msgid ""
"```\n"
"txindex=1\n"
"```"
msgstr ""
"```\n"
"txindex=1\n"
"```"

#: src/guides/inscriptions.md:63
msgid "Or, run `bitcoind` with `-txindex`:"
msgstr "O, ejecuta `bitcoind` con `-txindex`:"

#: src/guides/inscriptions.md:65
#: src/guides/inscriptions.md:74
msgid ""
"```\n"
"bitcoind -txindex\n"
"```"
msgstr ""
"```\n"
"bitcoind -txindex\n"
"```"

#: src/guides/inscriptions.md:69
msgid "Syncing the Bitcoin Blockchain"
msgstr "Sincronizando la Blockchain de Bitcoin"

#: src/guides/inscriptions.md:72
msgid "To sync the chain, run:"
msgstr "Para sincronizar la blockchain o cadena de bloques de bitcoin, ejecuta:"

#: src/guides/inscriptions.md:78
msgid "…and leave it running until `getblockcount`:"
msgstr "…y déjalo ejecutar hasta que `getblockcount`:"

#: src/guides/inscriptions.md:80
msgid ""
"```\n"
"bitcoin-cli getblockcount\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli getblockcount\n"
"```"

#: src/guides/inscriptions.md:84
msgid ""
"agrees with the block count on a block explorer like [the mempool.space "
"block explorer](https://mempool.space/). `ord` interacts with `bitcoind`, so "
"you should leave `bitcoind` running in the background when you're using "
"`ord`."
msgstr ""
"coincida con el recuento de bloques en un explorador de bloques como "
"[el explorador de bloques mempool.space](https://mempool.space/). `ord` "
"interactúa con `bitcoind`, así que debes dejar `bitcoind` ejecutándose en "
"segundo plano mientras estés usando `ord`."

#: src/guides/inscriptions.md:88
msgid "Installing `ord`"
msgstr "Instalación de ord"

#: src/guides/inscriptions.md:91
msgid ""
"The `ord` utility is written in Rust and can be built from "
"[source](https://github.com/ordinals/ord). Pre-built binaries are available "
"on the [releases page](https://github.com/ordinals/ord/releases)."
msgstr ""
"La utilidad `ord` está escrita en Rust y puede ser construida desde el "
"[código fuente](https://github.com/ordinals/ord). Los binarios preconstruidos están disponibles en la página de [lanzamientos](https://github.com/ordinals/ord/releases)."

#: src/guides/inscriptions.md:95
msgid "You can install the latest pre-built binary from the command line with:"
msgstr "Puedes instalar el último binario preconstruido desde la línea de comandos usando:"

#: src/guides/inscriptions.md:97
msgid ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"
msgstr ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"

#: src/guides/inscriptions.md:101
msgid "Once `ord` is installed, you should be able to run:"
msgstr "Una vez que ord esté instalado, deberías poder de ejecutar:"

#: src/guides/inscriptions.md:103
msgid ""
"```\n"
"ord --version\n"
"```"
msgstr ""
"```\n"
"ord --version\n"
"```"

#: src/guides/inscriptions.md:107
msgid "Which prints out `ord`'s version number."
msgstr "El cual muestra el número de versión de `ord`."

#: src/guides/inscriptions.md:109
msgid "Creating a Bitcoin Core Wallet"
msgstr "Creación de un monedero de Bitcoin Core"

#: src/guides/inscriptions.md:112
msgid ""
"`ord` uses Bitcoin Core to manage private keys, sign transactions, and "
"broadcast transactions to the Bitcoin network."
msgstr ""
"`ord` utiliza Bitcoin Core para gestionar claves privadas, firmar transacciones y transmitir transacciones a la red Bitcoin."

#: src/guides/inscriptions.md:115
msgid "To create a Bitcoin Core wallet named `ord` for use with `ord`, run:"
msgstr "Para crear un monedero de Bitcoin Core llamado `ord` que se utilizara con `ord`, ejecuta:"

#: src/guides/inscriptions.md:117
msgid ""
"```\n"
"ord wallet create\n"
"```"
msgstr ""
"```\n"
"ord wallet create\n"
"```"

#: src/guides/inscriptions.md:121
msgid "Receiving Sats"
msgstr "Recibir Sats"

#: src/guides/inscriptions.md:124
msgid ""
"Inscriptions are made on individual sats, using normal Bitcoin transactions "
"that pay fees in sats, so your wallet will need some sats."
msgstr "Las inscripciones se crean en sats individuales, a través del uso de transacciones estándar de Bitcoin que pagan comisiones en sats, razón por lo cual tu monedero necesitará tener algunos sats."

#: src/guides/inscriptions.md:127
msgid "Get a new address from your `ord` wallet by running:"
msgstr "Obtén una nueva dirección de tu monedero ord ejecutando:"

#: src/guides/inscriptions.md:129
#: src/guides/inscriptions.md:201
#: src/guides/inscriptions.md:229
msgid ""
"```\n"
"ord wallet receive\n"
"```"
msgstr ""
"```\n"
"ord wallet receive\n"
"```"

#: src/guides/inscriptions.md:133
msgid "And send it some funds."
msgstr "Y envíale algunos fondos."

#: src/guides/inscriptions.md:135
msgid "You can see pending transactions with:"
msgstr "Puedes ver las transacciones pendientes con:"

#: src/guides/inscriptions.md:137
#: src/guides/inscriptions.md:213
#: src/guides/inscriptions.md:240
msgid ""
"```\n"
"ord wallet transactions\n"
"```"
msgstr ""
"```\n"
"ord wallet transactions\n"
"```"

#: src/guides/inscriptions.md:141
msgid ""
"Once the transaction confirms, you should be able to see the transactions "
"outputs with `ord wallet outputs`."
msgstr ""
"Una vez que la transacción se confirme, deberías poder ver las salidas de la transacción con `ord wallet outputs`."

#: src/guides/inscriptions.md:144
msgid "Creating Inscription Content"
msgstr "Creación de Contenido para las Inscripciones"

#: src/guides/inscriptions.md:147
msgid ""
"Sats can be inscribed with any kind of content, but the `ord` wallet only "
"supports content types that can be displayed by the `ord` block explorer."
msgstr ""
"Los sats pueden ser inscritos con cualquier tipo de contenido, pero el monedero `ord` solo admite tipos de contenido que pueden ser mostrados por el explorador de bloques de `ord`."

#: src/guides/inscriptions.md:150
msgid ""
"Additionally, inscriptions are included in transactions, so the larger the "
"content, the higher the fee that the inscription transaction must pay."
msgstr ""
"Además, las inscripciones se incluyen en las transacciones, debido a esto, en cuanto más grande sea el contenido, mayor será la comisión que debe pagar por la transacción de la inscripción."

#: src/guides/inscriptions.md:153
msgid ""
"Inscription content is included in transaction witnesses, which receive the "
"witness discount. To calculate the approximate fee that an inscribe "
"transaction will pay, divide the content size by four and multiply by the "
"fee rate."
msgstr ""
"El contenido de inscripción se incluye en el testigo de la transacción, los "
"cuales reciben el descuento de testigo. Para calcular un aproximado de la comisión "
"que pagará la transacción de una inscripción, divide el tamaño del contenido por "
"cuatro y multiplica por la tasa de comisión."

#: src/guides/inscriptions.md:157
msgid ""
"Inscription transactions must be less than 400,000 weight units, or they "
"will not be relayed by Bitcoin Core. One byte of inscription content costs "
"one weight unit. Since an inscription transaction includes not just the "
"inscription content, limit inscription content to less than 400,000 weight "
"units. 390,000 weight units should be safe."
msgstr ""
"Las transacciones de inscripción deben tener un peso inferior a 400,000 unidades,"
"de lo contrario, no serán retransmitidas por Bitcoin Core. Un byte del contenido "
"de inscripción equivale a una unidad de peso. Considerando que una transacción de "
"inscripción abarca más que solo el contenido de la inscripción se debe restringir el "
"contenido de cada inscripción a menos de 400,000 unidades de peso. Para mantener un "
"margen de seguridad, se recomienda no exceder las 390,000 unidades de peso."

#: src/guides/inscriptions.md:163
msgid "Creating Inscriptions"
msgstr "Creación de Inscripciones"

#: src/guides/inscriptions.md:166
msgid "To create an inscription with the contents of `FILE`, run:"
msgstr "Para crear una inscripción con el contenido de `ARCHIVO`, ejecuta:"

#: src/guides/inscriptions.md:168
msgid ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE --file FILE\n"
"```"
msgstr ""
"```\n"
"ord wallet inscribe --fee-rate TARIFA_DE_COMISION ARCHIVO\n"
"```"

#: src/guides/inscriptions.md:172
msgid ""
"Ord will output two transactions IDs, one for the commit transaction, and "
"one for the reveal transaction, and the inscription ID. Inscription IDs are "
"of the form `TXIDiN`, where `TXID` is the transaction ID of the reveal "
"transaction, and `N` is the index of the inscription in the reveal "
"transaction."
msgstr ""
"Ord mostrará dos IDs de transacciones, uno para la transacción de compromiso, "
"uno para la transacción de revelación, y el ID de inscripción. Los IDs de inscripción "
"tienen el formato `TXIDiN`, donde `TXID` es el ID de la transacción de revelación, y `N` "
"es el índice de la inscripción en la transacción de revelación."

#: src/guides/inscriptions.md:177
msgid ""
"The commit transaction commits to a tapscript containing the content of the "
"inscription, and the reveal transaction spends from that tapscript, "
"revealing the content on chain and inscribing it on the first sat of the "
"input that contains the corresponding tapscript."
msgstr ""
"La transacción de compromiso se compromete a un tapscript que aloja el contenido "
"de la inscripción, mientras que la transacción de revelación consume ese tapscript, "
"revelando el contenido en la cadena e inscribiéndolo en el primer sat del input que contiene el tapscript correspondiente."

#: src/guides/inscriptions.md:182
msgid ""
"Wait for the reveal transaction to be mined. You can check the status of the "
"commit and reveal transactions using  [the mempool.space block "
"explorer](https://mempool.space/)."
msgstr ""
"Espera a que la transacción de revelación sea minada. Puedes verificar el estado de las transacciones de compromiso y revelación usando [el explorador de bloques mempool.space]( https://mempool.space/)."

#: src/guides/inscriptions.md:186
msgid ""
"Once the reveal transaction has been mined, the inscription ID should be "
"printed when you run:"
msgstr ""
"Una vez que la transacción de revelación haya sido minada, el ID de inscripción debería aparecer cuando ejecutes:"

#: src/guides/inscriptions.md:189
#: src/guides/inscriptions.md:220
#: src/guides/inscriptions.md:246
msgid ""
"```\n"
"ord wallet inscriptions\n"
"```"
msgstr ""
"```\n"
"ord wallet inscriptions\n"
"```"

#: src/guides/inscriptions.md:193
msgid ""
"And when you visit [the ordinals explorer](https://ordinals.com/) at "
"`ordinals.com/inscription/INSCRIPTION_ID`."
msgstr ""
"Y cuando visites [el explorador ordinals](https://ordinals.com/) en "
"`ordinals.com/inscription/INSCRIPTION_ID`."

#: src/guides/inscriptions.md:196
msgid "Sending Inscriptions"
msgstr "Enviar Inscripciones"

#: src/guides/inscriptions.md:199
msgid "Ask the recipient to generate a new address by running:"
msgstr "Pide al destinatario que genere una nueva dirección ejecutando:"

#: src/guides/inscriptions.md:205
msgid "Send the inscription by running:"
msgstr "Envía la inscripción ejecutando:"

#: src/guides/inscriptions.md:207
msgid ""
"```\n"
"ord wallet send --fee-rate <FEE_RATE> <ADDRESS> <INSCRIPTION_ID>\n"
"```"
msgstr ""
"```\n"
"ord wallet send --fee-rate <COMISIÓN> <DIRECCIÓN> <ID_INSCRIPCIÓN>\n"
"```"


#: src/guides/inscriptions.md:211
#: src/guides/inscriptions.md:239
msgid "See the pending transaction with:"
msgstr "Consulta la transacción con:"

#: src/guides/inscriptions.md:217
msgid ""
"Once the send transaction confirms, the recipient can confirm receipt by "
"running:"
msgstr ""
"Una vez que la transacción de envío haya sido confirmada, el destinatario podrá verificarlo ejecutando:"

#: src/guides/inscriptions.md:224
msgid "Receiving Inscriptions"
msgstr "Recibir Inscripciones"

#: src/guides/inscriptions.md:227
msgid "Generate a new receive address using:"
msgstr "Genera una nueva dirección de recepción usando:"

#: src/guides/inscriptions.md:233
msgid "The sender can transfer the inscription to your address using:"
msgstr "El remitente puede transferir la inscripción a tu dirección usando:"

#: src/guides/inscriptions.md:235
msgid ""
"```\n"
"ord wallet send ADDRESS INSCRIPTION_ID\n"
"```"
msgstr ""
"```\n"
"ord wallet send DIRECCIÓN ID_INSCRIPCIÓN\n"
"```"

#: src/guides/inscriptions.md:244
msgid ""
"Once the send transaction confirms, you can can confirm receipt by running:"
msgstr ""
"Una vez que la transacción de envío haya sido confirmada, puedes confirmar ejecutando:"

#: src/guides/sat-hunting.md:4
msgid ""
"_This guide is out of date. Since it was written, the `ord` binary was "
"changed to only build the full satoshi index when the `--index-sats` flag is "
"supplied. Additionally, `ord` now has a built-in wallet that wraps a Bitcoin "
"Core wallet. See `ord wallet --help`._"
msgstr "_Esta guía está desactualizada. Desde que se escribió, el binario `ord` "
"ord fue modificado para construir el índice completo de satoshis únicamente cuando "
"se utiliza la bandera `--index-sats`. Además, `ord` ahora tiene un monedero integrado que envuelve un monedero de Bitcoin Core. Ver `ord wallet –help`._"

#: src/guides/sat-hunting.md:9
msgid ""
"Ordinal hunting is difficult but rewarding. The feeling of owning a wallet "
"full of UTXOs, redolent with the scent of rare and exotic sats, is beyond "
"compare."
msgstr ""
"La caza de Ordinals es difícil pero gratificante. La sensación de poseer un monedero lleno de UTXOs, impregnado con el aroma de sats raros y exóticos, es incomparable."

#: src/guides/sat-hunting.md:12
msgid ""
"Ordinals are numbers for satoshis. Every satoshi has an ordinal number and "
"every ordinal number has a satoshi."
msgstr ""
"Los Ordinales son números para los satoshis. Cada satoshi tiene un número ordinal y cada número ordinal tiene un satoshi."

#: src/guides/sat-hunting.md:15
msgid "Preparation"
msgstr "Preparación"

#: src/guides/sat-hunting.md:18
msgid "There are a few things you'll need before you start."
msgstr "Antes de empezar, necesitarás algunas cosas."

#: src/guides/sat-hunting.md:20
msgid ""
"First, you'll need a synced Bitcoin Core node with a transaction index. To "
"turn on transaction indexing, pass `-txindex` on the command-line:"
msgstr ""
"Primero, necesitarás un nodo de Bitcoin Core sincronizado con un índice de transacciones. Para activar la indexación de transacciones, ejecuta `-txindex` en la línea de comandos:"

#: src/guides/sat-hunting.md:23
msgid ""
"```sh\n"
"bitcoind -txindex\n"
"```"
msgstr ""
"```sh\n"
"bitcoind -txindex\n"
"```"

#: src/guides/sat-hunting.md:27
msgid ""
"Or put the following in your [Bitcoin configuration "
"file](https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"
msgstr ""
"O escribe lo siguiente en tu archivo [de configuración de Bitcoin]"
"(https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"

#: src/guides/sat-hunting.md:34
msgid ""
"Launch it and wait for it to catch up to the chain tip, at which point the "
"following command should print out the current block height:"
msgstr ""
"Ejecutalo y espera hasta que llegue al final de la cadena; una vez hecho esto, el siguiente comando debería imprimir la altura del bloque actual:"

#: src/guides/sat-hunting.md:37
msgid ""
"```sh\n"
"bitcoin-cli getblockcount\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getblockcount\n"
"```"

#: src/guides/sat-hunting.md:41
msgid "Second, you'll need a synced `ord` index."
msgstr "En segundo lugar, vas a necesitar un índice de `ord` sincronizado."

#: src/guides/sat-hunting.md:43
msgid "Get a copy of `ord` from [the repo](https://github.com/ordinals/ord/)."
msgstr "Obtén una copia de ord desde [el repositorio](https://github.com/ordinals/ord/)."

#: src/guides/sat-hunting.md:45
msgid ""
"Run `RUST_LOG=info ord index`. It should connect to your bitcoin core node "
"and start indexing."
msgstr ""
"Ejecuta `RUST_LOG=info ord index`. Debería conectarse a tu nodo bitcoin core e iniciar el proceso de indexación."

#: src/guides/sat-hunting.md:48
msgid "Wait for it to finish indexing."
msgstr "Espera hasta que termine de indexar."

#: src/guides/sat-hunting.md:50
msgid "Third, you'll need a wallet with UTXOs that you want to search."
msgstr "En tercer lugar, necesitarás un monedero con los UTXOs que quieras analizar."

#: src/guides/sat-hunting.md:52
msgid "Searching for Rare Ordinals"
msgstr "Buscando Ordinals Raros"

#: src/guides/sat-hunting.md:55
msgid "Searching for Rare Ordinals in a Bitcoin Core Wallet"
msgstr "Buscando Ordinals Raros en un Monedero de Bitcoin Core"

#: src/guides/sat-hunting.md:57
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"searching for rare ordinals in a Bitcoin Core wallet is Easy. Assuming your "
"wallet is named `foo`:"
msgstr ""
"El comando `ord wallet` es solo un envoltorio alrededor de la API RPC de Bitcoin Core, así que buscar ordinals raros en un monedero de Bitcoin Core es fácil. Digamos que tu monedero se llama `foo`:"

#: src/guides/sat-hunting.md:61
msgid "Load your wallet:"
msgstr "Carga tu monedero:"

#: src/guides/sat-hunting.md:63
msgid ""
"```sh\n"
"bitcoin-cli loadwallet foo\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli loadwallet foo\n"
"```"

#: src/guides/sat-hunting.md:67
msgid "Display any rare ordinals wallet `foo`'s UTXOs:"
msgstr "2.	Visualiza los UTXOs raros del monedero de ordinales `foo`:"

#: src/guides/sat-hunting.md:69
#: src/guides/sat-hunting.md:132
#: src/guides/sat-hunting.md:233
msgid ""
"```sh\n"
"ord wallet sats\n"
"```"
msgstr ""
"```sh\n"
"ord wallet sats\n"
"```"

#: src/guides/sat-hunting.md:73
msgid "Searching for Rare Ordinals in a Non-Bitcoin Core Wallet"
msgstr "Buscando Ordinals Raros en un Monedero que no es de Bitcoin Core"

#: src/guides/sat-hunting.md:75
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"to search for rare ordinals in a non-Bitcoin Core wallet, you'll need to "
"import your wallet's descriptors into Bitcoin Core."
msgstr ""
"El comando `ord wallet` es solo un envoltorio alrededor de la API RPC de Bitcoin Core, "
"así que para buscar ordinales raros en un monedero que no es de Bitcoin Core, necesitarás importar los descriptores de tu monedero a Bitcoin Core."

#: src/guides/sat-hunting.md:79
msgid ""
"[Descriptors](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors.md) "
"describe the ways that wallets generate private keys and public keys."
msgstr ""
"Los [descriptores](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors.md) describen la manera en que los monederos generan llaves privadas y públicas."

#: src/guides/sat-hunting.md:82
msgid ""
"You should only import descriptors into Bitcoin Core for your wallet's "
"public keys, not its private keys."
msgstr "Solo deberías importar los descriptores en Bitcoin Core para las claves públicas de tu monedero, no para las claves privadas."

#: src/guides/sat-hunting.md:85
msgid ""
"If your wallet's public key descriptor is compromised, an attacker will be "
"able to see your wallet's addresses, but your funds will be safe."
msgstr ""
"Si el descriptor de llave pública de tu monedero está comprometido, un atacante podrá ver las direcciones de tu monedero, pero tus fondos estarán seguros."

#: src/guides/sat-hunting.md:88
msgid ""
"If your wallet's private key descriptor is compromised, an attacker can "
"drain your wallet of funds."
msgstr ""
"Si el descriptor de llave privada de tu monedero está comprometido, un "
"atacante podrá vaciar los fondos de tu monedero."

#: src/guides/sat-hunting.md:91
msgid ""
"Get the wallet descriptor from the wallet whose UTXOs you want to search for "
"rare ordinals. It will look something like this:"
msgstr ""
"Obtén el descriptor del monedero cuyos UTXOs quieres analizar para identificar si contiene ordinals raros. Se verá algo así:"

#: src/guides/sat-hunting.md:94
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\n"
"```"

#: src/guides/sat-hunting.md:98
msgid "Create a watch-only wallet named `foo-watch-only`:"
msgstr "Crea un monedero de solo lectura llamada `foo-solo-lectura`:"

#: src/guides/sat-hunting.md:100
msgid ""
"```sh\n"
"bitcoin-cli createwallet foo-watch-only true true\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli createwallet foo-solo-lectura true true\n"
"```"

#: src/guides/sat-hunting.md:104
msgid "Feel free to give it a better name than `foo-watch-only`!"
msgstr "¡Siéntete libre de asignarle un mejor nombre que `foo-solo-lectura`!"

#: src/guides/sat-hunting.md:106
msgid "Load the `foo-watch-only` wallet:"
msgstr "Carga el monedero `foo-solo-lectura`:"

#: src/guides/sat-hunting.md:108
#: src/guides/sat-hunting.md:199
msgid ""
"```sh\n"
"bitcoin-cli loadwallet foo-watch-only\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli loadwallet foo-solo-lectura\n"
"```"

#: src/guides/sat-hunting.md:112
msgid "Import your wallet descriptors into `foo-watch-only`:"
msgstr "Importa los descriptores de tu monedero a `foo-solo-lectura`:"

#: src/guides/sat-hunting.md:114
msgid ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\", "
"\"timestamp\":0 }]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\", "
"\"timestamp\":0 }]'\n"
"```"

#: src/guides/sat-hunting.md:119
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of `\"timestamp\"` instead of "
"`0`. This will reduce the time it takes for Bitcoin Core to search for your "
"wallet's UTXOs."
msgstr ""
"Si conoces la marca de tiempo Unix cuando tu monedero comenzó a recibir "
"transacciones, puedes usarla para el valor de `\"timestamp\"` en lugar de 0. "
"Esto reducirá el tiempo que Bitcoin Core tardará en buscar los UTXO en tu "
"monedero."

#: src/guides/sat-hunting.md:124
#: src/guides/sat-hunting.md:225
msgid "Check that everything worked:"
msgstr "Comprueba que todo haya funcionado correctamente:"

#: src/guides/sat-hunting.md:126
#: src/guides/sat-hunting.md:227
msgid ""
"```sh\n"
"bitcoin-cli getwalletinfo\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getwalletinfo\n"
"```"

#: src/guides/sat-hunting.md:130
#: src/guides/sat-hunting.md:231
msgid "Display your wallet's rare ordinals:"
msgstr "Visualiza los ordinals raros de tu monedero:"

#: src/guides/sat-hunting.md:136
msgid ""
"Searching for Rare Ordinals in a Wallet that Exports Multi-path Descriptors"
msgstr ""
"Buscando Ordinals Raros en un Monedero que Exporta Descriptores de Múltiples Rutas (multi-path)"

#: src/guides/sat-hunting.md:138
msgid ""
"Some descriptors describe multiple paths in one descriptor using angle "
"brackets, e.g., `<0;1>`. Multi-path descriptors are not yet supported by "
"Bitcoin Core, so you'll first need to convert them into multiple "
"descriptors, and then import those multiple descriptors into Bitcoin Core."
msgstr ""
"Algunos descriptores describen múltiples rutas en un descriptor utilizando paréntesis angular, "
"por ejemplo, <0;1>. Los descriptores de múltiples rutas aún no son compatibles con Bitcoin Core, "
"así que primero deberás convertirlos en múltiples descriptores y luego importarlos a Bitcoin Core."

#: src/guides/sat-hunting.md:143
msgid ""
"First get the multi-path descriptor from your wallet. It will look something "
"like this:"
msgstr ""
"En primer lugar, obtén el descriptor de múltiples rutas de tu monedero. "
"Se verá algo así:"

#: src/guides/sat-hunting.md:146
msgid ""
"```\n"
"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/<0;1>/*)#fw76ulgt\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/<0;1>/*)#fw76ulgt\n"
"```"

#: src/guides/sat-hunting.md:150
msgid "Create a descriptor for the receive address path:"
msgstr "Crea un descriptor para la ruta de dirección que lo recibirá:"

#: src/guides/sat-hunting.md:152
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)\n"
"```"

#: src/guides/sat-hunting.md:156
msgid "And the change address path:"
msgstr "Y la dirección que recibirá lo que sobra de bitcoin o el cambio:"

#: src/guides/sat-hunting.md:158
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)\n"
"```"

#: src/guides/sat-hunting.md:162
msgid ""
"Get and note the checksum for the receive address descriptor, in this case "
"`tpnxnxax`:"
msgstr ""
"Obtén y anota el checksum del descriptor de la dirección de recepción, en este "
"caso es `tpnxnxax`:"

#: src/guides/sat-hunting.md:165
msgid ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)'\n"
"```"

#: src/guides/sat-hunting.md:170
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src/guides/sat-hunting.md:180
msgid "And for the change address descriptor, in this case `64k8wnd7`:"
msgstr "Y para el descriptor de la dirección de cambio, en este caso `64k8wnd7`:"

#: src/guides/sat-hunting.md:182
msgid ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)'\n"
"```"

#: src/guides/sat-hunting.md:187
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src/guides/sat-hunting.md:197
msgid "Load the wallet you want to import the descriptors into:"
msgstr "Carga el monedero al cual deseas importar los descriptores:"

#: src/guides/sat-hunting.md:203
msgid ""
"Now import the descriptors, with the correct checksums, into Bitcoin Core."
msgstr "Ahora importa los descriptores, con los checksums correctos, a Bitcoin Core."

#: src/guides/sat-hunting.md:205
msgid ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"

#: src/guides/sat-hunting.md:220
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of the `\"timestamp\"` fields "
"instead of `0`. This will reduce the time it takes for Bitcoin Core to "
"search for your wallet's UTXOs."
msgstr ""
"Si conoces la marca de tiempo de Unix cuando tu monedero comenzó a recibir "
"transacciones por primera vez, puedes utilizarlo como el valor del campo `\"timestamp\"` en lugar de `0`. Esto reducirá el tiempo que tarda Bitcoin Core en buscar los UTXOs de tu monedero."

#: src/guides/sat-hunting.md:237
msgid "Exporting Descriptors"
msgstr "Exportar Descriptores"

#: src/guides/sat-hunting.md:241
msgid ""
"Navigate to the `Settings` tab, then to `Script Policy`, and press the edit "
"button to display the descriptor."
msgstr ""
"Haz clic en la pestaña `Settings`, luego en `Script Policy`, y presiona el botón de editar "
"para mostrar el descriptor."

#: src/guides/sat-hunting.md:244
msgid "Transferring Ordinals"
msgstr "Transferir Ordinals"

#: src/guides/sat-hunting.md:246
msgid ""
"The `ord` wallet supports transferring specific satoshis. You can also use "
"`bitcoin-cli` commands `createrawtransaction`, "
"`signrawtransactionwithwallet`, and `sendrawtransaction`, how to do so is "
"complex and outside the scope of this guide."
msgstr ""
"El monedero `ord` permite la transferencia de satoshis específicos. También "
"puedes usar los comandos `bitcoin-cli` tales como `createrawtransaction`, `signrawtransactionwithwallet` y `sendrawtransaction`, pero hacerlo es complejo y está fuera del alcance de esta guía."

#: src/guides/collecting.md:4
msgid ""
"Currently, [ord](https://github.com/ordinals/ord/) is the only wallet "
"supporting sat-control and sat-selection, which are required to safely store "
"and send rare sats and inscriptions, hereafter ordinals."
msgstr ""
"Actualmente, [ord](https://github.com/ordinals/ord/) es el único monedero que "
"tiene la funcionalidad de control de sat (sat-control) y selección de sat (sat-selection), funciones indispensables para almacenar y enviar de forma segura sats raros e inscripciones, ahora conocidos como ordinals."

#: src/guides/collecting.md:8
msgid ""
"The recommended way to send, receive, and store ordinals is with `ord`, but "
"if you are careful, it is possible to safely store, and in some cases send, "
"ordinals with other wallets."
msgstr ""
"La manera sugerida de enviar, recibir y guardar ordinals es utilizando `ord`, pero si se tiene cuidado, es posible almacenar de forma segura, y en ciertas circunstancias enviar, ordinals utilizando otros monederos."

#: src/guides/collecting.md:12
msgid ""
"As a general note, receiving ordinals in an unsupported wallet is not "
"dangerous. Ordinals can be sent to any bitcoin address, and are safe as long "
"as the UTXO that contains them is not spent. However, if that wallet is then "
"used to send bitcoin, it may select the UTXO containing the ordinal as an "
"input, and send the inscription or spend it to fees."
msgstr ""
"En términos generales, recibir ordinals en un monedero no compatible no es peligroso. "
"Los ordinals se pueden enviar a cualquier dirección de bitcoin, y son seguros siempre y "
"cuando el UTXO que los contiene no se gaste. Sin embargo, si luego se utiliza ese monedero "
"para enviar bitcoin, puede seleccionar el UTXO que contiene el ordinal como una entrada y por error enviar la inscripción o gastarlo en comisiones."

#: src/guides/collecting.md:18
msgid ""
"A [guide](./collecting/sparrow-wallet.md) to creating an `ord`\\-compatible "
"wallet with [Sparrow Wallet](https://sparrowwallet.com/), is available in "
"this handbook."
msgstr ""
"Hay una [guía](https://docs.ordinals.com/guides/collecting/sparrow-wallet.html) "
"disponible en este manual para crear un monedero compatible con `ord` usando el [Monedero Sparrow](https://sparrowwallet.com/)"

#: src/guides/collecting.md:21
msgid ""
"Please note that if you follow this guide, you should not use the wallet you "
"create to send BTC, unless you perform manual coin-selection to avoid "
"sending ordinals."
msgstr ""
"Ten presente que, si decides seguir esta guía, no deberías utilizar el monedero que creaste para enviar BTC, a menos que realices una selección manual de monedas para evitar enviar ordinals por error."

#: src/guides/collecting/sparrow-wallet.md:1
msgid "Collecting Inscriptions and Ordinals with Sparrow Wallet"
msgstr "Coleccionando Inscripciones y Ordinals con Sparrow"

#: src/guides/collecting/sparrow-wallet.md:4
msgid ""
"Users who cannot or have not yet set up the "
"[ord](https://github.com/ordinals/ord) wallet can receive inscriptions and "
"ordinals with alternative bitcoin wallets, as long as they are _very_ "
"careful about how they spend from that wallet."
msgstr ""
"Los usuarios que no pueden o no han configurado aún el monedero [ord](https://github.com/ordinals/ord) pueden "
"recibir inscripciones y ordinals usando monederos alternativos de bitcoin, siempre y cuando sean _muy_ cautelosos a la hora de realizar gastos desde ese monedero."

#: src/guides/collecting/sparrow-wallet.md:6
msgid ""
"This guide gives some basic steps on how to create a wallet with [Sparrow "
"Wallet](https://sparrowwallet.com/) which is compatible with `ord` and can "
"be later imported into `ord`"
msgstr ""
"Esta guía ofrece instrucciones básicas para crear un monedero con [Sparrow Wallet](https://sparrowwallet.com/) el cual es compatible con `ord` y podrá ser importado a `ord` en un futuro."

#: src/guides/collecting/sparrow-wallet.md:8
msgid "⚠️⚠️ Warning!! ⚠️⚠️"
msgstr "⚠️⚠️ ¡¡Advertencia!! ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:9
msgid ""
"As a general rule if you take this approach, you should use this wallet with "
"the Sparrow software as a receive-only wallet."
msgstr ""
"Como regla general, si tomas este enfoque, debes usar este monedero con el "
"software Sparrow solo como un monedero para recibir."

#: src/guides/collecting/sparrow-wallet.md:11
msgid ""
"Do not spend any satoshis from this wallet unless you are sure you know what "
"you are doing. You could very easily inadvertently lose access to your "
"ordinals and inscriptions if you don't heed this warning."
msgstr ""
"No gastes ningún satoshi de este monedero a menos que estés seguro de lo "
"que estás haciendo. Podrías perder fácilmente el acceso a tus ordinals e inscripciones si no haces caso a esta advertencia."

#: src/guides/collecting/sparrow-wallet.md:13
msgid "Wallet Setup & Receiving"
msgstr "Configuración del Monedero y Recibir"

#: src/guides/collecting/sparrow-wallet.md:15
msgid ""
"Download the Sparrow Wallet from the [releases "
"page](https://sparrowwallet.com/download/) for your particular operating "
"system."
msgstr "Descarga Sparrow Wallet [desde la página de descargas](https://sparrowwallet.com/download/) para tu sistema operativo específico."

#: src/guides/collecting/sparrow-wallet.md:17
msgid "Select `File -> New Wallet` and create a new wallet called `ord`."
msgstr "Selecciona `File -> New Wallet` y crea un nuevo monedero llamada `ord`."

#: src/guides/collecting/sparrow-wallet.md:19
msgid "![](images/wallet_setup_01.png)"
msgstr "![](images/wallet_setup_01.png)"

#: src/guides/collecting/sparrow-wallet.md:21
msgid ""
"Change the `Script Type` to `Taproot (P2TR)` and select the `New or Imported "
"Software Wallet` option."
msgstr "Cambia el `Script Type` (Tipo de script) a `Taproot (P2TR)` y selecciona la opción `New or Imported Software Wallet`."

#: src/guides/collecting/sparrow-wallet.md:23
msgid "![](images/wallet_setup_02.png)"
msgstr "![](images/wallet_setup_02.png)"

#: src/guides/collecting/sparrow-wallet.md:25
msgid ""
"Select `Use 12 Words` and then click `Generate New`. Leave the passphrase "
"blank."
msgstr "Selecciona `Use 12 Words` (Utilizar 12 palabras) y luego haz clic en `Generate New` (Generar nueva). Deja passphrase (Frase de contraseña) en blanco."

#: src/guides/collecting/sparrow-wallet.md:27
msgid "![](images/wallet_setup_03.png)"
msgstr "![](images/wallet_setup_03.png)"

#: src/guides/collecting/sparrow-wallet.md:29
msgid ""
"A new 12 word BIP39 seed phrase will be generated for you. Write this down "
"somewhere safe as this is your backup to get access to your wallet. NEVER "
"share or show this seed phrase to anyone else."
msgstr ""
"Se generará tu nueva frase semilla BIP39 de 12 palabras. Anota esto en un lugar seguro, ya que será tu respaldo para acceder a tu monedero. NUNCA compartas ni muestres esta frase semilla a nadie."

#: src/guides/collecting/sparrow-wallet.md:31
msgid "Once you have written down the seed phrase click `Confirm Backup`."
msgstr "Una vez que hayas anotado la frase semilla, haz clic en `Confirm Backup` (Confirmar copia de seguridad)."

#: src/guides/collecting/sparrow-wallet.md:33
msgid "![](images/wallet_setup_04.png)"
msgstr "![](images/wallet_setup_04.png)"

#: src/guides/collecting/sparrow-wallet.md:35
msgid ""
"Re-enter the seed phrase which you wrote down, and then click `Create "
"Keystore`."
msgstr "Ingresa la frase que anotaste y luego haz clic en` Create Keystore`."

#: src/guides/collecting/sparrow-wallet.md:37
msgid "![](images/wallet_setup_05.png)"
msgstr "![](images/wallet_setup_05.png)"

#: src/guides/collecting/sparrow-wallet.md:39
msgid "Click `Import Keystore`."
msgstr "Haz clic en `Import Keystore` (almacenamiento de llaves)."

#: src/guides/collecting/sparrow-wallet.md:41
msgid "![](images/wallet_setup_06.png)"
msgstr "![](images/wallet_setup_06.png)"

#: src/guides/collecting/sparrow-wallet.md:43
msgid "Click `Apply`. Add a password for the wallet if you want to."
msgstr "Haz clic en `Apply` (aplicar). Luego, agrega una contraseña al monedero si deseas"

#: src/guides/collecting/sparrow-wallet.md:45
msgid "![](images/wallet_setup_07.png)"
msgstr "![](images/wallet_setup_07.png)"

#: src/guides/collecting/sparrow-wallet.md:47
msgid ""
"You now have a wallet which is compatible with `ord`, and can be imported "
"into `ord` using the BIP39 Seed Phrase. To receive ordinals or inscriptions, "
"click on the `Receive` tab and copy a new address."
msgstr ""
"Ahora tienes un monedero que es compatible con `ord` y que puede ser importado "
"a `ord` utilizando la frase semilla BIP39. Para recibir ordinals o inscripciones, dirígete a la pestaña `Receive` (Recibir) y copia una nueva dirección."

#: src/guides/collecting/sparrow-wallet.md:49
msgid ""
"Each time you want to receive you should use a brand-new address, and not "
"re-use existing addresses."
msgstr ""
"Cada vez que quieras recibir, deberías usar una dirección completamente nueva y no reutilizar direcciones existentes."

#: src/guides/collecting/sparrow-wallet.md:51
msgid ""
"Note that bitcoin is different to some other blockchain wallets, in that "
"this wallet can generate an unlimited number of new addresses. You can "
"generate a new address by clicking on the `Get Next Address` button. You can "
"see all of your addresses in the `Addresses` tab of the app."
msgstr ""
"Ten en cuenta que bitcoin es diferente a algunos otros monederos de blockchain, "
"en el sentido de que el monedero de bitcoin puede generar un número ilimitado de "
"direcciones nuevas. Puedes generar una dirección nueva haciendo clic en el botón `Get Next Address` (Obtener la próxima dirección). Puedes ver todas tus direcciones en la pestaña `Addresses` (Direcciones)."

#: src/guides/collecting/sparrow-wallet.md:53
msgid ""
"You can add a label to each address, so you can keep track of what it was "
"used for."
msgstr ""
"Puedes asignar una etiqueta a cada dirección, permitiéndote llevar un seguimiento de su propósito o uso."

#: src/guides/collecting/sparrow-wallet.md:55
msgid "![](images/wallet_setup_08.png)"
msgstr "![](images/wallet_setup_08.png)"

#: src/guides/collecting/sparrow-wallet.md:57
msgid "Validating / Viewing Received Inscriptions"
msgstr "Validando / Viendo Inscripciones Recibidas"

#: src/guides/collecting/sparrow-wallet.md:59
msgid ""
"Once you have received an inscription you will see a new transaction in the "
"`Transactions` tab of Sparrow, as well as a new UTXO in the `UTXOs` tab."
msgstr ""
"Una vez que hayas recibido una inscripción, podrás observar una nueva transacción en la pestaña `Transactions` (transacciones) de Sparrow, así como un nuevo UTXO en la pestaña `UTXOs`."

#: src/guides/collecting/sparrow-wallet.md:61
msgid ""
"Initially this transaction may have an \"Unconfirmed\" status, and you will "
"need to wait for it to be mined into a bitcoin block before it is fully "
"received."
msgstr ""
"Inicialmente, esta transacción puede tener un estado \"Unconfirmed o No confirmado\", y tendrás que esperar a que sea minada en un bloque de bitcoin antes de que la recibas por completo."

#: src/guides/collecting/sparrow-wallet.md:63
msgid "![](images/validating_viewing_01.png)"
msgstr "![](images/validating_viewing_01.png)"

#: src/guides/collecting/sparrow-wallet.md:65
msgid ""
"To track the status of your transaction you can right-click on it,  select "
"`Copy Transaction ID` and then paste that transaction id into "
"[mempool.space](https://mempool.space)."
msgstr ""
"Para rastrear el estado de tu transacción, puedes hacer clic derecho sobre ella, seleccionar `Copy Transaction ID` (Copiar ID de Transacción) y luego pegar ese ID en el buscador de [mempool.space](https://mempool.space/)."

#: src/guides/collecting/sparrow-wallet.md:67
msgid "![](images/validating_viewing_02.png)"
msgstr "![](images/validating_viewing_02.png)"

#: src/guides/collecting/sparrow-wallet.md:69
msgid ""
"Once the transaction has confirmed, you can validate and view your "
"inscription by heading over to the `UTXOs` tab, finding the UTXO you want to "
"check, right-clicking on the `Output` and selecting `Copy Transaction "
"Output`. This transaction output id can then be pasted into the "
"[ordinals.com](https://ordinals.com) search."
msgstr ""
"Una vez que la transacción se confirme, puedes validar y visualizar tu inscripción dirigiéndote a la pestaña de "
"`UTXOs`, encuentra el UTXO que deseas verificar, y haz clic derecho sobre `Output` (Salida) y selecciona "
"`Copy Transaction Output` (Copiar Salida de Transacción). Puedes pegar este ID de salida de transacción en el buscador de [ordinals.com](https://ordinals.com/) para proceder con la verificación."

#: src/guides/collecting/sparrow-wallet.md:72
msgid "Freezing UTXO's"
msgstr "Congelar UTXO's"

#: src/guides/collecting/sparrow-wallet.md:73
msgid ""
"As explained above, each of your inscriptions is stored in an Unspent "
"Transaction Output (UTXO). You want to be very careful not to accidentally "
"spend your inscriptions, and one way to make it harder for this to happen is "
"to freeze the UTXO."
msgstr ""
"Como se explicó anteriormente, cada una de tus inscripciones está almacenada en "
"una Salida de Transacción No Gastado (UTXO). Debes tener mucho cuidado de no gastar accidentalmente tus inscripciones. Una manera de prevenir esto es congelar el UTXO correspondiente."

#: src/guides/collecting/sparrow-wallet.md:75
msgid ""
"To do this, go to the `UTXOs` tab, find the UTXO you want to freeze, "
"right-click on the `Output` and select `Freeze UTXO`."
msgstr ""
"Para hacerlo, ve a la pestaña `UTXOs`, encuentra el UTXO que deseas congelar, haz clic derecho en `Output` y selecciona `Frreeze UTXO` (Congelar UTXO)."

#: src/guides/collecting/sparrow-wallet.md:77
msgid ""
"This UTXO (Inscription) is now un-spendable within the Sparrow Wallet until "
"you unfreeze it."
msgstr ""
"Este UTXO (Inscripción) no se podrá gastar dentro del Monedero Sparrow hasta que lo descongeles."

#: src/guides/collecting/sparrow-wallet.md:79
msgid "Importing into `ord` wallet"
msgstr "Importando al monedero `ord`"

#: src/guides/collecting/sparrow-wallet.md:81
msgid ""
"For details on setting up Bitcoin Core and the `ord` wallet check out the "
"[Inscriptions Guide](../inscriptions.md)"
msgstr ""
"Para detalles sobre configurar Bitcoin Core y el monedero ord, revisa la "
"[Guía de Inscripciones Ordinal](../inscriptions.md)."

#: src/guides/collecting/sparrow-wallet.md:83
msgid ""
"When setting up `ord`, instead of running `ord wallet create` to create a "
"brand-new wallet, you can import your existing wallet using `ord wallet "
"restore \"BIP39 SEED PHRASE\"` using the seed phrase you generated with "
"Sparrow Wallet."
msgstr ""
"Cuando configures `ord`, en lugar de ejecutar` ord wallet create` para "
"crear un nuevo monedero, puedes importar tu monedero existente usando "
"`ord wallet restore \"BIP39 SEED PHRASE\"` con la frase semilla que generaste "
"en el monedero Sparrow."

#: src/guides/collecting/sparrow-wallet.md:85
msgid ""
"There is currently a [bug](https://github.com/ordinals/ord/issues/1589) "
"which causes an imported wallet to not be automatically rescanned against "
"the blockchain. To work around this you will need to manually trigger a "
"rescan using the bitcoin core cli: `bitcoin-cli -rpcwallet=ord "
"rescanblockchain 767430`"
msgstr ""
"Actualmente hay un [bug](https://github.com/ordinals/ord/issues/1589) que "
"impide que un monedero importado no se escanee automáticamente para encontrar "
"su contenido en la blockchain. Para solucionar esto tendrás que ejecutar "
"manualmente un escaneo usando el cli de bitcoin core: `bitcoin-cli -rpcwallet=ord rescanblockchain 767430`"

#: src/guides/collecting/sparrow-wallet.md:88
msgid ""
"You can then check your wallet's inscriptions using `ord wallet inscriptions`"
msgstr ""
"Luego puedes revisar las inscripciones de tu monedero usando `ord wallet inscriptions`"

#: src/guides/collecting/sparrow-wallet.md:90
msgid ""
"Note that if you have previously created a wallet with `ord`, then you will "
"already have a wallet with the default name, and will need to give your "
"imported wallet a different name. You can use the `--wallet` parameter in "
"all `ord` commands to reference a different wallet, eg:"
msgstr ""
"Ten en cuenta que si has creado previamente una cartera con `ord`, ya tendrás "
"una cartera con el nombre predeterminado y tendrás que darle un nombre diferente "
"a tu cartera importada. Puedes usar el parámetro `--wallet` en todos los comandos "
"de `ord` para hacer referencia a un monedero diferente, por ejemplo:"

#: src/guides/collecting/sparrow-wallet.md:92
msgid "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"
msgstr "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"

#: src/guides/collecting/sparrow-wallet.md:94
msgid "`ord --wallet ord_from_sparrow wallet inscriptions`"
msgstr "`ord --wallet ord_from_sparrow wallet inscriptions`"

#: src/guides/collecting/sparrow-wallet.md:96
msgid "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"
msgstr "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"

#: src/guides/collecting/sparrow-wallet.md:98
msgid "Sending inscriptions with Sparrow Wallet"
msgstr "Enviar inscripciones con el Monedero Sparrow"

#: src/guides/collecting/sparrow-wallet.md:100
msgid "⚠️⚠️ Warning ⚠️⚠️"
msgstr "⚠️⚠️ Advertencia ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:101
msgid ""
"While it is highly recommended that you set up a bitcoin core node and run "
"the `ord` software, there are certain limited ways you can send inscriptions "
"out of Sparrow Wallet in a safe way. Please note that this is not "
"recommended, and you should only do this if you fully understand what you "
"are doing."
msgstr ""
"Aunque es bastante recomendado que configures un nodo de bitcoin core y ejecutes el software `ord`, "
"existen algunas formas limitadas de enviar inscripciones desde el monedero Sparrow de manera segura. "
"Sin embargo, cabe señalar que esta no es la opción más recomendada y solo deberías proceder si entiendes "
"completamente las implicaciones y riesgos de lo que estás haciendo."

#: src/guides/collecting/sparrow-wallet.md:103
msgid ""
"Using the `ord` software will remove much of the complexity we are "
"describing here, as it is able to automatically and safely handle sending "
"inscriptions in an easy way."
msgstr ""
"Usar el software de `ord` eliminará gran parte de la complejidad que estamos "
"describiendo aquí, permitiéndote manejar el envío de inscripciones de forma automática "
"y segura con mayor facilidad."

#: src/guides/collecting/sparrow-wallet.md:105
msgid "⚠️⚠️ Additional Warning ⚠️⚠️"
msgstr "⚠️⚠️ Advertencia Adicional ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:106
msgid ""
"Don't use your sparrow inscriptions wallet to do general sends of "
"non-inscription bitcoin. You can setup a separate wallet in sparrow if you "
"need to do normal bitcoin transactions, and keep your inscriptions wallet "
"separate."
msgstr ""
"No uses tu monedero de inscripciones de Sparrow para hacer envíos de bitcoin que no involucren inscripciones. "
"Puedes configurar un monedero por separado en Sparrow para gestionar tus transacciones regulares de bitcoin, "
"manteniendo así tu monedero de inscripciones aislado."

#: src/guides/collecting/sparrow-wallet.md:108
msgid "Bitcoin's UTXO model"
msgstr "Modelo UTXO de Bitcoin"

#: src/guides/collecting/sparrow-wallet.md:109
msgid ""
"Before sending any transaction it's important that you have a good mental "
"model for bitcoin's Unspent Transaction Output (UTXO) system. The way "
"Bitcoin works is fundamentally different to many other blockchains such as "
"Ethereum. In Ethereum generally you have a single address in which you store "
"ETH, and you cannot differentiate between any of the ETH -  it is just all a "
"single value of the total amount in that address. Bitcoin works very "
"differently in that we generate a new address in the wallet for each "
"receive, and every time you receive sats to an address in your wallet you "
"are creating a new UTXO. Each UTXO can be seen and managed individually. You "
"can select specific UTXO's which you want to spend, and you can choose not "
"to spend certain UTXO's."
msgstr ""
"Antes de enviar cualquier transacción, es importante que entiendas a profundidad "
"cómo funciona el sistema de Salidas de Transacciones No Gastadas (UTXO) de Bitcoin. "
"La forma en que funciona Bitcoin es fundamentalmente diferente a muchas otras cadenas "
"de bloques, como Ethereum. En Ethereum, generalmente tienes una única dirección en la "
"que almacenas ETH, y no puedes diferenciar entre ninguno de los ETH: simplemente es "
"un valor único del total acumulado en esa dirección. Bitcoin funciona de manera muy diferente, "
"ya que generamos una nueva dirección en el monedero para cada recepción, y cada vez que recibes "
"sats en una dirección de tu monedero estás creando un nuevo UTXO. Cada UTXO se puede ver "
"y gestionar individualmente. Puedes seleccionar específicamente los UTXO que deseas gastar, "
"y puedes elegir no gastar ciertos UTXO."

#: src/guides/collecting/sparrow-wallet.md:111
msgid ""
"Some Bitcoin wallets do not expose this level of detail, and they just show "
"you a single summed up value of all the bitcoin in your wallet. However, "
"when sending inscriptions it is important that you use a wallet like Sparrow "
"which allows for UTXO control."
msgstr ""
"Algunos monederos de Bitcoin no exponen este nivel de detalle y solo te muestran "
"un valor único que suma todo el bitcoin en tu monedero. Sin embargo, al enviar "
"inscripciones, es importante que uses un monedero como Sparrow que permite el control de UTXO."

#: src/guides/collecting/sparrow-wallet.md:113
msgid "Inspecting your inscription before sending"
msgstr "Inspeccionando tu inscripción antes de enviarla"

#: src/guides/collecting/sparrow-wallet.md:114
msgid ""
"Like we have previously described inscriptions are inscribed onto sats, and "
"sats are stored within UTXOs. UTXO's are a collection of satoshis with some "
"particular value of the number of satoshis (the output value). Usually (but "
"not always) the inscription will be inscribed on the first satoshi in the "
"UTXO."
msgstr ""
"Como hemos descrito anteriormente, las inscripciones están inscritas en sats, "
"y los sats se almacenan dentro de los UTXO. Los UTXO son una colección de satoshis "
"con un valor particular del número de satoshis (el valor de salida). Usualmente "
"(pero no siempre) la inscripción estará inscrita en el primer satoshi del UTXO."

#: src/guides/collecting/sparrow-wallet.md:116
msgid ""
"When inspecting your inscription before sending the main thing you will want "
"to check is which satoshi in the UTXO your inscription is inscribed on."
msgstr ""
"Al inspeccionar tu inscripción antes de enviar, lo principal que querrás verificar "
"es en cual satoshi del UTXO está inscrita tu inscripción."

#: src/guides/collecting/sparrow-wallet.md:118
msgid ""
"To do this, you can follow the [Validating / Viewing Received "
"Inscriptions](./sparrow-wallet.md#validating--viewing-received-inscriptions) "
"described above to find the inscription page for your inscription on "
"ordinals.com"
msgstr ""
"Para hacer esto, puedes leer `Validando / Viendo las Inscripciones Recibidas` "
"descritas anteriormente para encontrar la página de inscripción de tu inscripción en ordinals.com"

#: src/guides/collecting/sparrow-wallet.md:120
msgid ""
"There you will find some metadata about your inscription which looks like "
"the following:"
msgstr ""
"Allí encontrarás algunos metadatos sobre tu inscripción, se verá así:"

#: src/guides/collecting/sparrow-wallet.md:122
msgid "![](images/sending_01.png)"
msgstr "![](images/sending_01.png)"

#: src/guides/collecting/sparrow-wallet.md:124
msgid "There is a few of important things to check here:"
msgstr "Hay varias cosas importantes que verificar en este punto:"

#: src/guides/collecting/sparrow-wallet.md:125
msgid ""
"The `output` identifier matches the identifier of the UTXO you are going to "
"send"
msgstr ""
"Que el identificador de `output` coincida con el identificador del UTXO que "
"vas a enviar"

#: src/guides/collecting/sparrow-wallet.md:126
msgid ""
"The `offset` of the inscription is `0` (this means that the inscription is "
"located on the first sat in the UTXO)"
msgstr ""
"Que el `offset` (desplazamiento) de la inscripción sea 0 (esto significa que la inscripción está ubicada en el primer sat del UTXO)"

#: src/guides/collecting/sparrow-wallet.md:127
msgid ""
"the `output_value` has enough sats to cover the transaction fee (postage) "
"for sending the transaction. The exact amount you will need depends on the "
"fee rate you will select for the transaction"
msgstr "Que el valor de ‘output_value’ tenga suficientes sats para cubrir la tarifa de transacción (postage) para enviar la transacción. La cantidad exacta que necesitarás dependerá de la tasa de comisión que seleccionarás para la transacción"

#: src/guides/collecting/sparrow-wallet.md:129
msgid ""
"If all of the above are true for your inscription, it should be safe for you "
"to send it using the method below."
msgstr ""
"Si todo lo anterior se sostiene para tu inscripción, deberías poder proceder con seguridad usando el siguiente método."

#: src/guides/collecting/sparrow-wallet.md:131
msgid ""
"⚠️⚠️ Be very careful sending your inscription particularly if the `offset` "
"value is not `0`. It is not recommended to use this method if that is the "
"case, as doing so you could accidentally send your inscription to a bitcoin "
"miner unless you know what you are doing."
msgstr ""
"⚠️⚠️ Ten mucho cuidado al enviar tu inscripción, especialmente si el `offset` "
"no es `0`. En tales casos, no se recomienda utilizar este método, ya que podrías "
"enviar accidentalmente tu inscripción a un minero de bitcoin a menos que sepas lo que estás haciendo."

#: src/guides/collecting/sparrow-wallet.md:133
msgid "Sending your inscription"
msgstr "Enviando tu inscripción"

#: src/guides/collecting/sparrow-wallet.md:134
msgid ""
"To send an inscription navigate to the `UTXOs` tab, and find the UTXO which "
"you previously validated contains your inscription."
msgstr ""
"Para enviar una inscripción, ve a la pestaña `UTXOs` y encuentra el UTXO que "
"previamente validaste y que contiene tu inscripción."

#: src/guides/collecting/sparrow-wallet.md:136
msgid ""
"If you previously froze the UXTO you will need to right-click on it and "
"unfreeze it."
msgstr ""
"Si anteriormente habías congelado este UTXO, "
"deberás hacer clic derecho sobre él para descongelarlo."

#: src/guides/collecting/sparrow-wallet.md:138
msgid ""
"Select the UTXO you want to send, and ensure that is the _only_ UTXO is "
"selected. You should see `UTXOs 1/1` in the interface. Once you are sure "
"this is the case you can hit `Send Selected`."
msgstr ""
"Selecciona el UTXO que deseas enviar, y asegúrate de que sea el _único_ "
"UTXO seleccionado. Deberías ver una indicación de `UTXOs 1/1` en la interfaz. "
"Una vez estés absolutamente seguro de haber seleccionado el UTXO correcto, "
"haz clic en `Send Selected` (Enviar seleccionados)."

#: src/guides/collecting/sparrow-wallet.md:140
msgid "![](images/sending_02.png)"
msgstr "![](images/sending_02.png)"

#: src/guides/collecting/sparrow-wallet.md:142
msgid ""
"You will then be presented with the transaction construction interface. "
"There is a few things you need to check here to make sure that this is a "
"safe send:"
msgstr ""
"Luego se te presentará la interfaz de construcción de transacciones. "
"Hay algunas cosas que debes verificar aquí para asegurarte de que se trata "
"de un envío seguro:"

#: src/guides/collecting/sparrow-wallet.md:144
msgid ""
"The transaction should have only 1 input, and this should be the UTXO with "
"the label you want to send"
msgstr ""
"La transacción debería tener solo 1 input (entrada), y esta debería ser el "
"UTXO con la etiqueta que quieres enviar."

#: src/guides/collecting/sparrow-wallet.md:145
msgid ""
"The transaction should have only 1 output, which is the address/label where "
"you want to send the inscription"
msgstr ""
"La transacción debería tener solo 1 output (salida), siendo esta la dirección/etiqueta "
"a donde deseas enviar la inscripción."

#: src/guides/collecting/sparrow-wallet.md:147
msgid ""
"If your transaction looks any different, for example you have multiple "
"inputs, or multiple outputs then this may not be a safe transfer of your "
"inscription, and you should abandon sending until you understand more, or "
"can import into the `ord` wallet."
msgstr ""
"Si tu transacción luce diferente, por ejemplo, tiene múltiples entradas o "
"múltiples salidas, entonces quizás no sea una transferencia segura de tu "
"inscripción, y deberías detener el envío hasta entender completamente el "
"procedimiento, o hasta que logres importarla al monedero `ord`."

#: src/guides/collecting/sparrow-wallet.md:149
msgid ""
"You should set an appropriate transaction fee, Sparrow will usually "
"recommend a reasonable one, but you can also check "
"[mempool.space](https://mempool.space) to see what the recommended fee rate "
"is for sending a transaction."
msgstr ""
"Es necesario fijar una comisión de transacción adecuada; "
"Sparrow normalmente sugerirá una adecuada, aunque también puedes ir a "
"[mempool.space](https://mempool.space/) para conocer la tasa recomendada "
"para enviar una transacción."

#: src/guides/collecting/sparrow-wallet.md:151
msgid ""
"You should add a label for the recipient address, a label like `alice "
"address for inscription #123` would be ideal."
msgstr ""
"Deberías añadir una etiqueta para la dirección del destinatario; "
"una etiqueta como `dirección de Alice para la inscripción #123` sería ideal."

#: src/guides/collecting/sparrow-wallet.md:153
msgid ""
"Once you have checked the transaction is a safe transaction using the checks "
"above, and you are confident to send it you can click `Create Transaction`."
msgstr ""
"Una vez que hayas verificado que la transacción es segura usando los criterios "
"mencionados anteriormente, y te sientas seguro de enviarla, puedes hacer clic en "
"`Create Transaction` (Crear Transacción)."

#: src/guides/collecting/sparrow-wallet.md:155
msgid "![](images/sending_03.png)"
msgstr "![](images/sending_03.png)"

#: src/guides/collecting/sparrow-wallet.md:157
msgid ""
"Here again you can double check that your transaction looks safe, and once "
"you are confident you can click `Finalize Transaction for Signing`."
msgstr ""
"Aquí puedes volver a revisar que tu transacción este correcta, una vez "
"estés seguro puedes hacer clic en `Finalize Transaction for Signing` "
"(Finalizar Transacción para Firmar)."

#: src/guides/collecting/sparrow-wallet.md:159
msgid "![](images/sending_04.png)"
msgstr "![](images/sending_04.png)"

#: src/guides/collecting/sparrow-wallet.md:161
msgid "Here you can triple check everything before hitting `Sign`."
msgstr "Aquí puedes revisar toda una vez más antes de hacer clic en `Sign` (Firmar)."

#: src/guides/collecting/sparrow-wallet.md:163
msgid "![](images/sending_05.png)"
msgstr "![](images/sending_05.png)"

#: src/guides/collecting/sparrow-wallet.md:165
msgid ""
"And then actually you get very very last chance to check everything before "
"hitting `Broadcast Transaction`. Once you broadcast the transaction it is "
"sent to the bitcoin network, and starts being propagated into the mempool."
msgstr ""
"Y luego, de hecho, tienes la última oportunidad de verificar todo antes de "
"hacer clic en `Broadcast Transaction` (Transmitir Transacción). Una vez que "
"transmites la transacción, se envía a la red de Bitcoin y comenzara a propagarse en el mempool."

#: src/guides/collecting/sparrow-wallet.md:167
msgid "![](images/sending_06.png)"
msgstr "![](images/sending_06.png)"

#: src/guides/collecting/sparrow-wallet.md:169
msgid ""
"If you want to track the status of your transaction you can copy the "
"`Transaction Id (Txid)` and paste that into "
"[mempool.space](https://mempool.space)"
msgstr ""
"Si deseas rastrear el estado de tu transacción, puedes copiar el "
"`Identificador de la Transacción (Txid)` y pegarlo en [mempool.space](https://mempool.space/)."

#: src/guides/collecting/sparrow-wallet.md:171
msgid ""
"Once the transaction has confirmed you can check the inscription page on "
"[ordinals.com](https://ordinals.com) to validate that it has moved to the "
"new output location and address."
msgstr ""
"Cuando la transacción haya sido confirmada, puedes revisar la página "
"de inscripciones en [ordinals.com](https://ordinals.com/) para confirmar que "
"ha sido movida a la nueva ubicación de salida y dirección."

#: src/guides/collecting/sparrow-wallet.md:173
msgid "Troubleshooting"
msgstr "Solución de problemas"

#: src/guides/collecting/sparrow-wallet.md:175
msgid ""
"Sparrow wallet is not showing a transaction/UTXO, but I can see it on "
"mempool.space!"
msgstr ""
"¡El monedero Sparrow no está mostrando una transacción/UTXO, pero puedo "
"verla en mempool.space!"

#: src/guides/collecting/sparrow-wallet.md:177
msgid ""
"Make sure that your wallet is connected to a bitcoin node. To validate this, "
"head into the `Preferences`\\-> `Server` settings, and click `Edit Existing "
"Connection`."
msgstr ""
"Asegúrate de que tu monedero esté conectado a un nodo de bitcoin. Para validar esto, "
"dirígete a `Preferences`\\-> `Server` settings y haz clic en `Edit Existing Connection` "
"(Editar conexión existente)."

#: src/guides/collecting/sparrow-wallet.md:179
msgid "![](images/troubleshooting_01.png)"
msgstr "![](images/troubleshooting_01.png)"

#: src/guides/collecting/sparrow-wallet.md:181
msgid ""
"From there you can select a node and click `Test Connection` to validate "
"that Sparrow is able to connect successfully."
msgstr ""
"Desde allí puedes seleccionar un nodo y hacer clic en `Test Connection` "
"(Probar conexión) para validar que Sparrow pueda conectarse exitosamente."

#: src/guides/collecting/sparrow-wallet.md:183
msgid "![](images/troubleshooting_02.png)"
msgstr "![](images/troubleshooting_02.png)"

#: src/guides/testing.md:4
msgid ""
"Ord can be tested using the following flags to specify the test network. For "
"more information on running Bitcoin Core for testing, see [Bitcoin's "
"developer "
"documentation](https://developer.bitcoin.org/examples/testing.html)."
msgstr ""
"Puedes hacer pruebas en `ord` utilizando las siguientes flags (banderas) "
"para especificar la red de pruebas. Para obtener más información sobre cómo "
"ejecutar Bitcoin Core en modo de pruebas, consulta la "
"[documentación para desarrolladores de Bitcoin](https://developer.bitcoin.org/examples/testing.html)."

#: src/guides/testing.md:7
msgid ""
"Most `ord` commands in [inscriptions](inscriptions.md) and "
"[explorer](explorer.md) can be run with the following network flags:"
msgstr ""
"La mayoría de los comandos de `ord` que se mencionaron en la página de "
"[inscripciones](inscriptions.md) y en [explorador](explorer.md) pueden "
"ejecutarse con las siguientes banderas de red:"

#: src/guides/testing.md:10
msgid "Network"
msgstr "Red"

#: src/guides/testing.md:10
msgid "Flag"
msgstr "Bandera"

#: src/guides/testing.md:12
msgid "Testnet"
msgstr "Testnet"

#: src/guides/testing.md:12
msgid "`--testnet` or `-t`"
msgstr "`--testnet` o `-t`"

#: src/guides/testing.md:13
msgid "Signet"
msgstr "Signet"

#: src/guides/testing.md:13
msgid "`--signet` or `-s`"
msgstr "`--signet` o `-s`"

#: src/guides/testing.md:14
msgid "Regtest"
msgstr "Regtest"

#: src/guides/testing.md:14
msgid "`--regtest` or `-r`"
msgstr "`--regtest` o `-r`"

#: src/guides/testing.md:16
msgid "Regtest doesn't require downloading the blockchain or indexing ord."
msgstr "Regtest no requiere que descargues la blockchain ni que se indexe ord."

#: src/guides/testing.md:21
msgid "Run bitcoind in regtest with:"
msgstr "Ejecutar bitcoind en regtest con:"

#: src/guides/testing.md:22
msgid ""
"```\n"
"bitcoind -regtest -txindex\n"
"```"
msgstr ""
"```\n"
"bitcoind -regtest -txindex\n"
"```"

#: src/guides/testing.md:25
msgid "Create a wallet in regtest with:"
msgstr "Crear un monedero en regtest con:"

#: src/guides/testing.md:26
msgid ""
"```\n"
"ord -r wallet create\n"
"```"
msgstr ""
"```\n"
"ord -r wallet create\n"
"```"

#: src/guides/testing.md:29
msgid "Get a regtest receive address with:"
msgstr "Obtener una dirección de recepción regtest:"

#: src/guides/testing.md:30
msgid ""
"```\n"
"ord -r wallet receive\n"
"```"
msgstr ""
"```\n"
"ord -r wallet receive\n"
"```"

#: src/guides/testing.md:33
msgid "Mine 101 blocks (to unlock the coinbase) with:"
msgstr "Minar 101 bloques (para desbloquear la transacción coinbase):"

#: src/guides/testing.md:34
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 101 <receive address>\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 101 <receive address>\n"
"```"

#: src/guides/testing.md:37
msgid "Inscribe in regtest with:"
msgstr "Inscribir en regtest:"

#: src/guides/testing.md:38
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file <file>\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file <archivo>\n"
"```"

#: src/guides/testing.md:41
msgid "Mine the inscription with:"
msgstr "Minar la inscripcion:"

#: src/guides/testing.md:42
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 1 <receive address>\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 1 <receive address>\n"
"```"

#: src/guides/testing.md:45
msgid "View the inscription in the regtest explorer:"
msgstr "Visualizar la inscripción en el explorador de regtest:"

#: src/guides/testing.md:46
msgid ""
"```\n"
"ord -r server\n"
"```"
msgstr ""
"```\n"
"ord -r server\n"
"```"

#: src/guides/testing.md:50
msgid "Testing Recursion"
msgstr "Prueba de Recursión"

#: src/guides/testing.md:53
msgid ""
"When testing out [recursion](../inscriptions/recursion.md), inscribe the "
"dependencies first (example with [p5.js](https://p5js.org):"
msgstr ""
"Cuando estés probando la [recursión](../inscriptions/recursion.md), inscribe primero las "
"dependencias (por ejemplo, con [p5.js](https://p5js.org/)):"

#: src/guides/testing.md:55
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file p5.js\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file p5.js\n"
"```"

#: src/guides/testing.md:58
msgid ""
"This should return a `inscription_id` which you can then reference in your "
"recursive inscription."
msgstr ""
"Esto debería proporcionar un `inscription_id` (id de inscripción) que luego "
"puedes referenciar en tu inscripción recursiva."

#: src/guides/testing.md:61
msgid ""
"ATTENTION: These ids will be different when inscribing on mainnet or signet, "
"so be sure to change those in your recursive inscription for each chain."
msgstr ""
"ATENCION: Estos IDs serán diferentes dependiendo de si estás inscribiendo en "
"la red principal (mainnet) o en signet, así que recuerda ajustarlos en tu "
"inscripción recursiva según la cadena que estés utilizando."

#: src/guides/testing.md:65
msgid "Then you can inscribe your recursive inscription with:"
msgstr "Luego podrás inscribir tu inscripción recursiva utilizando:"

#: src/guides/testing.md:66
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file recursive-inscription.html\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file recursive-inscription.html\n"
"```"

#: src/guides/testing.md:69
msgid "Finally you will have to mine some blocks and start the server:"
msgstr "Para finalizar, necesitarás minar algunos bloques e iniciar el servidor:"

#: src/guides/testing.md:70
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 6 <receive address>\n"
"ord -r server\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 6 <receive address>\n"
"ord -r server\n"
"```"

#: src/guides/moderation.md:4
msgid ""
"`ord` includes a block explorer, which you can run locally with `ord server`."
msgstr ""
"`ord` incluye un explorador de bloques, el cual puedes ejecutar "
"localmente utilizando `ord server`."

#: src/guides/moderation.md:6
msgid ""
"The block explorer allows viewing inscriptions. Inscriptions are "
"user-generated content, which may be objectionable or unlawful."
msgstr ""
"El explorador de bloques permite visualizar las inscripciones, "
"que son contenidos creados por los usuarios, y que pueden ser de "
"carácter objetable o incluso ilícito."

#: src/guides/moderation.md:9
msgid ""
"It is the responsibility of each individual who runs an ordinal block "
"explorer instance to understand their responsibilities with respect to "
"unlawful content, and decide what moderation policy is appropriate for their "
"instance."
msgstr ""
"Quien decida ejecutar una instancia del explorador de bloques de ordinal "
"debe ser consciente de sus responsabilidades ante contenidos ilegales y "
"definir una política de moderación adecuada para su propia instancia."

#: src/guides/moderation.md:13
msgid ""
"In order to prevent particular inscriptions from being displayed on an `ord` "
"instance, they can be included in a YAML config file, which is loaded with "
"the `--config` option."
msgstr ""
"Para prevenir que ciertas inscripciones se muestren en una instancia de `ord`, "
"estas pueden incluirse en un archivo de configuración YAML, el cual se carga utilizando la opción `--config`."

#: src/guides/moderation.md:17
msgid ""
"To hide inscriptions, first create a config file, with the inscription ID "
"you want to hide:"
msgstr ""
"Para comenzar a ocultar inscripciones, crea un archivo de configuración con "
"el ID de la inscripción que quieres esconder:"

#: src/guides/moderation.md:20
msgid ""
"```yaml\n"
"hidden:\n"
"- 0000000000000000000000000000000000000000000000000000000000000000i0\n"
"```"
msgstr ""
"```yaml\n"
"hidden:\n"
"- 0000000000000000000000000000000000000000000000000000000000000000i0\n"
"```"

#: src/guides/moderation.md:25
msgid ""
"The suggested name for `ord` config files is `ord.yaml`, but any filename "
"can be used."
msgstr ""
"Aunque se recomienda nombrar los archivos de configuración de `ord` como "
"`ord.yaml`, puedes utilizar cualquier otro nombre que prefieras."

#: src/guides/moderation.md:28
msgid "Then pass the file to `--config` when starting the server:"
msgstr "Luego pasa el archivo a `--config` cuando inicies el servidor:"

#: src/guides/moderation.md:30
msgid "`ord --config ord.yaml server`"
msgstr "`ord --config ord.yaml server`"

#: src/guides/moderation.md:32
msgid ""
"Note that the `--config` option comes after `ord` but before the `server` "
"subcommand."
msgstr ""
"Ten en cuenta que la opción `--config` va después de `ord` pero antes del "
"subcomando `server`."

#: src/guides/moderation.md:35
msgid "`ord` must be restarted in to load changes to the config file."
msgstr "Deberás reiniciar `ord` para cargar los cambios realizados en el archivo de configuración."

#: src/guides/moderation.md:37
msgid "`ordinals.com`"
msgstr "`ordinals.com`"

#: src/guides/moderation.md:40
msgid ""
"The `ordinals.com` instances use `systemd` to run the `ord server` service, "
"which is called `ord`, with a config file located at `/var/lib/ord/ord.yaml`."
msgstr ""
"Las instancias de `ordinals.com` utilizan `systemd` para ejecutar el servicio "
"del `servidor ord`, el cual se llama `ord`, con un archivo de configuración situado "
"en `/var/lib/ord/ord.yaml`."

#: src/guides/moderation.md:43
msgid "To hide an inscription on `ordinals.com`:"
msgstr "Para ocultar una inscripción en `ordinals.com`:"

#: src/guides/moderation.md:45
msgid "SSH into the server"
msgstr "Ingresa al servidor a través de SSH"

#: src/guides/moderation.md:46
msgid "Add the inscription ID to `/var/lib/ord/ord.yaml`"
msgstr "Añade el ID de la inscripción a `/var/lib/ord/ord.yaml`"

#: src/guides/moderation.md:47
msgid "Restart the service with `systemctl restart ord`"
msgstr "Reinicia el servicio utilizando el comando `systemctl restart ord`"

#: src/guides/moderation.md:48
msgid "Monitor the restart with `journalctl -u ord`"
msgstr "Supervisa el reinicio con el comando `journalctl -u ord`"

#: src/guides/moderation.md:50
msgid ""
"Currently, `ord` is slow to restart, so the site will not come back online "
"immediately."
msgstr ""
"Actualmente, `ord` tarda en reiniciarse, por lo que el sitio no volverá a "
"estar en línea inmediatamente."

#: src/guides/reindexing.md:4
msgid ""
"Sometimes the `ord` database must be reindexed, which means deleting the "
"database and restarting the indexing process with either `ord index update` or "
"`ord server`. Reasons to reindex are:"
msgstr ""
"En ocasiones, la base de datos de ord debe ser reindexada, esto implica eliminar "
"la base de datos y reiniciar el proceso de indexación con el comando "
"`ord index update` u `ord server`. Las razones para reindexar son:"

#: src/guides/reindexing.md:8
msgid "A new major release of ord, which changes the database scheme"
msgstr "Un nuevo lanzamiento grande de ord, que modifica el esquema de la base de datos"

#: src/guides/reindexing.md:9
msgid "The database got corrupted somehow"
msgstr "Corrupción de la base de datos por alguna razón."

#: src/guides/reindexing.md:11
msgid ""
"The database `ord` uses is called [redb](https://github.com/cberner/redb), "
"so we give the index the default file name `index.redb`. By default we store "
"this file in different locations depending on your operating system."
msgstr ""
"La base de datos que utiliza ord se llama [redb](https://github.com/cberner/redb), "
"por lo que se le asigna al índice el nombre de archivo predeterminado `index.redb`. "
"Este archivo se guarda de forma predeterminada en distintas ubicaciones, según el "
"sistema operativo que estés utilizando."

#: src/guides/reindexing.md:15
msgid "Platform"
msgstr "Plataforma"

#: src/guides/reindexing.md:15
msgid "Value"
msgstr "Valor                                            "

#: src/guides/reindexing.md:17
msgid "Linux"
msgstr ""

#: src/guides/reindexing.md:17
msgid "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"
msgstr ""

#: src/guides/reindexing.md:17
msgid "/home/<USER>/.local/share/ord"
msgstr ""

#: src/guides/reindexing.md:18
msgid "macOS"
msgstr ""

#: src/guides/reindexing.md:18
msgid "`$HOME`/Library/Application Support/ord"
msgstr ""

#: src/guides/reindexing.md:18
msgid "/Users/<USER>/Library/Application Support/ord"
msgstr ""

#: src/guides/reindexing.md:19
msgid "Windows"
msgstr ""

#: src/guides/reindexing.md:19
msgid "`{FOLDERID_RoamingAppData}`\\\\ord"
msgstr ""

#: src/guides/reindexing.md:19
msgid "C:\\Users\\<USER>\\AppData\\Roaming\\ord"
msgstr ""

#: src/guides/reindexing.md:21
msgid ""
"So to delete the database and reindex on MacOS you would have to run the "
"following commands in the terminal:"
msgstr ""
"Para eliminar la base de datos y reindexar en MacOS, tendrías que ejecutar "
"los siguientes comandos en la terminal:"

#: src/guides/reindexing.md:24
msgid ""
"```bash\n"
"rm ~/Library/Application Support/ord/index.redb\n"
"ord index update\n"
"```"
msgstr ""
"```bash\n"
"rm ~/Library/Application Support/ord/index.redb\n"
"ord index update\n"
"```"

#: src/guides/reindexing.md:29
msgid ""
"You can of course also set the location of the data directory yourself with "
"`ord --datadir <DIR> index update` or give it a specific filename and path "
"with `ord --index <FILENAME> index update`."
msgstr ""
"También tienes la opción de determinar la ubicación del directorio de datos "
"utilizando el comando `ord --datadir <DIR> index update` o asignarle un nombre "
"de archivo y ruta específicos utilizando el comando `ord --index <NOMBRE_DE_ARCHIVO> "
"index update`."

#: src/bounties.md:1
msgid "Ordinal Bounty Hunting Hints"
msgstr "Pistas para la Caza de Recompensas de Ordinals"


#: src/bounties.md:4
msgid ""
"The `ord` wallet can send and receive specific satoshis. Additionally, "
"ordinal theory is extremely simple. A clever hacker should be able to write "
"code from scratch to manipulate satoshis using ordinal theory in no time."
msgstr ""
"El monedero `ord` tiene la capacidad de enviar y recibir satoshis "
"específicos. Además, la teoría ordinal es sumamente sencilla. Un "
"hacker ingenioso debería poder crear código desde cero para manipular "
"satoshis utilizando la teoría ordinal en poco tiempo."

#: src/bounties.md:8
msgid ""
"For more information about ordinal theory, check out the [FAQ](./faq.md) for "
"an overview, the "
"[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) for the "
"technical details, and the [ord repo](https://github.com/ordinals/ord) for "
"the `ord` wallet and block explorer."
msgstr ""
"Para obtener más información sobre la teoría ordinal, visita la sección de "
"[preguntas frecuentes](./faq.md) para obtener una visión general, "
"el [BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) para los detalles "
"técnicos, y el [repositorio de ord](https://github.com/ordinals/ord) para "
"conocer más sobre el monedero y el explorador de bloques de `ord`."

#: src/bounties.md:14
msgid ""
"Satoshi was the original developer of ordinal theory. However, he knew that "
"others would consider it heretical and dangerous, so he hid his knowledge, "
"and it was lost to the sands of time. This potent theory is only now being "
"rediscovered. You can help by researching rare satoshis."
msgstr ""
"Satoshi fue el desarrollador original de la teoría ordinal. Sin embargo, "
"sabía que otros la considerarían herética y peligrosa, por lo que ocultó "
"su conocimiento, que terminó desapareciendo con el paso del tiempo. Solo "
"ahora estamos redescubriendo esta poderosa teoría. Puedes contribuir con "
"este resurgimiento investigando satoshis poco comunes."

#: src/bounties.md:19
msgid "Good luck and godspeed!"
msgstr "¡Buena suerte y buen viaje!"

#: src/bounty/0.md:1
msgid "Ordinal Bounty 0"
msgstr "Recompensa Ordinal 0"

#: src/bounty/0.md:4
#: src/bounty/1.md:4
#: src/bounty/2.md:4
#: src/bounty/3.md:4
msgid "Criteria"
msgstr "Criterios"

#: src/bounty/0.md:7
msgid ""
"Send a sat whose ordinal number ends with a zero to the submission address:"
msgstr ""
"Envía un sat cuyo número ordinal termine en cero a la dirección de entrega:"

#: src/bounty/0.md:9
msgid "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"
msgstr "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"

#: src/bounty/0.md:11
msgid "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"
msgstr "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"

#: src/bounty/0.md:13
msgid "The sat must be the first sat of the output you send."
msgstr "El sat debe ser el primer sat de la salida que envíes."

#: src/bounty/0.md:15
#: src/bounty/1.md:14
#: src/bounty/2.md:15
#: src/bounty/3.md:63
msgid "Reward"
msgstr "Recompensa"

#: src/bounty/0.md:18
msgid "100,000 sats"
msgstr "100,000 sats"

#: src/bounty/0.md:20
#: src/bounty/1.md:19
#: src/bounty/2.md:20
#: src/bounty/3.md:70
msgid "Submission Address"
msgstr "Dirección de Entrega"

#: src/bounty/0.md:23
msgid ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"
msgstr ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"

#: src/bounty/0.md:25
#: src/bounty/1.md:24
#: src/bounty/2.md:25
#: src/bounty/3.md:75
msgid "Status"
msgstr "Estado"

#: src/bounty/0.md:28
msgid ""
"Claimed by "
"[@count_null](https://twitter.com/rodarmor/status/1560793241473400833)!"
msgstr ""
"Reclamado por "
"[@count_null](https://twitter.com/rodarmor/status/1560793241473400833)!"

#: src/bounty/1.md:1
msgid "Ordinal Bounty 1"
msgstr "Recompensa Ordinal 1"

#: src/bounty/1.md:7
msgid ""
"The transaction that submits a UTXO containing the oldest sat, i.e., that "
"with the lowest number, amongst all submitted UTXOs will be judged the "
"winner."
msgstr ""
"La transacción que envíe un UTXO que contenga el sat más antiguo, "
"es decir, aquel con el número más bajo, entre todos los UTXOs enviados será "
"considerado el ganador."

#: src/bounty/1.md:10
msgid ""
"The bounty is open for submissions until block 753984—the first block of "
"difficulty adjustment period 374. Submissions included in block 753984 or "
"later will not be considered."
msgstr ""
"La convocatoria para participar en la recompensa permanecerá abierta hasta "
"el bloque 753984, que marca el primer bloque del período de ajuste de "
"dificultad 374. Los envíos que se incluyan a partir del bloque 753984 no "
"serán tomados en cuenta."

#: src/bounty/1.md:17
msgid "200,000 sats"
msgstr "200,000 sats"

#: src/bounty/1.md:22
msgid ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"
msgstr ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"

#: src/bounty/1.md:27
msgid ""
"Claimed by "
"[@ordinalsindex](https://twitter.com/rodarmor/status/1569883266508853251)!"
msgstr ""
"Reclamado por [@ordinalsindex](https://twitter.com/rodarmor/status/1569883266508853251)!"

#: src/bounty/2.md:1
msgid "Ordinal Bounty 2"
msgstr "Recompensa Ordinal 2"

#: src/bounty/2.md:7
msgid "Send an uncommon  sat to the submission address:"
msgstr "Envía un sat poco común a la dirección de entrega:\n "

#: src/bounty/2.md:9
msgid "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"
msgstr "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"

#: src/bounty/2.md:11
msgid "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"
msgstr "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"

#: src/bounty/2.md:13
msgid ""
"Confirm that the submission address has not received transactions before "
"submitting your entry. Only the first successful submission will be rewarded."
msgstr ""
"Confirma que la dirección de entrega no haya recibido transacciones antes de "
"enviar tu entrega. Solo la primera participación exitosa recibirá recompensa."

#: src/bounty/2.md:18
msgid "300,000 sats"
msgstr "300,000 sats"

#: src/bounty/2.md:23
msgid ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"
msgstr ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"

#: src/bounty/2.md:28
msgid ""
"Claimed by "
"[@utxoset](https://twitter.com/rodarmor/status/1582424455615172608)!"
msgstr ""
"Reclamado por [@utxoset]"
"(https://twitter.com/rodarmor/status/1582424455615172608)!"

#: src/bounty/3.md:1
msgid "Ordinal Bounty 3"
msgstr "Recompensa Ordinal 3"

#: src/bounty/3.md:7
msgid ""
"Ordinal bounty 3 has two parts, both of which are based on _ordinal names_. "
"Ordinal names are a modified base-26 encoding of ordinal numbers. To avoid "
"locking short names inside the unspendable genesis block coinbase reward, "
"ordinal names get _shorter_ as the ordinal number gets _longer_. The name of "
"sat 0, the first sat to be mined is `nvtdijuwxlp` and the name of sat "
"2,099,999,997,689,999, the last sat to be mined, is `a`."
msgstr ""
"La tercera recompensa ordinal consta de dos partes, ambas basadas en los "
"_nombres de ordinals_. Los nombres de los ordinals son una modificación de "
"la codificación base-26 de los números ordinales. Para prevenir que los nombres "
"más cortos queden atrapados en el bloque génesis el cual no se puede utilizar, "
"los nombres de los ordinals se van acortando a medida que el número ordinal "
"aumenta. El nombre del sat 0, el primer sat minado, es `nvtdijuwxlp` y el nombre "
"del sat 2,099,999,997,689,999, el último sat que será minado, es `a`."

#: src/bounty/3.md:14
msgid ""
"The bounty is open for submissions until block 840000—the first block after "
"the fourth halvening. Submissions included in block 840000 or later will not "
"be considered."
msgstr ""
"La convocatoria para participar en la recompensa permanecerá abierta hasta el "
"bloque 840000, el primer bloque después del cuarto halving. Los envíos que se "
"incluyan a partir del bloque 840000 no serán tomados en cuenta."

#: src/bounty/3.md:18
msgid ""
"Both parts use [frequency.tsv](frequency.tsv), a list of words and the "
"number of times they occur in the [Google Books Ngram "
"dataset](http://storage.googleapis.com/books/ngrams/books/datasetsv2.html). "
"filtered to only include the names of sats which will have been mined by the "
"end of the submission period, that appear at least 5000 times in the corpus."
msgstr ""
"Ambas partes usan [frequency.tsv](frequency.tsv), un documento que contiene "
"una lista de palabras junto con la cantidad de veces que aparecen en el "
"[set de datos Google Books Ngram](http://storage.googleapis.com/books/ngrams/books/datasetsv2.html), "
"Este archivo ha sido filtrado para incluir solo los nombres de sats que "
"habrán sido minados para el momento de cierre del periodo de entregas, "
"que aparecen por lo menos 5000 veces en el corpus."

#: src/bounty/3.md:24
msgid ""
"`frequency.tsv` is a file of tab-separated values. The first column is the "
"word, and the second is the number of times it appears in the corpus. The "
"entries are sorted from least-frequently occurring to most-frequently "
"occurring."
msgstr ""
"`frequency.tsv` es un archivo de valores separados por tabulaciones. La "
"primera columna es la palabra, y la segunda es el número de veces que "
"aparece en el corpus. Los datos están organizados de manera que las palabras "
"que aparecen con menor frecuencia están primero, seguidas por aquellas que "
"se encuentran con una mayor frecuencia."

#: src/bounty/3.md:29
msgid ""
"`frequency.tsv` was compiled using [this "
"program](https://github.com/casey/onegrams)."
msgstr ""
"`frequency.tsv` fue compilado [usando este programa]"
"(https://github.com/casey/onegrams)."

#: src/bounty/3.md:32
msgid ""
"To search an `ord` wallet for sats with a name in `frequency.tsv`, use the "
"following [`ord`](https://github.com/ordinals/ord) command:"
msgstr ""
"Para buscar sats en un monedero `ord` que coincidan con un nombre presente en "
"`frequency.tsv`, emplea el siguiente comando [`ord`](https://github.com/ordinals/ord):"

#: src/bounty/3.md:35
msgid ""
"```\n"
"ord wallet sats --tsv frequency.tsv\n"
"```"
msgstr ""
"```\n"
"ord wallet sats --tsv frequency.tsv\n"
"```"

#: src/bounty/3.md:39
msgid ""
"This command requires the sat index, so `--index-sats` must be passed to ord "
"when first creating the index."
msgstr ""
"Este comando requiere el índice de sats, por lo que se debe incluir el "
"parámetro `--index-sats` en ord cuando se crea el índice por primera vez."

#: src/bounty/3.md:42
msgid "Part 0"
msgstr "Parte 0"

#: src/bounty/3.md:44
msgid "_Rare sats pair best with rare words._"
msgstr "_Los sats raros hacen mejor pareja con palabras raras._"

#: src/bounty/3.md:46
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the lowest number of occurrences in `frequency.tsv` shall be the winner "
"of part 0."
msgstr "La transacción que envíe el UTXO que contiene el sat cuyo nombre aparece "
"con el menor número de ocurrencias en `frequency.tsv` será el ganador de la parte 0."

#: src/bounty/3.md:50
msgid "Part 1"
msgstr "Parte 1"

#: src/bounty/3.md:52
msgid "_Popularity is the font of value._"
msgstr "_La popularidad es la fuente del valor._"

#: src/bounty/3.md:54
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the highest number of occurrences in `frequency.tsv` shall be the "
"winner of part 1."
msgstr ""
"La transacción que envía el UTXO que contiene el sat cuyo nombre aparece con "
"el mayor número de ocurrencias en `frequency.tsv` será el ganador de la parte 1."

#: src/bounty/3.md:58
msgid "Tie Breaking"
msgstr "Desempate"

#: src/bounty/3.md:60
msgid ""
"In the case of a tie, where two submissions occur with the same frequency, "
"the earlier submission shall be the winner."
msgstr ""
"Si se produce un empate, donde dos presentaciones registren la misma frecuencia, "
"la presentación que se haya realizado primero será la ganadora. "

#: src/bounty/3.md:66
msgid "Part 0: 200,000 sats"
msgstr "Parte 0: 200,000 satoshis"

#: src/bounty/3.md:67
msgid "Part 1: 200,000 sats"
msgstr "Parte 1: 200,000 sats"

#: src/bounty/3.md:68
msgid "Total: 400,000 sats"
msgstr "Total: 400,000 sats"

#: src/bounty/3.md:73
msgid ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"
msgstr ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"

#: src/bounty/3.md:78
msgid "Unclaimed!"
msgstr "¡No se ha reclamado!"
