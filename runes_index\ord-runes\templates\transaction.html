<h1>Transaction <span class=monospace>{{self.txid}}</span></h1>
%% if self.inscription_count > 0 {
<h2>Inscription Geneses</h2>
<div class=thumbnails>
%% for index in 0..self.inscription_count {
{{ Iframe::thumbnail(InscriptionId { txid: self.txid, index }) }}
%% }
</div>
%% }
<dl>
%% if let Some(rune) = self.etching {
  <dt>etching</dt>
  <dd><a href=/rune/{{ rune }}>{{ rune }}</a></dd>
%% }
</dl>
<h2>{{"Input".tally(self.transaction.input.len())}}</h2>
<ul>
%% for input in &self.transaction.input {
  <li><a class=monospace href=/output/{{input.previous_output}}>{{input.previous_output}}</a></li>
%% }
</ul>
<h2>{{"Output".tally(self.transaction.output.len())}}</h2>
<ul class=monospace>
%% for (vout, output) in self.transaction.output.iter().enumerate() {
%% let outpoint = OutPoint::new(self.txid, vout as u32);
  <li>
    <a href=/output/{{outpoint}} class=monospace>
      {{ outpoint }}
    </a>
    <dl>
      <dt>value</dt><dd>{{ output.value }}</dd>
      <dt>script pubkey</dt><dd class=monospace>{{ output.script_pubkey.to_asm_string() }}</dd>
%% if let Ok(address) = self.chain.address_from_script(&output.script_pubkey) {
      <dt>address</dt><dd class=monospace>{{ address }}</dd>
%% }
    </dl>
  </li>
%% }
</ul>
