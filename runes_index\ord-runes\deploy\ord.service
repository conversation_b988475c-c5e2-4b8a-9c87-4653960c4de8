[Unit]
After=network.target
Description=Ord server
StartLimitBurst=120
StartLimitIntervalSec=10m

[Service]
AmbientCapabilities=CAP_NET_BIND_SERVICE
Environment=RUST_BACKTRACE=1
Environment=RUST_LOG=info
ExecStart=/usr/local/bin/ord \
  --bitcoin-data-dir /var/lib/bitcoind \
  --chain ${CHAIN} \
  --config-dir /var/lib/ord \
  --datadir /var/lib/ord \
  --index-runes \
  --index-sats \
  server \
  --acme-contact mailto:<EMAIL> \
  --csp-origin https://${CSP_ORIGIN} \
  --http \
  --https \
  --disable-json-api
Group=ord
LimitNOFILE=65536
MemoryDenyWriteExecute=true
NoNewPrivileges=true
PrivateDevices=true
PrivateTmp=true
ProtectHome=true
ProtectSystem=full
Restart=on-failure
RestartSec=5s
StateDirectory=ord
StateDirectoryMode=0700
TimeoutStopSec=10m
Type=simple
User=ord
WorkingDirectory=/var/lib/ord

[Install]
WantedBy=multi-user.target
