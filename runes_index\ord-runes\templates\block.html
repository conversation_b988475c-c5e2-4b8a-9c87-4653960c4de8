<h1>Block {{ self.height }}</h1>
<dl>
  <dt>hash</dt><dd class=monospace>{{self.hash}}</dd>
  <dt>target</dt><dd class=monospace>{{self.target}}</dd>
  <dt>timestamp</dt><dd><time>{{timestamp(self.block.header.time.into())}}</time></dd>
  <dt>size</dt><dd>{{self.block.size()}}</dd>
  <dt>weight</dt><dd>{{self.block.weight()}}</dd>
%% if self.height.0 > 0 {
  <dt>previous blockhash</dt><dd><a href=/block/{{self.block.header.prev_blockhash}} class=monospace>{{self.block.header.prev_blockhash}}</a></dd>
%% }
</dl>
<div class=center>
%% if let Some(prev_height) = self.height.n().checked_sub(1) {
<a class=prev href=/block/{{prev_height}}>prev</a>
%% } else {
prev
%% }
%% if self.height < self.best_height {
<a class=next href=/block/{{self.height + 1}}>next</a>
%% } else {
next
%% }
</div>
<h2>{{"Inscription".tally(self.inscription_count)}}</h2>
<div class=thumbnails>
%% for id in &self.featured_inscriptions {
    {{ Iframe::thumbnail(*id) }}
%% }
</div>
%% if &self.inscription_count > &self.featured_inscriptions.len() {
<div class=center>
  <a href="/inscriptions/block/{{ &self.height }}">more</a>
</div>
%% }
<h2>{{"Transaction".tally(self.block.txdata.len())}}</h2>
<ul class=monospace>
%% for tx in &self.block.txdata {
%% let txid = tx.txid();
  <li><a href=/tx/{{txid}}>{{txid}}</a></li>
%% }
</ul>
