Digital Artifacts
=================

Imagine a physical artifact. A rare coin, say, held safe for untold years in
the dark, secret clutch of a Viking hoard, now dug from the earth by your
grasping hands. It…

…has an owner. You. As long as you keep it safe, nobody can take it from you.

…is complete. It has no missing parts.

…can only be changed by you. If you were a trader, and you made your way to
18th century China, none but you could stamp it with your chop-mark.

…can only be disposed of by you. The sale, trade, or gift is yours to make,
to whomever you wish.

What are digital artifacts? Simply put, they are the digital equivalent of
physical artifacts.

For a digital thing to be a digital artifact, it must be like that coin of
yours:

- Digital artifacts can have owners. A number is not a digital artifact,
  because nobody can own it.

- Digital artifacts are complete. An NFT that points to off-chain content
  on IPFS or Arweave is incomplete, and thus not a digital artifact.

- Digital artifacts are permissionless. An NFT which cannot be sold without
  paying a royalty is not permissionless, and thus not a digital artifact.

- Digital artifacts are uncensorable. Perhaps you can change a database entry
  on a centralized ledger today, but maybe not tomorrow, and thus one cannot be
  a digital artifact.

- Digital artifacts are immutable. An NFT with an upgrade key is not a digital
  artifact.

The definition of a digital artifact is intended to reflect what NFTs *should*
be, sometimes are, and what inscriptions *always* are, by their very nature.
