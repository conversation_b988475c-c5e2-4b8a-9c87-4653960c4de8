use {super::*, ord::subcommand::epochs::Output, ordinals::Sat};

#[test]
fn empty() {
  assert_eq!(
    CommandBuilder::new("epochs").run_and_deserialize_output::<Output>(),
    Output {
      starting_sats: vec![
        <PERSON><PERSON>(0),
        <PERSON><PERSON>(1050000000000000),
        <PERSON><PERSON>(1575000000000000),
        <PERSON><PERSON>(1837500000000000),
        <PERSON><PERSON>(1968750000000000),
        <PERSON><PERSON>(2034375000000000),
        <PERSON><PERSON>(2067187500000000),
        <PERSON><PERSON>(2083593750000000),
        <PERSON><PERSON>(2091796875000000),
        <PERSON><PERSON>(2095898437500000),
        <PERSON><PERSON>(2097949218750000),
        <PERSON><PERSON>(2098974609270000),
        <PERSON><PERSON>(2099487304530000),
        <PERSON><PERSON>(2099743652160000),
        <PERSON><PERSON>(2099871825870000),
        <PERSON><PERSON>(2099935912620000),
        <PERSON><PERSON>(2099967955890000),
        Sat(2099983977420000),
        Sat(2099991988080000),
        <PERSON><PERSON>(2099995993410000),
        <PERSON><PERSON>(2099997995970000),
        Sat(2099998997250000),
        Sat(2099999497890000),
        Sat(2099999748210000),
        Sat(2099999873370000),
        Sat(2099999935950000),
        Sat(2099999967240000),
        Sat(2099999982780000),
        Sat(2099999990550000),
        Sat(2099999994330000),
        Sat(2099999996220000),
        Sat(2099999997060000),
        Sat(2099999997480000),
        Sat(2099999997690000)
      ]
    }
  );
}
