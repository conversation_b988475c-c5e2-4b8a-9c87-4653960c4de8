Ordinal Bounty Hunting Hints
============================

- The `ord` wallet can send and receive specific satoshis. Additionally,
  ordinal theory is extremely simple. A clever hacker should be able to write
  code from scratch to manipulate satoshis using ordinal theory in no time.

- For more information about ordinal theory, check out the [FAQ](./faq.md) for
  an overview, the
  [BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) for the
  technical details, and the [ord repo](https://github.com/ordinals/ord) for the
  `ord` wallet and block explorer.

- <PERSON><PERSON> was the original developer of ordinal theory. However, he knew that
  others would consider it heretical and dangerous, so he hid his knowledge,
  and it was lost to the sands of time. This potent theory is only now being
  rediscovered. You can help by researching rare satoshis.

Good luck and godspeed!
