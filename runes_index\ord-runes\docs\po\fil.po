msgid ""
msgstr ""
"Project-Id-Version: Ordinal Theory Handbook\n"
"POT-Creation-Date: 2023-10-11T09:45:53+08:00\n"
"PO-Revision-Date: \n"
"Last-Translator: <PERSON> @cirroxyz <<EMAIL>>\n"
"Language-Team: \n"
"Language: fil\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: src/SUMMARY.md:2 src/introduction.md:1
msgid "Introduction"
msgstr "Panimula"

#: src/SUMMARY.md:3
msgid "Overview"
msgstr "Pangkalahatang-ideya"

#: src/SUMMARY.md:4 src/digital-artifacts.md:1
msgid "Digital Artifacts"
msgstr "Digital Artifacts"

#: src/SUMMARY.md:5 src/SUMMARY.md:14 src/overview.md:221 src/inscriptions.md:1
msgid "Inscriptions"
msgstr "Inscriptions"

#: src/SUMMARY.md:6 src/inscriptions/metadata.md:1
msgid "Metadata"
msgstr "Metadata"

#: src/SUMMARY.md:7 src/inscriptions/provenance.md:1
msgid "Provenance"
msgstr "Provenance"

#: src/SUMMARY.md:8 src/inscriptions/recursion.md:1
msgid "Recursion"
msgstr "Recursion"

#: src/SUMMARY.md:9
msgid "FAQ"
msgstr "Mga Madalas Itanong"

#: src/SUMMARY.md:10
msgid "Contributing"
msgstr "Mag-ambag"

#: src/SUMMARY.md:11 src/donate.md:1
msgid "Donate"
msgstr "Donate"

#: src/SUMMARY.md:12
msgid "Guides"
msgstr "Mga gabay"

#: src/SUMMARY.md:13
msgid "Explorer"
msgstr "Explorer"

#: src/SUMMARY.md:15 src/guides/sat-hunting.md:1
msgid "Sat Hunting"
msgstr "Sat Hunting"

#: src/SUMMARY.md:16 src/guides/collecting.md:1
msgid "Collecting"
msgstr "Pagkolekta"

#: src/SUMMARY.md:17 src/guides/sat-hunting.md:239
msgid "Sparrow Wallet"
msgstr "Sparrow Wallet"

#: src/SUMMARY.md:18 src/guides/testing.md:1
msgid "Testing"
msgstr "Pagsuri"

#: src/SUMMARY.md:19 src/guides/moderation.md:1
msgid "Moderation"
msgstr "Moderation"

#: src/SUMMARY.md:20 src/guides/reindexing.md:1
msgid "Reindexing"
msgstr "Reindexing"

#: src/SUMMARY.md:21
msgid "Bounties"
msgstr "Bounties"

#: src/SUMMARY.md:22
msgid "Bounty 0: 100,000 sats Claimed!"
msgstr "Bounty 0: 100,000 sats Claimed!"

#: src/SUMMARY.md:23
msgid "Bounty 1: 200,000 sats Claimed!"
msgstr "Bounty 1: 200,000 sats Claimed!"

#: src/SUMMARY.md:24
msgid "Bounty 2: 300,000 sats Claimed!"
msgstr "Bounty 2: 300,000 sats Claimed!"

#: src/SUMMARY.md:25
msgid "Bounty 3: 400,000 sats"
msgstr "Bounty 3: 400,000 sats"

#: src/introduction.md:4
msgid ""
"This handbook is a guide to ordinal theory. Ordinal theory concerns itself "
"with satoshis, giving them individual identities and allowing them to be "
"tracked, transferred, and imbued with meaning."
msgstr ""
"Ang handbook na ito ay isang gabay sa pagunawa ng Ordinal Theory. Ang "
"Ordinal Theory ay may kinalaman sa satoshi, kung saan nagbibigay ito ng "
"indibidwal na pagkakakilanlan at nagpapahintulot sa kanila na masubaybayan, "
"mailipat, at magbigay ng kahulugan."

#: src/introduction.md:8
msgid ""
"Satoshis, not bitcoin, are the atomic, native currency of the Bitcoin "
"network. One bitcoin can be sub-divided into 100,000,000 satoshis, but no "
"further."
msgstr ""
"Ang Satoshis, ay ang atomic, “native currency” ng Bitcoin network. Ang isang "
"bitcoin ay maaaring hatiin sa 100,000,000 satoshis, at wala nang iba pa."

#: src/introduction.md:11
msgid ""
"Ordinal theory does not require a sidechain or token aside from Bitcoin, and "
"can be used without any changes to the Bitcoin network. It works right now."
msgstr ""
"Ang Ordinal theory ay hindi nangangailangan ng sidechain o token bukod sa "
"Bitcoin, at maaaring gamitin nang walang anumang pagbabago sa Bitcoin "
"network."

#: src/introduction.md:14
msgid ""
"Ordinal theory imbues satoshis with numismatic value, allowing them to be "
"collected and traded as curios."
msgstr ""
"Ang Ordinal theory ay nagbibigay sa satoshi ng numismatic na halaga, na "
"nagdudulot para sa mga ito kolektahin at ipagpalit bilang mga ‘rare’ na "
"bagay."

#: src/introduction.md:17
msgid ""
"Individual satoshis can be inscribed with arbitrary content, creating unique "
"Bitcoin-native digital artifacts that can be held in Bitcoin wallets and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"Ang mga indibidwal na satoshi ay maaaring ma-inscribe ng kahit anong "
"arbitraryo nilalaman, na lumilikha ng mga natatanging “Bitcoin-native” "
"digital na artifact na maaaring itago sa Bitcoin wallet at ilipat gamit ang "
"mga transaksyong Bitcoin. Ang mga inskripsiyon ay hindi nababago, secure, at "
"desentralisado gaya ng mismong Bitcoin."

#: src/introduction.md:22
msgid ""
"Other, more unusual use-cases are possible: off-chain colored-coins, public "
"key infrastructure with key rotation, a decentralized replacement for the "
"DNS. For now though, such use-cases are speculative, and exist only in the "
"minds of fringe ordinal theorists."
msgstr ""
"At iba pa na hindi pangkaraniwang gamit nito ay: mga off-chain colored-coin, "
"pampublikong imprastraktura ng key na may key rotation, isang "
"desentralisadong kapalit ng DNS. Gayunpaman, sa ngayon, ang mga ganitong "
"espekulasyon ng paggamit ay haka-haka, at umiiral lamang sa isipan ng mga "
"“fringe ordinal theorists”."

#: src/introduction.md:27
msgid "For more details on ordinal theory, see the [overview](overview.md)."
msgstr ""
"Para sa higit pang mga detalye sa Ordinal Theory, tingnan ang "
"[pangkalahatang-ideya](overview.md)"

#: src/introduction.md:29
msgid "For more details on inscriptions, see [inscriptions](inscriptions.md)."
msgstr ""
"Para sa higit pang mga detalye sa mga inskripsiyon, tingnan ang mga "
"[inskripsiyon](inscriptions.md)."

#: src/introduction.md:31
msgid ""
"When you're ready to get your hands dirty, a good place to start is with "
"[inscriptions](guides/inscriptions.md), a curious species of digital "
"artifact enabled by ordinal theory."
msgstr ""
"Kapag handa ka na, isang magandang lugar na maaring magsimula ay ang [mga "
"inskripsiyon](guides/inscriptions.md), isang kakaibang uri ng digital "
"artifact na pinagana ng Ordinal Theory."

#: src/introduction.md:35
msgid "Links"
msgstr "Mga Link"

#: src/introduction.md:38
msgid "[GitHub](https://github.com/ordinals/ord/)"
msgstr "[GitHub](https://github.com/ordinals/ord/)"

#: src/introduction.md:39
msgid "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src/introduction.md:40
msgid "[Discord](https://discord.gg/ordinals)"
msgstr "[Discord](https://discord.gg/ordinals)"

#: src/introduction.md:41
msgid "[Open Ordinals Institute Website](https://ordinals.org/)"
msgstr "[Open Ordinals Institute Website](https://ordinals.org/)"

#: src/introduction.md:42
msgid "[Open Ordinals Institute X](https://x.com/ordinalsorg)"
msgstr "[Open Ordinals Institute X](https://x.com/ordinalsorg)"

#: src/introduction.md:43
msgid "[Mainnet Block Explorer](https://ordinals.com)"
msgstr "[Mainnet Block Explorer](https://ordinals.com)"

#: src/introduction.md:44
msgid "[Signet Block Explorer](https://signet.ordinals.com)"
msgstr "[Signet Block Explorer](https://signet.ordinals.com)"

#: src/introduction.md:46
msgid "Videos"
msgstr "Mga Video"

#: src/introduction.md:49
msgid ""
"[Ordinal Theory Explained: Satoshi Serial Numbers and NFTs on Bitcoin]"
"(https://www.youtube.com/watch?v=rSS0O2KQpsI)"
msgstr ""
"[Ordinal Theory Explained: Satoshi Serial Numbers and NFTs on Bitcoin]"
"(https://www.youtube.com/watch?v=rSS0O2KQpsI)"

#: src/introduction.md:50
msgid ""
"[Ordinals Workshop with Rodarmor](https://www.youtube.com/watch?"
"v=MC_haVa6N3I)"
msgstr ""
"[Ordinals Workshop with Rodarmor](https://www.youtube.com/watch?"
"v=MC_haVa6N3I)"

#: src/introduction.md:51
msgid ""
"[Ordinal Art: Mint Your own NFTs on Bitcoin w/ @rodarmor](https://www."
"youtube.com/watch?v=j5V33kV3iqo)"
msgstr ""
"[Ordinal Art: Mint Your own NFTs on Bitcoin w/ @rodarmor](https://www."
"youtube.com/watch?v=j5V33kV3iqo)"

#: src/overview.md:1
msgid "Ordinal Theory Overview"
msgstr "Pangkalahatang-ideya ng Ordinal Theory"

#: src/overview.md:4
msgid ""
"Ordinals are a numbering scheme for satoshis that allows tracking and "
"transferring individual sats. These numbers are called [ordinal numbers]"
"(https://ordinals.com). Satoshis are numbered in the order in which they're "
"mined, and transferred from transaction inputs to transaction outputs first-"
"in-first-out. Both the numbering scheme and the transfer scheme rely on "
"_order_, the numbering scheme on the _order_ in which satoshis are mined, "
"and the transfer scheme on the _order_ of transaction inputs and outputs. "
"Thus the name, _ordinals_."
msgstr ""
"Ang mga ordinal ay isang scheme ng pagnunumero sa satoshi na nagbibigay-daan "
"sa pagsubaybay at paglilipat ng mga indibidwal na sat. Ang mga numerong ito "
"ay tinatawag na [ordinal numbers](https://ordinals.com). Satoshis ay "
"binibilang sa pagkakasunud-sunod kung kelan ito na-mina, at inilipat mula sa "
"transaction input patungo sa transaction output na first-in-first-out "
"(FIFO). Parehong umaasa ang numbering scheme at ang transfer scheme sa "
"_order_, ang numbering scheme sa pagkakasunud-sunod kung saan na-mina ang "
"satoshi, at ang transfer scheme ng sa pagkakasunud-sunod ng mga input at "
"output ng transaksyon.Kaya naman pinangalanan itong “Ordinal”."

#: src/overview.md:13
msgid ""
"Technical details are available in [the BIP](https://github.com/ordinals/ord/"
"blob/master/bip.mediawiki)."
msgstr ""
"Ang mga teknikal na detalye ay makikita sa [BIP](https://github.com/ordinals/"
"ord/blob/master/bip.mediawiki)."

#: src/overview.md:16
msgid ""
"Ordinal theory does not require a separate token, another blockchain, or any "
"changes to Bitcoin. It works right now."
msgstr ""
"Ang Ordinal theory ay hindi nangangailangan ng isang hiwalay na token, isa "
"pang blockchain, o anumang mga pagbabago sa Bitcoin."

#: src/overview.md:19
msgid "Ordinal numbers have a few different representations:"
msgstr "Ang mga ordinal numbers ay may ilang magkakaibang representasyon:"

#: src/overview.md:21
msgid ""
"_Integer notation_: [`2099994106992659`](https://ordinals.com/"
"sat/2099994106992659) The ordinal number, assigned according to the order in "
"which the satoshi was mined."
msgstr ""
"_Integer notation_: [`2099994106992659`](https://ordinals.com/"
"sat/2099994106992659) Ang ordinal na numero, na itinalaga ayon sa "
"pagkakasunud-sunod kung saan ang satoshi ay na-mina."

#: src/overview.md:26
msgid ""
"_Decimal notation_: [`3891094.16797`](https://ordinals.com/"
"sat/3891094.16797) The first number is the block height in which the satoshi "
"was mined, the second the offset of the satoshi within the block."
msgstr ""
"_Decimal notation_: [`3891094.16797`](https://ordinals.com/"
"sat/3891094.16797) Ang unang numero ay ang taas ng block kung saan na-mina "
"ang satoshi, ang pangalawa ay offset ng satoshi sa loob ng block."

#: src/overview.md:31
msgid ""
"_Degree notation_: [`3°111094′214″16797‴`](https://ordinals.com/"
"sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). We'll get to that in "
"a moment."
msgstr ""
"_Degree notation_: [`3°111094′214″16797‴`](https://ordinals.com/"
"sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). Ating tatalakayin sa "
"ilang sandali."

#: src/overview.md:35
msgid ""
"_Percentile notation_: [`99.**************%`](https://ordinals.com/"
"sat/99.**************%25) . The satoshi's position in Bitcoin's supply, "
"expressed as a percentage."
msgstr ""
"_Percentile notation_: [`99.**************%`](https://ordinals.com/"
"sat/99.**************%25) . Ang posisyon ng satoshi sa supply ng Bitcoin, na "
"bilang isang porsyento."

#: src/overview.md:39
msgid ""
"_Name_: [`satoshi`](https://ordinals.com/sat/satoshi). An encoding of the "
"ordinal number using the characters `a` through `z`."
msgstr ""
"_Name_: [`satoshi`](https://ordinals.com/sat/satoshi). Isang encoding ng "
"ordinal number gamit ang mga character `a` hanggang `z`."

#: src/overview.md:42
msgid ""
"Arbitrary assets, such as NFTs, security tokens, accounts, or stablecoins "
"can be attached to satoshis using ordinal numbers as stable identifiers."
msgstr ""
"Ang mga arbitrary na asset, gaya ng mga NFT, security token, account, o "
"stablecoin ay maaaring i-attach sa satoshis gamit ang ordinal numbers bilang "
"stable identifier."

#: src/overview.md:45
msgid ""
"Ordinals is an open-source project, developed [on GitHub](https://github.com/"
"ordinals/ord). The project consists of a BIP describing the ordinal scheme, "
"an index that communicates with a Bitcoin Core node to track the location of "
"all satoshis, a wallet that allows making ordinal-aware transactions, a "
"block explorer for interactive exploration of the blockchain, functionality "
"for inscribing satoshis with digital artifacts, and this manual."
msgstr ""

#: src/overview.md:52
msgid "Rarity"
msgstr "Rarity"

#: src/overview.md:55
msgid ""
"Humans are collectors, and since satoshis can now be tracked and "
"transferred, people will naturally want to collect them. Ordinal theorists "
"can decide for themselves which sats are rare and desirable, but there are "
"some hints…"
msgstr ""
"Ang mga tao ay likas na kolektor, at dahil sa ang satoshi ay maaari na "
"ngayong subaybayan at ilipat, ang mga tao ay natural na nais nang kolektahin "
"ang mga ito. Ang mga Ordinal theorists ay maaaring magpasya para sa kanilang "
"sarili kung aling mga sats ang rare at kanais-nais, ngunit may mga ilang "
"pahiwatig..."

#: src/overview.md:59
msgid ""
"Bitcoin has periodic events, some frequent, some more uncommon, and these "
"naturally lend themselves to a system of rarity. These periodic events are:"
msgstr ""
"Ang Bitcoin ay may mga pana-panahong kaganapan, ang ilan ay madalas, ang "
"ilan ay hindi karaniwan, at ang mga ito ay natural. Ang mga pana-panahong "
"pangyayaring ito ay:"

#: src/overview.md:62
msgid ""
"_Blocks_: A new block is mined approximately every 10 minutes, from now "
"until the end of time."
msgstr ""
"_Blocks_: Ang isang bagong bloke ay na-mimina humigit-kumulang bawat 10 "
"minuto, mula ngayon hanggang sa katapusan ng panahon."

#: src/overview.md:65
msgid ""
"_Difficulty adjustments_: Every 2016 blocks, or approximately every two "
"weeks, the Bitcoin network responds to changes in hashrate by adjusting the "
"difficulty target which blocks must meet in order to be accepted."
msgstr ""
"_Difficulty adjustments_: Bawat 2016 block, o humigit-kumulang bawat "
"dalawang linggo, ang Bitcoin network ay tumutugon sa mga pagbabago sa "
"hashrate sa pamamagitan ng pagsasaayos sa difficulty target kung aling mga "
"bloke ang dapat matugunan upang matanggap."

#: src/overview.md:69
msgid ""
"_Halvings_: Every 210,000 blocks, or roughly every four years, the amount of "
"new sats created in every block is cut in half."
msgstr ""
"_Halvings_: Bawat 210,000 block, o humigit-kumulang bawat apat na taon, ang "
"dami ng mga bagong sats na nalikha sa bawat block ay hinahati sa kalahati."

#: src/overview.md:72
msgid ""
"_Cycles_: Every six halvings, something magical happens: the halving and the "
"difficulty adjustment coincide. This is called a conjunction, and the time "
"period between conjunctions a cycle. A conjunction occurs roughly every 24 "
"years. The first conjunction should happen sometime in 2032."
msgstr ""
"_Cycles_: Bawat anim na paghahati, may mahiwagang nangyayari: ang paghahati "
"at ang difficulty target ay nagsasabay. Ito ay tinatawag na conjunction, at "
"ang yugto ng panahon sa pagitan ng mga conjunction ay isang cycle. Ang isang "
"conjunction ay nangyayari halos bawat 24 na taon. Inaasahan na mangyayari "
"ang unang conjunction sa 2023."

#: src/overview.md:77
msgid "This gives us the following rarity levels:"
msgstr "Nagbibigay ito sa atin ng mga sumusunod na antas ng rarity:"

#: src/overview.md:79
msgid "`common`: Any sat that is not the first sat of its block"
msgstr "`common`: Mga sat na hindi na-una sa block"

#: src/overview.md:80
msgid "`uncommon`: The first sat of each block"
msgstr "`uncommon`: Ang unang sat sa bawat block"

#: src/overview.md:81
msgid "`rare`: The first sat of each difficulty adjustment period"
msgstr "`rare`: Ang unang sat sa bawat difficulty target"

#: src/overview.md:82
msgid "`epic`: The first sat of each halving epoch"
msgstr "`epic`: Ang unang sat ng bawat halvings"

#: src/overview.md:83
msgid "`legendary`: The first sat of each cycle"
msgstr "`legendary`: Ang unang sat ng bawat cycle"

#: src/overview.md:84
msgid "`mythic`: The first sat of the genesis block"
msgstr "`mythic`: Ang kauna-unahang sat ng pinaka-unang block"

#: src/overview.md:86
msgid ""
"Which brings us to degree notation, which unambiguously represents an "
"ordinal number in a way that makes the rarity of a satoshi easy to see at a "
"glance:"
msgstr ""
"Na nagdadala sa atin sa degree notation, na malinaw na kumakatawan sa isang "
"ordinal number sa paraang ginagawang madaling makita ang rarity ng isang "
"satoshi sa isang sulyap:"

#: src/overview.md:97
msgid ""
"Ordinal theorists often use the terms \"hour\", \"minute\", \"second\", and "
"\"third\" for _A_, _B_, _C_, and _D_, respectively."
msgstr ""
"Ang Ordinal theorists ay kadalasang gumagamit ng mga termino \"hour\", "
"\"minute\", \"second\", at \"third\" for _A_, _B_, _C_, and _D_, ayon sa "
"pagkakabanggit."

#: src/overview.md:100
msgid "Now for some examples. This satoshi is common:"
msgstr "Ngayon para sa ilang mga halimbawa. Ang satoshi na ito ay common:"

#: src/overview.md:111
msgid "This satoshi is uncommon:"
msgstr "Ang satoshi na ito ay uncommon:"

#: src/overview.md:121
msgid "This satoshi is rare:"
msgstr "Ang satoshi na ito ay rare:"

#: src/overview.md:131
msgid "This satoshi is epic:"
msgstr "Ang satoshi na ito ay epic:"

#: src/overview.md:141
msgid "This satoshi is legendary:"
msgstr "Ang satoshi na ito ay legendary:"

#: src/overview.md:151
msgid "And this satoshi is mythic:"
msgstr "At ang mythic na satoshi:"

#: src/overview.md:161
msgid ""
"If the block offset is zero, it may be omitted. This is the uncommon satoshi "
"from above:"
msgstr ""
"Kung ang block offset ay zero, maaari itong alisin. Ito ang hindi "
"pangkaraniwang satoshi mula sa itaas:"

#: src/overview.md:171
msgid "Rare Satoshi Supply"
msgstr "Rare Satoshi Supply"

#: src/overview.md:174
msgid "Total Supply"
msgstr "Kabuuang Supply"

#: src/overview.md:176
msgid "`common`: 2.1 quadrillion"
msgstr "`common`: 2.1 quadrillion"

#: src/overview.md:177
msgid "`uncommon`: 6,929,999"
msgstr "`uncommon`: 6,929,999"

#: src/overview.md:178
msgid "`rare`: 3437"
msgstr "`rare`: 3437"

#: src/overview.md:179
msgid "`epic`: 32"
msgstr "`epic`: 32"

#: src/overview.md:180
msgid "`legendary`: 5"
msgstr "`legendary`: 5"

#: src/overview.md:181 src/overview.md:190
msgid "`mythic`: 1"
msgstr "`mythic`: 1"

#: src/overview.md:183
msgid "Current Supply"
msgstr "Kasalukuyang Supply"

#: src/overview.md:185
msgid "`common`: 1.9 quadrillion"
msgstr "`common`: 1.9 quadrillion"

#: src/overview.md:186
msgid "`uncommon`: 808,262"
msgstr "`uncommon`: 808,262"

#: src/overview.md:187
msgid "`rare`: 369"
msgstr "`rare`: 369"

#: src/overview.md:188
msgid "`epic`: 3"
msgstr "`epic`: 3"

#: src/overview.md:189
msgid "`legendary`: 0"
msgstr "`legendary`: 0"

#: src/overview.md:192
msgid ""
"At the moment, even uncommon satoshis are quite rare. As of this writing, "
"745,855 uncommon satoshis have been mined - one per 25.6 bitcoin in "
"circulation."
msgstr ""
"Sa ngayon, kahit na ang hindi pangkaraniwang satoshi ay medyo bihira. Sa "
"pagsulat na ito, 745,855 na hindi pangkaraniwang satoshi ang namina - isa sa "
"bawat 25.6 bitcoin ng sirkulasyon."

#: src/overview.md:196
msgid "Names"
msgstr "Mga pangalan"

#: src/overview.md:199
msgid ""
"Each satoshi has a name, consisting of the letters _A_ through _Z_, that get "
"shorter the further into the future the satoshi was mined. They could start "
"short and get longer, but then all the good, short names would be trapped in "
"the unspendable genesis block."
msgstr ""
"Ang bawat satoshi ay may pangalan, na binubuo ng mga letrang _A_ hanggang "
"_Z_, na nagiging mas mas maikli sa hinaharap na pag-mina ang satoshi. Maaari "
"silang magsimula nang mas maikli at mas mahaba, ngunit pagkatapos ay ang "
"lahat ng magagandang, maiikling pangalan ay maiipit sa hindi magugugol na "
"genesis block."

#: src/overview.md:204
msgid ""
"As an example, 1905530482684727°'s name is \"iaiufjszmoba\". The name of the "
"last satoshi to be mined is \"a\". Every combination of 10 characters or "
"less is out there, or will be out there, someday."
msgstr ""
"Bilang halimbawa, ang pangalan ng 1905530482684727°'s ay \"iaiufjszmoba\". "
"Ang pangalan ng huling satoshi na namina ay \"a\". Ang bawat kumbinasyon ng "
"10 character o mas kakaunti ay lalabas doon, balang araw."

#: src/overview.md:208
msgid "Exotics"
msgstr "Exotics"

#: src/overview.md:211
msgid ""
"Satoshis may be prized for reasons other than their name or rarity. This "
"might be due to a quality of the number itself, like having an integer "
"square or cube root. Or it might be due to a connection to a historical "
"event, such as satoshis from block 477,120, the block in which SegWit "
"activated, or 2099999997689999°, the last satoshi that will ever be mined."
msgstr ""
"Maaaring mahalaga ang Satoshi para sa mga dahilan maliban sa kanilang "
"pangalan o rarity. Maaaring dahil ito sa kalidad ng numero mismo, tulad ng "
"pagkakaroon ng integer square o cube root. O maaaring dahil ito sa isang "
"koneksyon sa isang makasaysayang kaganapan, tulad ng satoshis mula sa block "
"477,120, ang block kung saan na-activate ang SegWit, o 2099999997689999°, "
"ang huling satoshi na mamimina."

#: src/overview.md:217
msgid ""
"Such satoshis are termed \"exotic\". Which satoshis are exotic and what "
"makes them so is subjective. Ordinal theorists are encouraged to seek out "
"exotics based on criteria of their own devising."
msgstr ""
"Ang ganitong mga satoshi ay tinatawag na \"exotic\". Aling satoshi ang "
"kakaiba at kung ano ang dahilan ng mga ito ay subjective. Ang mga ordinal na "
"theorist ay hinihikayat na maghanap ng mga exotics batay sa pamantayan ng "
"kanilang sariling interpertasyon."

#: src/overview.md:224
msgid ""
"Satoshis can be inscribed with arbitrary content, creating Bitcoin-native "
"digital artifacts. Inscribing is done by sending the satoshi to be inscribed "
"in a transaction that reveals the inscription content on-chain. This content "
"is then inextricably linked to that satoshi, turning it into an immutable "
"digital artifact that can be tracked, transferred, hoarded, bought, sold, "
"lost, and rediscovered."
msgstr ""

#: src/overview.md:231
msgid "Archaeology"
msgstr "Arkeolohiya"

#: src/overview.md:234
msgid ""
"A lively community of archaeologists devoted to cataloging and collecting "
"early NFTs has sprung up. [Here's a great summary of historical NFTs by "
"Chainleft.](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-"
"N29oF4iwCgX3lacrvaG9Kjko)"
msgstr ""
"Ang isang komunidad ng mga arkeologo na nakatuon sa pag-catalog at "
"pagkolekta ng mga maagang NFT ay lumitaw. Narito ang isang buod ng mga "
"makasaysayang NFT ng [Chainleft.](https://mirror.xyz/chainleft.eth/"
"MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)"

#: src/overview.md:238
msgid ""
"A commonly accepted cut-off for early NFTs is March 19th, 2018, the date the "
"first ERC-721 contract, [SU SQUARES](https://tenthousandsu.com/), was "
"deployed on Ethereum."
msgstr ""
"Ang karaniwang tinatanggap na cut-off para sa mga maagang NFT ay Marso 19, "
"2018, ang petsa na ang unang kontrata ng ERC-721, [SU SQUARES](https://"
"tenthousandsu.com/), ay na-deploy sa Ethereum."

#: src/overview.md:242
msgid ""
"Whether or not ordinals are of interest to NFT archaeologists is an open "
"question! In one sense, ordinals were created in early 2022, when the "
"Ordinals specification was finalized. In this sense, they are not of "
"historical interest."
msgstr ""
"Kung ang NFT archaeologists ay interesado o hindi sa ordinals ay wala pang "
"kasagutan! Sa isang kahulugan, ginawa ang mga ordinal noong unang bahagi ng "
"2022, nang ang detalye ng Ordinal ay na-finalize. Sa ganitong kahulugan, "
"hindi sila kabilang bilang isang historical interest."

#: src/overview.md:247
msgid ""
"In another sense though, ordinals were in fact created by Satoshi Nakamoto "
"in 2009 when he mined the Bitcoin genesis block. In this sense, ordinals, "
"and especially early ordinals, are certainly of historical interest."
msgstr ""
"Gayunpaman, sa ibang kahulugan, ang mga ordinal ay sa katunayan ay nilikha "
"ni Satoshi Nakamoto noong 2009 nang siya ay nag-mina ng Bitcoin genesis "
"block. Sa ganitong diwa, ang mga ordinal, at lalo na ang mga maagang "
"ordinal, ay tiyak na may interes sa kasaysayan."

#: src/overview.md:251
msgid ""
"Many ordinal theorists favor the latter view. This is not least because the "
"ordinals were independently discovered on at least two separate occasions, "
"long before the era of modern NFTs began."
msgstr ""
"Maraming ordinal theorists ang pumapabor sa huling pananaw. Ito ay hindi "
"bababa sa dahil ang mga ordinal ay natuklasan sa hindi bababa sa dalawang "
"magkahiwalay na okasyon, bago pa nagsimula ang panahon ng mga modernong NFT."

#: src/overview.md:255
msgid ""
"On August 21st, 2012, Charlie Lee [posted a proposal to add proof-of-stake "
"to Bitcoin to the Bitcoin Talk forum](https://bitcointalk.org/index.php?"
"topic=102355.0). This wasn't an asset scheme, but did use the ordinal "
"algorithm, and was implemented but never deployed."
msgstr ""
"Noong Agosto 21, 2012, nag-post si Charlie Lee ng [panukalang magdagdag ng "
"proof-of-stake sa Bitcoin sa Bitcoin Talk forum](https://bitcointalk.org/"
"index.php?topic=102355.0). Hindi ito isang scheme ng asset, ngunit ginamit "
"ang ordinal na algorithm, at ipinatupad ngunit hindi kailanman na-deploy."

#: src/overview.md:261
msgid ""
"On October 8th, 2012, jl2012 [posted a scheme to the same forum](https://"
"bitcointalk.org/index.php?topic=117224.0) which uses decimal notation and "
"has all the important properties of ordinals. The scheme was discussed but "
"never implemented."
msgstr ""
"Noong ika-8 ng Oktubre, 2012, [nag-post si jl2012 ng scheme sa parehong "
"forum](https://bitcointalk.org/index.php?topic=117224.0) na gumagamit ng "
"decimal notation at mayroong lahat ng mahahalagang katangian ng mga ordinal. "
"Napag-usapan ang iskema ngunit hindi naipatupad."

#: src/overview.md:266
msgid ""
"These independent inventions of ordinals indicate in some way that ordinals "
"were discovered, or rediscovered, and not invented. The ordinals are an "
"inevitability of the mathematics of Bitcoin, stemming not from their modern "
"documentation, but from their ancient genesis. They are the culmination of a "
"sequence of events set in motion with the mining of the first block, so many "
"years ago."
msgstr ""
"Ang mga imbensyon ng mga ordinal na ito ay nagpapahiwatig sa ilang paraan na "
"ang mga ordinal ay natuklasan, o muling natuklasan, at hindi naimbento. Ang "
"mga ordinal ay isang hindi maiiwasang mathematics ng Bitcoin, hindi "
"nagmumula sa kanilang modernong dokumentasyon, ngunit mula sa kanilang "
"sinaunang genesis. Ang mga ito ay ang paghantong ng isang pagkakasunud-sunod "
"ng mga kaganapan na itinakda sa pagmimina ng unang bloke, napakaraming taon "
"na ang nakalilipas."

#: src/digital-artifacts.md:4
msgid ""
"Imagine a physical artifact. A rare coin, say, held safe for untold years in "
"the dark, secret clutch of a Viking hoard, now dug from the earth by your "
"grasping hands. It…"
msgstr ""
"Isipin ang isang pisikal na artifact. Ang isang rare na barya, halimbawa, ay "
"itinatago sa loob ng maraming taon sa dilim, lihim na pagkakahawak ng isang "
"Viking hoard, na ngayon ay hinukay mula sa lupa sa pamamagitan ng iyong mga "
"kamay na nakahawak. Ito..."

#: src/digital-artifacts.md:8
msgid ""
"…has an owner. You. As long as you keep it safe, nobody can take it from you."
msgstr ""
"...ay may may-ari. Ikaw. Hangga't pinapanatili mo itong ligtas, walang "
"sinuman ang makakakuha nito mula sa iyo."

#: src/digital-artifacts.md:10
msgid "…is complete. It has no missing parts."
msgstr "...ay kumpleto. Wala itong nawawalang bahagi."

#: src/digital-artifacts.md:12
msgid ""
"…can only be changed by you. If you were a trader, and you made your way to "
"18th century China, none but you could stamp it with your chop-mark."
msgstr ""
"...ay mapapalitan mo lang. Kung ikaw ay isang mangangalakal, at nagpunta ka "
"sa ika-18 siglong Tsina, maaari mong tatakan ito ng iyong chop-mark."

#: src/digital-artifacts.md:15
msgid ""
"…can only be disposed of by you. The sale, trade, or gift is yours to make, "
"to whomever you wish."
msgstr ""
"...ay maaari mo itapon. Ang pagbebenta, pangangalakal, o regalo, sa sinumang "
"nais mo."

#: src/digital-artifacts.md:18
msgid ""
"What are digital artifacts? Simply put, they are the digital equivalent of "
"physical artifacts."
msgstr ""
"Ano ang mga digital artifact? Sa madaling salita, sila ang digital na "
"katumbas ng mga pisikal na artifact."

#: src/digital-artifacts.md:21
msgid ""
"For a digital thing to be a digital artifact, it must be like that coin of "
"yours:"
msgstr ""
"Para maging isang digital na artifact ang isang digital na bagay, dapat "
"itong katulad ng barya mo:"

#: src/digital-artifacts.md:24
msgid ""
"Digital artifacts can have owners. A number is not a digital artifact, "
"because nobody can own it."
msgstr ""
"Maaaring magkaroon ng mga may-ari ang mga digital artifact. Ang isang numero "
"ay hindi isang digital na artifact, dahil walang sinuman ang maaaring magmay-"
"ari nito."

#: src/digital-artifacts.md:27
msgid ""
"Digital artifacts are complete. An NFT that points to off-chain content on "
"IPFS or Arweave is incomplete, and thus not a digital artifact."
msgstr ""
"Kumpleto ang digital artifact. Ang isang NFT na nasa off-chain na IPFS o "
"Arweave ay hindi kumpleto, at sa gayon ay hindi isang digital na artifact."

#: src/digital-artifacts.md:30
msgid ""
"Digital artifacts are permissionless. An NFT which cannot be sold without "
"paying a royalty is not permissionless, and thus not a digital artifact."
msgstr ""
"Ang mga digital artifact ay permissionless. Ang isang NFT na hindi maaaring "
"ibenta nang hindi nagbabayad ng royalty ay hindi permissionless, at sa gayon "
"ay hindi isang digital artifact."

#: src/digital-artifacts.md:33
msgid ""
"Digital artifacts are uncensorable. Perhaps you can change a database entry "
"on a centralized ledger today, but maybe not tomorrow, and thus one cannot "
"be a digital artifact."
msgstr ""
"Ang mga digital artifact ay uncensorable. Marahil ay maaari mong baguhin ang "
"isang database entry sa isang sentralisadong ledger ngayon, ngunit maaaring "
"hindi bukas, at sa gayon ang isa ay hindi maaaring maging isang digital "
"artifact."

#: src/digital-artifacts.md:37
msgid ""
"Digital artifacts are immutable. An NFT with an upgrade key is not a digital "
"artifact."
msgstr ""
"Ang mga digital artifact ay hindi nababago. Ang isang NFT na may upgrade key "
"ay hindi isang digital artifact."

#: src/digital-artifacts.md:40
msgid ""
"The definition of a digital artifact is intended to reflect what NFTs "
"_should_ be, sometimes are, and what inscriptions _always_ are, by their "
"very nature."
msgstr ""
"Ang kahulugan ng isang digital artifact ay nilayon upang ipakita kung ano "
"_dapat_ ang mga NFT, kung minsan, at kung _ano_ ang inskripsiyon, ayon sa "
"kanilang likas na katangian."

#: src/inscriptions.md:4
msgid ""
"Inscriptions inscribe sats with arbitrary content, creating bitcoin-native "
"digital artifacts, more commonly known as NFTs. Inscriptions do not require "
"a sidechain or separate token."
msgstr ""
"Ang inscriptions ay sats na naglalaman ng arbitraryong content, na lumilikha "
"ng bitcoin-native digital artifact, na mas karaniwang kilala bilang NFTs. "
"Ang mga inskripsiyon ay hindi nangangailangan ng sidechain o hiwalay na "
"token."

#: src/inscriptions.md:8
msgid ""
"These inscribed sats can then be transferred using bitcoin transactions, "
"sent to bitcoin addresses, and held in bitcoin UTXOs. These transactions, "
"addresses, and UTXOs are normal bitcoin transactions, addresses, and UTXOS "
"in all respects, with the exception that in order to send individual sats, "
"transactions must control the order and value of inputs and outputs "
"according to ordinal theory."
msgstr ""
"Ang mga naka-inscribe na sat na ito ay maaaring ilipat gamit ang mga "
"transaksyon sa bitcoin, ipadala sa mga bitcoin address, at gamitin sa mga "
"bitcoin UTXO. Ang mga transaksyon, address, at UTXO na ito ay normal na mga "
"transaksyon sa bitcoin, address, at UTXOS sa lahat ng aspeto, maliban nalang "
"para makapagpadala ng mga indibidwal na sats, dapat kontrolin ng transaksyon "
"ang pagkakasunud-sunod at halaga ng mga input at output ayon sa ordinal "
"theory."

#: src/inscriptions.md:15
msgid ""
"The inscription content model is that of the web. An inscription consists of "
"a content type, also known as a MIME type, and the content itself, which is "
"a byte string. This allows inscription content to be returned from a web "
"server, and for creating HTML inscriptions that use and remix the content of "
"other inscriptions."
msgstr ""
"Ang modelo ng inscription content ay sa web. Ang isang inskripsiyon ay "
"binubuo ng isang uri ng nilalaman, na kilala rin bilang MIME type, at ang "
"nilalaman mismo ay isang byte string. Ito ay nagbibigay-daan sa nilalaman ng "
"inskripsiyon na maibalik mula sa isang web server, at para sa paglikha ng "
"mga HTML inscriptions na gumagamit at nagbubuo ng nilalaman ng iba pang mga "
"inskripsiyon."

#: src/inscriptions.md:21
msgid ""
"Inscription content is entirely on-chain, stored in taproot script-path "
"spend scripts. Taproot scripts have very few restrictions on their content, "
"and additionally receive the witness discount, making inscription content "
"storage relatively economical."
msgstr ""
"Ang nilalaman ng inskripsiyon ay on-chain, na naka-imbak sa taproot script-"
"path spend scripts. Ang mga script ng Taproot ay may limitasyon sa kanilang "
"nilalaman, at bukod pa rito ay tumatanggap ng witness discount, na "
"ginagawang matipid ang pag-iimbak ang nilalaman ng isang inskripsiyon."

#: src/inscriptions.md:26
msgid ""
"Since taproot script spends can only be made from existing taproot outputs, "
"inscriptions are made using a two-phase commit/reveal procedure. First, in "
"the commit transaction, a taproot output committing to a script containing "
"the inscription content is created. Second, in the reveal transaction, the "
"output created by the commit transaction is spent, revealing the inscription "
"content on-chain."
msgstr ""
"Dahil ang spend ng taproot script ay maaari lamang gawin mula sa mga "
"kasalukuyang taproot output, ang mga inskripsiyon ay ginawa gamit ang isang "
"two-phase commit/reveal procedure. Una, sa commit transaction, isang taproot "
"output na nag-committ sa isang script na naglalaman ng inskripsyon ay nabuo. "
"Pangalawa, sa reveal ng transaksyon, ang output na nilikha ng commit na "
"transaksyon ay ginamit sa pagbayad, na mag-rereveal ng inscription content "
"sa on-chain."

#: src/inscriptions.md:33
msgid ""
"Inscription content is serialized using data pushes within unexecuted "
"conditionals, called \"envelopes\". Envelopes consist of an `OP_FALSE OP_IF "
"… OP_ENDIF` wrapping any number of data pushes. Because envelopes are "
"effectively no-ops, they do not change the semantics of the script in which "
"they are included, and can be combined with any other locking script."
msgstr ""
"Ang nilalaman ng inskripsiyon ay naka-serialize gamit ang data pushes sa "
"loob ng mga hindi naisagawang kondisyon, na tinatawag na \"envelopes\". Ang "
"envelopes ay binubuo ng isang `OP_FALSE OP_IF ...OP_ENDIF` na nag-wawrap sa "
"kahit anong numero ng data pushes. Dahil ang envelopes ay epektibong no-ops, "
"hindi nito binabago ang semantika ng script kung saan kasama ang mga ito, at "
"maaaring isama sa anumang iba pang locking script."

#: src/inscriptions.md:39
msgid ""
"A text inscription containing the string \"Hello, world!\" is serialized as "
"follows:"
msgstr ""
"Isang text inscription na naglalaman ng string na \"Hello, world!\" ay "
"serialized tulad ng sumusunod:"

#: src/inscriptions.md:42
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions.md:53
msgid ""
"First the string `ord` is pushed, to disambiguate inscriptions from other "
"uses of envelopes."
msgstr ""
"Una ang string na `ord` ay pushed, upang i-dismbiguate ang mga inskripsiyon "
"mula sa iba pang gamit ng envelopes."

#: src/inscriptions.md:56
msgid ""
"`OP_PUSH 1` indicates that the next push contains the content type, and "
"`OP_PUSH 0`indicates that subsequent data pushes contain the content itself. "
"Multiple data pushes must be used for large inscriptions, as one of "
"taproot's few restrictions is that individual data pushes may not be larger "
"than 520 bytes."
msgstr ""
"Ang `OP_PUSH 1` ay nagpapahiwatig na ang susunod na push ay naglalaman ng "
"content type, at `OP_PUSH 0` naman ay nagpapahiwatig na ang mga kasunod na "
"data ay naglalaman ng content mismo. Dapat gumamit ng maraming push data "
"para sa isang malalaking inskripsiyon, dahil ito sa limitasyon ng taproot, "
"na kung saan hindi maaring lumagpas sa 520 bytes ang bawat isang data push."

#: src/inscriptions.md:62
msgid ""
"The inscription content is contained within the input of a reveal "
"transaction, and the inscription is made on the first sat of its input. This "
"sat can then be tracked using the familiar rules of ordinal theory, allowing "
"it to be transferred, bought, sold, lost to fees, and recovered."
msgstr ""
"Ang nilalaman ng inskripsyon ay nakapaloob sa input ng isang naka-reveal na "
"transaksyon sa Bitcoin, at ang inskripsyon ay nagawa sa unang sat ng input "
"nito. Ang sat na ito ay maaaring masubaybayan gamit ang pamilyar na mga "
"panuntunan ng ordinal theory, na nagreresulta sa ito na ilipat, bilhin, "
"ibenta, mawala sa mga fees, at ma-recover."

#: src/inscriptions.md:67
msgid "Content"
msgstr "Content"

#: src/inscriptions.md:70
msgid ""
"The data model of inscriptions is that of a HTTP response, allowing "
"inscription content to be served by a web server and viewed in a web browser."
msgstr ""
"Ang data model ng mga inskripsiyon ay isang HTTP response, na nagbibigay-"
"daan sa nilalaman ng inskripsiyon na maipakita gamit ang web server at nang "
"web browser."

#: src/inscriptions.md:73
msgid "Fields"
msgstr "Fields"

#: src/inscriptions.md:76
msgid ""
"Inscriptions may include fields before an optional body. Each field consists "
"of two data pushes, a tag and a value."
msgstr ""
"Ang inskripsiyon ay maaring magkaroon ng fields bago ang optional body. Ang "
"bawat field ay binubuo ng dalawang data pushes, isang tag at isang value."

#: src/inscriptions.md:79
msgid ""
"Currently, the only defined field is `content-type`, with a tag of `1`, "
"whose value is the MIME type of the body."
msgstr ""
"Sa itaas na halimbawa, ang tanging tinukoy na field ay `content-type`, na "
"may tag na `1`, na ang halaga ay ang uri ng MIME ng body (text/plain;"
"charset=utf-8)."

#: src/inscriptions.md:82
msgid ""
"The beginning of the body and end of fields is indicated with an empty data "
"push."
msgstr ""
"Ang simula ng body at dulo ng mga fields ay mayroong isang walang laman na "
"data push."

#: src/inscriptions.md:85
msgid ""
"Unrecognized tags are interpreted differently depending on whether they are "
"even or odd, following the \"it's okay to be odd\" rule used by the "
"Lightning Network."
msgstr ""
"Ang mga hindi unkown tag ay sinusuri depende sa kung even o odd ang mga ito, "
"na sumusunod sa panuntunang \"it's okay to be odd\" na ginagamit ng "
"Lightning Network."

#: src/inscriptions.md:89
msgid ""
"Even tags are used for fields which may affect creation, initial assignment, "
"or transfer of an inscription. Thus, inscriptions with unrecognized even "
"fields must be displayed as \"unbound\", that is, without a location."
msgstr ""
"Ang mga tag na ginagamit para sa fields ay maaaring makaapekto sa pag-"
"create, paunang pagtatalaga, o paglipat ng isang inskripsiyon. Kaya, ang mga "
"inskripsiyon na unknown at kahit na ang fields ay dapat na ipakita bilang "
"\"unbound\", iyon ay, walang lokasyon."

#: src/inscriptions.md:93
msgid ""
"Odd tags are used for fields which do not affect creation, initial "
"assignment, or transfer, such as additional metadata, and thus are safe to "
"ignore."
msgstr ""
"Odd tags ay hindi nakakaapekto sa paggawa, paunang pagtatalaga, o "
"paglilipat, gaya ng karagdagang metadata, at sa gayon ay ligtas na huwag "
"pansinin."

#: src/inscriptions.md:96
msgid "Inscription IDs"
msgstr "Inscription IDs"

#: src/inscriptions.md:99
msgid ""
"The inscriptions are contained within the inputs of a reveal transaction. In "
"order to uniquely identify them they are assigned an ID of the form:"
msgstr ""
"Ang nilalaman ng inskripsyon ay nakapaloob sa input ng isang naka-reveal na "
"transaksyon. Upang natatanging makilala ang mga ito, binibigyan sila ng ID, "
"tulad ng:"

#: src/inscriptions.md:102
msgid "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"
msgstr "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"

#: src/inscriptions.md:104
msgid ""
"The part in front of the `i` is the transaction ID (`txid`) of the reveal "
"transaction. The number after the `i` defines the index (starting at 0) of "
"new inscriptions being inscribed in the reveal transaction."
msgstr ""
"Ang bahagi sa harap na `i` ay ang transaction ID (txid) ng reveal na "
"transaksyon. Ang numero pagkatapos ng `i` ay tumutukoy sa index (nagsisimula "
"sa 0) ng mga bagong inskripsiyon ng transaksyon."

#: src/inscriptions.md:108
msgid ""
"Inscriptions can either be located in different inputs, within the same "
"input or a combination of both. In any case the ordering is clear, since a "
"parser would go through the inputs consecutively and look for all "
"inscription `envelopes`."
msgstr ""
"Maaaring matatagpuan ang mga inskripsiyon sa iba't ibang input, sa loob ng "
"parehong input o kumbinasyon ng pareho. Sa anumang kaso ang order ay "
"madaling makita, dahil ang parser nage-scan sa mga input nang sunud-sunod at "
"hahanapin ang lahat ng inskripsiyon na may `envelopes`."

#: src/inscriptions.md:112
msgid "Input"
msgstr "Input"

#: src/inscriptions.md:112
msgid "Inscription Count"
msgstr "Inscription Count"

#: src/inscriptions.md:112
msgid "Indices"
msgstr "Indices"

#: src/inscriptions.md:114 src/inscriptions.md:117
msgid "0"
msgstr "0"

#: src/inscriptions.md:114 src/inscriptions.md:116
msgid "2"
msgstr "2"

#: src/inscriptions.md:114
msgid "i0, i1"
msgstr "i0, i1"

#: src/inscriptions.md:115 src/inscriptions.md:118
msgid "1"
msgstr "1"

#: src/inscriptions.md:115
msgid "i2"
msgstr "i2"

#: src/inscriptions.md:116 src/inscriptions.md:117
msgid "3"
msgstr "3"

#: src/inscriptions.md:116
msgid "i3, i4, i5"
msgstr "i3, i4, i5"

#: src/inscriptions.md:118
msgid "4"
msgstr "4"

#: src/inscriptions.md:118
msgid "i6"
msgstr "i6"

#: src/inscriptions.md:120
msgid "Sandboxing"
msgstr "Sandboxing"

#: src/inscriptions.md:123
msgid ""
"HTML and SVG inscriptions are sandboxed in order to prevent references to "
"off-chain content, thus keeping inscriptions immutable and self-contained."
msgstr ""
"Ang mga inskripsiyon na gaya ng HTML at SVG ay nasa-sandbox upang maiwasan "
"ang ma-reference sa off-chain content, sa gayo'y pinapanatili ang mga "
"inskripsiyon na hindi nababago at self-contained."

#: src/inscriptions.md:126
msgid ""
"This is accomplished by loading HTML and SVG inscriptions inside `iframes` "
"with the `sandbox` attribute, as well as serving inscription content with "
"`Content-Security-Policy` headers."
msgstr ""
"Nagagawa ito sa pamamagitan ng pag-load sa mga HTML at SVG na inskripsiyon "
"sa loob `iframes` na may `sandbox` na katangian, pati na rin ang pag-serve "
"ng nilalaman ng inskripsiyon na may `Content-Security-Policy` sa header."

#: src/inscriptions/metadata.md:4
msgid ""
"Inscriptions may include [CBOR](https://cbor.io/) metadata, stored as data "
"pushes in fields with tag `5`. Since data pushes are limited to 520 bytes, "
"metadata longer than 520 bytes must be split into multiple tag `5` fields, "
"which will then be concatenated before decoding."
msgstr ""
"Ang iscriptions ay maaring magkaroon ng [CBOR](https://cbor.io/) metadata, na naka-store bilang data "
"pushes sa fields na may tag `5`. Dahil limitado ang data pushes sa 520 bytes, "
"Ang metadata na mas mahaba sa 520 byte ay dapat hatiin sa maraming tag na `5` fields, "
"at pagkatapos ay pagsasamahin bago mag-decoding."

#: src/inscriptions/metadata.md:9
msgid ""
"Metadata is human readable, and all metadata will be displayed to the user "
"with its inscription. Inscribers are encouraged to consider how metadata "
"will be displayed, and make metadata concise and attractive."
msgstr ""
"Ang metadata ay madaling basahin, at lahat ng metadata ay ipapakita sa user "
"kasama ang inskripsiyon nito. Hinihikayat ang mga inscriber na isaalang-alang kung paano ang metadata "
"ay ipapakita, at gagawing maigsi at kaakit-akit ang metadata."

#: src/inscriptions/metadata.md:13
msgid "Metadata is rendered to HTML for display as follows:"
msgstr "Ang metadata ay nai-render sa HTML para ipakita tulad ng sumusunod:"

#: src/inscriptions/metadata.md:15
msgid ""
"`null`, `true`, `false`, numbers, floats, and strings are rendered as plain "
"text."
msgstr ""
"`null`, `true`, `false`, numbers, floats, at strings ay rendered bilang isang plain "
"text."

#: src/inscriptions/metadata.md:17
msgid "Byte strings are rendered as uppercase hexadecimal."
msgstr "Ang Byte strings ay rendered bilang isang uppercase na hexadecimal."

#: src/inscriptions/metadata.md:18
msgid ""
"Arrays are rendered as `<ul>` tags, with every element wrapped in `<li>` "
"tags."
msgstr ""
"Ang Arrays ay rendered gamit ang `<ul>` tags, kung saan ang mga element ay naka-wrapped sa `<li>` "
"tags."

#: src/inscriptions/metadata.md:20
msgid ""
"Maps are rendered as `<dl>` tags, with every key wrapped in `<dt>` tags, and "
"every value wrapped in `<dd>` tags."
msgstr ""
"Maps ay rendered gamit ang `<dl>` tags, kung saan ang bawat key ay wrapped sa `<dt>` tags, at "
"ang value nito ay naka-wrapped sa `<dd>` tags."

#: src/inscriptions/metadata.md:22
msgid ""
"Tags are rendered as the tag , enclosed in a `<sup>` tag, followed by the "
"value."
msgstr ""
"Tags ay rendered bilang tag , na naka-enclosed sa `<sup>` tag, kasunod ng "
"value."

#: src/inscriptions/metadata.md:25
msgid ""
"CBOR is a complex spec with many different data types, and multiple ways of "
"representing the same data. Exotic data types, such as tags, floats, and "
"bignums, and encoding such as indefinite values, may fail to display "
"correctly or at all. Contributions to `ord` to remedy this are welcome."
msgstr ""
"Ang CBOR ay isang kumplikadong spec na may maraming iba't ibang uri ng data, at maraming paraan ng "
"na kumakatawan sa parehong data. Mga kakaibang uri ng data, gaya ng mga tag, float, at "
"Ang mga bignum, at pag-encode tulad ng mga hindi tiyak na halaga, ay maaaring mabigong ipakita "
"tama o sa lahat. Ang mga kontribusyon sa `ord` upang malunasan ito ay malugod na tinatanggap."

#: src/inscriptions/metadata.md:30 src/inscriptions/provenance.md:27
#: src/guides/testing.md:18 src/guides/reindexing.md:15
msgid "Example"
msgstr "Halimbawa"

#: src/inscriptions/metadata.md:33
msgid ""
"Since CBOR is not human readable, in these examples it is represented as "
"JSON. Keep in mind that this is _only_ for these examples, and JSON metadata "
"will _not_ be displayed correctly."
msgstr ""
"Dahil ang CBOR ay hindi madaling basahin, sa mga halimbawang ito ay kinakatawan ito bilang "
"JSON. Tandaan na ito ay halimbawa lamang, at ang JSON metadata "
"ay _hindi_ mapapakita nang tama."

#: src/inscriptions/metadata.md:37
msgid ""
"The metadata `{\"foo\":\"bar\",\"baz\":[null,true,false,0]}` would be "
"included in an inscription as:"
msgstr ""
"Ang metadata na `{\"foo\":\"bar\",\"baz\":[null,true,false,0]}` ay "
"kasama sa isang inscription bilang:"

#: src/inscriptions/metadata.md:39
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"foo\":\"bar\",\"baz\":[null,true,false,0]}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"foo\":\"bar\",\"baz\":[null,true,false,0]}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/metadata.md:48
msgid "And rendered as:"
msgstr "At ma-rendered bilang:"

#: src/inscriptions/metadata.md:73
msgid "Metadata longer than 520 bytes must be split into multiple fields:"
msgstr "Ang metadata na mas mahaba sa 520 byte ay dapat hatiin sa maraming fields:"

#: src/inscriptions/metadata.md:75
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"very\":\"long\",\"metadata\":'\n"
"    OP_PUSH 0x05 OP_PUSH '\"is\",\"finally\":\"done\"}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"    ...\n"
"    OP_PUSH 0x05 OP_PUSH '{\"very\":\"long\",\"metadata\":'\n"
"    OP_PUSH 0x05 OP_PUSH '\"is\",\"finally\":\"done\"}'\n"
"    ...\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/metadata.md:85
msgid ""
"Which would then be concatinated into `{\"very\":\"long\",\"metadata\":"
"\"is\",\"finally\":\"done\"}`."
msgstr ""
"Na kung saan ay concatinated ito sa `{\"very\":\"long\",\"metadata\":"
"\"is\",\"finally\":\"done\"}`."

#: src/inscriptions/provenance.md:4
msgid ""
"The owner of an inscription can create child inscriptions, trustlessly "
"establishing the provenance of those children on-chain as having been "
"created by the owner of the parent inscription. This can be used for "
"collections, with the children of a parent inscription being members of the "
"same collection."
msgstr ""
"Ang may-ari ng isang inscription ay maaaring lumikha ng mga child "
"inscription, na sinisiguro na ng pinagmulan ng mga child inscription na nasa "
"on-chain na kabilang sa nilikha ng may-ari ng isang parent inscription. "
"Magagamit ito para sa mga koleksyon, kung saan ang mga child inscription ng "
"parent inscription ay mga miyembro ng parehong koleksyon."

#: src/inscriptions/provenance.md:9
msgid ""
"Children can themselves have children, allowing for complex hierarchies. For "
"example, an artist might create an inscription representing themselves, with "
"sub inscriptions representing collections that they create, with the "
"children of those sub inscriptions being items in those collections."
msgstr ""
"Kahit ang mga child inscription ay maaaring magkaroon ng mga child "
"inscriptions, na nagbibigay-daan para magkaroon ng hierarchy. Halimbawa, "
"maaring gumawa ang artist ng parent inscription na maraming child "
"inscription na kung saan may mga sub inscription pa ang mga ito, na kung "
"saan ang mga children at may sub inscription na kabilang din sa mga "
"koleksyon."

#: src/inscriptions/provenance.md:14
msgid "Specification"
msgstr "Pagtutukoy"

#: src/inscriptions/provenance.md:16
msgid "To create a child inscription C with parent inscription P:"
msgstr "Upang lumikha ng child inscription C na may parent inscription P:"

#: src/inscriptions/provenance.md:18
msgid "Create an inscribe transaction T as usual for C."
msgstr "Gumawa ng inscribe transaction T gaya ng dati para sa C."

#: src/inscriptions/provenance.md:19
msgid "Spend the parent P in one of the inputs of T."
msgstr "I-spend ang parent inscription P sa isa sa mga input ng T."

#: src/inscriptions/provenance.md:20
msgid ""
"Include tag `3`, i.e. `OP_PUSH 3`, in C, with the value of the serialized "
"binary inscription ID of P, serialized as the 32-byte `TXID`, followed by "
"the four-byte little-endian `INDEX`, with trailing zeroes omitted."
msgstr ""
"Isama ang tag `3`, ibig sabihin `OP_PUSH 3`, sa C, na may value ng "
"serialized binary inscription ID ng P, na naka-serialize bilang 32-byte "
"`TXID`, na sinusundan ng apat na byte na little-endian `INDEX`, kung saan "
"ang mga trailing zeroes ay hindi kasama."

#: src/inscriptions/provenance.md:24
msgid ""
"_NB_ The bytes of a bitcoin transaction ID are reversed in their text "
"representation, so the serialized transaction ID will be in the opposite "
"order."
msgstr ""
"_NB_ Ang mga byte ng isang bitcoin transaction ID ay nababaligtad sa "
"kanilang text representation, kaya ang serialized transaction ID ay nasa "
"kabaligtaran ang pagkakasunodsunod."

#: src/inscriptions/provenance.md:29
msgid ""
"An example of a child inscription of "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"
msgstr ""
"Isang halimbawa ng child inscription "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"

#: src/inscriptions/provenance.md:32
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:45
msgid ""
"Note that the value of tag `3` is binary, not hex, and that for the child "
"inscription to be recognized as a child, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` must be "
"spent as one of the inputs of the inscribe transaction."
msgstr ""
"Tandaan na ang halaga ng tag `3` ay binary, hindi hex, at para makilala ang "
"child inscription bilang isang child, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` dapat "
"na gastusin bilang isa sa mga input ng inscription transaction."

#: src/inscriptions/provenance.md:50
msgid ""
"Example encoding of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"
msgstr ""
"Halimbawang pag-encode ng inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"

#: src/inscriptions/provenance.md:63
msgid ""
"And of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"
msgstr ""
"At ng inskripsiyong ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"

#: src/inscriptions/provenance.md:75
msgid "Notes"
msgstr "Tandaan"

#: src/inscriptions/provenance.md:77
msgid ""
"The tag `3` is used because it is the first available odd tag. Unrecognized "
"odd tags do not make an inscription unbound, so child inscriptions would be "
"recognized and tracked by old versions of `ord`."
msgstr ""
"`3` Ginagamit ang tag dahil ito ang unang available na odd tag. Ang mga "
"hindi kilalang odd tag ay hindi gumagawa ng isang inskripsiyon na unbound, "
"kaya ang mga child inscription ay makikilala at masusubaybayan ng mga lumang "
"bersyon ng ord."

#: src/inscriptions/provenance.md:81
msgid ""
"A collection can be closed by burning the collection's parent inscription, "
"which guarantees that no more items in the collection can be issued."
msgstr ""
"Maaaring i-close ang isang koleksyon sa pamamagitan ng pag-burn sa parent "
"isncription ng koleksyon, na ginagarantiyahan na wala nang mga item sa "
"koleksyon ang maaaring maibigay."

#: src/inscriptions/recursion.md:4
msgid ""
"An important exception to [sandboxing](../inscriptions.md#sandboxing) is "
"recursion: access to `ord`'s `/content` endpoint is permitted, allowing "
"inscriptions to access the content of other inscriptions by requesting `/"
"content/<INSCRIPTION_ID>`."
msgstr ""
"Ang isang eksepsyon sa [sandboxing](../inscriptions.md#sandboxing) ay ang "
"recursion: pinahihintulutan ang pag-access sa endpoint ng `ord’s` `/"
"content`, na nagdudulot sa mga inskripsiyon na ma-access ang nilalaman ng "
"iba pang mga inskripsiyon sa pamamagitan ng paggamit ng `/content/"
"<INSCRIPTION_ID>`."

#: src/inscriptions/recursion.md:9
msgid "This has a number of interesting use-cases:"
msgstr "Ito ay nagdudulot ng magagandang use-cases:"

#: src/inscriptions/recursion.md:11
msgid "Remixing the content of existing inscriptions."
msgstr "Paggamit sa mga existing na inskripsyon."

#: src/inscriptions/recursion.md:13
msgid ""
"Publishing snippets of code, images, audio, or stylesheets as shared public "
"resources."
msgstr ""
"Pag-publish ng mga snippet ng code, mga larawan, audio, o mga stylesheet "
"bilang pampublikong resources."

#: src/inscriptions/recursion.md:16
msgid ""
"Generative art collections where an algorithm is inscribed as JavaScript, "
"and instantiated from multiple inscriptions with unique seeds."
msgstr ""
"Mga generative na koleksyon kung saan ang isang algorithm ay nakalagay "
"bilang JavaScript, na nag awtomatiko sa pag-create ng maraming inskripsyon "
"na may kanya-kanyang katangian."

#: src/inscriptions/recursion.md:19
msgid ""
"Generative profile picture collections where accessories and attributes are "
"inscribed as individual images, or in a shared texture atlas, and then "
"combined, collage-style, in unique combinations in multiple inscriptions."
msgstr ""
"Mga generative na koleksyon ng profile picture kung saan ang mga accessory "
"at attribute ay naka-inscribe bilang mga indibidwal na larawan, o sa isang "
"shared texture atlas, at pagkatapos ay pinagsama, parang collage, na may "
"kanya kanyang combinasyon."

#: src/inscriptions/recursion.md:23
msgid "A few other endpoints that inscriptions may access are the following:"
msgstr ""
"Ang ilan pang mga endpoint na maaaring ma-access ng mga inskripsiyon ay ang "
"mga sumusunod:"

#: src/inscriptions/recursion.md:25
msgid "`/blockheight`: latest block height."
msgstr "`/blockheight`: pinakabagong block height."

#: src/inscriptions/recursion.md:26
msgid "`/blockhash`: latest block hash."
msgstr "`/blockhash`: pinakabagong block hash."

#: src/inscriptions/recursion.md:27
msgid "`/blockhash/<HEIGHT>`: block hash at given block height."
msgstr "`/blockhash/<HEIGHT>`: block hash sa ibinigay na block height."

#: src/inscriptions/recursion.md:28
msgid "`/blocktime`: UNIX time stamp of latest block."
msgstr "`/blocktime`: UNIX time stamp ng pinakabagong block."

#: src/faq.md:1
msgid "Ordinal Theory FAQ"
msgstr "Mga Madalas Itanong"

#: src/faq.md:4
msgid "What is ordinal theory?"
msgstr "Ano ang Ordinal Theory?"

#: src/faq.md:7
msgid ""
"Ordinal theory is a protocol for assigning serial numbers to satoshis, the "
"smallest subdivision of a bitcoin, and tracking those satoshis as they are "
"spent by transactions."
msgstr ""
"Ang Ordinal Theory ay isang protocol para sa pagtatalaga ng mga serial "
"number sa satoshis, ang pinakamaliit na subdivision ng isang bitcoin, at "
"pagsubaybay sa mga satoshi na iyon habang ginagastos sila sa mga transaksyon."

#: src/faq.md:11
msgid ""
"These serial numbers are large numbers, like this 804766073970493. Every "
"satoshi, which is ¹⁄₁₀₀₀₀₀₀₀₀ of a bitcoin, has an ordinal number."
msgstr ""
"Ang mga serial number na ito ay malalaking numero, tulad nito "
"804766073970493. Ang bawat satoshi, na kumakatawan sa ¹⁄₁₀₀₀₀₀₀₀₀ ng isang "
"Bitcoin, ay may ordinal number."

#: src/faq.md:14
msgid ""
"Does ordinal theory require a side chain, a separate token, or changes to "
"Bitcoin?"
msgstr ""
"Nangangailangan ba ang Ordinal Theory ng pangalawang chain, hiwalay na "
"token, o mga pagbabago ng Bitcoin?"

#: src/faq.md:17
msgid ""
"Nope! Ordinal theory works right now, without a side chain, and the only "
"token needed is bitcoin itself."
msgstr ""
"Hindi. Gumagana ang ordinal theory sa ngayon, nang walang pangalawang chain, "
"at ang tanging token na kailangan ay ang Bitcoin mismo."

#: src/faq.md:20
msgid "What is ordinal theory good for?"
msgstr "Ano ang magandang gamit ng Ordinal Theory?"

#: src/faq.md:23
msgid ""
"Collecting, trading, and scheming. Ordinal theory assigns identities to "
"individual satoshis, allowing them to be individually tracked and traded, as "
"curios and for numismatic value."
msgstr ""
"Mangolekta, mag-trade at magnegosyo. Ang Ordinal Theory ay nagbibigay ng mga "
"pagkakakilanlan sa mga indibidwal na satoshi, upang ang mga ito ay "
"masubaybayan at i-trade, bilang mga rare o para sa kanilang numismatic na "
"halaga."

#: src/faq.md:27
msgid ""
"Ordinal theory also enables inscriptions, a protocol for attaching arbitrary "
"content to individual satoshis, turning them into bitcoin-native digital "
"artifacts."
msgstr ""
"Ang Ordinal Theory ay nagbibigay-daan din sa mga inscriptions, isang "
"protocol para sa pag-attach ng kahit anong content sa mga indibidwal na "
"satoshi, at dahil dito sila ay nagiging mga bitcoin-native na digital "
"artifacts."

#: src/faq.md:31
msgid "How does ordinal theory work?"
msgstr "Paano gumagana ang Ordinal Theory?"

#: src/faq.md:34
msgid ""
"Ordinal numbers are assigned to satoshis in the order in which they are "
"mined. The first satoshi in the first block has ordinal number 0, the second "
"has ordinal number 1, and the last satoshi of the first block has ordinal "
"number 4,999,999,999."
msgstr ""
"Ang ordinal numbers ay itinalaga sa satoshi na magkakasunud-sunod kung saan "
"ang mga ito ay na-mine. Ang unang satoshi sa unang bloke ay may ordinal na "
"numerong `0`, ang pangalawa ay may ordinal na numerong `1`, at ang huling "
"satoshi sa unang bloke ay may ordinal na numero 4,999,999,999."

#: src/faq.md:39
msgid ""
"Satoshis live in outputs, but transactions destroy outputs and create new "
"ones, so ordinal theory uses an algorithm to determine how satoshis hop from "
"the inputs of a transaction to its outputs."
msgstr ""
"Ang Satoshi ay nasa mga output, ngunit sinisira ng mga transaksyon ang mga "
"output na ito at lumikha ng mga bago muli. Samakatuwid, ang Ordinal Theory "
"ay gumagamit ng isang algorithm upang matukoy kung paano nai-pasa ang "
"satoshi mula sa mga input ng isang transaksyon patungo sa mga output nito."

#: src/faq.md:43
msgid "Fortunately, that algorithm is very simple."
msgstr "At ang algorithm na ito ay napaka-simple."

#: src/faq.md:45
msgid ""
"Satoshis transfer in first-in-first-out order. Think of the inputs to a "
"transaction as being a list of satoshis, and the outputs as a list of slots, "
"waiting to receive a satoshi. To assign input satoshis to slots, go through "
"each satoshi in the inputs in order, and assign each to the first available "
"slot in the outputs."
msgstr ""
"Ang Satoshi ay isang `first in, first out` order. Isipin ang mga input sa "
"isang transaksyon bilang isang listahan ng satoshi, at ang mga output bilang "
"isang listahan ng mga slot, naghihintay na makatanggap ng satoshi. Upang mag-"
"assign ng satoshi input sa mga slot, dapat isa-isa ang bawat input na "
"satoshi na may  pagkakasunud-sunod papunta sa mga bakanteng output slot."

#: src/faq.md:51
msgid ""
"Let's imagine a transaction with three inputs and two outputs. The inputs "
"are on the left of the arrow and the outputs are on the right, all labeled "
"with their values:"
msgstr ""
"Isipin ang isang transaksyon na may tatlong input at dalawang output. Ang "
"mga input ay nasa kaliwa ng arrow at ang mga output sa kanan, lahat ay may "
"label ng kanilang mga halaga:"

#: src/faq.md:57
msgid ""
"Now let's label the same transaction with the ordinal numbers of the "
"satoshis that each input contains, and question marks for each output slot. "
"Ordinal numbers are large, so let's use letters to represent them:"
msgstr ""
"Ngayon lagyan ng label ang parehong transaksyon gamit ang ordinal numbers ng "
"satoshi na naglalaman ng bawat entry, at mga tandang pananong para sa bawat "
"exit slot. Dahil ang ordinal numbers ay malaki, gamitin natin ang mga titik "
"upang kumatawan sa kanila:"

#: src/faq.md:63
msgid ""
"To figure out which satoshi goes to which output, go through the input "
"satoshis in order and assign each to a question mark:"
msgstr ""
"Upang malaman kung aling satoshi ang pupunta sa aling exit, kailangan mong "
"dumaan sa input na satoshis sa pagkakasunud-sunod at italaga ang bawat isa "
"sa mga tandang pananong:"

#: src/faq.md:68
msgid ""
"What about fees, you might ask? Good question! Let's imagine the same "
"transaction, this time with a two satoshi fee. Transactions with fees send "
"more satoshis in the inputs than are received by the outputs, so to make our "
"transaction into one that pays fees, we'll remove the second output:"
msgstr ""
"Paano ang tungkol sa fees? Magandang tanong! Isipin natin ang parehong "
"transaksyon, sa pagkakataong ito ay may bayad na dalawang satoshi. Ang mga "
"transaksyong may mga bayarin ay nagpapadala ng mas maraming satoshi sa mga "
"input kaysa sa natatanggap nila sa mga output, kaya para gawing isang "
"transaksyon ang nagbabayad, aalisin natin ang pangalawang output:"

#: src/faq.md:75
msgid "The satoshis "
msgstr "Ang Satoshis"

#: src/faq.md:75
msgid "e"
msgstr "e"

#: src/faq.md:75
msgid " and "
msgstr " at "

#: src/faq.md:75
msgid "f"
msgstr "f"

#: src/faq.md:75
msgid " now have nowhere to go in the outputs:"
msgstr " ay wala nang slot sa output:"

#: src/faq.md:80
msgid ""
"So they go to the miner who mined the block as fees. [The BIP](https://"
"github.com/ordinals/ord/blob/master/bip.mediawiki) has the details, but in "
"short, fees paid by transactions are treated as extra inputs to the coinbase "
"transaction, and are ordered how their corresponding transactions are "
"ordered in the block. The coinbase transaction of the block might look like "
"this:"
msgstr ""
"Kaya't bumalik sila sa miner na nagmimina ng block bilang bayad. Para sa "
"detalyeng kaalaman, mabuting bisitahin ang [BIP](https://github.com/ordinals/"
"ord/blob/master/bip.mediawiki), ngunit sa kabuuan, ang mga bayarin na "
"binayaran ng mga transaksyon ay itinuturing bilang karagdagang mga entry sa "
"transaksyon ng coinbase, at ordered sa parehong paraan tulad ng mga "
"kaukulang transaksyon na ordered sa block. Maaaring ganito ang hitsura ng "
"transaksyon sa coinbase ng block:"

#: src/faq.md:89
msgid "Where can I find the nitty-gritty details?"
msgstr "Saan ko mahahanap ang pinakatiyak na mga detalye?"

#: src/faq.md:92
msgid "[The BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr ""
"Dito sa [The BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src/faq.md:94
msgid ""
"Why are sat inscriptions called \"digital artifacts\" instead of \"NFTs\"?"
msgstr ""
"Bakit tinatawag na \"digital artifacts\" ang mga sat inscriptions sa halip "
"na \"NFTs\"?"

#: src/faq.md:97
msgid ""
"An inscription is an NFT, but the term \"digital artifact\" is used instead, "
"because it's simple, suggestive, and familiar."
msgstr ""
"Ang isang inskripsiyon ay isang NFT, ngunit ang terminong \"digital "
"artifact\" ay ginamit sa halip, dahil ito ay simple, nagpapahiwatig at "
"pamilyar."

#: src/faq.md:100
msgid ""
"The phrase \"digital artifact\" is highly suggestive, even to someone who "
"has never heard the term before. In comparison, NFT is an acronym, and "
"doesn't provide any indication of what it means if you haven't heard the "
"term before."
msgstr ""
"Ang terminong \"digital artifact\" ay madaling maunawaan, kahit na para sa "
"isang taong hindi pa nakarinig ng terminong ito dati. Sa paghahambing, ang "
"NFT ay isang acronym na hindi nagbibigay ng indikasyon kung ano ang ibig "
"sabihin nito."

#: src/faq.md:104
msgid ""
"Additionally, \"NFT\" feels like financial terminology, and the both word "
"\"fungible\" and sense of the word \"token\" as used in \"NFT\" is uncommon "
"outside of financial contexts."
msgstr ""
"Gayundin, ang \"NFT\" ay napagkakamalang may kinalaman sa pananalapi, at ang "
"salitang \"fungible\" pati na rin ang kahulugan ng salitang \"token\" na "
"ginamit sa \"NFT\" ay hindi karaniwan sa mga konteksto sa pananalapi."

#: src/faq.md:108
msgid "How do sat inscriptions compare to…"
msgstr "Paano maihahambing ang mga inskripsiyon sa sat sa..."

#: src/faq.md:111
msgid "Ethereum NFTs?"
msgstr "NFT Ethereum?"

#: src/faq.md:113
msgid "_Inscriptions are always immutable._"
msgstr "_Ang mga inscription ay hindi nababago or immutable, kailanman._"

#: src/faq.md:115
msgid ""
"There is simply no way to for the creator of an inscription, or the owner of "
"an inscription, to modify it after it has been created."
msgstr ""
"Walang paraan para sa creator ng isang inscription, o sa may-ari ng isang "
"inscription, na i-edit ito pagkatapos itong magawa."

#: src/faq.md:118
msgid ""
"Ethereum NFTs _can_ be immutable, but many are not, and can be changed or "
"deleted by the NFT contract owner."
msgstr ""
"Maaaring hindi nababago ang mga Ethereum NFT, ngunit maaaring baguhin o "
"sirain ng may-ari ang contract ng NFT."

#: src/faq.md:121
msgid ""
"In order to make sure that a particular Ethereum NFT is immutable, the "
"contract code must be audited, which requires detailed knowledge of the EVM "
"and Solidity semantics."
msgstr ""
"Upang matiyak na ang isang partikular na Ethereum NFT ay hindi nababago o "
"immutable, ang code ng kontrata ay dapat na i-audit, na nangangailangan ng "
"detalyadong kaalaman sa EVM at Solidity semantics."

#: src/faq.md:125
msgid ""
"It is very hard for a non-technical user to determine whether or not a given "
"Ethereum NFT is mutable or immutable, and Ethereum NFT platforms make no "
"effort to distinguish whether an NFT is mutable or immutable, and whether "
"the contract source code is available and has been audited."
msgstr ""
"Napakahirap para sa isang hindi teknikal na user na matukoy kung ang isang "
"partikular na Ethereum NFT ay nababago o hindi nababago, at ang mga Ethereum "
"NFT platform ay hindi nagsisikap na makilala kung ang isang NFT ay nababago "
"o hindi nababago, at kung ang source code ng kontrata ay magagamit at na-"
"audit."

#: src/faq.md:130
msgid "_Inscription content is always on-chain._"
msgstr "_Ang content ng inscription ay laging on-chain._"

#: src/faq.md:132
msgid ""
"There is no way for an inscription to refer to off-chain content. This makes "
"inscriptions more durable, because content cannot be lost, and scarcer, "
"because inscription creators must pay fees proportional to the size of the "
"content."
msgstr ""
"Walang paraan para sa isang inscription para i-refer ang sarili nito sa off-"
"chain. Ginagawa nitong mas matibay ang mga inscription, dahil hindi mawawala "
"ang content, at mas bihira, dahil dapat magbayad ang mga creator ng katumbas "
"na proporsyonal na laki ng content."

#: src/faq.md:136
msgid ""
"Some Ethereum NFT content is on-chain, but much is off-chain, and is stored "
"on platforms like IPFS or Arweave, or on traditional, fully centralized web "
"servers. Content on IPFS is not guaranteed to continue to be available, and "
"some NFT content stored on IPFS has already been lost. Platforms like "
"Arweave rely on weak economic assumptions, and will likely fail "
"catastrophically when these economic assumptions are no longer met. "
"Centralized web servers may disappear at any time."
msgstr ""
"Ang ilan sa nilalaman ng Ethereum NFT ay on-chain, ngunit karamihan sa mga "
"ito ay off-chain at naka-imbak sa mga platform tulad ng IPFS o Arweave, o "
"isang sentralisadong tradisyonal na mga web server. Ang paggamit sa IPFS ay "
"hindi safe, at ang ilang nilalaman ng NFT na nakaimbak sa IPFS ay maaring "
"mawala. Ang mga platform tulad ng Arweave ay umaasa sa mga dami ng gumagamit "
"nito at malamang na makakaranas ng malaking problema kapag hindi na "
"natugunan ang mga pangangailangan na ito. At ang mga sentralisadong web "
"server ay maaaring mawala anumang oras."

#: src/faq.md:144
msgid ""
"It is very hard for a non-technical user to determine where the content of a "
"given Ethereum NFT is stored."
msgstr ""
"Napakahirap para sa isang hindi teknikal na user ang gumagamit na matukoy "
"kung saan nakaimbak ang nilalaman ng isang partikular na Ethereum NFT."

#: src/faq.md:147
msgid "_Inscriptions are much simpler._"
msgstr "_Ang mga inscription ay mas simple._"

#: src/faq.md:149
msgid ""
"Ethereum NFTs depend on the Ethereum network and virtual machine, which are "
"highly complex, constantly changing, and which introduce changes via "
"backwards-incompatible hard forks."
msgstr ""
"Ang mga Ethereum NFT ay nakadepende sa Ethereum network at virtual machine, "
"na napakakomplikado, patuloy na nagbabago at nagdudulot ng mga pagbabago sa "
"pamamagitan ng mga backward compatible na hard forks."

#: src/faq.md:153
msgid ""
"Inscriptions, on the other hand, depend on the Bitcoin blockchain, which is "
"relatively simple and conservative, and which introduces changes via "
"backwards-compatible soft forks."
msgstr ""
"Ang mga inscriptions, sa kabilang banda, ay umaasa sa Bitcoin blockchain, na "
"simple at konserbatibo, at nagdudulot ng mga pagbabago sa pamamagitan ng "
"backward-compatible na soft forks."

#: src/faq.md:157
msgid "_Inscriptions are more secure._"
msgstr "_Ang mga inscription ay mas ligtas._"

#: src/faq.md:159
msgid ""
"Inscriptions inherit Bitcoin's transaction model, which allow a user to see "
"exactly which inscriptions are being transferred by a transaction before "
"they sign it. Inscriptions can be offered for sale using partially signed "
"transactions, which don't require allowing a third party, such as an "
"exchange or marketplace, to transfer them on the user's behalf."
msgstr ""
"Ang inscriptions ay direktang gumagamit ng Bitcoin transaction model, na "
"nagbibigay-daan sa isang user na makita kung aling inscriptions ang "
"inililipat ng isang transaksyon bagoito ma-sign. Maaring maibenta ang "
"inscriptions gamit ang partially signed transactions, na hindi "
"nangangailangan ng pagpapahintulot sa isang third party, tulad ng isang "
"exchange o marketplace, na ilipat ang mga ito sa ngalan ng user."

#: src/faq.md:165
msgid ""
"By comparison, Ethereum NFTs are plagued with end-user security "
"vulnerabilities. It is commonplace to blind-sign transactions, grant third-"
"party apps unlimited permissions over a user's NFTs, and interact with "
"complex and unpredictable smart contracts. This creates a minefield of "
"hazards for Ethereum NFT users which are simply not a concern for ordinal "
"theorists."
msgstr ""
"Sa paghahambing, ang mga Ethereum NFT ay maraming kahinaan sa seguridad ng "
"end-user. Karaniwang mahirap unawain ang pag-sign ng isang transaksyon, mga "
"hindi inaasahang permission access, at mga smart contract na may hindi "
"kanais nais na function tulad ng pagkuha sa iyong mga assets. Lumilikha ito "
"ng isang panganib para sa mga gumagamit ng Ethereum NFT na sadyang hindi "
"nakakabahala para sa mga ordinal theorists."

#: src/faq.md:171
msgid "_Inscriptions are scarcer._"
msgstr "_Mas bihira inscriptions._"

#: src/faq.md:173
msgid ""
"Inscriptions require bitcoin to mint, transfer, and store. This seems like a "
"downside on the surface, but the raison d'etre of digital artifacts is to be "
"scarce and thus valuable."
msgstr ""
"Ang inscriptions ay nangangailangan ng mga bitcoin na malikha, mailipat at "
"maiimbak. Ito ay tila isang downside sa unang tingin, ngunit ang buong punto "
"ng digital artifacts ay bihira at samakatuwid ay mahalaga."

#: src/faq.md:177
msgid ""
"Ethereum NFTs, on the other hand, can be minted in virtually unlimited "
"qualities with a single transaction, making them inherently less scarce, and "
"thus, potentially less valuable."
msgstr ""
"Ang mga Ethereum NFT, sa kabilang banda, ay maaaring ma-mint sa halos walang "
"limitasyong mga katangian sa isang transaksyon, na ginagawa itong likas na "
"hindi gaanong bihira, at samakatuwid ay potensyal na hindi gaanong mahalaga."

#: src/faq.md:181
msgid "_Inscriptions do not pretend to support on-chain royalties._"
msgstr ""
"_Hindi inaangkin ng inscriptions ang pag-suportahan sa mga on-chain royalty "
"ng blockchain._"

#: src/faq.md:183
msgid ""
"On-chain royalties are a good idea in theory but not in practice. Royalty "
"payment cannot be enforced on-chain without complex and invasive "
"restrictions. The Ethereum NFT ecosystem is currently grappling with "
"confusion around royalties, and is collectively coming to grips with the "
"reality that on-chain royalties, which were messaged to artists as an "
"advantage of NFTs, are not possible, while platforms race to the bottom and "
"remove royalty support."
msgstr ""
"Ang mga on-chain royalty ng blockchain ay isang magandang ideya sa teorya, "
"ngunit hindi sa parati. Ang pagbabayad ng royalties ay hindi maaaring ipataw "
"sa blockchain nang walang kumplikado at invasive na mga paghihigpit. Ang "
"Ethereum NFT ecosystem ay kasalukuyang nakikipagbuno sa pagkalito sa mga "
"royalty, at sama-samang nauunawaan ang katotohanan na ang on-chain "
"royalties, na ipinadala sa mga artista bilang isang bentahe ng mga NFT, ay "
"hindi posible, habang ang mga platform ay nagpapaunhan ibaba at alisin ang "
"suporta sa royalty."

#: src/faq.md:190
msgid ""
"Inscriptions avoid this situation entirely by making no false promises of "
"supporting royalties on-chain, thus avoiding the confusion, chaos, and "
"negativity of the Ethereum NFT situation."
msgstr ""
"Ganap na iniiwasan ng inscriptions ang sitwasyong ito sa pamamagitan ng "
"hindi paggawa ng mga maling pangako upang suportahan ang  on-chain royalties "
"sa blockchain, sa gayon ay iniiwasan ang pagkalito, kaguluhan at negatibiti "
"tulad ng sa Ethereum NFT."

#: src/faq.md:194
msgid "_Inscriptions unlock new markets._"
msgstr "_Ang inscriptions ay nagbubukas ng mga bagong ideya at posibilidad._"

#: src/faq.md:196
msgid ""
"Bitcoin's market capitalization and liquidity are greater than Ethereum's by "
"a large margin. Much of this liquidity is not available to Ethereum NFTs, "
"since many Bitcoiners prefer not to interact with the Ethereum ecosystem due "
"to concerns related to simplicity, security, and decentralization."
msgstr ""
"Ang market capitalization at liquidity ng Bitcoin ay higit na mataas kaysa "
"sa Ethereum. Karamihan sa liquidity na ito ay hindi available para sa "
"Ethereum NFTs, dahil mas gusto ng maraming bitcoiners na huwag makipag-"
"ugnayan sa Ethereum ecosystem dahil sa mga alalahanin tungkol sa pagiging "
"simple, seguridad, at desentralisasyon."

#: src/faq.md:201
msgid ""
"Such Bitcoiners may be more interested in inscriptions than Ethereum NFTs, "
"unlocking new classes of collector."
msgstr ""
"Ang mga bitcoiner na ito ay maaaring mas interesado sa inscriptions kaysa sa "
"mga Ethereum NFT, na nagbubukas ng mga bagong klase ng mga kolektor."

#: src/faq.md:204
msgid "_Inscriptions have a richer data model._"
msgstr "_Ang inscriptions ay may mas magandang data model._"

#: src/faq.md:206
msgid ""
"Inscriptions consist of a content type, also known as a MIME type, and "
"content, which is an arbitrary byte string. This is the same data model used "
"by the web, and allows inscription content to evolve with the web, and come "
"to support any kind of content supported by web browsers, without requiring "
"changes to the underlying protocol."
msgstr ""
"Ang inscriptions ay binubuo ng content type, na kilala rin bilang MIME type, "
"at content, na isang arbitrary na byte string. Ito ang parehong data model "
"na ginagamit ng web, na nagbibigay-daan sa inscription ng content na mag-"
"evolve kasama ang web at suportahan ang lahat ng uri ng nilalaman na "
"sinusuportahan ng mga web browser, nang hindi kinakailangang baguhin ang "
"pinagbabatayan na protocol."

#: src/faq.md:212
msgid "RGB and Taro assets?"
msgstr "RGB and Taro assets?"

#: src/faq.md:214
msgid ""
"RGB and Taro are both second-layer asset protocols built on Bitcoin. "
"Compared to inscriptions, they are much more complicated, but much more "
"featureful."
msgstr ""
"Ang RGB at Taro ay parehong Bitcoin-based na sidechain asset protocol. Kung "
"ikukumpara sa mga inscriptions, ang mga ito ay mas kumplikado, ngunit mas "
"maraming feature."

#: src/faq.md:217
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas the primary use-case of RGB and Taro are fungible tokens, so the "
"user experience for inscriptions is likely to be simpler and more polished "
"than the user experience for RGB and Taro NFTs."
msgstr ""
"Ang Ordinal Theory ay idinisenyo mula sa simula para sa mga digital "
"artifact, samantalang ang pangunahing kaso ng paggamit para sa RGB at Taro "
"ay para sa mga fungible na token, kaya ang karanasan ng user para sa mga pag-"
"signup ay malamang na maging mas simple at mas malinis kaysa sa karanasan ng "
"user para sa mga NFT mula sa RGB at Taro."

#: src/faq.md:222
msgid ""
"RGB and Taro both store content off-chain, which requires additional "
"infrastructure, and which may be lost. By contrast, inscription content is "
"stored on-chain, and cannot be lost."
msgstr ""
"Parehong nag-iimbak ang RGB at Taro ng off-chain na content, na "
"nangangailangan ng karagdagang imprastraktura at maaaring mawala. Sa "
"kabilang banda, ang nilalaman ng inskripsiyon ay nakaimbak sa on-chain at "
"hindi maaaring mawala."

#: src/faq.md:226
msgid ""
"Ordinal theory, RGB, and Taro are all very early, so this is speculation, "
"but ordinal theory's focus may give it the edge in terms of features for "
"digital artifacts, including a better content model, and features like "
"globally unique symbols."
msgstr ""
"Ang Ordinal Theory, RGB, at Taro ay bago lamang, kaya haka-haka lamang ito, "
"ngunit ang direksyon ng Ordinal Theory ay maaaring magbigay ng edge para sa "
"digital artifacts, kabilang ang isang pinakamahusay na data model at mga "
"natatanging simbolo."

#: src/faq.md:231
msgid "Counterparty assets?"
msgstr "Iba pang mga assets?"

#: src/faq.md:233
msgid ""
"Counterparty has its own token, XCP, which is required for some "
"functionality, which makes most bitcoiners regard it as an altcoin, and not "
"an extension or second layer for bitcoin."
msgstr ""
"Ang Counterparty ay may sariling token, XCP, na kinakailangan para sa ilang "
"partikular na functionality, kaya itinuturing ito ng karamihan sa mga "
"bitcoiner na isang altcoin, hindi isang extension o pangalawang layer para "
"sa bitcoin."

#: src/faq.md:237
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas Counterparty was primarily designed for financial token issuance."
msgstr ""
"Ang Ordinal Theory ay dinisenyo mula sa simula para sa mga digital na "
"artifact, samantalang ang counterparty ay pangunahing idinisenyo para sa pag-"
"isyu ng mga financial token."

#: src/faq.md:240
msgid "Inscriptions for…"
msgstr "Inscriptions para sa…"

#: src/faq.md:243
msgid "Artists"
msgstr "Mga Artists"

#: src/faq.md:245
msgid ""
"_Inscriptions are on Bitcoin._ Bitcoin is the digital currency with the "
"highest status and greatest chance of long-term survival. If you want to "
"guarantee that your art survives into the future, there is no better way to "
"publish it than as inscriptions."
msgstr ""
"_Ang inscriptions ay nasa Bitcoin._ Ang Bitcoin ay may pinakamataas na "
"tsansa na mag survive bilang isa kilalang digital currency. Kung gusto mong "
"tiyakin ang pangmatagalan ng iyong sining, mas mabuti na opsyon na i-likha "
"ang mga ito bilang isang inscriptions."

#: src/faq.md:250
msgid ""
"_Cheaper on-chain storage._ At $20,000 per BTC and the minimum relay fee of "
"1 satoshi per vbyte, publishing inscription content costs $50 per 1 million "
"bytes."
msgstr ""
"_Mas murang on-chain na storage._ Sa $20,000 bawat BTC at isang minimum na "
"bayad na 1 satoshi bawat vbyte, ang pag-post ng nilalaman ng inscription ay "
"nagkakahalaga lamang ng $50 bawat 1 milyong bytes."

#: src/faq.md:254
msgid ""
"_Inscriptions are early!_ Inscriptions are still in development, and have "
"not yet launched on mainnet. This gives you an opportunity to be an early "
"adopter, and explore the medium as it evolves."
msgstr ""
"_Ang inscriptions ay bago pa lang!_ Ang mga inscription ay nasa ilalim pa "
"rin ng development at hindi pa nailunsad sa mainnet. Nagbibigay ito sa iyo "
"ng pagkakataong maging isang maagang pag-adopt at tuklasin ang medium habang "
"bago pa ito."

#: src/faq.md:258
msgid ""
"_Inscriptions are simple._ Inscriptions do not require writing or "
"understanding smart contracts."
msgstr ""
"_Ang inscriptions ay simple._ Ang mga pagpaparehistro ay hindi "
"nangangailangan ng paggawa o pag-unawa sa mga smart contract."

#: src/faq.md:261
msgid ""
"_Inscriptions unlock new liquidity._ Inscriptions are more accessible and "
"appealing to bitcoin holders, unlocking an entirely new class of collector."
msgstr ""
"_Ang inscriptions ay nagbubukas ng karagdagang liquidity._ Ang inscriptions "
"ay mas naa-access at kaakit-akit sa mga Bitcoin holder, na nagbubukas ng "
"posibilidad sa isang bagong kategorya ng mga kolektor."

#: src/faq.md:264
msgid ""
"_Inscriptions are designed for digital artifacts._ Inscriptions are designed "
"from the ground up to support NFTs, and feature a better data model, and "
"features like globally unique symbols and enhanced provenance."
msgstr ""
"_Ang inscriptions ay idinisenyo para sa digital artifacts._ Ang mga "
"inscription ay idinisenyo mula sa simula upang suportahan ang mga NFT, at "
"nagpapakita ng mas mahusay na data model, pati na rin ang mga tampok tulad "
"ng mga unique symbol at pagberipika ng mga pinagmulan."

#: src/faq.md:268
msgid ""
"_Inscriptions do not support on-chain royalties._ This is negative, but only "
"depending on how you look at it. On-chain royalties have been a boon for "
"creators, but have also created a huge amount of confusion in the Ethereum "
"NFT ecosystem. The ecosystem now grapples with this issue, and is engaged in "
"a race to the bottom, towards a royalties-optional future. Inscriptions have "
"no support for on-chain royalties, because they are technically infeasible. "
"If you choose to create inscriptions, there are many ways you can work "
"around this limitation: withhold a portion of your inscriptions for future "
"sale, to benefit from future appreciation, or perhaps offer perks for users "
"who respect optional royalties."
msgstr ""
"_Ang inscriptions ay walang on-chain royalties._ Ito ay isang negatibo, "
"ngunit ito ay depende sa kung paano mo ito titingnan. Ang mga royalty ng "
"blockchain ay naging pakinabang para sa mga creator, ngunit lumikha din ng "
"malaking kalituhan sa Ethereum NFT ecosystem. Ang ecosystem ay nakikipagbuno "
"na ngayon sa problemang ito at nagsimula sa isang shift para ibaba ang "
"royalty papunta sa 0 royalty. Ang mga inscription ay hindi sumusuporta sa "
"mga royalty, dahil ang mga ito ay teknikal na hindi magagawa. Kung magpasya "
"kang lumikha ng mga inscription, maaari mong iwasan ang limitasyong ito sa "
"maraming paraan: magkaroon ng porsyento sa mga future sales, o marahil ay "
"mag-aalok ng mga benepisyo sa mga user na sumusuporta sa mga opsyonal na "
"royalties."

#: src/faq.md:279
msgid "Collectors"
msgstr "Mga kolektor"

#: src/faq.md:281
msgid ""
"_Inscriptions are simple, clear, and have no surprises._ They are always "
"immutable and on-chain, with no special due diligence required."
msgstr ""
"_Ang inscriptions ay simple, malinaw at secure._ Hindi nababago at on-chain, "
"nang hindi kailangan ng espesyal na effot."

#: src/faq.md:284
msgid ""
"_Inscriptions are on Bitcoin._ You can verify the location and properties of "
"inscriptions easily with Bitcoin full node that you control."
msgstr ""
"_Ang inscriptions ay nasa Bitcoin._ Madali mong mabe-verify ang lokasyon at "
"mga katangian ng mga inscription sa pamamagitan ng pag-setup ng sariling "
"full Bitcoin node."

#: src/faq.md:287
msgid "Bitcoiners"
msgstr "(Bitcoiners) Mga tumatangkilik sa Bitcoin"

#: src/faq.md:289
msgid ""
"Let me begin this section by saying: the most important thing that the "
"Bitcoin network does is decentralize money. All other use-cases are "
"secondary, including ordinal theory. The developers of ordinal theory "
"understand and acknowledge this, and believe that ordinal theory helps, at "
"least in a small way, Bitcoin's primary mission."
msgstr ""
"Ang pinakamahalagang bagay na ginagawa ng Bitcoin network ay ang pagkakaroon "
"ng desentralisado na pera. Kabilang na dito ang Ordinal Theory. Ang mga nag-"
"develop ng Ordinal Theory ay nauunawaan at kinikilala ito, at naniniwala na "
"ang Ordinal Theory ay nag-aambag, kahit sa maliit na paraan, sa pangunahing "
"misyon ng Bitcoin."

#: src/faq.md:295
msgid ""
"Unlike many other things in the altcoin space, digital artifacts have merit. "
"There are, of course, a great deal of NFTs that are ugly, stupid, and "
"fraudulent. However, there are many that are fantastically creative, and "
"creating and collecting art has been a part of the human story since its "
"inception, and predates even trade and money, which are also ancient "
"technologies."
msgstr ""
"Hindi tulad ng altcoins, may merito ang mga digital artifact. Siyempre, "
"maraming NFT na pangit, hangal, at mapanlinlang. Ang paglikha at koleksyon "
"ng mga likhang sining ay bahagi na ng kasaysayan ng sangkatauhan mula nang "
"ito ay mabuo at nauna pa sa komersiyo at pananalapi, na mga sinaunang "
"teknolohiya din."

#: src/faq.md:302
msgid ""
"Bitcoin provides an amazing platform for creating and collecting digital "
"artifacts in a secure, decentralized way, that protects users and artists in "
"the same way that it provides an amazing platform for sending and receiving "
"value, and for all the same reasons."
msgstr ""
"Nagbibigay ang Bitcoin ng isang platform para sa paglikha at pagkolekta ng "
"mga digital artifact sa isang secure at desentralisadong paraan, na "
"nagpoprotekta sa mga user at artist sa parehong paraan na nagbibigay ito ng "
"isang platform para sa pagpapadala at pagtanggap, at para sa lahat ng "
"parehong dahilan."

#: src/faq.md:307
msgid ""
"Ordinals and inscriptions increase demand for Bitcoin block space, which "
"increase Bitcoin's security budget, which is vital for safeguarding "
"Bitcoin's transition to a fee-dependent security model, as the block subsidy "
"is halved into insignificance."
msgstr ""
"Ang mga ordinal at inscription ay nagpapataas ng demand para sa Bitcoin "
"block space, na nagdaragdag sa badyet na mahalaga upang mapangalagaan ang "
"seguridad ng Bitcoin at pag-transition nito sa fee-dependent security model, "
"dahil ang block subsidy ay hinahati sa hindi gaanong kahalagahan."

#: src/faq.md:312
msgid ""
"Inscription content is stored on-chain, and thus the demand for block space "
"for use in inscriptions is unlimited. This creates a buyer of last resort "
"for _all_ Bitcoin block space. This will help support a robust fee market, "
"which ensures that Bitcoin remains secure."
msgstr ""
"Ang mga nilalaman ng inscriptions ay naka-imbak on-chain, kaya ang block "
"space demand para sa inscriptions ay walang limitasyon. Lumilikha ito ng "
"isang buyer kung tawagin, para sa bawat block ng Bitcoin. Makakatulong ito "
"sa pagsuporta sa isang matatag na market fee, na nagpapanatili sa Bitcoin na "
"maging secure."

#: src/faq.md:317
msgid ""
"Inscriptions also counter the narrative that Bitcoin cannot be extended or "
"used for new use-cases. If you follow projects like DLCs, Fedimint, "
"Lightning, Taro, and RGB, you know that this narrative is false, but "
"inscriptions provide a counter argument which is easy to understand, and "
"which targets a popular and proven use case, NFTs, which makes it highly "
"legible."
msgstr ""
"Ang inscriptions ay sumasalungat sa ideya na ang Bitcoin ay hindi maaaring "
"palawigin o gamitin para sa mga bagong idea. Kung susundin mo ang mga "
"proyekto tulad ng mga DLC, Fedimint, Lightning, Taro, at RGB, alam mong mali "
"ang claim na ito, ngunit nagbibigay ang inscriptions ng kontra-argumento na "
"madaling maunawaan, at napatunayang model sa paggamit nito tulad nalamang ng "
"mga NFTs, lubos na legible."

#: src/faq.md:323
msgid ""
"If inscriptions prove, as the authors hope, to be highly sought after "
"digital artifacts with a rich history, they will serve as a powerful hook "
"for Bitcoin adoption: come for the fun, rich art, stay for the decentralized "
"digital money."
msgstr ""
"Kung ang inscriptions ay lumabas, gaya ng inaasahan ng mga may-akda, "
"magdudulot ito ng isang matagumpay na model para sa Bitcoin adoption: para "
"sa kasiyahan, mayamang sining, at manatiling desentralisadong digital money."

#: src/faq.md:327
msgid ""
"Inscriptions are an extremely benign source of demand for block space. "
"Unlike, for example, stablecoins, which potentially give large stablecoin "
"issuers influence over the future of Bitcoin development, or DeFi, which "
"might centralize mining by introducing opportunities for MEV, digital art "
"and collectables on Bitcoin, are unlikely to produce individual entities "
"with enough power to corrupt Bitcoin. Art is decentralized."
msgstr ""
"Ang mga inskripsiyon ay isang napakahusay na mapagkukunan ng pangangailangan "
"para sa block space. Hindi tulad, halimbawa, ng mga stablecoin, na posibleng "
"magbigay ng malaking impluwensya ng mga issuer sa hinaharap sa pag-unlad ng "
"Bitcoin, o DeFi, na maaaring isentro ang pagmimina sa pamamagitan ng "
"pagpapakilala ng opportunities para sa MEV, digital art at mga collectable "
"sa Bitcoin, ay malamang na hindi makagawa ng mga indibidwal na entity na may "
"sapat na kakayahan upang sirain ang Bitcoin. Ang sining ay desentralisado."

#: src/faq.md:334
msgid ""
"Inscription users and service providers are incentivized to run Bitcoin full "
"nodes, to publish and track inscriptions, and thus throw their economic "
"weight behind the honest chain."
msgstr ""
"Ang mga user ng inscription at mga service provider ay binibigyang insentibo "
"na magpatakbo ng buong Bitcoin node, mag-publish at subaybayan ang mga "
"inscription, at sa gayon ay maging mataas ang integrity ng chain."

#: src/faq.md:338
msgid ""
"Ordinal theory and inscriptions do not meaningfully affect Bitcoin's "
"fungibility. Bitcoin users can ignore both and be unaffected."
msgstr ""
"Ang Ordinal Theory at inscription ay hindi gaanong nakakaapekto sa pagiging "
"epektibo ng Bitcoin. Maaaring balewalain ng mga gumagamit ng Bitcoin ang "
"pareho at manatiling hindi maaapektuhan."

#: src/faq.md:341
msgid ""
"We hope that ordinal theory strengthens and enriches bitcoin, and gives it "
"another dimension of appeal and functionality, enabling it more effectively "
"serve its primary use case as humanity's decentralized store of value."
msgstr ""
"Umaasa kami na ang Ordinal Theory ay magpapalakas at magpapayaman sa "
"Bitcoin, at binibigyan ito ng appeal at functionality, na nagbibigay-daan "
"dito na mas epektibong magsilbi sa paggamit nito bilang isang "
"desentralisadong model sa sangkatauhan."

#: src/contributing.md:1
msgid "Contributing to `ord`"
msgstr "Mag-ambag sa `ord`"

#: src/contributing.md:4
msgid "Suggested Steps"
msgstr "Mga iminungkahing hakbang"

#: src/contributing.md:7
msgid "Find an issue you want to work on."
msgstr "Maghanap ng isyu na gusto mong ayusin."

#: src/contributing.md:8
msgid ""
"Figure out what would be a good first step towards resolving the issue. This "
"could be in the form of code, research, a proposal, or suggesting that it be "
"closed, if it's out of date or not a good idea in the first place."
msgstr ""
"Tukuyin kung ano ang magiging magandang unang hakbang patungo sa paglutas ng "
"isyu. Ito ay maaaring nasa anyo ng code, pananaliksik, panukala, o sa "
"pamamagitan ng pagmumungkahi na i-close ito, kung ito ay lipas na o hindi "
"magandang ideya sa simula pa lang."

#: src/contributing.md:11
msgid ""
"Comment on the issue with an outline of your suggested first step, and "
"asking for feedback. Of course, you can dive in and start writing code or "
"tests immediately, but this avoids potentially wasted effort, if the issue "
"is out of date, not clearly specified, blocked on something else, or "
"otherwise not ready to implement."
msgstr ""
"Magkomento sa problema, at humihingi ng feedback. Maaari kang sumali at "
"magsimulang magsulat ng code o mag test, ngunit nakakatipid sa oras ang "
"pagberipika kung ang paksa ay luma na, kung hindi ito malinaw, kung ito ay "
"hindi pa handang ipatupad."

#: src/contributing.md:16
msgid ""
"If the issue requires a code change or bugfix, open a draft PR with tests, "
"and ask for feedback. This makes sure that everyone is on the same page "
"about what needs to be done, or what the first step in solving the issue "
"should be. Also, since tests are required, writing the tests first makes it "
"easy to confirm that the change can be tested easily."
msgstr ""
"Kung ang isyu ay nangangailangan ng pagbabago ng code o pag-aayos sa isang "
"bug, magbukas ng PR (pull request) draft na may kasamang test, at humingi ng "
"feedback. Nakakatulong ito na matiyak na ang lahat ay sumasang-ayon sa kung "
"ano ang kailangang gawin o ang unang hakbang sa paglutas ng problema. Dapat "
"tandaan na ang paggawa ng tests at mahalaga upang madali at mabilis ma "
"peripika ang iyong PR."

#: src/contributing.md:21
msgid ""
"Mash the keyboard randomly until the tests pass, and refactor until the code "
"is ready to submit."
msgstr ""
"Gumawa ng maraming test, at i-tweak ang code hanggang sa handa na itong "
"isumite."

#: src/contributing.md:23
msgid "Mark the PR as ready to review."
msgstr "Markahan ang PR ng “ready to review”."

#: src/contributing.md:24
msgid "Revise the PR as needed."
msgstr "Baguhin ang PR kung kinakailangan."

#: src/contributing.md:25
msgid "And finally, mergies!"
msgstr "At sa huli, maari itong mai-merge!"

#: src/contributing.md:27
msgid "Start small"
msgstr "Magsimula sa maliit"

#: src/contributing.md:30
msgid ""
"Small changes will allow you to make an impact quickly, and if you take the "
"wrong tack, you won't have wasted much time."
msgstr ""
"Ang mga maliliit pagbabago ay magbibigay-daan sayo na makapag-ambag ng "
"mabilis, at kung hindi, hindi ka mag-aaksaya ng maraming oras."

#: src/contributing.md:33
msgid "Ideas for small issues:"
msgstr "Mga ideya:"

#: src/contributing.md:34
msgid "Add a new test or test case that increases test coverage"
msgstr "Magdagdag ng bagong test o test cases"

#: src/contributing.md:35
msgid "Add or improve documentation"
msgstr "Magdagdag o pagbutihin ang dokumentasyon"

#: src/contributing.md:36
msgid ""
"Find an issue that needs more research, and do that research and summarize "
"it in a comment"
msgstr ""
"Maghanap ng isyu na nangangailangan ng higit pang pananaliksik, gawin ang "
"pagsasaliksik na iyon, at ibuod ito sa isang komento."

#: src/contributing.md:38
msgid "Find an out-of-date issue and comment that it can be closed"
msgstr "Maghanap ng out-of-date na isyu."

#: src/contributing.md:39
msgid ""
"Find an issue that shouldn't be done, and provide constructive feedback "
"detailing why you think that is the case"
msgstr ""
"Maghanap ng isyu na hindi dapat gawin, at magbigay ng constructive na "
"feedback na nagdedetalye kung bakit sa tingin mo ay ganito ang sitwasyon."

#: src/contributing.md:42
msgid "Merge early and often"
msgstr "Mag-merge nang maaga at madalas"

#: src/contributing.md:45
msgid ""
"Break up large tasks into multiple smaller steps that individually make "
"progress. If there's a bug, you can open a PR that adds a failing ignored "
"test. This can be merged, and the next step can be to fix the bug and "
"unignore the test. Do research or testing, and report on your results. Break "
"a feature into small sub-features, and implement them one at a time."
msgstr ""
"Hatiin ang malalaking gawain sa mas maliliit na hakbang. Kung mayroong isang "
"bug, maaari kang magbukas ng PR na nagdaragdag ng isang failing ignored "
"test. Maaari itong i-merge, at ang susunod na hakbang ay ang pag-ayos ng bug "
"at karagdagang tests. Magsagawa ng pananaliksik o pag-testing, at iulat ang "
"iyong mga resulta. Hatiin ang isang feature sa maliliit na sub-feature at "
"isa-isang i-fix ang mga ito."

#: src/contributing.md:51
msgid ""
"Figuring out how to break down a larger PR into smaller PRs where each can "
"be merged is an art form well-worth practicing. The hard part is that each "
"PR must itself be an improvement."
msgstr ""
"Ang paghahanap ng paraan upang hatiin ang isang malaking PR sa mas maliliit "
"na PR ay isang form of art na magandang practice bilang isang contributor. "
"Ang mahirap na bahagi ay ang bawat PR ay dapat na isang improvement."

#: src/contributing.md:55
msgid ""
"I strive to follow this advice myself, and am always better off when I do."
msgstr ""
"Sinisikap kong sundin ang payo na ito sa aking sarili, at palaging mas "
"maganda kapag ginagawa ko."

#: src/contributing.md:57
msgid ""
"Small changes are fast to write, review, and merge, which is much more fun "
"than laboring over a single giant PR that takes forever to write, review, "
"and merge. Small changes don't take much time, so if you need to stop "
"working on a small change, you won't have wasted much time as compared to a "
"larger change that represents many hours of work. Getting a PR in quickly "
"improves the project a little bit immediately, instead of having to wait a "
"long time for larger improvement. Small changes are less likely to "
"accumulate merge conflict. As the Athenians said: _The fast commit what they "
"will, the slow merge what they must._"
msgstr ""
"Ang mga maliliit na changes ay mabilis na bumalangkas, nagre-rebisa, at "
"nagsasama, na mas maganda kaysa sa pagtatrabaho sa isang malaking PR na "
"tumatagal nang walang hanggan sa pag-draft, pagbabago, at pagsasama. Ang "
"maliliit na changes ay hindi tumatagal ng maraming oras, kaya kung kailangan "
"mong huminto sa paggawa sa isang maliit na changes, hindi ka magsasayang ng "
"maraming oras kumpara sa isang mas malaking changes. Ang mabilis pag-PR ay "
"nakakatulong na mapabuti ang proyekto kaagad, imbes na maghintay ng mahabang "
"panahon para sa mas malaking changes. Ang maliliit na changes ay mas "
"malamang na magdulot kakaunting conflict sa pagme-merge. Gaya ng sinabi ng "
"mga taga-Athenians: _The fast commit what they will, the slow merge what "
"they must._"

#: src/contributing.md:67
msgid "Get help"
msgstr "Humingi ng tulong"

#: src/contributing.md:70
msgid ""
"If you're stuck for more than 15 minutes, ask for help, like a Rust Discord, "
"Stack Exchange, or in a project issue or discussion."
msgstr ""
"Kung natigil ka nang higit sa 15 minuto, humingi ng tulong, halimbawa sa "
"Rust Discord, Stack Exchange, o sa project issue or discussion board."

#: src/contributing.md:73
msgid "Practice hypothesis-driven debugging"
msgstr "I-practice ang hypothesis-driven debugging"

#: src/contributing.md:76
msgid ""
"Formulate a hypothesis as to what is causing the problem. Figure out how to "
"test that hypothesis. Perform that tests. If it works, great, you fixed the "
"issue or now you know how to fix the issue. If not, repeat with a new "
"hypothesis."
msgstr ""
"Bumuo ng hypothesis tungkol sa sanhi ng problema. Alamin kung paano i-test "
"ang hypothesis na ito. Gawin ang test na ito. Kung ito ay gumana, maaari "
"mong malutas ang problema o ngayon alam kung paano ayusin ito. Kung hindi, "
"magsimulang muli sa isang bagong hypothesis."

#: src/contributing.md:81
msgid "Pay attention to error messages"
msgstr "Bigyang-pansin ang mga mensahe ng error"

#: src/contributing.md:84
msgid "Read all error messages and don't tolerate warnings."
msgstr ""
"Basahin ang lahat ng mga mensahe ng error at huwag i-tolerate ang mga "
"warnings."

#: src/donate.md:4
msgid ""
"Ordinals is open-source and community funded. The current lead maintainer of "
"`ord` is [raphjaph](https://github.com/raphjaph/). Raph's work on `ord` is "
"entirely funded by donations. If you can, please consider donating!"
msgstr ""
"Ang Ordinals ay open-source na software na pinondohan ng komunidad. Ang "
"kasalukuyang maintainer ng `ord` ay si [raphjaph](https://github.com/"
"raphjaph/). Ang trabaho ni Raph sa `ord` ay ganap na pinondohan ng mga "
"donasyon. Maari kang magbigay ng donasyon kung gusto mo suportahan ang "
"development ng `ord`."

#: src/donate.md:8
msgid ""
"The donation address for Bitcoin is "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************). The "
"donation address for inscriptions is "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************)."
msgstr ""
"Ang Bitcoin address para sa donasyon ay "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************). At ang "
"Ordinal address naman ay "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************)."

#: src/donate.md:11
msgid ""
"Both addresses are in a 2 of 4 multisig wallet with keys held by [raphjaph]"
"(https://twitter.com/raphjaph), [erin](https://twitter.com/realizingerin), "
"[rodarmor](https://twitter.com/rodarmor), and [ordinally](https://twitter."
"com/veryordinally)."
msgstr ""
"Ang parehong mga address ay nasa 2 sa 4 na multisig na wallet na ang mga "
"secret key ay hawak nina [raphjaph](https://twitter.com/raphjaph), [erin]"
"(https://twitter.com/realizingerin), [rodarmor](https://twitter.com/"
"rodarmor), at [ordinally](https://twitter.com/veryordinally)."

#: src/donate.md:17
msgid ""
"Donations received will go towards funding maintenance and development of "
"`ord`, as well as hosting costs for [ordinals.com](https://ordinals.com)."
msgstr ""
"Ang mga natanggap na donasyon ay gagamitin upang tustusan ang pagpapanatili "
"at pagpapaunlad ng `ord`, gayundin ang mga gastos sa pagho-host ng [ordinals."
"com](https://ordinals.com)."

#: src/donate.md:20
msgid "Thank you for donating!"
msgstr "Salamat sa mga donasyon!"

#: src/guides.md:1
msgid "Ordinal Theory Guides"
msgstr "Ordinal Theory Guides"

#: src/guides.md:4
msgid ""
"See the table of contents for a list of guides, including a guide to the "
"explorer, a guide for sat hunters, and a guide to inscriptions."
msgstr ""
"Tingnan ang listahan ng mga gabay, kabilang ang guide sa ordinal explorer, "
"sats hunter, at guide sa inscriptions."

#: src/guides/explorer.md:1
msgid "Ordinal Explorer"
msgstr "Ordinal Explorer"

#: src/guides/explorer.md:4
msgid ""
"The `ord` binary includes a block explorer. We host a instance of the block "
"explorer on mainnet at [ordinals.com](https://ordinals.com), and on signet "
"at [signet.ordinals.com](https://signet.ordinals.com)."
msgstr ""
"Ang `ord` binary ay may kasamang block explorer. Nagho-host kami ng block "
"explorer sa mainnet ng [ordinals.com](https://ordinals.com), at sa signet sa "
"[signet.ordinals.com](https://signet.ordinals.com)."

#: src/guides/explorer.md:8
msgid "Running The Explorer"
msgstr "Pagpapagana sa Explorer"

#: src/guides/explorer.md:9
msgid "The server can be run locally with:"
msgstr "Ang server ay maaring i-run sa iyong computer gamit ang:"

#: src/guides/explorer.md:11
msgid "`ord server`"
msgstr "`ord server`"

#: src/guides/explorer.md:13
msgid "To specify a port add the `--http-port` flag:"
msgstr "Para mag specify ng port i-add ang `--http-port` na flag:"

#: src/guides/explorer.md:15
msgid "`ord server --http-port 8080`"
msgstr "`ord server --http-port 8080`"

#: src/guides/explorer.md:17
msgid ""
"To enable the JSON-API endpoints add the `--enable-json-api` or `-j` flag "
"(see [here](#json-api) for more info):"
msgstr ""
"Para ma-enable ang JSON-API endpoints i-add ang `--enable-json-api` or `-j` "
"flag (tingnan [sa](#json-api) para karagdagang impormasyon):"

#: src/guides/explorer.md:20
msgid "`ord --enable-json-api server`"
msgstr "`ord --enable-json-api server`"

#: src/guides/explorer.md:22
msgid "To test how your inscriptions will look you can run:"
msgstr "Maaring i-test ang iyong inscriptions gamit naman ang:"

#: src/guides/explorer.md:24
msgid "`ord preview <FILE1> <FILE2> ...`"
msgstr "`ord preview <FILE1> <FILE2> ...`"

#: src/guides/explorer.md:26
msgid "Search"
msgstr "Search"

#: src/guides/explorer.md:29
msgid "The search box accepts a variety of object representations."
msgstr "Tumatanggap ng paghahanap ng iba't ibang representasyon ng object."

#: src/guides/explorer.md:31
msgid "Blocks"
msgstr "Blocks"

#: src/guides/explorer.md:33
msgid "Blocks can be searched by hash, for example, the genesis block:"
msgstr ""
"Maaaring hanapin ang mga block sa pamamagitan ng hash, halimbawa, ang "
"genesis block:"

#: src/guides/explorer.md:35
msgid ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://"
"ordinals.com/"
"search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"
msgstr ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://"
"ordinals.com/"
"search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"

#: src/guides/explorer.md:37
msgid "Transactions"
msgstr "Transactions"

#: src/guides/explorer.md:39
msgid ""
"Transactions can be searched by hash, for example, the genesis block "
"coinbase transaction:"
msgstr ""
"Maaaring hanapin ang mga transaksyon sa pamamagitan ng hash, halimbawa, ang "
"genesis block coinbase na transaksyon:"

#: src/guides/explorer.md:42
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"

#: src/guides/explorer.md:44
msgid "Outputs"
msgstr "Outputs"

#: src/guides/explorer.md:46
msgid ""
"Transaction outputs can searched by outpoint, for example, the only output "
"of the genesis block coinbase transaction:"
msgstr ""
"Maaaring hanapin ang mga output ng transaksyon sa pamamagitan ng outpoint, "
"halimbawa, ang solong output ng genesis block coinbase na transaksyon:"

#: src/guides/explorer.md:49
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"

#: src/guides/explorer.md:51
msgid "Sats"
msgstr "Sats"

#: src/guides/explorer.md:53
msgid ""
"Sats can be searched by integer, their position within the entire bitcoin "
"supply:"
msgstr ""
"Maaaring hanapin ang Sats sa pamamagitan ng buong numero, ang kanilang "
"posisyon sa buong supply ng bitcoin:"

#: src/guides/explorer.md:56
msgid "[2099994106992659](https://ordinals.com/search/2099994106992659)"
msgstr "[2099994106992659](https://ordinals.com/search/2099994106992659)"

#: src/guides/explorer.md:58
msgid "By decimal, their block and offset within that block:"
msgstr ""
"Sa pamamagitan ng decimal, ang kanilang block at ang kanilang offset sa "
"block:"

#: src/guides/explorer.md:60
msgid "[481824.0](https://ordinals.com/search/481824.0)"
msgstr "[481824.0](https://ordinals.com/search/481824.0)"

#: src/guides/explorer.md:62
msgid ""
"By degree, their cycle, blocks since the last halving, blocks since the last "
"difficulty adjustment, and offset within their block:"
msgstr ""
"Ayon sa antas, ang kanilang cycle, mga bloke mula noong huling halving, mga "
"bloke mula noong huling difficulty adjustment, at offset sa kanilang mga "
"bloke:"

#: src/guides/explorer.md:65
msgid "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"
msgstr "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"

#: src/guides/explorer.md:67
msgid ""
"By name, their base 26 representation using the letters \"a\" through \"z\":"
msgstr ""
"Sa kanilang pangalan, ang kanilang representasyon sa base-26 gamit ang mga "
"letrang \"a\" hanggang \"z\":"

#: src/guides/explorer.md:69
msgid "[ahistorical](https://ordinals.com/search/ahistorical)"
msgstr "[ahistorical](https://ordinals.com/search/ahistorical)"

#: src/guides/explorer.md:71
msgid ""
"Or by percentile, the percentage of bitcoin's supply that has been or will "
"have been issued when they are mined:"
msgstr ""
"O ayon sa percentile, ang porsyento ng supply ng bitcoin na ibibigay o "
"ibibigay kapag na-mina:"

#: src/guides/explorer.md:74
msgid "[100%](https://ordinals.com/search/100%)"
msgstr "[100%](https://ordinals.com/search/100%)"

#: src/guides/explorer.md:76
msgid "JSON-API"
msgstr "JSON-API"

#: src/guides/explorer.md:79
msgid ""
"You can run `ord` with the `--enable-json-api` flag to access endpoints that "
"return JSON instead of HTML if you set the HTTP `Accept: application/json` "
"header. The structure of theses objects closely follows what is shown in the "
"HTML. These endpoints are:"
msgstr ""
"Maaari mong patakbuhin ang `ord` gamit ang `--enable-json-api` flag upang ma-"
"access ang mga endpoint na ibalik ang JSON sa halip na HTML kung na-set mo "
"ang HTTP `Accept: application/json` header. Ang istraktura ng mga bagay na "
"ito ay malapit na sumusunod sa kung ano ang ipinapakita sa HTML. Ang mga "
"endpoint na ito ay:"

#: src/guides/explorer.md:84
msgid "`/inscription/<INSCRIPTION_ID>`"
msgstr "`/inscription/<INSCRIPTION_ID>`"

#: src/guides/explorer.md:85
msgid "`/inscriptions`"
msgstr "/inscriptions"

#: src/guides/explorer.md:86
msgid "`/inscriptions/block/<BLOCK_HEIGHT>`"
msgstr "`/inscriptions/block/<BLOCK_HEIGHT>`"

#: src/guides/explorer.md:87
msgid "`/inscriptions/block/<BLOCK_HEIGHT>/<PAGE_INDEX>`"
msgstr "`/inscriptions/block/<BLOCK_HEIGHT>/<PAGE_INDEX>`"

#: src/guides/explorer.md:88
msgid "`/inscriptions/<FROM>`"
msgstr "`/inscriptions/<FROM>`"

#: src/guides/explorer.md:89
msgid "`/inscriptions/<FROM>/<N>`"
msgstr "`/inscriptions/<FROM>/<N>`"

#: src/guides/explorer.md:90 src/guides/explorer.md:91
msgid "`/output/<OUTPOINT>`"
msgstr "`/output/<OUTPOINT>`"

#: src/guides/explorer.md:92
msgid "`/sat/<SAT>`"
msgstr "`/sat/<SAT>`"

#: src/guides/explorer.md:94
msgid "To get a list of the latest 100 inscriptions you would do:"
msgstr ""
"Para makakuha ng listahan ng pinakabagong 100 inskripsiyon na gagawin mo:"

#: src/guides/explorer.md:96
msgid ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/inscriptions'\n"
"```"
msgstr ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/inscriptions'\n"
"```"

#: src/guides/explorer.md:100
msgid ""
"To see information about a UTXO, which includes inscriptions inside it, do:"
msgstr ""
"Upang makakita ng impormasyon tungkol sa isang UTXO, na may kasamang mga "
"inskripsiyon sa loob nito, gawin ang:"

#: src/guides/explorer.md:102
msgid ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/output/"
"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed:0'\n"
"```"
msgstr ""
"```\n"
"curl -s -H \"Accept: application/json\" 'http://0.0.0.0:80/output/"
"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed:0'\n"
"```"

#: src/guides/explorer.md:106
msgid "Which returns:"
msgstr "Na mag re-return ng:"

#: src/guides/explorer.md:108
msgid ""
"```\n"
"{\n"
"  \"value\": 10000,\n"
"  \"script_pubkey\": \"OP_PUSHNUM_1 OP_PUSHBYTES_32 "
"156cc4878306157720607cdcb4b32afa4cc6853868458d7258b907112e5a434b\",\n"
"  \"address\": "
"\"bc1pz4kvfpurqc2hwgrq0nwtfve2lfxvdpfcdpzc6ujchyr3ztj6gd9sfr6ayf\",\n"
"  \"transaction\": "
"\"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed\",\n"
"  \"sat_ranges\": null,\n"
"  \"inscriptions\": [\n"
"    \"6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0\"\n"
"  ]\n"
"}\n"
"```"
msgstr ""
"```\n"
"{\n"
"  \"value\": 10000,\n"
"  \"script_pubkey\": \"OP_PUSHNUM_1 OP_PUSHBYTES_32 "
"156cc4878306157720607cdcb4b32afa4cc6853868458d7258b907112e5a434b\",\n"
"  \"address\": "
"\"bc1pz4kvfpurqc2hwgrq0nwtfve2lfxvdpfcdpzc6ujchyr3ztj6gd9sfr6ayf\",\n"
"  \"transaction\": "
"\"bc4c30829a9564c0d58e6287195622b53ced54a25711d1b86be7cd3a70ef61ed\",\n"
"  \"sat_ranges\": null,\n"
"  \"inscriptions\": [\n"
"    \"6fb976ab49dcec017f1e201e84395983204ae1a7c2abf7ced0a85d692e442799i0\"\n"
"  ]\n"
"}\n"
"```"

#: src/guides/inscriptions.md:1
msgid "Ordinal Inscription Guide"
msgstr "Gabay sa Ordinal inscriptions"

#: src/guides/inscriptions.md:4
msgid ""
"Individual sats can be inscribed with arbitrary content, creating Bitcoin-"
"native digital artifacts that can be held in a Bitcoin wallet and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"Ang mga indibidwal na sat ay maaaring ma-inscribe ng arbitrary na content, "
"na lumilikha ng mga digital artifact na pinagana ng Bitcoin na maaaring i-"
"store sa isang Bitcoin wallet at ilipat sa pamamagitan ng mga transaksyon sa "
"Bitcoin. Ang mga inscription ay hindi nababago, secure, at desentralisado "
"gaya ng mismong Bitcoin."

#: src/guides/inscriptions.md:9
msgid ""
"Working with inscriptions requires a Bitcoin full node, to give you a view "
"of the current state of the Bitcoin blockchain, and a wallet that can create "
"inscriptions and perform sat control when constructing transactions to send "
"inscriptions to another wallet."
msgstr ""
"Ang pagtratrabaho sa inscriptions ay nangangailangan ng isang Bitcoin full "
"node, upang mabigyan ka ng view ng kasalukuyang estado ng Bitcoin "
"blockchain, at isang wallet na maaaring lumikha ng mga inscription at "
"magsagawa ng sat check kapag gumagawa ng mga transaksyon upang magpadala ng "
"mga inscription sa ibang wallet."

#: src/guides/inscriptions.md:14
msgid ""
"Bitcoin Core provides both a Bitcoin full node and wallet. However, the "
"Bitcoin Core wallet cannot create inscriptions and does not perform sat "
"control."
msgstr ""
"Ang Bitcoin Core ay nagbibigay ng parehong buong node at isang Bitcoin "
"wallet. Gayunpaman, ang Bitcoin Core Wallet ay hindi makakalikha ng "
"inscriptions at hindi nagsasagawa ng mga sat control."

#: src/guides/inscriptions.md:17
msgid ""
"This requires [`ord`](https://github.com/ordinals/ord), the ordinal utility. "
"`ord` doesn't implement its own wallet, so `ord wallet` subcommands interact "
"with Bitcoin Core wallets."
msgstr ""
"Nangangailangan ito ng [`ord`](https://github.com/ordinals/ord), ang ordinal "
"utility. Hindi nagpapatupad ang `ord` ng sarili nitong wallet, kaya ang mga "
"subcommand ng ord wallet ay nakikipag-ugnayan sa mga wallet ng Bitcoin Core."

#: src/guides/inscriptions.md:21
msgid "This guide covers:"
msgstr "Sinasaklaw ng gabay na ito ang mga sumusunod na punto:"

#: src/guides/inscriptions.md:23 src/guides/inscriptions.md:39
msgid "Installing Bitcoin Core"
msgstr "Pag-install ng Bitcoin Core"

#: src/guides/inscriptions.md:24
msgid "Syncing the Bitcoin blockchain"
msgstr "Pag-synchronize ng Bitcoin Blockchain"

#: src/guides/inscriptions.md:25
msgid "Creating a Bitcoin Core wallet"
msgstr "Gumawa ng Bitcoin Core wallet"

#: src/guides/inscriptions.md:26
msgid "Using `ord wallet receive` to receive sats"
msgstr "Gamitin ang `ord wallet receive` para makatanggap ng sats"

#: src/guides/inscriptions.md:27
msgid "Creating inscriptions with `ord wallet inscribe`"
msgstr "Gumawa ng inscriptions gamit ang `ord wallet inscribe`"

#: src/guides/inscriptions.md:28
msgid "Sending inscriptions with `ord wallet send`"
msgstr "Magpadala ng inscriptions gamit ang `ord wallet send`"

#: src/guides/inscriptions.md:29
msgid "Receiving inscriptions with `ord wallet receive`"
msgstr "Pagtanggap ng inscriptions gamit ang `ord wallet receive`"

#: src/guides/inscriptions.md:31
msgid "Getting Help"
msgstr "Kumuha ng tulong"

#: src/guides/inscriptions.md:34
msgid ""
"If you get stuck, try asking for help on the [Ordinals Discord Server]"
"(https://discord.com/invite/87cjuz4FYg), or checking GitHub for relevant "
"[issues](https://github.com/ordinals/ord/issues) and [discussions](https://"
"github.com/ordinals/ord/discussions)."
msgstr ""
"Kung natigil ka, subukang humingi ng tulong sa server ng [Ordinals Discord]"
"(https://discord.com/invite/87cjuz4FYg), o tingnan ang GitHub para sa mga "
"[isyu](https://github.com/ordinals/ord/issues) at [discussions](https://"
"github.com/ordinals/ord/discussions)."

#: src/guides/inscriptions.md:42
msgid ""
"Bitcoin Core is available from [bitcoincore.org](https://bitcoincore.org/) "
"on the [download page](https://bitcoincore.org/en/download/)."
msgstr ""
"Ang Bitcoin Core ay mada-download sa [bitcoincore.org](https://bitcoincore."
"org/) sa pahina ng [pag-download](https://bitcoincore.org/en/download/)."

#: src/guides/inscriptions.md:45
msgid "Making inscriptions requires Bitcoin Core 24 or newer."
msgstr "Ang inscriptions ay nangangailangan ng Bitcoin Core 24 o mas bago."

#: src/guides/inscriptions.md:47
msgid ""
"This guide does not cover installing Bitcoin Core in detail. Once Bitcoin "
"Core is installed, you should be able to run `bitcoind -version` "
"successfully from the command line. Do _NOT_ use `bitcoin-qt`."
msgstr ""
"Hindi saklaw ng gabay na ito ang pag-install ng Bitcoin Core nang detalyado. "
"Kapag na-install na ang Bitcoin Core, dapat ay matagumpay mong mapatakbo ang "
"`bitcoind -version` mula sa command line. _HUWAG_ gamitin ang `bitcoin-qt`."

#: src/guides/inscriptions.md:51
msgid "Configuring Bitcoin Core"
msgstr "Bitcoin Core Setup"

#: src/guides/inscriptions.md:54
msgid "`ord` requires Bitcoin Core's transaction index and rest interface."
msgstr "Ang `ord` ay nangangailangan ng index ng transaksyon at rest interface ng Bitcoin Core."

#: src/guides/inscriptions.md:56
msgid ""
"To configure your Bitcoin Core node to maintain a transaction index, add the "
"following to your `bitcoin.conf`:"
msgstr ""
"I-configure ang iyong Bitcoin Core node upang mapanatili ang index ng mga "
"transaksyon, idagdag ang sumusunod sa iyong bitcoin.conf file:"

#: src/guides/inscriptions.md:63
msgid "Or, run `bitcoind` with `-txindex`:"
msgstr "O, patakbuhin ang `bitcoind` gamit ang `-txindex`:"

#: src/guides/inscriptions.md:69
msgid ""
"Details on creating or modifying your `bitcoin.conf` file can be found [here]"
"(https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md)."
msgstr ""
"Ang mga detalye sa paggawa o pagbabago ng iyong `bitcoin.conf` na file ay matatagpuan [dito]"
"(https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md)."

#: src/guides/inscriptions.md:72
msgid "Syncing the Bitcoin Blockchain"
msgstr "Pag-synchronize ng Bitcoin Blockchain"

#: src/guides/inscriptions.md:75
msgid "To sync the chain, run:"
msgstr "Upang i-sync ang blockchain patakbuhin ang sumusunod na command"

#: src/guides/inscriptions.md:81
msgid "…and leave it running until `getblockcount`:"
msgstr "…at hayaan itong tumakbo hanggang sa `getblockcount`:"

#: src/guides/inscriptions.md:87
msgid ""
"agrees with the block count on a block explorer like [the mempool.space "
"block explorer](https://mempool.space/). `ord` interacts with `bitcoind`, so "
"you should leave `bitcoind` running in the background when you're using "
"`ord`."
msgstr ""
"tumutugma sa bilang ng mga bloke tulad nalamang ng [mempool.space block "
"explorer](https://mempool.space/). Nakikipag-ugnayan ang `ord` sa "
"`bitcoind`, kaya dapat mong iwanan ang `bitcoind` na tumatakbo sa background "
"kapag gumagamit ng `ord`."

#: src/guides/inscriptions.md:91
msgid ""
"The blockchain takes about 600GB of disk space. If you have an external "
"drive you want to store blocks on, use the configuration option "
"`blocksdir=<external_drive_path>`. This is much simpler than using the "
"`datadir` option because the cookie file will still be in the default "
"location for `bitcoin-cli` and `ord` to find."
msgstr ""
"Ang blockchain ay humigit-kumulang 600GB disk space. Kung mayroon kang external "
"drive na gamitin para mag-store ng blocks, gamitin ang configuration option "
"`blocksdir=<external_drive_path>`. Ito ay mas simple kaysa sa paggamit ng "
"`datadir` na opsyon dahil ang cookie file ay nasa default pa ring "
"lokasyon para mahanap ang `bitcoin-cli` at `ord`."

#: src/guides/inscriptions.md:97 src/guides/collecting/sparrow-wallet.md:173
msgid "Troubleshooting"
msgstr "Pag-Troubleshoot"

#: src/guides/inscriptions.md:100
msgid ""
"Make sure you can access `bitcoind` with `bitcoin-cli -getinfo` and that it "
"is fully synced."
msgstr ""
"Tiyaking maa-access mo ang `bitcoind` gamit ang `bitcoin-cli -getinfo` at ito ay "
"ay ganap na naka-sync."

#: src/guides/inscriptions.md:103
msgid ""
"If `bitcoin-cli -getinfo` returns `Could not connect to the server`, "
"`bitcoind` is not running."
msgstr ""
"Kung ibabalik ng `bitcoin-cli -getinfo` ang `Could not connect to the server`, "
"Hindi tumatakbo ang iyong `bitcoind`."

#: src/guides/inscriptions.md:106
msgid ""
"Make sure `rpcuser`, `rpcpassword`, or `rpcauth` are _NOT_ set in your "
"`bitcoin.conf` file. `ord` requires using cookie authentication. Make sure "
"there is a file `.cookie` in your bitcoin data directory."
msgstr ""
"Tiyaking `rpcuser`, `rpcpassword`, o `rpcauth` ay _HINDI_ naka-set sa iyong "
"`bitcoin.conf` file. Ang `ord` ay nangangailangan ng paggamit ng cookie authentication. Tiyaking "
"may file na `.cookie` sa iyong bitcoin data directory."

#: src/guides/inscriptions.md:110
msgid ""
"If `bitcoin-cli -getinfo` returns `Could not locate RPC credentials`, then "
"you must specify the cookie file location. If you are using a custom data "
"directory (specifying the `datadir` option), then you must specify the "
"cookie location like `bitcoin-cli -rpccookiefile=<your_bitcoin_datadir>/."
"cookie -getinfo`. When running `ord` you must specify the cookie file "
"location with `--cookie-file=<your_bitcoin_datadir>/.cookie`."
msgstr ""
"Kung ibabalik ng `bitcoin-cli -getinfo` ang `Could not locate RPC credentials`, kung gayon "
"dapat mong tukuyin ang lokasyon ng cookie file. Kung gumagamit ka ng custom na data "
"directory (tumutukoy sa `datadir`), dapat mong tukuyin ang "
"lokasyon ng cookie tulad ng `bitcoin-cli -rpccookiefile=<your_bitcoin_datadir>/."
"cookie -getinfo`. Kapag nagpapatakbo ng `ord` dapat mong tukuyin ang cookie file "
"location na may `--cookie-file=<your_bitcoin_datadir>/.cookie`."

#: src/guides/inscriptions.md:118
msgid ""
"Make sure you do _NOT_ have `disablewallet=1` in your `bitcoin.conf` file. "
"If `bitcoin-cli listwallets` returns `Method not found` then the wallet is "
"disabled and you won't be able to use `ord`."
msgstr ""
"Tiyaking _WALA_ kang `disablewallet=1` sa iyong `bitcoin.conf` file."
"Kung ibabalik ng `bitcoin-cli listwallet` ang `Method not found` ang wallet ay "
"naka-disable at hindi mo magagamit ang `ord`."

#: src/guides/inscriptions.md:122
msgid ""
"Make sure `txindex=1` is set. Run `bitcoin-cli getindexinfo` and it should "
"return something like"
msgstr ""
"Tiyaking nakatakda ang `txindex=1`. Patakbuhin ang `bitcoin-cli getindexinfo` at dapat itong "
"ibalik ang isang bagay tulad ng"

#: src/guides/inscriptions.md:124
msgid ""
"```json\n"
"{\n"
"  \"txindex\": {\n"
"    \"synced\": true,\n"
"    \"best_block_height\": 776546\n"
"  }\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"txindex\": {\n"
"    \"synced\": true,\n"
"    \"best_block_height\": 776546\n"
"  }\n"
"}\n"
"```"

#: src/guides/inscriptions.md:132
msgid ""
"If it only returns `{}`, `txindex` is not set. If it returns `\"synced\": "
"false`, `bitcoind` is still creating the `txindex`. Wait until `\"synced\": "
"true` before using `ord`."
msgstr ""
"Kung ibinabalik nito ang `{}`, hindi naka-set ang `txindex`. Kung ibinabalik nito ang `\"naka-sync\": "
"false`, ginagawa pa rin ng `bitcoind` ang `txindex`. Maghintay ang `\"synced\": "
"true` bago gamitin ang `ord`."

#: src/guides/inscriptions.md:136
msgid ""
"If you have `maxuploadtarget` set it can interfere with fetching blocks for "
"`ord` index. Either remove it or set `whitebind=127.0.0.1:8333`."
msgstr ""
"Kung mayroon kang naka-set na `maxuploadtarget` maaari itong makagambala sa pagkuha ng mga bloke para sa "
"index ng `ord`. Alisin ito o i-set ang `whitebind=127.0.0.1:8333`."

#: src/guides/inscriptions.md:139
msgid "Installing `ord`"
msgstr "Pag-install ng `ord`"

#: src/guides/inscriptions.md:142
msgid ""
"The `ord` utility is written in Rust and can be built from [source](https://"
"github.com/ordinals/ord). Pre-built binaries are available on the [releases "
"page](https://github.com/ordinals/ord/releases)."
msgstr ""
"Ang `ord` utility ay nakasulat sa Rust at maaaring i-compile mula sa [source]"
"(https://github.com/ordinals/ord). Available ang mga pre-built na binary sa "
"page ng mga [release page](https://github.com/ordinals/ord/releases)."

#: src/guides/inscriptions.md:146
msgid "You can install the latest pre-built binary from the command line with:"
msgstr ""
"Maaari mong i-install ang pinakabagong prebuilt binary mula sa command line "
"gamit ang:"

#: src/guides/inscriptions.md:148
msgid ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"
msgstr ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"

#: src/guides/inscriptions.md:152
msgid "Once `ord` is installed, you should be able to run:"
msgstr "Kapag na-install na ang ord, dapat mong patakbuhin ang:"

#: src/guides/inscriptions.md:158
msgid "Which prints out `ord`'s version number."
msgstr "na magpapakita ng numero ng bersyon ng `ord`."

#: src/guides/inscriptions.md:160
msgid "Creating a Bitcoin Core Wallet"
msgstr "Paglikha ng Bitcoin Core Wallet"

#: src/guides/inscriptions.md:163
msgid ""
"`ord` uses Bitcoin Core to manage private keys, sign transactions, and "
"broadcast transactions to the Bitcoin network."
msgstr ""
"Ang `ord` ay gumagamit ng Bitcoin Core upang pamahalaan ang mga private key, "
"lagdaan ang mga transaksyon, at i-broadcast ang mga ito sa network ng "
"Bitcoin."

#: src/guides/inscriptions.md:166
msgid "To create a Bitcoin Core wallet named `ord` for use with `ord`, run:"
msgstr ""
"Para gumawa ng Bitcoin Core wallet na ang pangalan ay `ord` para gamitin sa "
"`ord`, patakbuhin ang:"

#: src/guides/inscriptions.md:172
msgid "Receiving Sats"
msgstr "Tumanggap ng sats"

#: src/guides/inscriptions.md:175
msgid ""
"Inscriptions are made on individual sats, using normal Bitcoin transactions "
"that pay fees in sats, so your wallet will need some sats."
msgstr ""
"Ang mga inscription ay ginagawa sa mga indibidwal na sats, gamit ang mga "
"normal na transaksyon sa Bitcoin na nagbabayad ng gamit ang sats, kaya ang "
"iyong wallet ay mangangailangan ng mga sats."

#: src/guides/inscriptions.md:178
msgid "Get a new address from your `ord` wallet by running:"
msgstr "Kumuha ng bagong address mula sa iyong ord wallet sa pamamagitan ng:"

#: src/guides/inscriptions.md:184
msgid "And send it some funds."
msgstr "At magpadala ng pondo."

#: src/guides/inscriptions.md:186
msgid "You can see pending transactions with:"
msgstr "Maaari mong tingnan ang mga kasalukuyang transaksyon gamit ang:"

#: src/guides/inscriptions.md:192
msgid ""
"Once the transaction confirms, you should be able to see the transactions "
"outputs with `ord wallet outputs`."
msgstr ""
"Kapag nakumpirma na ang transaksyon, dapat mong makita ang mga resulta ng "
"transaksyon sa mga`ord wallet outputs`."

#: src/guides/inscriptions.md:195
msgid "Creating Inscription Content"
msgstr "Lumikha ng Inscription Content"

#: src/guides/inscriptions.md:198
msgid ""
"Sats can be inscribed with any kind of content, but the `ord` wallet only "
"supports content types that can be displayed by the `ord` block explorer."
msgstr ""
"Maaaring i-inscribe ang Sats sa anumang uri ng content, ngunit "
"sinusuportahan lamang ng ord wallet ang mga uri ng content na maaaring i-"
"display ng `ord` block explorer."

#: src/guides/inscriptions.md:201
msgid ""
"Additionally, inscriptions are included in transactions, so the larger the "
"content, the higher the fee that the inscription transaction must pay."
msgstr ""
"Gayundin, ang mga inscription ay kasama sa mga transaksyon, kaya kung mas "
"maraming nilalaman, mas mataas ang bayad para sa transaksyon sa inscription."

#: src/guides/inscriptions.md:204
msgid ""
"Inscription content is included in transaction witnesses, which receive the "
"witness discount. To calculate the approximate fee that an inscribe "
"transaction will pay, divide the content size by four and multiply by the "
"fee rate."
msgstr ""
"Ang nilalaman ng inscription ay kasama sa mga witnesses transaksyon, na "
"tumatanggap ng diskwento. Upang kalkulahin ang tinatayang bayad na babayaran "
"ng isang inscribe na transaksyon, hatiin ang laki ng content sa apat at i-"
"multiply sa rate ng bayad."

#: src/guides/inscriptions.md:208
msgid ""
"Inscription transactions must be less than 400,000 weight units, or they "
"will not be relayed by Bitcoin Core. One byte of inscription content costs "
"one weight unit. Since an inscription transaction includes not just the "
"inscription content, limit inscription content to less than 400,000 weight "
"units. 390,000 weight units should be safe."
msgstr ""
"Ang mga transaksyon sa inscription ay dapat na mas mababa sa 400,00, o hindi "
"sila ipapadala ng Bitcoin Core. Ang isang byte na nilalaman ng inscription "
"ay nagkakahalaga ng isang yunit ng timbang. Dahil ang isang transaksyon sa "
"inscription ay hindi kasama lamang ang nilalaman ng inscription, angkop na "
"limitahan ang nilalaman ng pagpapatala sa mas mababa sa 400,000. Dapat ay "
"sapat na ang 390,000 weight units."

#: src/guides/inscriptions.md:214
msgid "Creating Inscriptions"
msgstr "Paglikha ng mga inscription"

#: src/guides/inscriptions.md:217
msgid "To create an inscription with the contents of `FILE`, run:"
msgstr ""
"Upang lumikha ng inscription na may mga nilalaman na `FILE`, patakbuhin ang:"

#: src/guides/inscriptions.md:223
msgid ""
"Ord will output two transactions IDs, one for the commit transaction, and "
"one for the reveal transaction, and the inscription ID. Inscription IDs are "
"of the form `TXIDiN`, where `TXID` is the transaction ID of the reveal "
"transaction, and `N` is the index of the inscription in the reveal "
"transaction."
msgstr ""
"Maglalabas ang Ord ng dalawang transaction ID, isa para sa commit "
"transaction at isa para sa reveal transaction, kasama ang inscription ID. "
"Ang mga Inscription ID ay nasa anyong `TXIDiN`, kung saan ang `TXID` ay ang "
"transaction ID ng reveal na transaksyon, at ang `N` ay ang index ng pag-"
"inscribe sa reveal na transaksyon."

#: src/guides/inscriptions.md:228
msgid ""
"The commit transaction commits to a tapscript containing the content of the "
"inscription, and the reveal transaction spends from that tapscript, "
"revealing the content on chain and inscribing it on the first sat of the "
"input that contains the corresponding tapscript."
msgstr ""
"Ang commit na transaksyon ay commit sa isang tapscript na naglalaman ng "
"content ng inscription, at ang reveal na transaction ay gumagastos mula sa "
"tapscript na iyon, na nire-reveal ang content sa string at nii-inscribe ito "
"sa unang sat ng input na naglalaman ng kaukulang tapscript."

#: src/guides/inscriptions.md:233
msgid ""
"Wait for the reveal transaction to be mined. You can check the status of the "
"commit and reveal transactions using  [the mempool.space block explorer]"
"(https://mempool.space/)."
msgstr ""
"Hintaying mamimina ang reveal na transaksyon. Maaari mong suriin ang status "
"ng commit at i-reveal ang mga transaksyon gamit ang [mempool.space block "
"explorer](https://mempool.space/)."

#: src/guides/inscriptions.md:237
msgid ""
"Once the reveal transaction has been mined, the inscription ID should be "
"printed when you run:"
msgstr ""
"Kapag ang inscription na transaksyon ay nakuha na, ang inscription ID ay "
"dapat na mai-print gamit ang:"

#: src/guides/inscriptions.md:244
msgid "Parent-Child Inscriptions"
msgstr "Parent-Child Inscriptions"

#: src/guides/inscriptions.md:247
msgid ""
"Parent-child inscriptions enable what is colloquially known as collections, "
"see [provenance](../inscriptions/provenance.md) for more information."
msgstr ""
"Ang Parent-child inscriptions ay nagbibigay-daan sa kung ano ang karaniwang "
"kilala bilang mga koleksyon, tingnan ang [provenance](../inscriptions/"
"provenance.md) para sa higit pang impormasyon."

#: src/guides/inscriptions.md:250
msgid ""
"To make an inscription a child of another, the parent inscription has to be "
"inscribed and present in the wallet. To choose a parent run `ord wallet "
"inscriptions` and copy the inscription id (`<PARENT_INSCRIPTION_ID>`)."
msgstr ""

#: src/guides/inscriptions.md:254
msgid "Now inscribe the child inscription and specify the parent like so:"
msgstr "Ngayon i-inscribe ang child inscription at tukuyin ang parent tulad nito:"

#: src/guides/inscriptions.md:260
msgid ""
"This relationship cannot be added retroactively, the parent has to be "
"present at inception of the child."
msgstr ""

#: src/guides/inscriptions.md:263
msgid "Sending Inscriptions"
msgstr "Magpadala ng mga inscription"

#: src/guides/inscriptions.md:266
msgid "Ask the recipient to generate a new address by running:"
msgstr "Mag-generate ng bagong address gamit ang:"

#: src/guides/inscriptions.md:272
msgid "Send the inscription by running:"
msgstr ""

#: src/guides/inscriptions.md:278 src/guides/inscriptions.md:306
msgid "See the pending transaction with:"
msgstr "Tingnan ang mga pending na transaction gamit ang:"

#: src/guides/inscriptions.md:284
msgid ""
"Once the send transaction confirms, the recipient can confirm receipt by "
"running:"
msgstr ""
"Kapag nakumpirma na ang transaksyon sa pagpapadala, maaaring kumpirmahin ng "
"tatanggap sa pamamagitan ng:"

#: src/guides/inscriptions.md:291
msgid "Receiving Inscriptions"
msgstr "Pagtanggap ng mga inscriptions"

#: src/guides/inscriptions.md:294
msgid "Generate a new receive address using:"
msgstr "Bumuo ng bagong address sa pagtanggap gamit ang:"

#: src/guides/inscriptions.md:300
msgid "The sender can transfer the inscription to your address using:"
msgstr ""
"Maaaring ipasa ng nagpadala ang inscription sa iyong address gamit ang:"

#: src/guides/inscriptions.md:311
msgid ""
"Once the send transaction confirms, you can can confirm receipt by running:"
msgstr ""
"Kapag nakumpirma na ang transaksyon sa pagpapadala, maaari mong kumpirmahin "
"sa pamamagitan ng:"

#: src/guides/sat-hunting.md:4
msgid ""
"_This guide is out of date. Since it was written, the `ord` binary was "
"changed to only build the full satoshi index when the `--index-sats` flag is "
"supplied. Additionally, `ord` now has a built-in wallet that wraps a Bitcoin "
"Core wallet. See `ord wallet --help`._"
msgstr ""
"Ang gabay na ito ay hindi na napapanahon. Ang `ord` binary ay binago upang "
"bumuo lamang ng buong satoshi index kapag ang `--index-sats` na opsyon ay "
"ibinigay. Bukod pa rito, mayroon na ngayong built-in na wallet ang `ord` na "
"nakapaloob sa isang Bitcoin Core wallet. Tingnan ang `ord wallet --help` na "
"command."

#: src/guides/sat-hunting.md:9
msgid ""
"Ordinal hunting is difficult but rewarding. The feeling of owning a wallet "
"full of UTXOs, redolent with the scent of rare and exotic sats, is beyond "
"compare."
msgstr ""
"Ang ordinal hunting ay mahirap ngunit kapaki-pakinabang. Ang pakiramdam ng "
"pagmamay-ari ng pitaka puno ng mga UTXOs, nag naglalaman ng mga rare at "
"exotic sats, lubos na mahalaga."

#: src/guides/sat-hunting.md:12
msgid ""
"Ordinals are numbers for satoshis. Every satoshi has an ordinal number and "
"every ordinal number has a satoshi."
msgstr ""
"Ang mga ordinal ay mga numero para sa mga satoshi. Ang bawat satoshi ay may "
"ordinal na numero at bawat ordinal na numero ay may satoshi."

#: src/guides/sat-hunting.md:15
msgid "Preparation"
msgstr "Paghahanda"

#: src/guides/sat-hunting.md:18
msgid "There are a few things you'll need before you start."
msgstr "Mayroong ilang mga bagay na kakailanganin mo bago ka magsimula."

#: src/guides/sat-hunting.md:20
msgid ""
"First, you'll need a synced Bitcoin Core node with a transaction index. To "
"turn on transaction indexing, pass `-txindex` on the command-line:"
msgstr ""
"Una, kakailanganin mo ng naka-sync na Bitcoin Core node na may transaction "
"index. Upang i-on ang pag-index ng transaksyon, ipasa ang `-txindex` sa "
"command-line:"

#: src/guides/sat-hunting.md:27
msgid ""
"Or put the following in your [Bitcoin configuration file](https://github.com/"
"bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"
msgstr ""
"O ilagay ang sumusunod sa iyong [Bitcoin configuration file](https://github."
"com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"

#: src/guides/sat-hunting.md:34
msgid ""
"Launch it and wait for it to catch up to the chain tip, at which point the "
"following command should print out the current block height:"
msgstr ""
"I-launch ito at hintayin itong makaabot sa dulo ng chain, kung saan ang "
"sumusunod na command ay dapat mag-print ng kasalukuyang taas ng bloke:"

#: src/guides/sat-hunting.md:41
msgid "Second, you'll need a synced `ord` index."
msgstr "Pangalawa, kakailanganin mo ng naka-sync na `ord` index."

#: src/guides/sat-hunting.md:43
msgid "Get a copy of `ord` from [the repo](https://github.com/ordinals/ord/)."
msgstr ""
"Kumuha ng kopya ng `ord` mula sa [repo](https://github.com/ordinals/ord/)."

#: src/guides/sat-hunting.md:45
msgid ""
"Run `RUST_LOG=info ord index`. It should connect to your bitcoin core node "
"and start indexing."
msgstr ""
"I-run ang `RUST_LOG=info ord index`. Dapat itong kumonekta sa iyong bitcoin "
"core node at simulan ang pag-index."

#: src/guides/sat-hunting.md:48
msgid "Wait for it to finish indexing."
msgstr "Hintaying matapos ang pag-index."

#: src/guides/sat-hunting.md:50
msgid "Third, you'll need a wallet with UTXOs that you want to search."
msgstr ""
"Pangatlo, kakailanganin mo ng wallet na may mga UTXO na gusto mong hanapin."

#: src/guides/sat-hunting.md:52
msgid "Searching for Rare Ordinals"
msgstr "Paghahanap ng Rare Ordinals"

#: src/guides/sat-hunting.md:55
msgid "Searching for Rare Ordinals in a Bitcoin Core Wallet"
msgstr "Paghahanap ng Rare Ordinals sa isang Bitcoin Core Wallet"

#: src/guides/sat-hunting.md:57
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"searching for rare ordinals in a Bitcoin Core wallet is Easy. Assuming your "
"wallet is named `foo`:"
msgstr ""
"Ang command na `ord wallet` ay isang wrapper lamang sa RPC API ng Bitcoin "
"Core, kaya madali ang paghahanap ng mga rare ordinals sa isang Bitcoin Core "
"wallet. Ipagpalagay na ang iyong wallet ay pinangalanang `foo`:"

#: src/guides/sat-hunting.md:61
msgid "Load your wallet:"
msgstr "I-load ang iyong wallet:"

#: src/guides/sat-hunting.md:67
msgid "Display any rare ordinals wallet `foo`'s UTXOs:"
msgstr "Ipakita ang anumang mga bihirang ordinals wallet na `foo`'s UTXOs:"

#: src/guides/sat-hunting.md:73
msgid "Searching for Rare Ordinals in a Non-Bitcoin Core Wallet"
msgstr "Paghahanap ng Rare Ordinals sa Non-Bitcoin Core Wallet"

#: src/guides/sat-hunting.md:75
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"to search for rare ordinals in a non-Bitcoin Core wallet, you'll need to "
"import your wallet's descriptors into Bitcoin Core."
msgstr ""
"Ang command na `ord wallet` ay isang wrapper lamang sa RPC API ng Bitcoin "
"Core, kaya upang maghanap ng mga rate ordinals sa isang wallet na hindi "
"Bitcoin Core, kakailanganin mong i-import ang mga descriptor ng iyong wallet "
"sa Bitcoin Core."

#: src/guides/sat-hunting.md:79
msgid ""
"[Descriptors](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors."
"md) describe the ways that wallets generate private keys and public keys."
msgstr ""
"[Mga Deskriptor](https://github.com/bitcoin/bitcoin/blob/master/doc/"
"descriptors.md) naglalarawan ng mga paraan kung saan ang mga wallet ay nag-"
"generate ng mga private kets at public keys."

#: src/guides/sat-hunting.md:82
msgid ""
"You should only import descriptors into Bitcoin Core for your wallet's "
"public keys, not its private keys."
msgstr ""
"Dapat ka lang mag-import ng mga deskriptor sa Bitcoin Core para sa iyong "
"wallet public keys, hindi ang mga private keys."

#: src/guides/sat-hunting.md:85
msgid ""
"If your wallet's public key descriptor is compromised, an attacker will be "
"able to see your wallet's addresses, but your funds will be safe."
msgstr ""
"Kung nakompromiso ang public key descriptor ng iyong wallet, ang isang "
"attacker ay maaring makit ang mga address ng iyong wallet, ngunit magiging "
"ligtas ang iyong mga funds."

#: src/guides/sat-hunting.md:88
msgid ""
"If your wallet's private key descriptor is compromised, an attacker can "
"drain your wallet of funds."
msgstr ""
"Kung ang deskriptor ng private key ng iyong wallet ay nakompromiso, ang "
"isang attacker ay maaaring ubusin ang iyong pitaka ng mga funds."

#: src/guides/sat-hunting.md:91
msgid ""
"Get the wallet descriptor from the wallet whose UTXOs you want to search for "
"rare ordinals. It will look something like this:"
msgstr ""
"Kunin ang wallet descriptor mula sa wallet na may mga UTXO na gusto mong "
"hanaping rare ordinals. Magiging ganito ang hitsura:"

#: src/guides/sat-hunting.md:98
msgid "Create a watch-only wallet named `foo-watch-only`:"
msgstr "Gumawa ng watch-only wallet na `foo-watch-only`:"

#: src/guides/sat-hunting.md:104
msgid "Feel free to give it a better name than `foo-watch-only`!"
msgstr ""
"Huwag mag-atubiling bigyan ito ng mas magandang pangalan kaysa sa `foo-watch-"
"only`!"

#: src/guides/sat-hunting.md:106
msgid "Load the `foo-watch-only` wallet:"
msgstr "I-load ang wallet na `foo-watch-only`:"

#: src/guides/sat-hunting.md:112
msgid "Import your wallet descriptors into `foo-watch-only`:"
msgstr "I-import ang iyong mga deskriptor ng wallet sa `foo-watch-only`:"

#: src/guides/sat-hunting.md:114
msgid ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\", \"timestamp\":0 }]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\", \"timestamp\":0 }]'\n"
"```"

#: src/guides/sat-hunting.md:119
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of `\"timestamp\"` instead of "
"`0`. This will reduce the time it takes for Bitcoin Core to search for your "
"wallet's UTXOs."
msgstr ""
"Kung alam mo ang timestamp ng Unix noong unang nagsimulang makatanggap ang "
"iyong wallet transactions, maaari mo itong gamitin para sa halaga ng "
"`\"timestamp\"` sa halip na `0`. Ito ay magbabawas sa oras na kinakailangan "
"ng Bitcoin Core upang mahanap ang iyong mga UTXOs."

#: src/guides/sat-hunting.md:124 src/guides/sat-hunting.md:225
msgid "Check that everything worked:"
msgstr "Suriin kung gumagana ang lahat:"

#: src/guides/sat-hunting.md:130 src/guides/sat-hunting.md:231
msgid "Display your wallet's rare ordinals:"
msgstr "Ipakita ang mga rare ordinal ng iyong wallet:"

#: src/guides/sat-hunting.md:136
msgid ""
"Searching for Rare Ordinals in a Wallet that Exports Multi-path Descriptors"
msgstr ""
"Paghahanap ng Rare Ordinals sa isang Wallet na Nag-e-export ng Multi-path "
"Descriptors"

#: src/guides/sat-hunting.md:138
msgid ""
"Some descriptors describe multiple paths in one descriptor using angle "
"brackets, e.g., `<0;1>`. Multi-path descriptors are not yet supported by "
"Bitcoin Core, so you'll first need to convert them into multiple "
"descriptors, and then import those multiple descriptors into Bitcoin Core."
msgstr ""
"Ang ilang mga deskriptor ay naglalarawan ng maraming mga paths sa isang "
"deskriptor gamit ang angle bracket, hal., `<0;1>`. Ang mga multi-path "
"descriptor ay hindi pa sinusuportahan ng Bitcoin Core, kaya kailangan mo "
"munang i-convert ang mga ito sa maramihang mga descriptor, at pagkatapos ay "
"i-import ang maraming mga descriptor na iyon sa Bitcoin Core."

#: src/guides/sat-hunting.md:143
msgid ""
"First get the multi-path descriptor from your wallet. It will look something "
"like this:"
msgstr ""
"Kunin mo muna ang multi-path descriptor mula sa iyong wallet. Magmumukha "
"itong isang bagay ganito:"

#: src/guides/sat-hunting.md:150
msgid "Create a descriptor for the receive address path:"
msgstr "Lumikha ng descriptor para sa path ng receive address:"

#: src/guides/sat-hunting.md:156
msgid "And the change address path:"
msgstr "At ang path ng pagbabago ng address:"

#: src/guides/sat-hunting.md:162
msgid ""
"Get and note the checksum for the receive address descriptor, in this case "
"`tpnxnxax`:"
msgstr ""
"Kunin at tandaan ang checksum para sa receive address descriptor, sa case na "
"ito `tpnxnxax`:"

#: src/guides/sat-hunting.md:170
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src/guides/sat-hunting.md:180
msgid "And for the change address descriptor, in this case `64k8wnd7`:"
msgstr "At para sa change address descriptor, sa case na ito `64k8wnd7`:"

#: src/guides/sat-hunting.md:187
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src/guides/sat-hunting.md:197
msgid "Load the wallet you want to import the descriptors into:"
msgstr "I-load ang wallet na gusto mong i-import ang mga descriptor sa:"

#: src/guides/sat-hunting.md:203
msgid ""
"Now import the descriptors, with the correct checksums, into Bitcoin Core."
msgstr ""
"Ngayon i-import ang mga descriptor, na may tamang mga checksum, sa Bitcoin "
"Core."

#: src/guides/sat-hunting.md:205
msgid ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"

#: src/guides/sat-hunting.md:220
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of the `\"timestamp\"` fields "
"instead of `0`. This will reduce the time it takes for Bitcoin Core to "
"search for your wallet's UTXOs."
msgstr ""
"Kung alam mo ang timestamp ng Unix noong unang nagsimulang makatanggap ang "
"iyong wallet transactions, maaari mo itong gamitin para sa value ng mga "
"field na `\"timestamp\"` sa halip na `0`. Babawasan nito ang oras na "
"kinakailangan para sa Bitcoin Core sa hanapin ang mga UTXOs."

#: src/guides/sat-hunting.md:237
msgid "Exporting Descriptors"
msgstr "Pag-export ng mga Descriptors"

#: src/guides/sat-hunting.md:241
msgid ""
"Navigate to the `Settings` tab, then to `Script Policy`, and press the edit "
"button to display the descriptor."
msgstr ""
"Mag-navigate sa tab na `Setting`, pagkatapos ay sa `Script Policy`, at "
"pindutin ang i-edit button to display the descriptor."

#: src/guides/sat-hunting.md:244
msgid "Transferring Ordinals"
msgstr "Pag-transfer ng Ordinals"

#: src/guides/sat-hunting.md:246
msgid ""
"The `ord` wallet supports transferring specific satoshis. You can also use "
"`bitcoin-cli` commands `createrawtransaction`, "
"`signrawtransactionwithwallet`, and `sendrawtransaction`, how to do so is "
"complex and outside the scope of this guide."
msgstr ""
"Sinusuportahan ng `ord` wallet ang pag-transfer ng mga partikular na "
"satoshi. Maaari mo ring gamitin ang `bitcoin-cli` commands "
"`createrawtransaction`,`signrawtransactionwithwallet`, at "
"`sendrawtransaction`, kung paano gawin ito ay kumplikado at sa labas ng "
"saklaw ng gabay na ito."

#: src/guides/collecting.md:4
msgid ""
"Currently, [ord](https://github.com/ordinals/ord/) is the only wallet "
"supporting sat-control and sat-selection, which are required to safely store "
"and send rare sats and inscriptions, hereafter ordinals."
msgstr ""
"Sa kasalukuyan, ang [ord](https://github.com/ordinals/ord/) ay ang tanging "
"wallet na sumusuporta sa sat control at selection, na kinakailangan upang "
"ligtas na mag-store at magpadala ng mga rare sats at inscription, pagkatapos "
"ay tinutukoy bilang mga ordinal."

#: src/guides/collecting.md:8
msgid ""
"The recommended way to send, receive, and store ordinals is with `ord`, but "
"if you are careful, it is possible to safely store, and in some cases send, "
"ordinals with other wallets."
msgstr ""
"Inirerekomenda na magpadala, tumanggap at mag-imbak ng mga ordinal na may "
"`ord`, ngunit kung ikaw ay maingat, posible na mag-store, at sa ilang mga "
"kaso, magpadala ng mga ordinal nang ligtas kasama ng iba pang mga wallet."

#: src/guides/collecting.md:12
msgid ""
"As a general note, receiving ordinals in an unsupported wallet is not "
"dangerous. Ordinals can be sent to any bitcoin address, and are safe as long "
"as the UTXO that contains them is not spent. However, if that wallet is then "
"used to send bitcoin, it may select the UTXO containing the ordinal as an "
"input, and send the inscription or spend it to fees."
msgstr ""
"Sa pangkalahatan, hindi mapanganib na makatanggap ng mga ordinal sa isang "
"hindi supported na wallet. Ang mga ordinal ay maaaring ipadala sa anumang "
"bitcoin address at ligtas hangga't ang UTXO na naglalaman ng mga ito ay "
"hindi ginagastos. Gayunpaman, kung gagamitin ang wallet na ito upang "
"magpadala ng mga bitcoin, maaari nitong piliin ang UTXO na naglalaman ng "
"ordinal bilang input, at maaaring ipadala ang inscription o funds upang "
"magbayad ng fees."

#: src/guides/collecting.md:18
msgid ""
"A [guide](./collecting/sparrow-wallet.md) to creating an `ord`\\-compatible "
"wallet with [Sparrow Wallet](https://sparrowwallet.com/), is available in "
"this handbook."
msgstr ""
"Ang [gabay](./collecting/sparrow-wallet.md) sa paggawa ng `ord`\\-compatible "
"na wallet na [Sparrow Wallet](https://sparrowwallet.com/), ay available sa "
"handbook na ito."

#: src/guides/collecting.md:21
msgid ""
"Please note that if you follow this guide, you should not use the wallet you "
"create to send BTC, unless you perform manual coin-selection to avoid "
"sending ordinals."
msgstr ""
"Pakitandaan na kung sinusunod mo ang gabay na ito, hindi mo dapat gamitin "
"ang wallet na nilikha mo upang magpadala ng BTC maliban kung gagawa ka ng "
"manu-manong pagpili ng coin upang maiwasan ang pagpapadala ng mga ordinal."

#: src/guides/collecting/sparrow-wallet.md:1
msgid "Collecting Inscriptions and Ordinals with Sparrow Wallet"
msgstr "Pagkolekta ng mga Inscription at Ordinal gamit ang Sparrow Wallet"

#: src/guides/collecting/sparrow-wallet.md:4
msgid ""
"Users who cannot or have not yet set up the [ord](https://github.com/"
"ordinals/ord) wallet can receive inscriptions and ordinals with alternative "
"bitcoin wallets, as long as they are _very_ careful about how they spend "
"from that wallet."
msgstr ""
"Ang mga user na hindi o hindi pa nakakapag-set up ng [ord](https://github."
"com/ordinals/ord) wallet ay maaaring makatanggap ng mga inscription at "
"ordinal sa iba pang bitcoin wallet, basta't _maingat_ sila kung paano nila "
"ginagastos ang pera mula sa wallet na iyon."

#: src/guides/collecting/sparrow-wallet.md:6
msgid ""
"This guide gives some basic steps on how to create a wallet with [Sparrow "
"Wallet](https://sparrowwallet.com/) which is compatible with `ord` and can "
"be later imported into `ord`"
msgstr ""
"Nagbibigay ang gabay na ito ng ilang pangunahing hakbang kung paano gumawa "
"ng [Sparrow Wallet](https://sparrowwallet.com/) na tugma sa `ord` at maaring "
"ma-import sa `ord`"

#: src/guides/collecting/sparrow-wallet.md:8
msgid "⚠️⚠️ Warning!! ⚠️⚠️"
msgstr ""

#: src/guides/collecting/sparrow-wallet.md:9
msgid ""
"As a general rule if you take this approach, you should use this wallet with "
"the Sparrow software as a receive-only wallet."
msgstr ""
"Sa pangkalahatan, kung gagawin mo ang diskarteng ito, dapat mong gamitin ang "
"wallet na ito gamit ang Sparrow software bilang receive wallet lamang."

#: src/guides/collecting/sparrow-wallet.md:11
msgid ""
"Do not spend any satoshis from this wallet unless you are sure you know what "
"you are doing. You could very easily inadvertently lose access to your "
"ordinals and inscriptions if you don't heed this warning."
msgstr ""
"Huwag gumastos ng satoshi mula sa wallet na ito maliban kung sigurado kang "
"alam mo ang iyong ginagawa. Madali mong hindi sinasadyang mawalan ng access "
"sa iyong mga ordinal at inscription kung babalewalain mo ang babalang ito."

#: src/guides/collecting/sparrow-wallet.md:13
msgid "Wallet Setup & Receiving"
msgstr "Wallet Setup at Receiving"

#: src/guides/collecting/sparrow-wallet.md:15
msgid ""
"Download the Sparrow Wallet from the [releases page](https://sparrowwallet."
"com/download/) for your particular operating system."
msgstr ""
"I-download ang Sparrow Wallet mula sa [releases page](https://sparrowwallet."
"com/download/) para sa iyong partikular na operating system."

#: src/guides/collecting/sparrow-wallet.md:17
msgid "Select `File -> New Wallet` and create a new wallet called `ord`."
msgstr ""
"Piliin ang `File -> New Wallet` at gumawa ng bagong wallet na tinatawag na "
"`ord`."

#: src/guides/collecting/sparrow-wallet.md:19
msgid "![](images/wallet_setup_01.png)"
msgstr "![](images/wallet_setup_01.png)"

#: src/guides/collecting/sparrow-wallet.md:21
msgid ""
"Change the `Script Type` to `Taproot (P2TR)` and select the `New or Imported "
"Software Wallet` option."
msgstr ""
"Palitan ang `Script Type` sa `Taproot (P2TR)` at piliin ang ``New or "
"Imported Software Wallet` option."

#: src/guides/collecting/sparrow-wallet.md:23
msgid "![](images/wallet_setup_02.png)"
msgstr "![](images/wallet_setup_02.png)"

#: src/guides/collecting/sparrow-wallet.md:25
msgid ""
"Select `Use 12 Words` and then click `Generate New`. Leave the passphrase "
"blank."
msgstr ""
"Piliin ang `Use 12 Words` at pagkatapos ay i-click ang `Generate New`. "
"Iwanan ang passphrase na blangko."

#: src/guides/collecting/sparrow-wallet.md:27
msgid "![](images/wallet_setup_03.png)"
msgstr "![](images/wallet_setup_03.png)"

#: src/guides/collecting/sparrow-wallet.md:29
msgid ""
"A new 12 word BIP39 seed phrase will be generated for you. Write this down "
"somewhere safe as this is your backup to get access to your wallet. NEVER "
"share or show this seed phrase to anyone else."
msgstr ""
"Ma-ge-generate para sa iyo ang isang bagong BIP39 12 word seed phrase. "
"Isulat ito sa isang lugar na ligtas dahil ito ang iyong backup para makakuha "
"ng access sa iyong wallet. HUWAG ibahagi o ipakita ang seed na pariralang "
"ito sa iba."

#: src/guides/collecting/sparrow-wallet.md:31
msgid "Once you have written down the seed phrase click `Confirm Backup`."
msgstr "Kapag naisulat mo na ang seed phrase i-click ang `Confirm Backup`."

#: src/guides/collecting/sparrow-wallet.md:33
msgid "![](images/wallet_setup_04.png)"
msgstr "![](images/wallet_setup_04.png)"

#: src/guides/collecting/sparrow-wallet.md:35
msgid ""
"Re-enter the seed phrase which you wrote down, and then click `Create "
"Keystore`."
msgstr ""
"Muling i-enter ang seed phrase na iyong isinulat, at pagkatapos ay i-click "
"ang `Create Keystore`."

#: src/guides/collecting/sparrow-wallet.md:37
msgid "![](images/wallet_setup_05.png)"
msgstr "![](images/wallet_setup_05.png)"

#: src/guides/collecting/sparrow-wallet.md:39
msgid "Click `Import Keystore`."
msgstr "I-click `Import Keystore`."

#: src/guides/collecting/sparrow-wallet.md:41
msgid "![](images/wallet_setup_06.png)"
msgstr "![](images/wallet_setup_06.png)"

#: src/guides/collecting/sparrow-wallet.md:43
msgid "Click `Apply`. Add a password for the wallet if you want to."
msgstr "I-click ang `Apply`. I-add ang password para sa wallet kung gusto mo."

#: src/guides/collecting/sparrow-wallet.md:45
msgid "![](images/wallet_setup_07.png)"
msgstr "![](images/wallet_setup_07.png)"

#: src/guides/collecting/sparrow-wallet.md:47
msgid ""
"You now have a wallet which is compatible with `ord`, and can be imported "
"into `ord` using the BIP39 Seed Phrase. To receive ordinals or inscriptions, "
"click on the `Receive` tab and copy a new address."
msgstr ""
"Mayroon ka na ngayong wallet na compatible sa `ord`, at maaaring i-import sa "
"`ord` gamit ang BIP39 Seed Phrase. Upang makatanggap ng mga ordinal o "
"inskripsiyon, mag-click sa tab na `Receive` at kumopya ng bagong address."

#: src/guides/collecting/sparrow-wallet.md:49
msgid ""
"Each time you want to receive you should use a brand-new address, and not re-"
"use existing addresses."
msgstr ""
"Sa tuwing gusto mong makatanggap dapat kang gumamit ng bagong address, at "
"hindi muling-gumamit ng mga kasalukuyang address."

#: src/guides/collecting/sparrow-wallet.md:51
msgid ""
"Note that bitcoin is different to some other blockchain wallets, in that "
"this wallet can generate an unlimited number of new addresses. You can "
"generate a new address by clicking on the `Get Next Address` button. You can "
"see all of your addresses in the `Addresses` tab of the app."
msgstr ""
"Tandaan na ang bitcoin ay iba sa ibang mga wallet ng blockchain, sa maaaring "
"bumuo ang wallet na ito ng walang limitasyong bilang ng mga bagong address. "
"Maaari mong i-generate ng bagong address sa pamamagitan ng pag-click sa `Get "
"Next Address` na buton. Maaari mong tingnan ang lahat ng iyong address sa "
"tab na `Addresses` ng app."

#: src/guides/collecting/sparrow-wallet.md:53
msgid ""
"You can add a label to each address, so you can keep track of what it was "
"used for."
msgstr ""
"Maaari kang magdagdag ng label sa bawat address, para masubaybayan mo kung "
"ano iyon at ginagamit para saan."

#: src/guides/collecting/sparrow-wallet.md:55
msgid "![](images/wallet_setup_08.png)"
msgstr "![](images/wallet_setup_08.png)"

#: src/guides/collecting/sparrow-wallet.md:57
msgid "Validating / Viewing Received Inscriptions"
msgstr "Pagpapatunay / Pagtingin sa Mga Natanggap na Inskripsiyon"

#: src/guides/collecting/sparrow-wallet.md:59
msgid ""
"Once you have received an inscription you will see a new transaction in the "
"`Transactions` tab of Sparrow, as well as a new UTXO in the `UTXOs` tab."
msgstr ""
"Kapag nakatanggap ka ng inskripsiyon makakakita ka ng bagong transaksyon sa "
"tab ng `Transactions` ng Sparrow, pati na rin ang isang bagong UTXO sa tab "
"na `UTXOs`."

#: src/guides/collecting/sparrow-wallet.md:61
msgid ""
"Initially this transaction may have an \"Unconfirmed\" status, and you will "
"need to wait for it to be mined into a bitcoin block before it is fully "
"received."
msgstr ""
"Sa una ang transaksyong ito ay maaaring magkaroon ng isang \"Unconfirmed\" "
"na status, at ikaw ay kailangan maghintay na ito ay mamina sa isang bitcoin "
"block bago ito ganap na matanggap."

#: src/guides/collecting/sparrow-wallet.md:63
msgid "![](images/validating_viewing_01.png)"
msgstr "![](images/validating_viewing_01.png)"

#: src/guides/collecting/sparrow-wallet.md:65
msgid ""
"To track the status of your transaction you can right-click on it,  select "
"`Copy Transaction ID` and then paste that transaction id into [mempool.space]"
"(https://mempool.space)."
msgstr ""
"Upang subaybayan ang status ng iyong transaksyon maaari kang mag-right click "
"dito, piliin ang `Copy Transaction ID` at pagkatapos ay i-paste ang "
"transaction id sa [mempool.space](https://mempool.space)."

#: src/guides/collecting/sparrow-wallet.md:67
msgid "![](images/validating_viewing_02.png)"
msgstr "![](images/validating_viewing_02.png)"

#: src/guides/collecting/sparrow-wallet.md:69
msgid ""
"Once the transaction has confirmed, you can validate and view your "
"inscription by heading over to the `UTXOs` tab, finding the UTXO you want to "
"check, right-clicking on the `Output` and selecting `Copy Transaction "
"Output`. This transaction output id can then be pasted into the [ordinals."
"com](https://ordinals.com) search."
msgstr ""
"Kapag nakumpirma na ang transaksyon, maaari mong i-validate at tingnan ang "
"iyong inskripsyon sa pamamagitan ng pagpunta sa tab na `UTXOs`, paghahanap "
"ng UTXO na gusto mong suriin, pag-right-click sa `Output` at pagpili sa "
"`Copy Transaction Output`. Ang output id ng transaksyon na ito ay maaaring i-"
"paste sa [ordinals.com](https://ordinals.com) search."

#: src/guides/collecting/sparrow-wallet.md:72
msgid "Freezing UTXO's"
msgstr "Pag-freeze ng UTXO's"

#: src/guides/collecting/sparrow-wallet.md:73
msgid ""
"As explained above, each of your inscriptions is stored in an Unspent "
"Transaction Output (UTXO). You want to be very careful not to accidentally "
"spend your inscriptions, and one way to make it harder for this to happen is "
"to freeze the UTXO."
msgstr ""
"Tulad ng ipinaliwanag sa itaas, ang bawat isa sa iyong mga inskripsiyon ay "
"naka-store sa isang Unspent Transaction Output (UTXO). Gusto mong maging "
"maingat na hindi aksidenteng ma-gastos mo ang iyong mga inskripsiyon, at "
"isang paraan para mas mahirap itong mangyari ay upang i-freeze ang UTXO."

#: src/guides/collecting/sparrow-wallet.md:75
msgid ""
"To do this, go to the `UTXOs` tab, find the UTXO you want to freeze, right-"
"click on the `Output` and select `Freeze UTXO`."
msgstr ""
"Upang gawin ito, pumunta sa tab na `UTXOs`, hanapin ang UTXO na gusto mong i-"
"freeze, i-right--click sa `Output` at piliin ang `Freeze UTXO`."

#: src/guides/collecting/sparrow-wallet.md:77
msgid ""
"This UTXO (Inscription) is now un-spendable within the Sparrow Wallet until "
"you unfreeze it."
msgstr ""
"Ang UTXO (Inscription) na ito ay hindi na gagastusin sa loob ng Sparrow "
"Wallet hanggang sa i-unfreeze mo ito."

#: src/guides/collecting/sparrow-wallet.md:79
msgid "Importing into `ord` wallet"
msgstr "Pag-import sa `ord` wallet"

#: src/guides/collecting/sparrow-wallet.md:81
msgid ""
"For details on setting up Bitcoin Core and the `ord` wallet check out the "
"[Inscriptions Guide](../inscriptions.md)"
msgstr ""
"Para sa mga detalye sa pagse-set up ng Bitcoin Core at ang `ord` wallet "
"tingnan ang [Inscriptions Guide](../inscriptions.md)"

#: src/guides/collecting/sparrow-wallet.md:83
msgid ""
"When setting up `ord`, instead of running `ord wallet create` to create a "
"brand-new wallet, you can import your existing wallet using `ord wallet "
"restore \"BIP39 SEED PHRASE\"` using the seed phrase you generated with "
"Sparrow Wallet."
msgstr ""
"Kapag nagse-set up ng `ord`, sa halip na i-run ang `ord wallet create` upang "
"lumikha ng isang bagong-bagong pitaka, maaari mong i-import ang iyong "
"existing na pitaka gamit ang `ord wallet restore \"BIP39 SEED PHRASE\"` "
"gamit ang seed phrase na nabuo mo gamit ang Sparrow Wallet."

#: src/guides/collecting/sparrow-wallet.md:85
msgid ""
"There is currently a [bug](https://github.com/ordinals/ord/issues/1589) "
"which causes an imported wallet to not be automatically rescanned against "
"the blockchain. To work around this you will need to manually trigger a "
"rescan using the bitcoin core cli: `bitcoin-cli -rpcwallet=ord "
"rescanblockchain 767430`"
msgstr ""
"Kasalukuyang may [bug](https://github.com/ordinals/ord/issues/1589) na "
"nagiging sanhi ng isang na-import na wallet upang hindi awtomatikong muling "
"i-scan versus sa blockchain. Upang magawa ito, kailangan mong manu-manong "
"mag-trigger ang rescan gamit ang bitcoin core cli: `bitcoin-cli -"
"rpcwallet=ord rescanblockchain 767430`"

#: src/guides/collecting/sparrow-wallet.md:88
msgid ""
"You can then check your wallet's inscriptions using `ord wallet inscriptions`"
msgstr ""
"Pagkatapos ay maaari mong suriin ang mga inskripsiyon ng iyong wallet gamit "
"ang `ord wallet inscriptions`"

#: src/guides/collecting/sparrow-wallet.md:90
msgid ""
"Note that if you have previously created a wallet with `ord`, then you will "
"already have a wallet with the default name, and will need to give your "
"imported wallet a different name. You can use the `--wallet` parameter in "
"all `ord` commands to reference a different wallet, eg:"
msgstr ""
"Tandaan na kung nakagawa ka dati ng wallet na may `ord`, pagkatapos ay "
"mayroon nang wallet na may default na pangalan, at kakailanganing ibigay ang "
"iyong imported na wallet ng bagong pangalan. Maaari mong gamitin ang "
"parameter na `--wallet` sa lahat ng commands ng `ord` para ma-reference sa "
"ibang wallet, hal:"

#: src/guides/collecting/sparrow-wallet.md:92
msgid "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"
msgstr "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"

#: src/guides/collecting/sparrow-wallet.md:94
msgid "`ord --wallet ord_from_sparrow wallet inscriptions`"
msgstr "`ord --wallet ord_from_sparrow wallet inscriptions`"

#: src/guides/collecting/sparrow-wallet.md:96
msgid "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"
msgstr "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"

#: src/guides/collecting/sparrow-wallet.md:98
msgid "Sending inscriptions with Sparrow Wallet"
msgstr "Pagpapadala ng mga inskripsiyon gamit ang Sparrow Wallet"

#: src/guides/collecting/sparrow-wallet.md:100
msgid "⚠️⚠️ Warning ⚠️⚠️"
msgstr ""

#: src/guides/collecting/sparrow-wallet.md:101
msgid ""
"While it is highly recommended that you set up a bitcoin core node and run "
"the `ord` software, there are certain limited ways you can send inscriptions "
"out of Sparrow Wallet in a safe way. Please note that this is not "
"recommended, and you should only do this if you fully understand what you "
"are doing."
msgstr ""
"Habang lubos na inirerekomenda na mag-set up ka ng bitcoin core node at "
"magpatakbo ng `ord` software, may ilang partikular na limitadong paraan na "
"maaari kang magpadala ng mga inskripsiyon mula sa Sparrow Wallet sa ligtas "
"na paraan. Pakitandaan na hindi ito inirerekomenda, at dapat mo lang gawin "
"ito kung lubos mong naiintindihan kung ano ang iyong ginagawa."

#: src/guides/collecting/sparrow-wallet.md:103
msgid ""
"Using the `ord` software will remove much of the complexity we are "
"describing here, as it is able to automatically and safely handle sending "
"inscriptions in an easy way."
msgstr ""
"Ang paggamit ng `ord` software ay mag-aalis sa pagiging kumplikado sa "
"paglalarawan dito, dahil awtomatiko at ligtas nitong pangasiwaan ang "
"pagpapadala ng mga inskripsiyon sa madaling paraan."

#: src/guides/collecting/sparrow-wallet.md:105
msgid "⚠️⚠️ Additional Warning ⚠️⚠️"
msgstr "⚠️⚠️ Dagdag na Warning ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:106
msgid ""
"Don't use your sparrow inscriptions wallet to do general sends of non-"
"inscription bitcoin. You can setup a separate wallet in sparrow if you need "
"to do normal bitcoin transactions, and keep your inscriptions wallet "
"separate."
msgstr ""
"Huwag gamitin ang iyong sparrow inscriptions wallet para gumawa ng mga "
"pangkalahatang pagpapadala ng non-inscription bitcoin. Maaari kang mag-set "
"up ng hiwalay na wallet sa sparrow kung kailangan mong gawin ang mga normal "
"na transaksyon sa bitcoin, at panatilihin ang iyong inscription wallet na "
"hiwalay."

#: src/guides/collecting/sparrow-wallet.md:108
msgid "Bitcoin's UTXO model"
msgstr "Bitcoin's UTXO model"

#: src/guides/collecting/sparrow-wallet.md:109
msgid ""
"Before sending any transaction it's important that you have a good mental "
"model for bitcoin's Unspent Transaction Output (UTXO) system. The way "
"Bitcoin works is fundamentally different to many other blockchains such as "
"Ethereum. In Ethereum generally you have a single address in which you store "
"ETH, and you cannot differentiate between any of the ETH -  it is just all a "
"single value of the total amount in that address. Bitcoin works very "
"differently in that we generate a new address in the wallet for each "
"receive, and every time you receive sats to an address in your wallet you "
"are creating a new UTXO. Each UTXO can be seen and managed individually. You "
"can select specific UTXO's which you want to spend, and you can choose not "
"to spend certain UTXO's."
msgstr ""
"Bago magpadala ng anumang transaksyon, mahalaga na mayroon kang mabuting "
"modelo para sa sistema ng Unspent Transaction Output (UTXO) ng bitcoin. Ang "
"paraan ng Bitcoin ay pangunahing naiiba sa maraming iba pang mga blockchain "
"tulad ng Ethereum. Sa Ethereum sa pangkalahatan ay mayroon kang isang "
"address kung saan ka nag-iimbak ETH, at hindi ka makakapag-iba sa pagitan ng "
"alinman sa ETH - ito ay isang mahalagang kabuuang halaga sa address na iyon. "
"Kakaiba gumana ang Bitcoin sa paggawa namin ng bagong address sa wallet para "
"sa bawat tumatanggap, at sa tuwing makakatanggap ka sats sa isang address sa "
"iyong wallet ikaw ay lumilikha ng bagong UTXO. Ang bawat UTXO ay makikita at "
"pag-manage nang paisa-isa. Ikaw ay maaaring pumili ng mga partikular na UTXO "
"na gusto mong gastusin, at maaari mong piliin na huwag gumastos ng ilang mga "
"UTXO."

#: src/guides/collecting/sparrow-wallet.md:111
msgid ""
"Some Bitcoin wallets do not expose this level of detail, and they just show "
"you a single summed up value of all the bitcoin in your wallet. However, "
"when sending inscriptions it is important that you use a wallet like Sparrow "
"which allows for UTXO control."
msgstr ""
"Ang ilang mga wallet ng Bitcoin ay hindi inilalantad ang antas ng detalyeng "
"ito, at ipinapakita lang nila ang summed up na halaga ng lahat ng bitcoin sa "
"iyong wallet. Gayunpaman, kapag nagpapadala ng mga inskripsiyon, mahalaga na "
"gumamit ka ng wallet tulad ng Sparrow na nagpapahintulot para sa kontrol ng "
"UTXO."

#: src/guides/collecting/sparrow-wallet.md:113
msgid "Inspecting your inscription before sending"
msgstr "Pag-susuri ng iyong inskripsiyon bago ipadala"

#: src/guides/collecting/sparrow-wallet.md:114
msgid ""
"Like we have previously described inscriptions are inscribed onto sats, and "
"sats are stored within UTXOs. UTXO's are a collection of satoshis with some "
"particular value of the number of satoshis (the output value). Usually (but "
"not always) the inscription will be inscribed on the first satoshi in the "
"UTXO."
msgstr ""
"Tulad ng inilarawan natin dati, ang mga inskripsiyon ay nakasulat sa sats, "
"at ang mga sats ay nakaimbak sa loob ng mga UTXO. Ang mga UTXO ay isang "
"koleksyon ng mga satoshi na may ilang partikular na halaga ng bilang ng mga "
"satoshi (ang output value). Karaniwan (ngunit hindi palagi) ang inskripsiyon "
"ay isusulat sa unang satoshi sa UTXO."

#: src/guides/collecting/sparrow-wallet.md:116
msgid ""
"When inspecting your inscription before sending the main thing you will want "
"to check is which satoshi in the UTXO your inscription is inscribed on."
msgstr ""
"Kapag sinisiyasat ang iyong inskripsiyon bago ipadala ang pangunahing bagay "
"na gusto mong tingnan kung saang satoshi sa UTXO nakalagay ang inskripsiyon "
"mo."

#: src/guides/collecting/sparrow-wallet.md:118
msgid ""
"To do this, you can follow the [Validating / Viewing Received Inscriptions]"
"(./sparrow-wallet.md#validating--viewing-received-inscriptions) described "
"above to find the inscription page for your inscription on ordinals.com"
msgstr ""
"Upang gawin ito, maaari mong sundin ang [Validating / Viewing Received "
"Inscriptions](./sparrow-wallet.md#validating--viewing-received-inscriptions) "
"inilarawan sa itaas upang mahanap ang pahina ng inskripsyon para sa iyong "
"inskripsyon sa ordinals.com"

#: src/guides/collecting/sparrow-wallet.md:120
msgid ""
"There you will find some metadata about your inscription which looks like "
"the following:"
msgstr ""
"Doon makikita mo ang ilang metadata tungkol sa iyong inskripsiyon na mukhang "
"ang mga sumusunod:"

#: src/guides/collecting/sparrow-wallet.md:122
msgid "![](images/sending_01.png)"
msgstr "![](images/sending_01.png)"

#: src/guides/collecting/sparrow-wallet.md:124
msgid "There is a few of important things to check here:"
msgstr "Mayroong ilang mahahalagang bagay na dapat suriin dito:"

#: src/guides/collecting/sparrow-wallet.md:125
msgid ""
"The `output` identifier matches the identifier of the UTXO you are going to "
"send"
msgstr ""
"Ang `output` identifier ay tumutugma sa identifier ng UTXO na ipapadala"

#: src/guides/collecting/sparrow-wallet.md:126
msgid ""
"The `offset` of the inscription is `0` (this means that the inscription is "
"located on the first sat in the UTXO)"
msgstr ""
"Ang `offset` ng inskripsiyon ay `0` (ito ay nangangahulugan na ang "
"inskripsiyon ay matatagpuan sa unang sat sa UTXO)"

#: src/guides/collecting/sparrow-wallet.md:127
msgid ""
"the `output_value` has enough sats to cover the transaction fee (postage) "
"for sending the transaction. The exact amount you will need depends on the "
"fee rate you will select for the transaction"
msgstr ""
"ang `output_value` ay may sapat na sats upang mabayaran ang bayad sa "
"transaksyon (postage) para sa pagpapadala ng transaksyon. Ang eksaktong "
"halaga na kakailanganin mo ay depende sa rate ng bayad na pipiliin mo para "
"sa transaksyon"

#: src/guides/collecting/sparrow-wallet.md:129
msgid ""
"If all of the above are true for your inscription, it should be safe for you "
"to send it using the method below."
msgstr ""
"Kung ang lahat ng nasa itaas ay totoo para sa iyong inskripsiyon, ito ay "
"dapat na ligtas para sa iyo upang ipadala ito gamit ang paraan sa ibaba."

#: src/guides/collecting/sparrow-wallet.md:131
msgid ""
"⚠️⚠️ Be very careful sending your inscription particularly if the `offset` "
"value is not `0`. It is not recommended to use this method if that is the "
"case, as doing so you could accidentally send your inscription to a bitcoin "
"miner unless you know what you are doing."
msgstr ""
"⚠️⚠️ Maging maingat sa pagpapadala ng iyong inskripsiyon lalo na kung ang "
"`offset` value ay hindi `0`. Hindi inirerekomenda na gamitin ang paraang ito "
"kung iyon ang kaso, sa paggawa nito maaari mong hindi sinasadyang ipadala "
"ang iyong inskripsiyon sa isang bitcoin miner maliban kung alam mo ang iyong "
"ginagawa."

#: src/guides/collecting/sparrow-wallet.md:133
msgid "Sending your inscription"
msgstr "Pag-padala ng iyong inscription"

#: src/guides/collecting/sparrow-wallet.md:134
msgid ""
"To send an inscription navigate to the `UTXOs` tab, and find the UTXO which "
"you previously validated contains your inscription."
msgstr ""
"Upang magpadala ng inskripsiyon, mag-navigate sa tab na `UTXOs`, at hanapin "
"ang UTXO na dati mong na-validate ay naglalaman ng iyong inskripsiyon."

#: src/guides/collecting/sparrow-wallet.md:136
msgid ""
"If you previously froze the UXTO you will need to right-click on it and "
"unfreeze it."
msgstr ""
"Kung dati mong na-freeze ang UXTO kakailanganin mong i-right-click ito at i-"
"unfreeze ito."

#: src/guides/collecting/sparrow-wallet.md:138
msgid ""
"Select the UTXO you want to send, and ensure that is the _only_ UTXO is "
"selected. You should see `UTXOs 1/1` in the interface. Once you are sure "
"this is the case you can hit `Send Selected`."
msgstr ""
"Piliin ang UTXO na gusto mong ipadala, at tiyaking iyon ang _only_ UTXO na "
"napili. Dapat mong makita ang `UTXOs 1/1` sa interface. Kapag sigurado ka na "
"ito ang case na maaari mong pindutin ang `Send Selected`."

#: src/guides/collecting/sparrow-wallet.md:140
msgid "![](images/sending_02.png)"
msgstr "![](images/sending_02.png)"

#: src/guides/collecting/sparrow-wallet.md:142
msgid ""
"You will then be presented with the transaction construction interface. "
"There is a few things you need to check here to make sure that this is a "
"safe send:"
msgstr ""
"Pagkatapos ay ipapakita sa iyo ang interface ng pagbuo ng transaksyon.May "
"ilang bagay na kailangan mong suriin dito upang matiyak na ito ay isang "
"ligtas na ipadala:"

#: src/guides/collecting/sparrow-wallet.md:144
msgid ""
"The transaction should have only 1 input, and this should be the UTXO with "
"the label you want to send"
msgstr ""
"Ang transaksyon ay dapat magkaroon lamang ng 1 input, at ito ay dapat na ang "
"UTXO na may label na gusto mong ipadala"

#: src/guides/collecting/sparrow-wallet.md:145
msgid ""
"The transaction should have only 1 output, which is the address/label where "
"you want to send the inscription"
msgstr ""
"Ang transaksyon ay dapat magkaroon lamang ng 1 output, na kung saan ay ang "
"address/label kung saan gusto mong ipadala ang inskripsiyon"

#: src/guides/collecting/sparrow-wallet.md:147
msgid ""
"If your transaction looks any different, for example you have multiple "
"inputs, or multiple outputs then this may not be a safe transfer of your "
"inscription, and you should abandon sending until you understand more, or "
"can import into the `ord` wallet."
msgstr ""
"Kung iba ang hitsura ng iyong transaksyon, halimbawa marami kang mga input, "
"o maramihang mga output kung gayon ito ay maaaring hindi isang ligtas na "
"paglipat ng iyong inskripsyon, at dapat mong iwanan ang pagpapadala hanggang "
"sa mas maunawaan mo, o maaaring mag-import sa `ord` wallet."

#: src/guides/collecting/sparrow-wallet.md:149
msgid ""
"You should set an appropriate transaction fee, Sparrow will usually "
"recommend a reasonable one, but you can also check [mempool.space](https://"
"mempool.space) to see what the recommended fee rate is for sending a "
"transaction."
msgstr ""
"Dapat kang magtakda ng naaangkop na bayarin sa transaksyon, ang Sparrow ay "
"karaniwang magrekomenda ng rate, ngunit maaari mo ring tingnan ang [mempool."
"space](https://mempool.space) upang makita kung ano ang inirerekomendang "
"rate ng bayad para sa pagpapadala ng transaksyon."

#: src/guides/collecting/sparrow-wallet.md:151
msgid ""
"You should add a label for the recipient address, a label like `alice "
"address for inscription #123` would be ideal."
msgstr ""
"Dapat kang magdagdag ng label para sa address ng tatanggap, isang label "
"tulad ng `alice address para sa inscription #123` ay magiging perpekto."

#: src/guides/collecting/sparrow-wallet.md:153
msgid ""
"Once you have checked the transaction is a safe transaction using the checks "
"above, and you are confident to send it you can click `Create Transaction`."
msgstr ""
"Kapag nasuri mo na ang transaksyon ay isang ligtas na transaksyon gamit ang "
"mga tseke sa itaas, at tiwala kang maipadala ito maaari mong i-click ang "
"`Create Transaction`."

#: src/guides/collecting/sparrow-wallet.md:155
msgid "![](images/sending_03.png)"
msgstr "![](images/sending_03.png)"

#: src/guides/collecting/sparrow-wallet.md:157
msgid ""
"Here again you can double check that your transaction looks safe, and once "
"you are confident you can click `Finalize Transaction for Signing`."
msgstr ""
"Dito muli, maaari mong i-double check kung ang iyong transaksyon ay mukhang "
"ligtas, at kapag tiwala ka na, maaari mong i-click ang `Finalize Transaction "
"for Signing`."

#: src/guides/collecting/sparrow-wallet.md:159
msgid "![](images/sending_04.png)"
msgstr "![](images/sending_04.png)"

#: src/guides/collecting/sparrow-wallet.md:161
msgid "Here you can triple check everything before hitting `Sign`."
msgstr "Dito maaari mong i-triple check ang lahat bago pindutin ang `Sign`."

#: src/guides/collecting/sparrow-wallet.md:163
msgid "![](images/sending_05.png)"
msgstr "![](images/sending_05.png)"

#: src/guides/collecting/sparrow-wallet.md:165
msgid ""
"And then actually you get very very last chance to check everything before "
"hitting `Broadcast Transaction`. Once you broadcast the transaction it is "
"sent to the bitcoin network, and starts being propagated into the mempool."
msgstr ""
"At pagkatapos ay talagang makakakuha ka ng huling pagkakataon upang suriin "
"ang lahat bago pindutin ang `Broadcast Transaction`. Kapag nai-broadcast mo "
"ang transaksyon ito ay ipapadala sa bitcoin network, at nagsimulang i-reveal "
"sa mempool."

#: src/guides/collecting/sparrow-wallet.md:167
msgid "![](images/sending_06.png)"
msgstr "![](images/sending_06.png)"

#: src/guides/collecting/sparrow-wallet.md:169
msgid ""
"If you want to track the status of your transaction you can copy the "
"`Transaction Id (Txid)` and paste that into [mempool.space](https://mempool."
"space)"
msgstr ""
"Kung gusto mong subaybayan ang status ng iyong transaksyon maaari mong "
"kopyahin ang `Transaction Id (Txid)` at i-paste iyon sa [mempool.space]"
"(https://mempool.space)"

#: src/guides/collecting/sparrow-wallet.md:171
msgid ""
"Once the transaction has confirmed you can check the inscription page on "
"[ordinals.com](https://ordinals.com) to validate that it has moved to the "
"new output location and address."
msgstr ""
"Kapag nakumpirma na ang transaksyon maaari mong tingnan ang pahina ng "
"inskripsiyon sa [ordinals.com](https://ordinals.com) upang ma-validate na "
"lumipat ito sa bagong lokasyon at address ng output."

#: src/guides/collecting/sparrow-wallet.md:175
msgid ""
"Sparrow wallet is not showing a transaction/UTXO, but I can see it on "
"mempool.space!"
msgstr ""
"Hindi nagpapakita ng transaksyon/UTXO ang Sparrow wallet, ngunit nakikita ko "
"ito sa mempool.space!"

#: src/guides/collecting/sparrow-wallet.md:177
msgid ""
"Make sure that your wallet is connected to a bitcoin node. To validate this, "
"head into the `Preferences`\\-> `Server` settings, and click `Edit Existing "
"Connection`."
msgstr ""
"Tiyaking nakakonekta ang iyong wallet sa isang bitcoin node. Para "
"mapatunayan ito, pumunta sa `Preferences`\\-> `Server` na mga setting, at i-"
"click ang `Edit Existing Connection`."

#: src/guides/collecting/sparrow-wallet.md:179
msgid "![](images/troubleshooting_01.png)"
msgstr "![](images/troubleshooting_01.png)"

#: src/guides/collecting/sparrow-wallet.md:181
msgid ""
"From there you can select a node and click `Test Connection` to validate "
"that Sparrow is able to connect successfully."
msgstr ""
"Mula doon maaari kang pumili ng isang node at i-click ang `Test Connection` "
"upang ma-validate na matagumpay na nakakonekta ang Sparrow."

#: src/guides/collecting/sparrow-wallet.md:183
msgid "![](images/troubleshooting_02.png)"
msgstr "![](images/troubleshooting_02.png)"

#: src/guides/testing.md:4
msgid ""
"Ord can be tested using the following flags to specify the test network. For "
"more information on running Bitcoin Core for testing, see [Bitcoin's "
"developer documentation](https://developer.bitcoin.org/examples/testing."
"html)."
msgstr ""
"Maaaring subukan ang Ord gamit ang mga sumusunod na flag upang tukuyin ang "
"test network. Para sa higit pang impormasyon sa pagpapatakbo ng Bitcoin Core "
"para sa testing, tingnan ang [Bitcoin's developer documentation](https://"
"developer.bitcoin.org/examples/testing.html)."

#: src/guides/testing.md:7
msgid ""
"Most `ord` commands in [inscriptions](inscriptions.md) and [explorer]"
"(explorer.md) can be run with the following network flags:"
msgstr ""
"Karamihan sa commands ng `ord` sa [inscriptions](inscriptions.md) at "
"[explorer](explorer.md) ay maaaring i-run gamit ang mga sumusunod na flag ng "
"network:"

#: src/guides/testing.md:10
msgid "Network"
msgstr "Network"

#: src/guides/testing.md:10
msgid "Flag"
msgstr "Flag"

#: src/guides/testing.md:12
msgid "Testnet"
msgstr "Testnet"

#: src/guides/testing.md:12
msgid "`--testnet` or `-t`"
msgstr "`--testnet` or `-t`"

#: src/guides/testing.md:13
msgid "Signet"
msgstr "Signet"

#: src/guides/testing.md:13
msgid "`--signet` or `-s`"
msgstr "`--signet` or `-s`"

#: src/guides/testing.md:14
msgid "Regtest"
msgstr "Regtest"

#: src/guides/testing.md:14
msgid "`--regtest` or `-r`"
msgstr "`--regtest` or `-r`"

#: src/guides/testing.md:16
msgid "Regtest doesn't require downloading the blockchain or indexing ord."
msgstr ""
"Ang regtest ay hindi nangangailangan ng pag-download ng blockchain o "
"indexing ord."

#: src/guides/testing.md:21
msgid "Run bitcoind in regtest with:"
msgstr "I-run ang bitcoind sa regtest gamit ang:"

#: src/guides/testing.md:25
msgid "Create a wallet in regtest with:"
msgstr "Gumawa ng wallet bilang regtest gamit ang:"

#: src/guides/testing.md:29
msgid "Get a regtest receive address with:"
msgstr "Kumuha ng isang regtest receive address na may:"

#: src/guides/testing.md:33
msgid "Mine 101 blocks (to unlock the coinbase) with:"
msgstr "Mine 101 blocks (upang i-unlock ang coinbase) gamit ang:"

#: src/guides/testing.md:37
msgid "Inscribe in regtest with:"
msgstr "I-inscribe sa regtest kasama:"

#: src/guides/testing.md:41
msgid "Mine the inscription with:"
msgstr "I-mine and inscription gamit ang:"

#: src/guides/testing.md:45
msgid "View the inscription in the regtest explorer:"
msgstr "Tingnan ang inskripsiyon sa regtest explorer:"

#: src/guides/testing.md:50
msgid "Testing Recursion"
msgstr "Pag-test ng Recursion"

#: src/guides/testing.md:53
msgid ""
"When testing out [recursion](../inscriptions/recursion.md), inscribe the "
"dependencies first (example with [p5.js](https://p5js.org)):"
msgstr ""
"Kapag sinusubukan ang [recursion](../inscriptions/recursion.md), isulat ang "
"mga dependencies muna (halimbawa sa [p5.js](https://p5js.org)):"

#: src/guides/testing.md:58
msgid ""
"This should return a `inscription_id` which you can then reference in your "
"recursive inscription."
msgstr ""
"Dapat itong magbalik ng `inscription_id` na maaari mong ireference sa iyong "
"recursive inscription."

#: src/guides/testing.md:61
msgid ""
"ATTENTION: These ids will be different when inscribing on mainnet or signet, "
"so be sure to change those in your recursive inscription for each chain."
msgstr ""
"PANSIN: Magiging iba ang mga id na ito kapag nag-inscribe sa mainnet o "
"signet, kaya siguraduhing baguhin ang mga nasa iyong recursive inscription "
"para sa bawat chain."

#: src/guides/testing.md:65
msgid "Then you can inscribe your recursive inscription with:"
msgstr "Pagkatapos ay maaari mong isulat ang iyong recursive inscription ng:"

#: src/guides/testing.md:69
msgid "Finally you will have to mine some blocks and start the server:"
msgstr ""
"Sa wakas, kakailanganin mong mag-mine ng ilang mga bloke at simulan ang "
"server:"

#: src/guides/moderation.md:4
msgid ""
"`ord` includes a block explorer, which you can run locally with `ord server`."
msgstr ""
"Ang `ord` ay may kasamang block explorer, na maaari mong patakbuhin sa local "
"gamit ang `ord server`."

#: src/guides/moderation.md:6
msgid ""
"The block explorer allows viewing inscriptions. Inscriptions are user-"
"generated content, which may be objectionable or unlawful."
msgstr ""
"Nagbibigay ito ng kakayahan sayo na tingnan ang mga inskripsiyon sa block "
"explorer. Ang mga inscription ay maaaring hindi kanais-nais o ilegal."

#: src/guides/moderation.md:9
msgid ""
"It is the responsibility of each individual who runs an ordinal block "
"explorer instance to understand their responsibilities with respect to "
"unlawful content, and decide what moderation policy is appropriate for their "
"instance."
msgstr ""
"Responsibilidad ng bawat tao na namamahala sa isang instance ng ordinal "
"block explorer na maunawaan ang kanilang mga responsibilidad tungkol sa "
"lumalabag na content at magpasya sa naaangkop na patakaran sa pagmo-moderate "
"para sa kanilang instance."

#: src/guides/moderation.md:13
msgid ""
"In order to prevent particular inscriptions from being displayed on an `ord` "
"instance, they can be included in a YAML config file, which is loaded with "
"the `--config` option."
msgstr ""
"Upang maiwasang maipakita ang mga partikular na inscription sa isang `ord` "
"instance, maaaring isama ang mga ito sa isang configuration file ng YAML, na "
"may kasamang opsyong na `--config`."

#: src/guides/moderation.md:17
msgid ""
"To hide inscriptions, first create a config file, with the inscription ID "
"you want to hide:"
msgstr ""
"Upang itago ang mga inscription, gumawa muna ng configuration file, na may "
"ID ng inscription na gusto mong itago:"

#: src/guides/moderation.md:25
msgid ""
"The suggested name for `ord` config files is `ord.yaml`, but any filename "
"can be used."
msgstr ""
"Ang pangalan ng  file ng pagsasaayos ng `ord` ay `ord.yaml`, ngunit maaaring "
"gamitin ang anumang filename."

#: src/guides/moderation.md:28
msgid "Then pass the file to `--config` when starting the server:"
msgstr "Pagkatapos ay ipasa ang file sa `--config` kapag sinimulan ang server:"

#: src/guides/moderation.md:30
msgid "`ord --config ord.yaml server`"
msgstr "`ord --config ord.yaml server`"

#: src/guides/moderation.md:32
msgid ""
"Note that the `--config` option comes after `ord` but before the `server` "
"subcommand."
msgstr ""
"Tandaan na ang `--config` na opsyon ay pagkatapos ng `ord` ngunit bago ang "
"`server` subcommand."

#: src/guides/moderation.md:35
msgid "`ord` must be restarted in to load changes to the config file."
msgstr ""
"Ang `ord` ay dapat na i-restart upang ang mga pagbabagong ginawa sa "
"configuration file ay ma-apply."

#: src/guides/moderation.md:37
msgid "`ordinals.com`"
msgstr "`ordinals.com`"

#: src/guides/moderation.md:40
msgid ""
"The `ordinals.com` instances use `systemd` to run the `ord server` service, "
"which is called `ord`, with a config file located at `/var/lib/ord/ord.yaml`."
msgstr ""
"Ang instance ng ordinals.com ay gumagamit ng `systemd` upang patakbuhin ang "
"service ng `ord server`, na tinatawag na `ord`, na may configuration file na "
"matatagpuan sa `/var/lib/ord/ord.yaml`."

#: src/guides/moderation.md:43
msgid "To hide an inscription on `ordinals.com`:"
msgstr "Upang itago ang isang inscription sa `ordinals.com`:"

#: src/guides/moderation.md:45
msgid "SSH into the server"
msgstr "Kumonekta sa SSH server"

#: src/guides/moderation.md:46
msgid "Add the inscription ID to `/var/lib/ord/ord.yaml`"
msgstr "Idagdag ang inscription ID sa `/var/lib/ord/ord.yaml`"

#: src/guides/moderation.md:47
msgid "Restart the service with `systemctl restart ord`"
msgstr "I-restart ang serbisyo gamit ang `systemctl restart ord`"

#: src/guides/moderation.md:48
msgid "Monitor the restart with `journalctl -u ord`"
msgstr "Subaybayan ang pag-reboot gamit ang `journalctl -u ord`"

#: src/guides/moderation.md:50
msgid ""
"Currently, `ord` is slow to restart, so the site will not come back online "
"immediately."
msgstr ""
"Sa kasalukuyan, ang `ord` ay mabagal na mag-restart, kaya ang site ay hindi "
"babalik kaagad online."

#: src/guides/reindexing.md:4
msgid ""
"Sometimes the `ord` database must be reindexed, which means deleting the "
"database and restarting the indexing process with either `ord index update` "
"or `ord server`. Reasons to reindex are:"
msgstr ""
"Minsan ang database ng `ord` ay dapat na i-reindex, na nangangahulugan ng "
"pagtanggal ng database at i-restart ang proseso ng pag-index gamit ang "
"alinman sa `ord index update` o `ord server`. Ang mga dahilan para muling i-"
"index ay:"

#: src/guides/reindexing.md:8
msgid "A new major release of ord, which changes the database scheme"
msgstr ""
"Isang bagong pangunahing release ng ord, na nagbabago sa scheme ng database"

#: src/guides/reindexing.md:9
msgid "The database got corrupted somehow"
msgstr "Na-corrupt ang database"

#: src/guides/reindexing.md:11
msgid ""
"The database `ord` uses is called [redb](https://github.com/cberner/redb), "
"so we give the index the default file name `index.redb`. By default we store "
"this file in different locations depending on your operating system."
msgstr ""
"Ang database na ginagamit ng `ord` ay tinatawag na [redb](https://github.com/"
"cberner/redb), kaya binibigyan namin ang index ng default na pangalan ng "
"file na `index.redb`. Bilang default, ni-store namin ang file na ito sa "
"iba't ibang lokasyon depende sa iyong operating system."

#: src/guides/reindexing.md:15
msgid "Platform"
msgstr "Platform"

#: src/guides/reindexing.md:15
msgid "Value"
msgstr "Value"

#: src/guides/reindexing.md:17
msgid "Linux"
msgstr "Linux"

#: src/guides/reindexing.md:17
msgid "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"
msgstr "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"

#: src/guides/reindexing.md:17
msgid "/home/<USER>/.local/share/ord"
msgstr "/home/<USER>/.local/share/ord"

#: src/guides/reindexing.md:18
msgid "macOS"
msgstr "macOS"

#: src/guides/reindexing.md:18
msgid "`$HOME`/Library/Application Support/ord"
msgstr "`$HOME`/Library/Application Support/ord"

#: src/guides/reindexing.md:18
msgid "/Users/<USER>/Library/Application Support/ord"
msgstr "/Users/<USER>/Library/Application Support/ord"

#: src/guides/reindexing.md:19
msgid "Windows"
msgstr "Windows"

#: src/guides/reindexing.md:19
msgid "`{FOLDERID_RoamingAppData}`\\\\ord"
msgstr "`{FOLDERID_RoamingAppData}`\\\\ord"

#: src/guides/reindexing.md:19
msgid "C:\\Users\\<USER>\\AppData\\Roaming\\ord"
msgstr "C:\\Users\\<USER>\\AppData\\Roaming\\ord"

#: src/guides/reindexing.md:21
msgid ""
"So to delete the database and reindex on MacOS you would have to run the "
"following commands in the terminal:"
msgstr ""
"Kaya para tanggalin ang database at reindex sa MacOS kailangan mong i-run "
"ang sumusunod sa mga commands sa terminal:"

#: src/guides/reindexing.md:29
msgid ""
"You can of course also set the location of the data directory yourself with "
"`ord --datadir <DIR> index update` or give it a specific filename and path "
"with `ord --index <FILENAME> index update`."
msgstr ""
"Siyempre maaari mo ring itakda ang lokasyon ng direktoryo ng data sa iyong "
"sarili gamit ang `ord --datadir <DIR> index update` o bigyan ito ng "
"partikular na filename at path na may `ord --index <FILENAME> index update`."

#: src/bounties.md:1
msgid "Ordinal Bounty Hunting Hints"
msgstr "Ordinal Bounty Hunting Hints"

#: src/bounties.md:4
msgid ""
"The `ord` wallet can send and receive specific satoshis. Additionally, "
"ordinal theory is extremely simple. A clever hacker should be able to write "
"code from scratch to manipulate satoshis using ordinal theory in no time."
msgstr ""
"Ang `ord` wallet ay maaaring magpadala at tumanggap ng partikular na "
"satoshi. Gayundin, ang ordinal theory ay napakasimple. Ang isang matalinong "
"hacker ay dapat na makapagsulat ng code mula sa simula upang manipulahin ang "
"mga satoshi gamit ang ordinal theory."

#: src/bounties.md:8
msgid ""
"For more information about ordinal theory, check out the [FAQ](./faq.md) for "
"an overview, the [BIP](https://github.com/ordinals/ord/blob/master/bip."
"mediawiki) for the technical details, and the [ord repo](https://github.com/"
"ordinals/ord) for the `ord` wallet and block explorer."
msgstr ""
"Para sa higit pang impormasyon sa Ordinal Theory, tingnan ang FAQ para sa "
"isang pangkalahatang-ideya, ang [BIP](https://github.com/ordinals/ord/blob/"
"master/bip.mediawiki) para sa mga teknikal na detalye, at ang [ord "
"repository](https://github.com/ordinals/ord) para sa ord wallet at block "
"explorer."

#: src/bounties.md:14
msgid ""
"Satoshi was the original developer of ordinal theory. However, he knew that "
"others would consider it heretical and dangerous, so he hid his knowledge, "
"and it was lost to the sands of time. This potent theory is only now being "
"rediscovered. You can help by researching rare satoshis."
msgstr ""
"Satoshi ang unang bumuo ng Ordinal Theory. Gayunpaman, alam niyang ituturing "
"siya ng iba na erehe at mapanganib, kaya itinago niya ang kanyang kaalaman "
"at nawala ito sa paglipas panahon. Ang makapangyarihang teoryang ito ay "
"ngayon lamang muling natuklasan. Maaari kang mag-ambag dito sa pamamagitan "
"ng paghahanap ng mga bihirang satoshi."

#: src/bounties.md:19
msgid "Good luck and godspeed!"
msgstr "Good luck and godspeed!"

#: src/bounty/0.md:1
msgid "Ordinal Bounty 0"
msgstr "Ordinal Bounty 0"

#: src/bounty/0.md:4 src/bounty/1.md:4 src/bounty/2.md:4 src/bounty/3.md:4
msgid "Criteria"
msgstr "Criteria"

#: src/bounty/0.md:7
msgid ""
"Send a sat whose ordinal number ends with a zero to the submission address:"
msgstr ""
"Magpadala ng isang sat na ang ordinal na numero ay nagtatapos sa isang zero "
"sa address ng pagsusumite:"

#: src/bounty/0.md:9
msgid "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"
msgstr "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"

#: src/bounty/0.md:11
msgid "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"
msgstr "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"

#: src/bounty/0.md:13
msgid "The sat must be the first sat of the output you send."
msgstr "Ang sat ay dapat ang unang sat ng output na iyong ipinadala."

#: src/bounty/0.md:15 src/bounty/1.md:14 src/bounty/2.md:15 src/bounty/3.md:63
msgid "Reward"
msgstr "Reward"

#: src/bounty/0.md:18
msgid "100,000 sats"
msgstr "100,000 sats"

#: src/bounty/0.md:20 src/bounty/1.md:19 src/bounty/2.md:20 src/bounty/3.md:70
msgid "Submission Address"
msgstr "Submission Address"

#: src/bounty/0.md:23
msgid ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/"
"address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"
msgstr ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/"
"address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"

#: src/bounty/0.md:25 src/bounty/1.md:24 src/bounty/2.md:25 src/bounty/3.md:75
msgid "Status"
msgstr "Status"

#: src/bounty/0.md:28
msgid ""
"Claimed by [@count_null](https://twitter.com/rodarmor/"
"status/1560793241473400833)!"
msgstr ""
"Claimed ni [@count_null](https://twitter.com/rodarmor/"
"status/1560793241473400833)!"

#: src/bounty/1.md:1
msgid "Ordinal Bounty 1"
msgstr "Ordinal Bounty 1"

#: src/bounty/1.md:7
msgid ""
"The transaction that submits a UTXO containing the oldest sat, i.e., that "
"with the lowest number, amongst all submitted UTXOs will be judged the "
"winner."
msgstr ""
"Ang transaksyon na nagsusumite ng UTXO na naglalaman ng pinakamatandang sat, "
"ibig sabihin, na may pinakamababang bilang, sa lahat ng isinumiteng UTXO ay "
"huhusgahan ang winner."

#: src/bounty/1.md:10
msgid ""
"The bounty is open for submissions until block 753984—the first block of "
"difficulty adjustment period 374. Submissions included in block 753984 or "
"later will not be considered."
msgstr ""
"Bukas ang bounty para sa mga pagsusumite hanggang block 753984-ang unang "
"block ng difficulty adjustment period 374. Kasama ang mga pagsusumite sa "
"block 753984 o pataas ay hindi isasaalang-alang."

#: src/bounty/1.md:17
msgid "200,000 sats"
msgstr "200,000 sats"

#: src/bounty/1.md:22
msgid ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/"
"address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"
msgstr ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/"
"address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"

#: src/bounty/1.md:27
msgid ""
"Claimed by [@ordinalsindex](https://twitter.com/rodarmor/"
"status/1569883266508853251)!"
msgstr ""
"Claimed ni [@ordinalsindex](https://twitter.com/rodarmor/"
"status/1569883266508853251)!"

#: src/bounty/2.md:1
msgid "Ordinal Bounty 2"
msgstr "Ordinal Bounty 2"

#: src/bounty/2.md:7
msgid "Send an "
msgstr "Send an "

#: src/bounty/2.md:7
msgid "uncommon"
msgstr "uncommon"

#: src/bounty/2.md:7
msgid " sat to the submission address:"
msgstr " sat sa address ng pagsusumite:"

#: src/bounty/2.md:9
msgid "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"
msgstr "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"

#: src/bounty/2.md:11
msgid "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"
msgstr "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"

#: src/bounty/2.md:13
msgid ""
"Confirm that the submission address has not received transactions before "
"submitting your entry. Only the first successful submission will be rewarded."
msgstr ""
"I-confirm na ang submission address ay hindi nakatanggap ng mga transaksyon "
"bago pagsusumite ng iyong entry. Tanging ang unang matagumpay na pagsusumite "
"ay gagantimpalaan."

#: src/bounty/2.md:18
msgid "300,000 sats"
msgstr "300,000 sats"

#: src/bounty/2.md:23
msgid ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/"
"address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"
msgstr ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/"
"address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"

#: src/bounty/2.md:28
msgid ""
"Claimed by [@utxoset](https://twitter.com/rodarmor/"
"status/1582424455615172608)!"
msgstr ""
"Claimed ni [@utxoset](https://twitter.com/rodarmor/"
"status/1582424455615172608)!"

#: src/bounty/3.md:1
msgid "Ordinal Bounty 3"
msgstr "Ordinal Bounty 3"

#: src/bounty/3.md:7
msgid ""
"Ordinal bounty 3 has two parts, both of which are based on _ordinal names_. "
"Ordinal names are a modified base-26 encoding of ordinal numbers. To avoid "
"locking short names inside the unspendable genesis block coinbase reward, "
"ordinal names get _shorter_ as the ordinal number gets _longer_. The name of "
"sat 0, the first sat to be mined is `nvtdijuwxlp` and the name of sat "
"2,099,999,997,689,999, the last sat to be mined, is `a`."
msgstr ""
"Ang Ordinal bounty 3 ay may dalawang bahagi, na parehong nakabatay sa "
"_ordinal names_.Ang mga ordinal names ay isang modified base-26 encoding ng "
"ordinal numbers. Upang maiwasan ang pag-lock ng shot names sa loob ng "
"unspendable genesis block coinbase reward, ordinal names ay maagiging "
"_shorter_ habang ang ordinal number ay nagiging _longer_. Ang pangalan ng "
"sat 0, ang unang sat na na-mina ay `nvtdijuwxlp` at ang pangalan ng sat ay "
"2,099,999,997,689,999, ang huling sat na miminahin, ay `a`."

#: src/bounty/3.md:14
msgid ""
"The bounty is open for submissions until block 840000—the first block after "
"the fourth halvening. Submissions included in block 840000 or later will not "
"be considered."
msgstr ""
"Bukas ang bounty para sa mga pagsusumite hanggang block 840000-ang unang "
"block pagkatapos ng fourth halvening. Ang mga pagsusumite na kasama sa block "
"840000 o mas bago ay hindi ikonsidera."

#: src/bounty/3.md:18
msgid ""
"Both parts use [frequency.tsv](frequency.tsv), a list of words and the "
"number of times they occur in the [Google Books Ngram dataset](http://"
"storage.googleapis.com/books/ngrams/books/datasetsv2.html). filtered to only "
"include the names of sats which will have been mined by the end of the "
"submission period, that appear at least 5000 times in the corpus."
msgstr ""
"Ang parehong bahagi ay gumagamit ng [frequency.tsv](frequency.tsv), isang "
"listahan ng mga salita at ang bilang ng beses na nangyari ang mga ito sa "
"[Google Books Ngram dataset](http://storage.googleapis.com/books/ngrams/"
"books/datasetsv2.html). na-filter lamang sa isama ang mga pangalan ng sats "
"na mamimina sa pagtatapos ng panahon ng pagsusumite, na lilitaw nang hindi "
"bababa sa 5000 beses sa corpus."

#: src/bounty/3.md:24
msgid ""
"`frequency.tsv` is a file of tab-separated values. The first column is the "
"word, and the second is the number of times it appears in the corpus. The "
"entries are sorted from least-frequently occurring to most-frequently "
"occurring."
msgstr ""
"Ang `frequency.tsv` ay isang file ng mga tab-separated values. Ang unang "
"column ay ang word, at ang pangalawa ay ang bilang ng beses na ito ay "
"lumabas sa corpus. Ang entries ay pinagsunod-sunod mula sa hindi "
"pinakamadalas na nangyayari hanggang sa pinaka-madalas nangyayari."

#: src/bounty/3.md:29
msgid ""
"`frequency.tsv` was compiled using [this program](https://github.com/casey/"
"onegrams)."
msgstr ""
"Ang `frequency.tsv` ay compiled gamit ang [program na ito](https://github."
"com/casey/onegrams)."

#: src/bounty/3.md:32
msgid ""
"To search an `ord` wallet for sats with a name in `frequency.tsv`, use the "
"following [`ord`](https://github.com/ordinals/ord) command:"
msgstr ""
"Upang maghanap ng `ord` wallet para sa sats na may pangalan sa `frequency."
"tsv`, gamitin ang sumusunod sa [`ord`](https://github.com/ordinals/ord) na "
"command:"

#: src/bounty/3.md:39
msgid ""
"This command requires the sat index, so `--index-sats` must be passed to ord "
"when first creating the index."
msgstr ""
"Ang command na ito ay nangangailangan ng sat index, kaya ang `--index-sats` "
"ay dapat ipasa sa ord noong unang gumawa ng index."

#: src/bounty/3.md:42
msgid "Part 0"
msgstr "Part 0"

#: src/bounty/3.md:44
msgid "_Rare sats pair best with rare words._"
msgstr "_Rare sats pair best with rare words._"

#: src/bounty/3.md:46
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the lowest number of occurrences in `frequency.tsv` shall be the winner "
"of part 0."
msgstr ""
"Ang transaksyon na nagsusumite ng UTXO na naglalaman ng sat na ang pangalan "
"ay lumalabas na may pinakamababang bilang ng occurrences sa `frequency.tsv` "
"ang siyang mananalo ng part 0."

#: src/bounty/3.md:50
msgid "Part 1"
msgstr "Part 1"

#: src/bounty/3.md:52
msgid "_Popularity is the font of value._"
msgstr "_Popularity is the font of value._"

#: src/bounty/3.md:54
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the highest number of occurrences in `frequency.tsv` shall be the "
"winner of part 1."
msgstr ""
"Ang transaksyon na nagsusumite ng UTXO na naglalaman ng sat na ang pangalan "
"ay lumalabas na may pinakamataas na bilang ng occurrences sa `frequency.tsv` "
"ay ang nagwagi sa part 1."

#: src/bounty/3.md:58
msgid "Tie Breaking"
msgstr "Tie Breaking"

#: src/bounty/3.md:60
msgid ""
"In the case of a tie, where two submissions occur with the same frequency, "
"the earlier submission shall be the winner."
msgstr ""
"Sa kaso ng isang tie, kung saan ang dalawang pagsusumite ay nagaganap na may "
"parehong frequency, ang naunang pagsusumite ay siyang mananalo."

#: src/bounty/3.md:66
msgid "Part 0: 200,000 sats"
msgstr "Part 0: 200,000 sats"

#: src/bounty/3.md:67
msgid "Part 1: 200,000 sats"
msgstr "Part 1: 200,000 sats"

#: src/bounty/3.md:68
msgid "Total: 400,000 sats"
msgstr "Total: 400,000 sats"

#: src/bounty/3.md:73
msgid ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/"
"address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"
msgstr ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/"
"address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"

#: src/bounty/3.md:78
msgid "Unclaimed!"
msgstr "Unclaimed!"
