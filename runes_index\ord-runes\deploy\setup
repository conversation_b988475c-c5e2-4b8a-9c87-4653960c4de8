#!/usr/bin/env bash

# This script is idempotent.

set -euxo pipefail

CHAIN=$1
DOMAIN=$2
BRANCH=$3
COMMIT=$4
REVISION="ord-$BRANCH-$COMMIT"

export DEBIAN_FRONTEND=noninteractive

touch ~/.hushlogin

hostnamectl set-hostname $DOMAIN

apt-get install --yes \
  acl \
  clang \
  curl \
  libsqlite3-dev\
  libssl-dev \
  locales-all \
  pkg-config \
  ufw \
  vim

apt-get remove --yes --auto-remove

ufw default allow outgoing
ufw default deny incoming

ufw allow 8080
ufw allow http
ufw allow https
ufw allow ssh

case $CHAIN in
  main)
    COOKIE_FILE_DIR=/var/lib/bitcoind
    CSP_ORIGIN=ordinals.com
    ufw allow 8333
    ;;
  regtest)
    COOKIE_FILE_DIR=/var/lib/bitcoind/regtest
    CSP_ORIGIN=regtest.ordinals.com
    ufw allow 18444
    ;;
  signet)
    COOKIE_FILE_DIR=/var/lib/bitcoind/signet
    CSP_ORIGIN=signet.ordinals.com
    ufw allow 38333
    ;;
  test)
    COOKIE_FILE_DIR=/var/lib/bitcoind/testnet3
    CSP_ORIGIN=testnet.ordinals.com
    ufw allow 18333
    ;;
  *)
    echo "Unknown chain: $CHAIN"
    exit 1
    ;;
esac

mkdir -p \
  /etc/systemd/system/bitcoind.service.d \
  /etc/systemd/system/ord.service.d

printf "[Service]\nEnvironment=CHAIN=%s\nEnvironment=CSP_ORIGIN=%s\n" $CHAIN $CSP_ORIGIN \
  | tee /etc/systemd/system/bitcoind.service.d/override.conf \
  > /etc/systemd/system/ord.service.d/override.conf

sed -i -E 's/#?PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sshd -t
systemctl restart sshd

ufw --force enable

if ! which bitcoind; then
  ./bin/install-bitcoin-core-linux
fi

bitcoind --version

if [[ ! -e ~/.cargo/env ]]; then
  curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
fi

source ~/.cargo/env

rustup update stable

cargo build --release
install --backup target/release/ord /usr/local/bin/ord

id --user bitcoin || useradd --system bitcoin
id --user ord || useradd --system ord

cp deploy/bitcoind.service /etc/systemd/system/

mkdir -p /etc/bitcoin
cp deploy/bitcoin.conf /etc/bitcoin/bitcoin.conf

if [[ ! -e ~/.bitcoin/bitcoin.conf ]]; then
  mkdir -p ~/.bitcoin
  ln -s /etc/bitcoin/bitcoin.conf ~/.bitcoin/bitcoin.conf
fi

systemctl daemon-reload
systemctl enable bitcoind
systemctl restart bitcoind

while [[ ! -f $COOKIE_FILE_DIR/.cookie ]]; do
  echo "Waiting for bitcoind…"
  sleep 1
done

setfacl -m ord:x /var/lib/bitcoind
setfacl -m ord:x $COOKIE_FILE_DIR
setfacl -dm ord:r $COOKIE_FILE_DIR
setfacl -m ord:r $COOKIE_FILE_DIR/.cookie

journalctl --unit ord --vacuum-time 1s

cp deploy/ord.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable ord
systemctl restart ord

while ! curl --fail https://$DOMAIN/status > /dev/null; do
  echo "Waiting for ord at https://$DOMAIN/status…"
  sleep 1
done
