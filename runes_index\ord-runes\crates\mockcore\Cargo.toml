[package]
name = "mockcore"
description = "Mock Bitcoin Core RPC server"
version = "0.0.1"
edition = "2021"
license = "CC0-1.0"
homepage = "https://github.com/ordinals/ord"
repository = "https://github.com/ordinals/ord"

[dependencies]
bitcoin = { version = "0.30.0", features = ["serde", "rand"] }
base64 = "0.21.0"
hex = "0.4.3"
jsonrpc-core = "18.0.0"
jsonrpc-derive = "18.0.0"
jsonrpc-http-server = "18.0.0"
ord-bitcoincore-rpc = "0.17.2"
reqwest = { version = "0.11.10", features = ["blocking"] }
serde = { version = "1.0.137", features = ["derive"] }
serde_json = { version = "1.0.81" }
tempfile = "3.2.0"
