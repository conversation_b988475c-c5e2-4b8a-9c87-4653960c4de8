require('dotenv').config();
const { Pool } = require('pg');

console.log('环境变量检查:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_DATABASE:', process.env.DB_DATABASE);
console.log('DB_USER:', process.env.DB_USER);
console.log('USE_EXTRA_TABLES:', process.env.USE_EXTRA_TABLES);
console.log();

// 数据库连接配置
const db_pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_DATABASE || 'postgres',
  password: process.env.DB_PASSWD,
  port: parseInt(process.env.DB_PORT || "5432"),
  max: process.env.DB_MAX_CONNECTIONS || 10,
  ssl: process.env.DB_SSL == 'true' ? true : false
});

async function query_db(query, params = []) {
  return await db_pool.query(query, params);
}

async function debugHoldersAPI() {
  console.log('=== BRC20 Holders API 调试脚本 ===\n');
  
  try {
    // 1. 检查数据库连接
    console.log('1. 检查数据库连接...');
    const connectionTest = await query_db('SELECT NOW() as current_time');
    console.log('✓ 数据库连接成功:', connectionTest.rows[0].current_time);
    console.log();

    // 2. 检查当前区块高度
    console.log('2. 检查当前区块高度...');
    const blockHeightRes = await query_db('SELECT max(block_height) as max_block_height FROM brc20_block_hashes');
    const currentBlockHeight = blockHeightRes.rows[0].max_block_height;
    console.log('✓ 当前区块高度:', currentBlockHeight);
    console.log();

    // 3. 检查是否启用了额外表
    const useExtraTables = process.env.USE_EXTRA_TABLES == 'true';
    console.log('3. 检查配置...');
    console.log('✓ USE_EXTRA_TABLES:', useExtraTables);
    console.log();

    // 4. 检查数据库表结构
    console.log('4. 检查数据库表结构...');
    
    // 检查 brc20_current_balances 表是否存在
    const tableExistsRes = await query_db(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'brc20_current_balances'
      );
    `);
    const tableExists = tableExistsRes.rows[0].exists;
    console.log('✓ brc20_current_balances 表存在:', tableExists);

    if (tableExists) {
      // 检查表结构
      const columnsRes = await query_db(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'brc20_current_balances'
        ORDER BY ordinal_position;
      `);
      console.log('✓ 表结构:');
      columnsRes.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type}`);
      });
    }
    console.log();

    // 5. 检查表中的数据总量
    console.log('5. 检查数据总量...');
    if (tableExists) {
      const totalRowsRes = await query_db('SELECT COUNT(*) as total_rows FROM brc20_current_balances');
      console.log('✓ brc20_current_balances 总记录数:', totalRowsRes.rows[0].total_rows);

      // 检查有多少不同的 ticker
      const tickersRes = await query_db('SELECT COUNT(DISTINCT tick) as ticker_count FROM brc20_current_balances');
      console.log('✓ 不同 ticker 数量:', tickersRes.rows[0].ticker_count);

      // 列出前10个 ticker
      const topTickersRes = await query_db(`
        SELECT tick, COUNT(*) as holder_count, SUM(CAST(overall_balance AS BIGINT)) as total_supply
        FROM brc20_current_balances 
        WHERE CAST(overall_balance AS BIGINT) > 0
        GROUP BY tick 
        ORDER BY holder_count DESC 
        LIMIT 10
      `);
      console.log('✓ 前10个 ticker (按持有者数量排序):');
      topTickersRes.rows.forEach(row => {
        console.log(`  - ${row.tick}: ${row.holder_count} 持有者, 总供应量: ${row.total_supply}`);
      });
    }
    console.log();

    // 6. 专门检查 "ordi" ticker
    console.log('6. 检查 "ordi" ticker...');
    if (tableExists) {
      // 检查是否存在 ordi 记录
      const ordiExistsRes = await query_db(`
        SELECT COUNT(*) as ordi_count 
        FROM brc20_current_balances 
        WHERE tick = 'ordi'
      `);
      console.log('✓ ordi 记录总数:', ordiExistsRes.rows[0].ordi_count);

      // 检查有余额的 ordi 记录
      const ordiWithBalanceRes = await query_db(`
        SELECT COUNT(*) as ordi_with_balance_count 
        FROM brc20_current_balances 
        WHERE tick = 'ordi' AND CAST(overall_balance AS BIGINT) > 0
      `);
      console.log('✓ 有余额的 ordi 记录数:', ordiWithBalanceRes.rows[0].ordi_with_balance_count);

      // 如果有记录，显示前5个持有者
      if (parseInt(ordiWithBalanceRes.rows[0].ordi_with_balance_count) > 0) {
        const ordiHoldersRes = await query_db(`
          SELECT pkscript, wallet, overall_balance, available_balance
          FROM brc20_current_balances
          WHERE tick = 'ordi' AND CAST(overall_balance AS BIGINT) > 0
          ORDER BY CAST(overall_balance AS BIGINT) DESC
          LIMIT 5
        `);
        console.log('✓ ordi 前5个持有者:');
        ordiHoldersRes.rows.forEach((row, index) => {
          console.log(`  ${index + 1}. 钱包: ${row.wallet || 'N/A'}`);
          console.log(`     PKScript: ${row.pkscript}`);
          console.log(`     总余额: ${row.overall_balance}`);
          console.log(`     可用余额: ${row.available_balance}`);
          console.log();
        });
      } else {
        console.log('✗ 没有找到有余额的 ordi 持有者');
        
        // 检查是否有零余额的记录
        const ordiZeroBalanceRes = await query_db(`
          SELECT COUNT(*) as zero_balance_count 
          FROM brc20_current_balances 
          WHERE tick = 'ordi' AND CAST(overall_balance AS BIGINT) = 0
        `);
        console.log('✓ 零余额的 ordi 记录数:', ordiZeroBalanceRes.rows[0].zero_balance_count);
      }

      // 检查是否有大小写问题
      const ordiCaseRes = await query_db(`
        SELECT DISTINCT tick 
        FROM brc20_current_balances 
        WHERE LOWER(tick) = 'ordi'
      `);
      console.log('✓ 包含 "ordi" 的 ticker (大小写变体):');
      ordiCaseRes.rows.forEach(row => {
        console.log(`  - "${row.tick}"`);
      });
    }
    console.log();

    // 7. 检查历史余额表作为对比
    console.log('7. 检查历史余额表...');
    const historicTableExistsRes = await query_db(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'brc20_historic_balances'
      );
    `);
    const historicTableExists = historicTableExistsRes.rows[0].exists;
    console.log('✓ brc20_historic_balances 表存在:', historicTableExists);

    if (historicTableExists) {
      const historicOrdiRes = await query_db(`
        SELECT COUNT(*) as historic_ordi_count 
        FROM brc20_historic_balances 
        WHERE tick = 'ordi'
      `);
      console.log('✓ 历史表中 ordi 记录数:', historicOrdiRes.rows[0].historic_ordi_count);
    }
    console.log();

    // 8. 模拟 API 查询
    console.log('8. 模拟 API 查询...');
    if (useExtraTables && tableExists) {
      const apiQuery = `
        SELECT pkscript, wallet, overall_balance, available_balance
        FROM brc20_current_balances
        WHERE tick = $1
        ORDER BY overall_balance ASC;
      `;
      const apiRes = await query_db(apiQuery, ['ordi']);
      console.log('✓ API 查询结果数量:', apiRes.rows.length);
      
      if (apiRes.rows.length > 0) {
        // 过滤掉零余额的记录
        const filteredRows = apiRes.rows.filter(row => parseInt(row.overall_balance) != 0);
        console.log('✓ 过滤后的结果数量 (排除零余额):', filteredRows.length);
        
        if (filteredRows.length > 0) {
          console.log('✓ API 应该返回成功结果');
        } else {
          console.log('✗ API 会返回 "no holders found" 因为所有记录都是零余额');
        }
      } else {
        console.log('✗ API 会返回 "no holders found" 因为没有找到记录');
      }
    } else {
      console.log('✗ 无法模拟 API 查询 - 额外表未启用或不存在');
    }

  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  } finally {
    await db_pool.end();
    console.log('\n=== 调试完成 ===');
  }
}

// 运行调试脚本
debugHoldersAPI();
