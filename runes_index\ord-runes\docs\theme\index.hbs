<!DOCTYPE HTML>
<html lang="{{ language }}" class="{{ default_theme }}" dir="{{ text_direction }}">
    <head>
        <!-- Book generated using mdBook -->
        <meta charset="UTF-8">
        <title>{{ title }}</title>
        {{#if is_print }}
        <meta name="robots" content="noindex">
        {{/if}}
        {{#if base_url}}
        <base href="{{ base_url }}">
        {{/if}}


        <!-- Custom HTML head -->
        {{> head}}

        <meta name="description" content="{{ description }}">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="theme-color" content="#ffffff">

        {{#if favicon_svg}}
        <link rel="icon" href="{{ path_to_root }}favicon.svg">
        {{/if}}
        {{#if favicon_png}}
        <link rel="shortcut icon" href="{{ path_to_root }}favicon.png">
        {{/if}}
        <link rel="stylesheet" href="{{ path_to_root }}css/variables.css">
        <link rel="stylesheet" href="{{ path_to_root }}css/general.css">
        <link rel="stylesheet" href="{{ path_to_root }}css/chrome.css">
        {{#if print_enable}}
        <link rel="stylesheet" href="{{ path_to_root }}css/print.css" media="print">
        {{/if}}

        <!-- Fonts -->
        <link rel="stylesheet" href="{{ path_to_root }}FontAwesome/css/font-awesome.css">
        {{#if copy_fonts}}
        <link rel="stylesheet" href="{{ path_to_root }}fonts/fonts.css">
        {{/if}}

        <!-- Highlight.js Stylesheets -->
        <link rel="stylesheet" href="{{ path_to_root }}highlight.css">
        <link rel="stylesheet" href="{{ path_to_root }}tomorrow-night.css">
        <link rel="stylesheet" href="{{ path_to_root }}ayu-highlight.css">

        <!-- Custom theme stylesheets -->
        {{#each additional_css}}
        <link rel="stylesheet" href="{{ ../path_to_root }}{{ this }}">
        {{/each}}

        {{#if mathjax_support}}
        <!-- MathJax -->
        <script async src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script>
        {{/if}}
    </head>
    <body class="sidebar-visible no-js">
    <div id="body-container">
        <!-- Provide site root to javascript -->
        <script>
            var path_to_root = "{{ path_to_root }}";
            var default_theme = window.matchMedia("(prefers-color-scheme: dark)").matches ? "{{ preferred_dark_theme }}" : "{{ default_theme }}";
        </script>

        <!-- Work around some values being stored in localStorage wrapped in quotes -->
        <script>
            try {
                var theme = localStorage.getItem('mdbook-theme');
                var sidebar = localStorage.getItem('mdbook-sidebar');

                if (theme.startsWith('"') && theme.endsWith('"')) {
                    localStorage.setItem('mdbook-theme', theme.slice(1, theme.length - 1));
                }

                if (sidebar.startsWith('"') && sidebar.endsWith('"')) {
                    localStorage.setItem('mdbook-sidebar', sidebar.slice(1, sidebar.length - 1));
                }
            } catch (e) { }
        </script>

        <!-- Set the theme before any content is loaded, prevents flash -->
        <script>
            var theme;
            try { theme = localStorage.getItem('mdbook-theme'); } catch(e) { }
            if (theme === null || theme === undefined) { theme = default_theme; }
            var html = document.querySelector('html');
            html.classList.remove('{{ default_theme }}')
            html.classList.add(theme);
            var body = document.querySelector('body');
            body.classList.remove('no-js')
            body.classList.add('js');
        </script>

        <input type="checkbox" id="sidebar-toggle-anchor" class="hidden">

        <!-- Hide / unhide sidebar before it is displayed -->
        <script>
            var body = document.querySelector('body');
            var sidebar = null;
            var sidebar_toggle = document.getElementById("sidebar-toggle-anchor");
            if (document.body.clientWidth >= 1080) {
                try { sidebar = localStorage.getItem('mdbook-sidebar'); } catch(e) { }
                sidebar = sidebar || 'visible';
            } else {
                sidebar = 'hidden';
            }
            sidebar_toggle.checked = sidebar === 'visible';
            body.classList.remove('sidebar-visible');
            body.classList.add("sidebar-" + sidebar);
        </script>

        <nav id="sidebar" class="sidebar" aria-label="Table of contents">
            <div class="sidebar-scrollbox">
                {{#toc}}{{/toc}}
            </div>
            <div id="sidebar-resize-handle" class="sidebar-resize-handle"></div>
        </nav>

        <!-- Track and set sidebar scroll position -->
        <script>
            var sidebarScrollbox = document.querySelector('#sidebar .sidebar-scrollbox');
            sidebarScrollbox.addEventListener('click', function(e) {
                if (e.target.tagName === 'A') {
                    sessionStorage.setItem('sidebar-scroll', sidebarScrollbox.scrollTop);
                }
            }, { passive: true });
            var sidebarScrollTop = sessionStorage.getItem('sidebar-scroll');
            sessionStorage.removeItem('sidebar-scroll');
            if (sidebarScrollTop) {
                // preserve sidebar scroll position when navigating via links within sidebar
                sidebarScrollbox.scrollTop = sidebarScrollTop;
            } else {
                // scroll sidebar to current active section when navigating via "next/previous chapter" buttons
                var activeSection = document.querySelector('#sidebar .active');
                if (activeSection) {
                    activeSection.scrollIntoView({ block: 'center' });
                }
            }
        </script>

        <div id="page-wrapper" class="page-wrapper">

            <div class="page">
                {{> header}}
                <div id="menu-bar-hover-placeholder"></div>
                <div id="menu-bar" class="menu-bar sticky">
                    <div class="left-buttons">
                        <label id="sidebar-toggle" class="icon-button" for="sidebar-toggle-anchor" title="Toggle Table of Contents" aria-label="Toggle Table of Contents" aria-controls="sidebar">
                            <i class="fa fa-bars"></i>
                        </label>
                        <button id="theme-toggle" class="icon-button" type="button" title="Change theme" aria-label="Change theme" aria-haspopup="true" aria-expanded="false" aria-controls="theme-list">
                            <i class="fa fa-paint-brush"></i>
                        </button>
                        <ul id="theme-list" class="theme-popup" aria-label="Themes" role="menu">
                            <li role="none"><button role="menuitem" class="theme" id="light">Light</button></li>
                            <li role="none"><button role="menuitem" class="theme" id="rust">Rust</button></li>
                            <li role="none"><button role="menuitem" class="theme" id="coal">Coal</button></li>
                            <li role="none"><button role="menuitem" class="theme" id="navy">Navy</button></li>
                            <li role="none"><button role="menuitem" class="theme" id="ayu">Ayu</button></li>
                        </ul>
                        {{#if search_enabled}}
                        <button id="search-toggle" class="icon-button" type="button" title="Search. (Shortkey: s)" aria-label="Toggle Searchbar" aria-expanded="false" aria-keyshortcuts="S" aria-controls="searchbar">
                            <i class="fa fa-search"></i>
                        </button>
                        {{/if}}
                    </div>

                    <h1 class="menu-title">{{ book_title }}</h1>

                    <div class="right-buttons">
                        <button id="language-toggle" class="icon-button" type="button"
                                title="Change language" aria-label="Change language"
                                aria-haspopup="true" aria-expanded="false"
                                aria-controls="language-list">
                            <i class="fa fa-globe"></i>
                        </button>
                        <ul id="language-list" class="theme-popup" aria-label="Languages" role="menu">
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="en">English</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="de">Deutsch</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="fr">Français</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="es">Español</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="pt">Português</a>
                          </button></li>
                           <li role="none"><button role="menuitem" class="theme">
                              <a id="ru">Русский</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="zh">中文版</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="ja">日本語版</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="ko">한국어</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="fil">Filipino</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="ar">العربية</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="hi">हिंदी</a>
                          </button></li>
                          <li role="none"><button role="menuitem" class="theme">
                              <a id="it">Italiano</a>
                          </button></li>
                        </ul>

                        <script>
                          let langToggle = document.getElementById("language-toggle");
                          let langList = document.getElementById("language-list");
                          langToggle.addEventListener("click", (event) => {
                              langList.style.display = langList.style.display == "block" ? "none" : "block";
                          });
                          let selectedLang = document.getElementById("{{ language }}");
                          if (selectedLang) {
                              selectedLang.parentNode.classList.add("theme-selected");
                          }

                          // The path to the root, taking the current
                          // language into account.
                          {{#if (eq language "en")}}
                          let full_path_to_root = "{{ path_to_root }}";
                          {{else}}
                          let full_path_to_root = "{{ path_to_root }}../";
                          {{/if}}
                          // The page path (mdbook only gives us
                          // access to the path to the Markdown file).
                          let path = "{{ path }}".replace(/\.md$/, ".html");
                          for (let lang of langList.querySelectorAll("a")) {
                              if (lang.id == "en") {
                                  lang.href = `${full_path_to_root}${path}`;
                              } else {
                                  lang.href = `${full_path_to_root}${lang.id}/${path}`;
                              }
                          }
                        </script>

                        {{#if print_enable}}
                        <a href="{{ path_to_root }}print.html" title="Print this book" aria-label="Print this book">
                            <i id="print-button" class="fa fa-print"></i>
                        </a>
                        {{/if}}
                        {{#if git_repository_url}}
                        <a href="{{git_repository_url}}" title="Git repository" aria-label="Git repository">
                            <i id="git-repository-button" class="fa {{git_repository_icon}}"></i>
                        </a>
                        {{/if}}
                        {{#if git_repository_edit_url}}
                        <a href="{{git_repository_edit_url}}" title="Suggest an edit" aria-label="Suggest an edit">
                            <i id="git-edit-button" class="fa fa-edit"></i>
                        </a>
                        {{/if}}

                    </div>
                </div>

                {{#if search_enabled}}
                <div id="search-wrapper" class="hidden">
                    <form id="searchbar-outer" class="searchbar-outer">
                        <input type="search" id="searchbar" name="searchbar" placeholder="Search this book ..." aria-controls="searchresults-outer" aria-describedby="searchresults-header">
                    </form>
                    <div id="searchresults-outer" class="searchresults-outer hidden">
                        <div id="searchresults-header" class="searchresults-header"></div>
                        <ul id="searchresults">
                        </ul>
                    </div>
                </div>
                {{/if}}

                <!-- Apply ARIA attributes after the sidebar and the sidebar toggle button are added to the DOM -->
                <script>
                    document.getElementById('sidebar-toggle').setAttribute('aria-expanded', sidebar === 'visible');
                    document.getElementById('sidebar').setAttribute('aria-hidden', sidebar !== 'visible');
                    Array.from(document.querySelectorAll('#sidebar a')).forEach(function(link) {
                        link.setAttribute('tabIndex', sidebar === 'visible' ? 0 : -1);
                    });
                </script>

                <div id="content" class="content">
                    <main>
                        {{{ content }}}
                    </main>

                    <nav class="nav-wrapper" aria-label="Page navigation">
                        <!-- Mobile navigation buttons -->
                        {{#previous}}
                            <a rel="prev" href="{{ path_to_root }}{{link}}" class="mobile-nav-chapters previous" title="Previous chapter" aria-label="Previous chapter" aria-keyshortcuts="Left">
                                <i class="fa fa-angle-left"></i>
                            </a>
                        {{/previous}}

                        {{#next}}
                            <a rel="next prefetch" href="{{ path_to_root }}{{link}}" class="mobile-nav-chapters next" title="Next chapter" aria-label="Next chapter" aria-keyshortcuts="Right">
                                <i class="fa fa-angle-right"></i>
                            </a>
                        {{/next}}

                        <div style="clear: both"></div>
                    </nav>
                </div>
            </div>

            <nav class="nav-wide-wrapper" aria-label="Page navigation">
                {{#previous}}
                    <a rel="prev" href="{{ path_to_root }}{{link}}" class="nav-chapters previous" title="Previous chapter" aria-label="Previous chapter" aria-keyshortcuts="Left">
                        <i class="fa fa-angle-left"></i>
                    </a>
                {{/previous}}

                {{#next}}
                    <a rel="next prefetch" href="{{ path_to_root }}{{link}}" class="nav-chapters next" title="Next chapter" aria-label="Next chapter" aria-keyshortcuts="Right">
                        <i class="fa fa-angle-right"></i>
                    </a>
                {{/next}}
            </nav>

        </div>

        {{#if live_reload_endpoint}}
        <!-- Livereload script (if served using the cli tool) -->
        <script>
            const wsProtocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsAddress = wsProtocol + "//" + location.host + "/" + "{{{live_reload_endpoint}}}";
            const socket = new WebSocket(wsAddress);
            socket.onmessage = function (event) {
                if (event.data === "reload") {
                    socket.close();
                    location.reload();
                }
            };

            window.onbeforeunload = function() {
                socket.close();
            }
        </script>
        {{/if}}

        {{#if google_analytics}}
        <!-- Google Analytics Tag -->
        <script>
            var localAddrs = ["localhost", "127.0.0.1", ""];

            // make sure we don't activate google analytics if the developer is
            // inspecting the book locally...
            if (localAddrs.indexOf(document.location.hostname) === -1) {
                (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
                (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
                m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
                })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

                ga('create', '{{google_analytics}}', 'auto');
                ga('send', 'pageview');
            }
        </script>
        {{/if}}

        {{#if playground_line_numbers}}
        <script>
            window.playground_line_numbers = true;
        </script>
        {{/if}}

        {{#if playground_copyable}}
        <script>
            window.playground_copyable = true;
        </script>
        {{/if}}

        {{#if playground_js}}
        <script src="{{ path_to_root }}ace.js"></script>
        <script src="{{ path_to_root }}editor.js"></script>
        <script src="{{ path_to_root }}mode-rust.js"></script>
        <script src="{{ path_to_root }}theme-dawn.js"></script>
        <script src="{{ path_to_root }}theme-tomorrow_night.js"></script>
        {{/if}}

        {{#if search_js}}
        <script src="{{ path_to_root }}elasticlunr.min.js"></script>
        <script src="{{ path_to_root }}mark.min.js"></script>
        <script src="{{ path_to_root }}searcher.js"></script>
        {{/if}}

        <script src="{{ path_to_root }}clipboard.min.js"></script>
        <script src="{{ path_to_root }}highlight.js"></script>
        <script src="{{ path_to_root }}book.js"></script>

        <!-- Custom JS scripts -->
        {{#each additional_js}}
        <script src="{{ ../path_to_root }}{{this}}"></script>
        {{/each}}

        {{#if is_print}}
        {{#if mathjax_support}}
        <script>
        window.addEventListener('load', function() {
            MathJax.Hub.Register.StartupHook('End', function() {
                window.setTimeout(window.print, 100);
            });
        });
        </script>
        {{else}}
        <script>
        window.addEventListener('load', function() {
            window.setTimeout(window.print, 100);
        });
        </script>
        {{/if}}
        {{/if}}

    </div>
    </body>
</html>
