msgid ""
msgstr ""
"Project-Id-Version: Ordinal Theory Handbook\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Poedit 3.3.2\n"

#: src/SUMMARY.md:2 src/introduction.md:1
msgid "Introduction"
msgstr "Вступление"

#: src/SUMMARY.md:3
msgid "Overview"
msgstr "Обзор"

#: src/SUMMARY.md:4 src/digital-artifacts.md:1
msgid "Digital Artifacts"
msgstr "Цифровые артефакты"

#: src/SUMMARY.md:5 src/SUMMARY.md:13 src/overview.md:221 src/inscriptions.md:1
msgid "Inscriptions"
msgstr "Надписи"

#: src/SUMMARY.md:6 src/inscriptions/provenance.md:1
msgid "Provenance"
msgstr "Провенанс"

#: src/SUMMARY.md:7 src/inscriptions/recursion.md:1
msgid "Recursion"
msgstr "Рекурсия"

#: src/SUMMARY.md:8
msgid "FAQ"
msgstr "ЧАВО"

#: src/SUMMARY.md:9
msgid "Contributing"
msgstr "Вклад"

#: src/SUMMARY.md:10 src/donate.md:1
msgid "Donate"
msgstr "Пожертвование"

#: src/SUMMARY.md:11
msgid "Guides"
msgstr "Руководства"

#: src/SUMMARY.md:12
msgid "Explorer"
msgstr "Проводник"

#: src/SUMMARY.md:14 src/guides/sat-hunting.md:1
msgid "Sat Hunting"
msgstr "Охота на сатоши"

#: src/SUMMARY.md:15 src/guides/collecting.md:1
msgid "Collecting"
msgstr "Коллекционирование"

#: src/SUMMARY.md:16 src/guides/sat-hunting.md:239
msgid "Sparrow Wallet"
msgstr "Sparrow Wallet"

#: src/SUMMARY.md:17 src/guides/testing.md:1
msgid "Testing"
msgstr "Тестирование"

#: src/SUMMARY.md:18 src/guides/moderation.md:1
msgid "Moderation"
msgstr "Модерация"

#: src/SUMMARY.md:19 src/guides/reindexing.md:1
msgid "Reindexing"
msgstr "Переиндексация"

#: src/SUMMARY.md:20
msgid "Bounties"
msgstr "Награды"

#: src/SUMMARY.md:21
msgid "Bounty 0: 100,000 sats Claimed!"
msgstr "Награда 0: 100 000 sats Собрано!"

#: src/SUMMARY.md:22
msgid "Bounty 1: 200,000 sats Claimed!"
msgstr "Награда 1: 200 000 sats Собрано!"

#: src/SUMMARY.md:23
msgid "Bounty 2: 300,000 sats Claimed!"
msgstr "Награда 2: 300 000 sats Собрано!"

#: src/SUMMARY.md:24
msgid "Bounty 3: 400,000 sats"
msgstr "Награда 3: 400 000 sats"

#: src/introduction.md:4
msgid ""
"This handbook is a guide to ordinal theory. Ordinal theory concerns itself with satoshis, giving them individual identities and allowing them to be tracked, "
"transferred, and imbued with meaning."
msgstr ""
"Данное пособие является руководством по ordinal theory. Ordinal theory занимается сатоши, наделяя их индивидуальностью и позволяя отслеживать, передавать и "
"наделять смыслом."

#: src/introduction.md:8
msgid "Satoshis, not bitcoin, are the atomic, native currency of the Bitcoin network. One bitcoin can be sub-divided into 100,000,000 satoshis, but no further."
msgstr "Сатоши, а не биткоин, являются основной атомарной валютой сети Bitcoin. Один биткоин может быть разделен на 100,000,000 сатоши, но не более."

#: src/introduction.md:11
msgid "Ordinal theory does not require a sidechain or token aside from Bitcoin, and can be used without any changes to the Bitcoin network. It works right now."
msgstr "Ordinal theory не требует сайдчейна или токена помимо Bitcoin и может использоваться без каких-либо изменений в сети Bitcoin. Она работает уже сейчас."

#: src/introduction.md:14
msgid "Ordinal theory imbues satoshis with numismatic value, allowing them to be collected and traded as curios."
msgstr "Ordinal theory наделяет сатоши нумизматической ценностью, позволяя коллекционировать их и торговать ими как сувенирами."

#: src/introduction.md:17
msgid ""
"Individual satoshis can be inscribed with arbitrary content, creating unique Bitcoin-native digital artifacts that can be held in Bitcoin wallets and "
"transferred using Bitcoin transactions. Inscriptions are as durable, immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"На отдельные сатоши можно наносить надписи произвольного содержания, создавая уникальные цифровые артефакты, характерные для биткоина, которые можно хранить в "
"биткоин-кошельках и передавать с помощью биткоин-транзакций. Надписи так же долговечны, неизменяемы, безопасны и децентрализованы, как и сам Биткойн."

#: src/introduction.md:22
msgid ""
"Other, more unusual use-cases are possible: off-chain colored-coins, public key infrastructure with key rotation, a decentralized replacement for the DNS. For "
"now though, such use-cases are speculative, and exist only in the minds of fringe ordinal theorists."
msgstr ""
"Возможны и другие, более необычные варианты использования: colored-coins вне блокчейна, инфраструктура открытых ключей с ротацией ключей, децентрализованная "
"замена для DNS. Однако пока такие варианты использования являются умозрительными и существуют только в головах сторонних ordinal теоритиков."

#: src/introduction.md:27
msgid "For more details on ordinal theory, see the [overview](overview.md)."
msgstr "Подробнее о порядковой теории смотрите в [Обзор Ordinal theory](overview.md)."

#: src/introduction.md:29
msgid "For more details on inscriptions, see [inscriptions](inscriptions.md)."
msgstr "Подробнее об inscriptions смотрите в разделе [inscriptions](inscriptions.md)."

#: src/introduction.md:31
msgid ""
"When you're ready to get your hands dirty, a good place to start is with [inscriptions](guides/inscriptions.md), a curious species of digital artifact enabled "
"by ordinal theory."
msgstr ""
"Если вы готовы замарать руки, то начать стоит с [надписей] (guides/inscriptions.md) - любопытного вида цифровых артефактов, созданных на основе ordinal theory."

#: src/introduction.md:35
msgid "Links"
msgstr "Ссылки"

#: src/introduction.md:38
msgid "[GitHub](https://github.com/ordinals/ord/)"
msgstr ""

#: src/introduction.md:39
msgid "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr ""

#: src/introduction.md:40
msgid "[Discord](https://discord.gg/ordinals)"
msgstr ""

#: src/introduction.md:41
msgid "[Open Ordinals Institute Website](https://ordinals.org/)"
msgstr "[Сайт Open Ordinals Institute](https://ordinals.org/)"

#: src/introduction.md:42
msgid "[Open Ordinals Institute X](https://x.com/ordinalsorg)"
msgstr "[Open Ordinals Institute X](https://x.com/ordinalsorg)"

#: src/introduction.md:43
msgid "[Mainnet Block Explorer](https://ordinals.com)"
msgstr "[Mainnet Block Explorer](https://ordinals.com)"

#: src/introduction.md:44
msgid "[Signet Block Explorer](https://signet.ordinals.com)"
msgstr "[Signet Block Explorer](https://signet.ordinals.com)"

#: src/introduction.md:46
msgid "Videos"
msgstr "Видео"

#: src/introduction.md:49
msgid "[Ordinal Theory Explained: Satoshi Serial Numbers and NFTs on Bitcoin](https://www.youtube.com/watch?v=rSS0O2KQpsI)"
msgstr "[Объяснение Ordinal Theory: Серийные Номера Сатоши и NFT в Bitcoin](https://www.youtube.com/watch?v=rSS0O2KQpsI)"

#: src/introduction.md:50
msgid "[Ordinals Workshop with Rodarmor](https://www.youtube.com/watch?v=MC_haVa6N3I)"
msgstr "[Семинар по Ordinals с Rodarmor](https://www.youtube.com/watch?v=MC_haVa6N3I)"

#: src/introduction.md:51
msgid "[Ordinal Art: Mint Your own NFTs on Bitcoin w/ @rodarmor](https://www.youtube.com/watch?v=j5V33kV3iqo)"
msgstr "[Ordinal Art: Mint собственных NFT на Bitcoin с @rodarmor](https://www.youtube.com/watch?v=j5V33kV3iqo)"

#: src/overview.md:1
msgid "Ordinal Theory Overview"
msgstr "Обзор Ordinal Theory"

#: src/overview.md:4
msgid ""
"Ordinals are a numbering scheme for satoshis that allows tracking and transferring individual sats. These numbers are called [ordinal numbers](https://ordinals."
"com). Satoshis are numbered in the order in which they're mined, and transferred from transaction inputs to transaction outputs first-in-first-out. Both the "
"numbering scheme and the transfer scheme rely on _order_, the numbering scheme on the _order_ in which satoshis are mined, and the transfer scheme on the "
"_order_ of transaction inputs and outputs. Thus the name, _ordinals_."
msgstr ""
"Ordinals - это схема нумерации сатоши, позволяющая отслеживать и передавать отдельные сатоши. Эти номера называются [ordinal numbers](https://ordinals.com). "
"Сатоши нумеруются в том порядке, в котором они добываются, и передаются от входов транзакций к выходам транзакций в порядке: первым пришёл - первым ушёл "
"(FIFO). И схема нумерации, и схема перевода зависят от _порядка_, причем схема нумерации - от _порядка_ добычи сатоши, а схема перевода - от _порядка_ входов и "
"выходов транзакций. Отсюда и название - _ordinals_."

#: src/overview.md:13
msgid "Technical details are available in [the BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)."
msgstr "Техническая информация представлена в [the BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)."

#: src/overview.md:16
msgid "Ordinal theory does not require a separate token, another blockchain, or any changes to Bitcoin. It works right now."
msgstr "Ordinal theory не требует создания отдельного токена, другого блокчейна или каких-либо изменений в Bitcoin. Она работает прямо сейчас."

#: src/overview.md:19
msgid "Ordinal numbers have a few different representations:"
msgstr "Ordinal numbers имеют несколько различных представлений:"

#: src/overview.md:21
msgid ""
"_Integer notation_: [`2099994106992659`](https://ordinals.com/sat/2099994106992659) The ordinal number, assigned according to the order in which the satoshi "
"was mined."
msgstr ""
"_Целочисленные обозначения_: [`2099994106992659`](https://ordinals.com/sat/2099994106992659) Ordinal numer, присваиваемый в соответствии с порядком, в котором "
"были добыты сатоши."

#: src/overview.md:26
msgid ""
"_Decimal notation_: [`3891094.16797`](https://ordinals.com/sat/3891094.16797) The first number is the block height in which the satoshi was mined, the second "
"the offset of the satoshi within the block."
msgstr ""
"_Десятичные обозначения_: [`3891094.16797`](https://ordinals.com/sat/3891094.16797) Первое число - это высота блока, в котором был добыт сатоши, второе - "
"смещение сатоши внутри блока."

#: src/overview.md:31
msgid "_Degree notation_: [`3°111094′214″16797‴`](https://ordinals.com/sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). We'll get to that in a moment."
msgstr ""
"_Обозначение степени_: [`3°111094′214″16797‴`](https://ordinals.com/sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). К этому мы вернемся в ближайшее "
"время."

#: src/overview.md:35
msgid ""
"_Percentile notation_: [`99.99971949060254%`](https://ordinals.com/sat/99.99971949060254%25) . The satoshi's position in Bitcoin's supply, expressed as a "
"percentage."
msgstr ""
"_Процентное обозначение_: [`99.99971949060254%`](https://ordinals.com/sat/99.99971949060254%25) . Положение сатоши в Bitcoin supply, выраженное в процентах."

#: src/overview.md:39
msgid "_Name_: [`satoshi`](https://ordinals.com/sat/satoshi). An encoding of the ordinal number using the characters `a` through `z`."
msgstr "_Название_: [`satoshi`](https://ordinals.com/sat/satoshi). Кодировка порядкового номера с помощью символов от `a` до `z`."

#: src/overview.md:42
msgid "Arbitrary assets, such as NFTs, security tokens, accounts, or stablecoins can be attached to satoshis using ordinal numbers as stable identifiers."
msgstr ""
"К сатоши могут быть присоединены произвольные активы, такие как NFT, безопасные токены, счета или стейблкоины, с использованием ordinal numbers в качестве "
"стабильных идентификаторов."

#: src/overview.md:45
msgid ""
"Ordinals is an open-source project, developed [on GitHub](https://github.com/ordinals/ord). The project consists of a BIP describing the ordinal scheme, an "
"index that communicates with a Bitcoin Core node to track the location of all satoshis, a wallet that allows making ordinal-aware transactions, a block "
"explorer for interactive exploration of the blockchain, functionality for inscribing satoshis with digital artifacts, and this manual."
msgstr ""
"Ordinals - это проект с открытым исходным кодом, разрабатываемый [на GitHub](https://github.com/ordinals/ord). Проект состоит из BIP, описывающего ordinal "
"схему, индекса, взаимодействующего с Bitcoin Core node для отслеживания местоположения всех сатоши, кошелька, позволяющего совершать осведомленные транзакции с "
"учетом ordinal, обозревать блоки для интерактивного изучения блокчейна, функциональности для надписи сатоши цифровыми артефактами, а также данного руководства."

#: src/overview.md:52
msgid "Rarity"
msgstr "Редкость"

#: src/overview.md:55
msgid ""
"Humans are collectors, and since satoshis can now be tracked and transferred, people will naturally want to collect them. Ordinal theorists can decide for "
"themselves which sats are rare and desirable, but there are some hints…"
msgstr ""
"Люди - коллекционеры, и поскольку сатоши теперь можно отслеживать и передавать, люди, естественно, захотят их собирать. Ordinal теоретики могут сами решить, "
"какие сатоши являются редкими и желанными, но есть некоторые подсказки…"

#: src/overview.md:59
msgid "Bitcoin has periodic events, some frequent, some more uncommon, and these naturally lend themselves to a system of rarity. These periodic events are:"
msgstr ""
"В биткоине периодически происходят события, одни из которых происходят часто, другие - более редко, и они естественным образом приводят к появлению системы "
"редкости. К таким периодическим событиям относятся:"

#: src/overview.md:62
msgid "_Blocks_: A new block is mined approximately every 10 minutes, from now until the end of time."
msgstr "_Блоки_: Новый блок добывается примерно каждые 10 минут, начиная с настоящего момента и до конца времен."

#: src/overview.md:65
msgid ""
"_Difficulty adjustments_: Every 2016 blocks, or approximately every two weeks, the Bitcoin network responds to changes in hashrate by adjusting the difficulty "
"target which blocks must meet in order to be accepted."
msgstr ""
"_Корректировка сложности_: Каждые 2016 блоков, или примерно раз в две недели, сеть Bitcoin реагирует на изменения хешрейта, изменяя целевой уровень сложности, "
"которому должны соответствовать блоки, чтобы быть принятыми."

#: src/overview.md:69
msgid "_Halvings_: Every 210,000 blocks, or roughly every four years, the amount of new sats created in every block is cut in half."
msgstr "_Халвинг_: Каждые 210 000 блоков или примерно раз в четыре года, количество новых sats, создаваемых в каждом блоке, сокращается вдвое."

#: src/overview.md:72
msgid ""
"_Cycles_: Every six halvings, something magical happens: the halving and the difficulty adjustment coincide. This is called a conjunction, and the time period "
"between conjunctions a cycle. A conjunction occurs roughly every 24 years. The first conjunction should happen sometime in 2032."
msgstr ""
"_Циклы_: Каждые шесть халвингов происходит нечто волшебное: халвинг и корректировка сложности совпадают. Это называется объединением, а период времени между "
"объединениями - циклом. Объединение происходит примерно раз в 24 года. Первое объединение должно произойти в 2032 году."

#: src/overview.md:77
msgid "This gives us the following rarity levels:"
msgstr "Таким образом, мы получаем следующие уровни редкости:"

#: src/overview.md:79
msgid "`common`: Any sat that is not the first sat of its block"
msgstr "`common`: Любой sat, который не является первым sat в своем блоке"

#: src/overview.md:80
msgid "`uncommon`: The first sat of each block"
msgstr "`uncommon`: Первый sat каждого блока"

#: src/overview.md:81
msgid "`rare`: The first sat of each difficulty adjustment period"
msgstr "`rare`: Первый sat каждого периода корректировки сложности"

#: src/overview.md:82
msgid "`epic`: The first sat of each halving epoch"
msgstr "`epic`: Первый sat каждой эпохи халвинга"

#: src/overview.md:83
msgid "`legendary`: The first sat of each cycle"
msgstr "`legendary`: Первый sat каждого цикла"

#: src/overview.md:84
msgid "`mythic`: The first sat of the genesis block"
msgstr "`mythic`: Первый sat genesis блока"

#: src/overview.md:86
msgid "Which brings us to degree notation, which unambiguously represents an ordinal number in a way that makes the rarity of a satoshi easy to see at a glance:"
msgstr ""
"Это приводит нас к обозначению степени, которая однозначно представляет ordinal number таким образом, что редкость сатоши легко увидеть с первого взгляда:"

#: src/overview.md:89
msgid ""
"```\n"
"A°B′C″D‴\n"
"│ │ │ ╰─ Index of sat in the block\n"
"│ │ ╰─── Index of block in difficulty adjustment period\n"
"│ ╰───── Index of block in halving epoch\n"
"╰─────── Cycle, numbered starting from 0\n"
"```"
msgstr ""
"```\n"
"A°B′C″D‴\n"
"│ │ │ ╰─ Индекс sat в блоке\n"
"│ │ ╰─── Индекс блока в период корректировки сложности\n"
"│ ╰───── Индекс блока в эпоху халвинга\n"
"╰─────── Цикл, нумерация которого начинается с 0\n"
"```"

#: src/overview.md:97
msgid "Ordinal theorists often use the terms \"hour\", \"minute\", \"second\", and \"third\" for _A_, _B_, _C_, and _D_, respectively."
msgstr "Ordinal теоретики часто используют термины \"час\", \"минута\", \"секунда\" и \"треть\" для обозначения _A_, _B_, _C_, и _D_, соответственно."

#: src/overview.md:100
msgid "Now for some examples. This satoshi is common:"
msgstr "Теперь несколько примеров. Этот сатоши является common:"

#: src/overview.md:102
msgid ""
"```\n"
"1°1′1″1‴\n"
"│ │ │ ╰─ Not first sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── Not first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″1‴\n"
"│ │ │ ╰─ Не первый sat в блоке\n"
"│ │ ╰─── Не первый блок в период корректировки сложности\n"
"│ ╰───── Не первый блок в эпоху халвинга\n"
"╰─────── Второй циклn\n"
"```"

#: src/overview.md:111
msgid "This satoshi is uncommon:"
msgstr "Этот сатоши uncommon:"

#: src/overview.md:113
msgid ""
"```\n"
"1°1′1″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── Not first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″0‴\n"
"│ │ │ ╰─ Первый sat в блоке\n"
"│ │ ╰─── Не первый блок в период корректировки сложности\n"
"│ ╰───── Не первый блок в эпоху халвинга\n"
"╰─────── Второй цикл\n"
"```"

#: src/overview.md:121
msgid "This satoshi is rare:"
msgstr "Это rare сатоши:"

#: src/overview.md:123
msgid ""
"```\n"
"1°1′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── Not the first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′0″0‴\n"
"│ │ │ ╰─ Первый sat в блоке\n"
"│ │ ╰─── Первый блок в период корректировки сложности\n"
"│ ╰───── Не первый блок в эпохе халвинга\n"
"╰─────── Второй цикл\n"
"```"

#: src/overview.md:131
msgid "This satoshi is epic:"
msgstr "Это epic сатоши:"

#: src/overview.md:133
msgid ""
"```\n"
"1°0′1″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°0′1″0‴\n"
"│ │ │ ╰─ Первый sat в блоке\n"
"│ │ ╰─── Не первый блок в период корректировки сложности\n"
"│ ╰───── Первый блок в эпоху халвинга\n"
"╰─────── Второй цикл\n"
"```"

#: src/overview.md:141
msgid "This satoshi is legendary:"
msgstr "Это legendary сатоши:"

#: src/overview.md:143
msgid ""
"```\n"
"1°0′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°0′0″0‴\n"
"│ │ │ ╰─ Первый sat в блоке\n"
"│ │ ╰─── Первый блок в период корректировки сложности\n"
"│ ╰───── Первый блок в эпоху халвинга\n"
"╰─────── Второй цикл\n"
"```"

#: src/overview.md:151
msgid "And this satoshi is mythic:"
msgstr "И это mithic сатоши:"

#: src/overview.md:153
msgid ""
"```\n"
"0°0′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── First cycle\n"
"```"
msgstr ""
"```\n"
"0°0′0″0‴\n"
"│ │ │ ╰─ Первый sat в блоке\n"
"│ │ ╰─── Первый блок в период корректировки сложности\n"
"│ ╰───── Первый блок в эпоху халвинга\n"
"╰─────── Второй цикл\n"
"```"

#: src/overview.md:161
msgid "If the block offset is zero, it may be omitted. This is the uncommon satoshi from above:"
msgstr "Если смещение блока равно нулю, то его можно не указывать. Это и есть uncommon сатоши, о котором говорилось выше:"

#: src/overview.md:164
msgid ""
"```\n"
"1°1′1″\n"
"│ │ ╰─ Not first block in difficulty adjustment period\n"
"│ ╰─── Not first block in halving epoch\n"
"╰───── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″\n"
"│ │ ╰─ Не первый блок в периоде корректировки сложности\n"
"│ ╰─── Не первый блок в эпоху халвинга\n"
"╰───── Второй цикл\n"
"```"

#: src/overview.md:171
msgid "Rare Satoshi Supply"
msgstr "Количество rare сатоши"

#: src/overview.md:174
msgid "Total Supply"
msgstr "Общее количество"

#: src/overview.md:176
msgid "`common`: 2.1 quadrillion"
msgstr "`common`: 2.1 квадриллион"

#: src/overview.md:177
msgid "`uncommon`: 6,929,999"
msgstr "`uncommon`: 6,929,999"

#: src/overview.md:178
msgid "`rare`: 3437"
msgstr "`rare`: 3437"

#: src/overview.md:179
msgid "`epic`: 32"
msgstr "`epic`: 32"

#: src/overview.md:180
msgid "`legendary`: 5"
msgstr "`legendary`: 5"

#: src/overview.md:181 src/overview.md:190
msgid "`mythic`: 1"
msgstr "`mythic`: 1"

#: src/overview.md:183
msgid "Current Supply"
msgstr "Текущее количество"

#: src/overview.md:185
msgid "`common`: 1.9 quadrillion"
msgstr "`common`: 1.9 квадриллион"

#: src/overview.md:186
msgid "`uncommon`: 745,855"
msgstr "`uncommon`: 745,855"

#: src/overview.md:187
msgid "`rare`: 369"
msgstr "`rare`: 369"

#: src/overview.md:188
msgid "`epic`: 3"
msgstr "`epic`: 3"

#: src/overview.md:189
msgid "`legendary`: 0"
msgstr "`legendary`: 0"

#: src/overview.md:192
msgid "At the moment, even uncommon satoshis are quite rare. As of this writing, 745,855 uncommon satoshis have been mined - one per 25.6 bitcoin in circulation."
msgstr ""
"В настоящее время даже uncommon сатоши являются довольно большой редкостью. На данный момент добыто 745 855 uncommon сатоши - по одному на 25,6 биткоина в "
"обороте."

#: src/overview.md:196
msgid "Names"
msgstr "Названия"

#: src/overview.md:199
msgid ""
"Each satoshi has a name, consisting of the letters _A_ through _Z_, that get shorter the further into the future the satoshi was mined. They could start short "
"and get longer, but then all the good, short names would be trapped in the unspendable genesis block."
msgstr ""
"Каждый сатоши имеет название, состоящее из букв _A_ - _Z_, которое становится короче по мере добычи сатоши в будущем. Они могли бы начинаться с коротких и "
"становиться длиннее, но тогда все хорошие, короткие названия оказались бы запертыми в неизрасходованном  genesis блоке ."

#: src/overview.md:204
msgid ""
"As an example, 1905530482684727°'s name is \"iaiufjszmoba\". The name of the last satoshi to be mined is \"a\". Every combination of 10 characters or less is "
"out there, or will be out there, someday."
msgstr ""
"Например, название 1905530482684727° - \"iaiufjszmoba\". Название последнего добытого сатоши - \"a\". Все комбинации из 10 символов или меньше уже существуют "
"или когда-нибудь будут существовать."

#: src/overview.md:208
msgid "Exotics"
msgstr "Экзотика"

#: src/overview.md:211
msgid ""
"Satoshis may be prized for reasons other than their name or rarity. This might be due to a quality of the number itself, like having an integer square or cube "
"root. Or it might be due to a connection to a historical event, such as satoshis from block 477,120, the block in which SegWit activated, or 2099999997689999°, "
"the last satoshi that will ever be mined."
msgstr ""
"Сатоши могут цениться не только по причине их названия или редкости. Это может быть связано с качеством самого числа, например, с наличием целого квадратного "
"или кубического корня. Или это может быть связано с историческим событием, например, сатоши из блока 477 120 - блока, в котором активировался SegWit, или "
"2099999997689999° - последнего сатоши, который когда-либо будет добыт."

#: src/overview.md:217
msgid ""
"Such satoshis are termed \"exotic\". Which satoshis are exotic and what makes them so is subjective. Ordinal theorists are encouraged to seek out exotics based "
"on criteria of their own devising."
msgstr ""
"Такие сатоши называются \"экзотическими\". Какие сатоши являются экзотическими и что их делает таковыми - вопрос субъективный. Теоретикам ordinal предлагается "
"искать экзотику по собственным критериям."

#: src/overview.md:224
msgid ""
"Satoshis can be inscribed with arbitrary content, creating Bitcoin-native digital artifacts. Inscribing is done by sending the satoshi to be inscribed in a "
"transaction that reveals the inscription content on-chain. This content is then inextricably linked to that satoshi, turning it into an immutable digital "
"artifact that can be tracked, transferred, hoarded, bought, sold, lost, and rediscovered."
msgstr ""
"На сатоши можно нанести произвольное содержание, создавая цифровые артефакты, характерные для Биткойна. Нанесение надписи осуществляется путем отправки сатоши "
"в транзакции, которая раскрывает содержание on-chain надписи. Затем это содержимое неразрывно связывается с сатоши, превращая его в неизменяемый цифровой "
"артефакт, который можно отслеживать, передавать, накапливать, покупать, продавать, терять и вновь находить."

#: src/overview.md:231
msgid "Archaeology"
msgstr "Археология"

#: src/overview.md:234
msgid ""
"A lively community of archaeologists devoted to cataloging and collecting early NFTs has sprung up. [Here's a great summary of historical NFTs by Chainleft.]"
"(https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)"
msgstr ""
"Возникло активное сообщество археологов, занимающихся каталогизацией и коллекционированием ранних NFT. [Вот отличный обзор исторических NFT, составленный "
"Chainleft.](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)"

#: src/overview.md:238
msgid ""
"A commonly accepted cut-off for early NFTs is March 19th, 2018, the date the first ERC-721 contract, [SU SQUARES](https://tenthousandsu.com/), was deployed on "
"Ethereum."
msgstr ""
"Общепринятой точкой отсчета для ранних NFT считается 19 марта 2018 года, когда на Ethereum был развернут первый контракт ERC-721. [SU SQUARES](https://"
"tenthousandsu.com/)"

#: src/overview.md:242
msgid ""
"Whether or not ordinals are of interest to NFT archaeologists is an open question! In one sense, ordinals were created in early 2022, when the Ordinals "
"specification was finalized. In this sense, they are not of historical interest."
msgstr ""
"Вопрос о том, представляют ли ординалы интерес для археологов NFT, остается открытым! С одной стороны, ordinals были созданы в начале 2022 года, когда была "
"завершена работа над спецификацией Ordinal. В этом смысле они не представляют исторического интереса."

#: src/overview.md:247
msgid ""
"In another sense though, ordinals were in fact created by Satoshi Nakamoto in 2009 when he mined the Bitcoin genesis block. In this sense, ordinals, and "
"especially early ordinals, are certainly of historical interest."
msgstr ""
"С другой стороны, ordinals были фактически созданы Сатоши Накамото в 2009 году при майнинге генезисного блока Bitcoin. В этом смысле ordinals, и особенно "
"ранние ordinals, безусловно, представляют исторический интерес."

#: src/overview.md:251
msgid ""
"Many ordinal theorists favor the latter view. This is not least because the ordinals were independently discovered on at least two separate occasions, long "
"before the era of modern NFTs began."
msgstr ""
"Многие ordinal теоретики придерживаются последней точки зрения. Не в последнюю очередь это связано с тем, что ordianls были независимо обнаружены, по крайней "
"мере, в двух отдельных случаях, задолго до начала эры современных NFT."

#: src/overview.md:255
msgid ""
"On August 21st, 2012, Charlie Lee [posted a proposal to add proof-of-stake to Bitcoin to the Bitcoin Talk forum](https://bitcointalk.org/index.php?"
"topic=102355.0). This wasn't an asset scheme, but did use the ordinal algorithm, and was implemented but never deployed."
msgstr ""
"21 августа 2012 года Чарли Ли [разместил на форуме Bitcoin Talk предложение добавить в биткоин proof-of-stake](https://bitcointalk.org/index.php?"
"topic=102355.0). Это не было схемой активов, но использовало ordinal алгоритм и было реализовано, но так и не было развернуто."

#: src/overview.md:261
msgid ""
"On October 8th, 2012, jl2012 [posted a scheme to the same forum](https://bitcointalk.org/index.php?topic=117224.0) which uses decimal notation and has all the "
"important properties of ordinals. The scheme was discussed but never implemented."
msgstr ""
"8 октября 2012 г., jl2012 [разместил схему на том же форуме](https://bitcointalk.org/index.php?topic=117224.0) которая использует десятичную систему "
"обозначений и обладает всеми важными свойствами ordinal. Эта схема обсуждалась, но так и не была реализована."

#: src/overview.md:266
msgid ""
"These independent inventions of ordinals indicate in some way that ordinals were discovered, or rediscovered, and not invented. The ordinals are an "
"inevitability of the mathematics of Bitcoin, stemming not from their modern documentation, but from their ancient genesis. They are the culmination of a "
"sequence of events set in motion with the mining of the first block, so many years ago."
msgstr ""
"Эти независимые изобретения ordinals в какой-то мере указывают на то, что ordinals были открыты или заново открыты, а не изобретены. Ordinals - это "
"неизбежность математики биткоина, вытекающая не из их современного документирования, а из их древнего генезиса. Они являются кульминацией последовательности "
"событий, начавшихся много лет назад с момента добычи первого блока."

#: src/digital-artifacts.md:4
msgid ""
"Imagine a physical artifact. A rare coin, say, held safe for untold years in the dark, secret clutch of a Viking hoard, now dug from the earth by your grasping "
"hands. It…"
msgstr ""
"Представьте себе физический артефакт. Редкая монета, скажем, хранившаяся несметное количество лет в темных тайниках клада викингов, а теперь выкопанная из "
"земли вашими цепкими руками. Она…"

#: src/digital-artifacts.md:8
msgid "…has an owner. You. As long as you keep it safe, nobody can take it from you."
msgstr "...имеет владельца. Вы. Пока вы храните его в безопасности, никто не может его у вас отнять."

#: src/digital-artifacts.md:10
msgid "…is complete. It has no missing parts."
msgstr "...завершен. В нем нет недостающих частей."

#: src/digital-artifacts.md:12
msgid "…can only be changed by you. If you were a trader, and you made your way to 18th century China, none but you could stamp it with your chop-mark."
msgstr "...может быть изменен только вами. Если бы вы были торговцем и попали в Китай XVIII века, никто, кроме вас, не смог бы поставить на нем свой знак."

#: src/digital-artifacts.md:15
msgid "…can only be disposed of by you. The sale, trade, or gift is yours to make, to whomever you wish."
msgstr "...может быть отчужден только вами. Продать, обменять или подарить - это ваше право, кому бы вы ни пожелали."

#: src/digital-artifacts.md:18
msgid "What are digital artifacts? Simply put, they are the digital equivalent of physical artifacts."
msgstr "Что такое цифровые артефакты? Проще говоря, это цифровой эквивалент физических артефактов."

#: src/digital-artifacts.md:21
msgid "For a digital thing to be a digital artifact, it must be like that coin of yours:"
msgstr "Чтобы цифровая вещь стала цифровым артефактом, она должна быть похожа на вашу монету:"

#: src/digital-artifacts.md:24
msgid "Digital artifacts can have owners. A number is not a digital artifact, because nobody can own it."
msgstr "У цифровых артефактов могут быть владельцы. Число не является цифровым артефактом, поскольку никто не может им владеть."

#: src/digital-artifacts.md:27
msgid "Digital artifacts are complete. An NFT that points to off-chain content on IPFS or Arweave is incomplete, and thus not a digital artifact."
msgstr ""
"Цифровые артефакты полностью готовы. NFT, указывающий на содержимое вне цепочки на IPFS или Arweave, является неполным, а значит, не является цифровым "
"артефактом."

#: src/digital-artifacts.md:30
msgid "Digital artifacts are permissionless. An NFT which cannot be sold without paying a royalty is not permissionless, and thus not a digital artifact."
msgstr ""
"Цифровые артефакты не имеют разрешений. NFT, который нельзя продать без оплаты роялти, не является безразрешительным, а значит, не является цифровым артефактом."

#: src/digital-artifacts.md:33
msgid ""
"Digital artifacts are uncensorable. Perhaps you can change a database entry on a centralized ledger today, but maybe not tomorrow, and thus one cannot be a "
"digital artifact."
msgstr ""
"Цифровые артефакты не поддаются цензуре. Возможно, вы можете изменить запись в базе данных централизованном Ledger сегодня, но, возможно, не сможете завтра, и "
"поэтому она не может быть цифровым артефактом."

#: src/digital-artifacts.md:37
msgid "Digital artifacts are immutable. An NFT with an upgrade key is not a digital artifact."
msgstr "Цифровые артефакты неизменяемы. NFT с ключом обновления не является цифровым артефактом."

#: src/digital-artifacts.md:40
msgid ""
"The definition of a digital artifact is intended to reflect what NFTs _should_ be, sometimes are, and what inscriptions _always_ are, by their very nature."
msgstr "Определение цифрового артефакта призвано отразить то, чем NFT _должны_ быть, иногда являются, и то, чем надписи _всегда_ являются по своей природе."

#: src/inscriptions.md:4
msgid ""
"Inscriptions inscribe sats with arbitrary content, creating bitcoin-native digital artifacts, more commonly known as NFTs. Inscriptions do not require a "
"sidechain or separate token."
msgstr ""
"Надписи содержат sat c произвольным содержанием, создавая цифровые артефакты, характерные для биткоина, более известные как NFT. Надписи не требуют сайдчейна "
"или отдельного токена."

#: src/inscriptions.md:8
msgid ""
"These inscribed sats can then be transferred using bitcoin transactions, sent to bitcoin addresses, and held in bitcoin UTXOs. These transactions, addresses, "
"and UTXOs are normal bitcoin transactions, addresses, and UTXOS in all respects, with the exception that in order to send individual sats, transactions must "
"control the order and value of inputs and outputs according to ordinal theory."
msgstr ""
"Эти надписанные sats могут передаваться с помощью биткоин-транзакций, отправляться на биткоин-адреса и храниться в биткоин UTXO. Эти транзакции, адреса и UTXO "
"во всех отношениях являются обычными биткойн-транзакциями, адресами и UTXOS, за исключением того, что для отправки отдельных sats, транзакции должны "
"контролировать порядок и значение входов и выходов в соответствии с ordinal theory."

#: src/inscriptions.md:15
msgid ""
"The inscription content model is that of the web. An inscription consists of a content type, also known as a MIME type, and the content itself, which is a byte "
"string. This allows inscription content to be returned from a web server, and for creating HTML inscriptions that use and remix the content of other "
"inscriptions."
msgstr ""
"Модель содержания надписей соответствует модели содержания web. Надпись состоит из типа содержимого, известного также как тип-MIME, и самого содержимого, "
"которое представляет собой байтовую строку. Это позволяет возвращать содержимое надписи с веб-сервера, а также создавать HTML-надписи, использующие и "
"переделывающие содержимое других надписей."

#: src/inscriptions.md:21
msgid ""
"Inscription content is entirely on-chain, stored in taproot script-path spend scripts. Taproot scripts have very few restrictions on their content, and "
"additionally receive the witness discount, making inscription content storage relatively economical."
msgstr ""
"Содержимое надписей полностью находится on-chain и хранится в скриптах taproot script-path spend. Скрипты taproot имеют очень мало ограничений на содержимое и "
"дополнительно получают скидку свидетеля, что делает хранение содержимого надписей относительно экономичным."

#: src/inscriptions.md:26
msgid ""
"Since taproot script spends can only be made from existing taproot outputs, inscriptions are made using a two-phase commit/reveal procedure. First, in the "
"commit transaction, a taproot output committing to a script containing the inscription content is created. Second, in the reveal transaction, the output "
"created by the commit transaction is spent, revealing the inscription content on-chain."
msgstr ""
"Поскольку траты taproot script-path spend могут осуществляться только из существующих выходов taproot, надписи делаются с помощью двухфазной процедуры commit/"
"reveal. Во-первых, в транзакции commit создается выход taproot, фиксирующий скрипт, содержащий содержимое надписи. Во-вторых, в транзакции reveal созданный "
"транзакцией commit выход расходуется, раскрывая содержимое on-chain надписи."

#: src/inscriptions.md:33
msgid ""
"Inscription content is serialized using data pushes within unexecuted conditionals, called \"envelopes\". Envelopes consist of an `OP_FALSE OP_IF … OP_ENDIF` "
"wrapping any number of data pushes. Because envelopes are effectively no-ops, they do not change the semantics of the script in which they are included, and "
"can be combined with any other locking script."
msgstr ""
"Сериализация содержимого надписи осуществляется с помощью data pushes внутри неисполняемых условий, называемых \"конвертами\". Конверты состоят из `OP_FALSE "
"OP_IF … OP_ENDIF` обертывающих любое количество data pushes. Поскольку конверты являются фактически no-ops, они не изменяют семантику скрипта, в который "
"включены, и могут быть объединены с любым другим блокирующим скриптом."

#: src/inscriptions.md:39
msgid "A text inscription containing the string \"Hello, world!\" is serialized as follows:"
msgstr "Текстовая надпись, содержащая строку \"Hello, world!\" сериализуется следующим образом:"

#: src/inscriptions.md:42
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions.md:53
msgid "First the string `ord` is pushed, to disambiguate inscriptions from other uses of envelopes."
msgstr "Сначала вводится строка `ord`, чтобы отделить надписи от других видов использования конвертов."

#: src/inscriptions.md:56
msgid ""
"`OP_PUSH 1` indicates that the next push contains the content type, and `OP_PUSH 0`indicates that subsequent data pushes contain the content itself. Multiple "
"data pushes must be used for large inscriptions, as one of taproot's few restrictions is that individual data pushes may not be larger than 520 bytes."
msgstr ""
"`OP_PUSH 1` указывает, что следующий push содержит тип содержимого, а `OP_PUSH 0` - что последующие push-файлы содержат само содержимое. Для больших надписей "
"необходимо использовать несколько push данных, так как одно из немногих ограничений taproot заключается в том, что размер отдельных push данных не может "
"превышать 520 байт."

#: src/inscriptions.md:62
msgid ""
"The inscription content is contained within the input of a reveal transaction, and the inscription is made on the first sat of its input. This sat can then be "
"tracked using the familiar rules of ordinal theory, allowing it to be transferred, bought, sold, lost to fees, and recovered."
msgstr ""
"Содержание надписи содержится во входе reveal транзакции, и надпись делается на первом sat ее входа. Затем этот sat может быть отслежен по известным правилам "
"ordinal theory, что позволяет передавать, покупать, продавать, терять в комиссиях и восстанавливать его."

#: src/inscriptions.md:67
msgid "Content"
msgstr "Содержимое"

#: src/inscriptions.md:70
msgid "The data model of inscriptions is that of a HTTP response, allowing inscription content to be served by a web server and viewed in a web browser."
msgstr "Модель данных надписей представляет собой HTTP-ответ, что позволяет обслуживать содержимое надписей на веб-сервере и просматривать его в браузере."

#: src/inscriptions.md:73
msgid "Fields"
msgstr "Поля"

#: src/inscriptions.md:76
msgid "Inscriptions may include fields before an optional body. Each field consists of two data pushes, a tag and a value."
msgstr "Надписи могут включать поля перед необязательным текстом. Каждое поле состоит из двух push данных - тега и значения."

#: src/inscriptions.md:79
msgid "Currently, the only defined field is `content-type`, with a tag of `1`, whose value is the MIME type of the body."
msgstr "В настоящее время единственным определяемым полем является `content-type`, с тегом `1`, значением которого является тип-MIME текста."

#: src/inscriptions.md:82
msgid "The beginning of the body and end of fields is indicated with an empty data push."
msgstr "Начало основного текста и конец полей обозначаются пустым вводом данных."

#: src/inscriptions.md:85
msgid ""
"Unrecognized tags are interpreted differently depending on whether they are even or odd, following the \"it's okay to be odd\" rule used by the Lightning "
"Network."
msgstr ""
"Нераспознанные метки интерпретируются по-разному в зависимости от того, четные они или нечетные, в соответствии с правилом \"нормально быть нечетным\", "
"используемым в Lightning Network."

#: src/inscriptions.md:89
msgid ""
"Even tags are used for fields which may affect creation, initial assignment, or transfer of an inscription. Thus, inscriptions with unrecognized even fields "
"must be displayed as \"unbound\", that is, without a location."
msgstr ""
"Четные метки используются для полей, которые могут повлиять на создание, первичное присвоение или перенос надписи. Таким образом, надписи с нераспознанными "
"четными полями должны отображаться как \"несвязанные\", т.е. без указания местоположения."

#: src/inscriptions.md:93
msgid "Odd tags are used for fields which do not affect creation, initial assignment, or transfer, such as additional metadata, and thus are safe to ignore."
msgstr ""
"Нечетные теги используются для полей, которые не влияют на создание, первоначальное назначение или передачу, например, дополнительные метаданные, и поэтому их "
"можно игнорировать."

#: src/inscriptions.md:96
msgid "Inscription IDs"
msgstr "ID надписей"

#: src/inscriptions.md:99
msgid "The inscriptions are contained within the inputs of a reveal transaction. In order to uniquely identify them they are assigned an ID of the form:"
msgstr "Надписи содержатся во входах транзакции раскрытия. Для их однозначной идентификации им присваивается ID вида:"

#: src/inscriptions.md:102
msgid "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"
msgstr "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"

#: src/inscriptions.md:104
msgid ""
"The part in front of the `i` is the transaction ID (`txid`) of the reveal transaction. The number after the `i` defines the index (starting at 0) of new "
"inscriptions being inscribed in the reveal transaction."
msgstr ""
"Часть перед `i` - это ID транзакции (`txid`) транзакции раскрытия. Число после `i` определяет индекс (начиная с 0) новых надписей, заносимых в транзакцию "
"раскрытия."

#: src/inscriptions.md:108
msgid ""
"Inscriptions can either be located in different inputs, within the same input or a combination of both. In any case the ordering is clear, since a parser would "
"go through the inputs consecutively and look for all inscription `envelopes`."
msgstr ""
"Надписи могут располагаться либо в разных входах, либо в одном входе, либо в комбинации обоих вариантов. В любом случае порядок следования понятен, так как "
"синтаксический анализатор будет последовательно просматривать входы и искать все надписи-\"конверты\"."

#: src/inscriptions.md:112
msgid "Input"
msgstr "Вход"

#: src/inscriptions.md:112
msgid "Inscription Count"
msgstr ""

#: src/inscriptions.md:112
msgid "Indices"
msgstr ""

#: src/inscriptions.md:114 src/inscriptions.md:117
msgid "0"
msgstr ""

#: src/inscriptions.md:114 src/inscriptions.md:116
msgid "2"
msgstr ""

#: src/inscriptions.md:114
msgid "i0, i1"
msgstr ""

#: src/inscriptions.md:115 src/inscriptions.md:118
msgid "1"
msgstr ""

#: src/inscriptions.md:115
msgid "i2"
msgstr ""

#: src/inscriptions.md:116 src/inscriptions.md:117
msgid "3"
msgstr ""

#: src/inscriptions.md:116
msgid "i3, i4, i5"
msgstr ""

#: src/inscriptions.md:118
msgid "4"
msgstr ""

#: src/inscriptions.md:118
msgid "i6"
msgstr ""

#: src/inscriptions.md:120
msgid "Sandboxing"
msgstr "Песочница"

#: src/inscriptions.md:123
msgid "HTML and SVG inscriptions are sandboxed in order to prevent references to off-chain content, thus keeping inscriptions immutable and self-contained."
msgstr ""
"Надписи HTML и SVG находятся в песочнице для предотвращения ссылок на off-chain содержимое, что позволяет сохранить неизменяемость и самодостаточность надписей."

#: src/inscriptions.md:126
msgid ""
"This is accomplished by loading HTML and SVG inscriptions inside `iframes` with the `sandbox` attribute, as well as serving inscription content with `Content-"
"Security-Policy` headers."
msgstr ""
"Это достигается за счет загрузки HTML и SVG-надписей внутри `iframe` с атрибутом `sandbox`, а также предоставления содержимого надписей с заголовками `Content-"
"Security-Policy`."

#: src/inscriptions/provenance.md:4
msgid ""
"The owner of an inscription can create child inscriptions, trustlessly establishing the provenance of those children on-chain as having been created by the "
"owner of the parent inscription. This can be used for collections, with the children of a parent inscription being members of the same collection."
msgstr ""
"Владелец надписи может создавать дочерние надписи, не сомневаясь в том, что эти дочерние on-chain надписи были созданы владельцем родительской надписи. Это "
"может быть использовано для коллекций, когда дочерние надписи от родительской надписи являются членами одной коллекции."

#: src/inscriptions/provenance.md:9
msgid ""
"Children can themselves have children, allowing for complex hierarchies. For example, an artist might create an inscription representing themselves, with sub "
"inscriptions representing collections that they create, with the children of those sub inscriptions being items in those collections."
msgstr ""
"Дети могут создавать дочерние элементы, что позволяет создавать комплексные иерархии. Например, художник может создать надпись, представляющую его самого, с "
"вложенными надписями, представляющими созданные им коллекции, а дочерними элементами этих sub надписей являются предметы этих коллекций."

#: src/inscriptions/provenance.md:14
msgid "Specification"
msgstr "Описание"

#: src/inscriptions/provenance.md:16
msgid "To create a child inscription C with parent inscription P:"
msgstr "Чтобы создать child надпись C с parent надписью P:"

#: src/inscriptions/provenance.md:18
msgid "Create an inscribe transaction T as usual for C."
msgstr "Создайте транзакцию надписи T, как обычно, для C."

#: src/inscriptions/provenance.md:19
msgid "Spend the parent P in one of the inputs of T."
msgstr "Проведите parent P по одному из входов T."

#: src/inscriptions/provenance.md:20
msgid ""
"Include tag `3`, i.e. `OP_PUSH 3`, in C, with the value of the serialized binary inscription ID of P, serialized as the 32-byte `TXID`, followed by the four-"
"byte little-endian `INDEX`, with trailing zeroes omitted."
msgstr ""
"Включите в C тег `3`, т.е. `OP_PUSH 3`, со значением сериализованного двоичного ID надписи P, сериализованного как 32-байтовый `TXID`, за которым следует "
"четырехбайтовый little-endian `INDEX`, с опущенными задними нулями."

#: src/inscriptions/provenance.md:24
msgid "_NB_ The bytes of a bitcoin transaction ID are reversed in their text representation, so the serialized transaction ID will be in the opposite order."
msgstr ""
"_NB_ В текстовом представлении байты ID транзакции биткоина расположены в обратном порядке, поэтому сериализованные ID транзакции будут расположены в обратном "
"порядке."

#: src/inscriptions/provenance.md:27 src/guides/testing.md:18 src/guides/reindexing.md:15
msgid "Example"
msgstr "Пример"

#: src/inscriptions/provenance.md:29
msgid "An example of a child inscription of `000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"
msgstr "Пример child надписи из `000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"

#: src/inscriptions/provenance.md:32
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH 0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH 0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:45
msgid ""
"Note that the value of tag `3` is binary, not hex, and that for the child inscription to be recognized as a child, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` must be spent as one of the inputs of the inscribe transaction."
msgstr ""
"Заметим, что значение метки `3` двоичное, а не шестнадцатеричное, и что для того, чтобы надпись была признана детской, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` должен быть потрачен как один из входов inscribe транзакции."

#: src/inscriptions/provenance.md:50
msgid "Example encoding of inscription ID `000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"
msgstr "Пример кодировки ID надписи `000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"

#: src/inscriptions/provenance.md:53
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH 0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100ff\n"
"  …\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH 0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100ff\n"
"  …\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:63
msgid "And of inscription ID `000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"
msgstr "И ID надписи `000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"

#: src/inscriptions/provenance.md:65
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH 0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a090807060504030201000001\n"
"  …\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH 0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a090807060504030201000001\n"
"  …\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:75
msgid "Notes"
msgstr "Примечаниe"

#: src/inscriptions/provenance.md:77
msgid ""
"The tag `3` is used because it is the first available odd tag. Unrecognized odd tags do not make an inscription unbound, so child inscriptions would be "
"recognized and tracked by old versions of `ord`."
msgstr ""
"Метка `3` используется потому что это первая доступная нечетная метка. Нераспознанные нечетные метки не делают надпись несвязанной, поэтому дочерние надписи "
"будут распознаваться и отслеживаться старыми версиями `ord`."

#: src/inscriptions/provenance.md:81
msgid "A collection can be closed by burning the collection's parent inscription, which guarantees that no more items in the collection can be issued."
msgstr "Коллекция может быть закрыта путем сжигания родительской надписи коллекции, что гарантирует, что больше ни один предмет коллекции не может быть выпущен."

#: src/inscriptions/recursion.md:4
msgid ""
"An important exception to [sandboxing](../inscriptions.md#sandboxing) is recursion: access to `ord`'s `/content` endpoint is permitted, allowing inscriptions "
"to access the content of other inscriptions by requesting `/content/<INSCRIPTION_ID>`."
msgstr ""
"Важным исключением из [песочницы](../inscriptions.md#sandboxing) является рекурсия: доступ к конечной точке `ord`'s `/content` разрешен, что позволяет надписям "
"получать доступ к содержимому других надписей, запрашивая `/content/<INSCRIPTION_ID>`."

#: src/inscriptions/recursion.md:9
msgid "This has a number of interesting use-cases:"
msgstr "Это имеет ряд интересных вариантов использования:"

#: src/inscriptions/recursion.md:11
msgid "Remixing the content of existing inscriptions."
msgstr "Ремикс содержания существующих надписей."

#: src/inscriptions/recursion.md:13
msgid "Publishing snippets of code, images, audio, or stylesheets as shared public resources."
msgstr "Публикация фрагментов кода, изображений, аудио или таблиц стилей в качестве общедоступных ресурсов."

#: src/inscriptions/recursion.md:16
msgid "Generative art collections where an algorithm is inscribed as JavaScript, and instantiated from multiple inscriptions with unique seeds."
msgstr "Генеративные художественные коллекции, в которых алгоритм записан как JavaScript и инстанцирован из множества надписей с уникальными seeds."

#: src/inscriptions/recursion.md:19
msgid ""
"Generative profile picture collections where accessories and attributes are inscribed as individual images, or in a shared texture atlas, and then combined, "
"collage-style, in unique combinations in multiple inscriptions."
msgstr ""
"Генеративные коллекции изображений профиля (PFP), в которых аксессуары и атрибуты вписываются в виде отдельных изображений или в общий атлас текстур, а затем "
"комбинируются, в стиле коллажа, в уникальных сочетаниях в нескольких надписях."

#: src/inscriptions/recursion.md:23
msgid "A few other endpoints that inscriptions may access are the following:"
msgstr "Среди других конечных точек, к которым могут обращаться надписи, можно назвать следующие:"

#: src/inscriptions/recursion.md:25
msgid "`/blockheight`: latest block height."
msgstr "`/blockheight`: высота последнего блока."

#: src/inscriptions/recursion.md:26
msgid "`/blockhash`: latest block hash."
msgstr "`/blockhash`: хэш последнего блока."

#: src/inscriptions/recursion.md:27
msgid "`/blockhash/<HEIGHT>`: block hash at given block height."
msgstr "`/blockhash/<HEIGHT>`: хэш блока при заданной высоте блока."

#: src/inscriptions/recursion.md:28
msgid "`/blocktime`: UNIX time stamp of latest block."
msgstr "`/blocktime`: UNIX-метка времени последнего блока."

#: src/faq.md:1
msgid "Ordinal Theory FAQ"
msgstr "ЧАВО по Ordinals Theory"

#: src/faq.md:4
msgid "What is ordinal theory?"
msgstr "Что такое ordinal theory?"

#: src/faq.md:7
msgid ""
"Ordinal theory is a protocol for assigning serial numbers to satoshis, the smallest subdivision of a bitcoin, and tracking those satoshis as they are spent by "
"transactions."
msgstr ""
"Ordinal theory - это протокол для присвоения порядковых номеров сатоши, наименьшей части биткоина, и отслеживания этих сатоши по мере их расходования в ходе "
"транзакций."

#: src/faq.md:11
msgid "These serial numbers are large numbers, like this 804766073970493. Every satoshi, which is ¹⁄₁₀₀₀₀₀₀₀₀ of a bitcoin, has an ordinal number."
msgstr ""
"Эти серийные номера представляют собой большие числа, как, например, этот 804766073970493. Каждый сатоши, который представляет собой ¹⁄₁₀₀₀₀₀₀₀₀ биткоина, "
"имеет порядковый номер."

#: src/faq.md:14
msgid "Does ordinal theory require a side chain, a separate token, or changes to Bitcoin?"
msgstr "Требует ли ordinal theory создания side chain, отдельного токена или изменений в Bitcoin?"

#: src/faq.md:17
msgid "Nope! Ordinal theory works right now, without a side chain, and the only token needed is bitcoin itself."
msgstr "Нет! Ordinal theory работает прямо сейчас, без side chain, и единственным необходимым токеном является сам биткоин."

#: src/faq.md:20
msgid "What is ordinal theory good for?"
msgstr "Чем полезна ordinal theory?"

#: src/faq.md:23
msgid ""
"Collecting, trading, and scheming. Ordinal theory assigns identities to individual satoshis, allowing them to be individually tracked and traded, as curios and "
"for numismatic value."
msgstr ""
"Коллекционирование, торговля и интриги. Ordinal theory присваивает идентификацию отдельным сатоши, что позволяет отслеживать их по отдельности и торговать ими "
"как диковинами и нумизматическими ценностями."

#: src/faq.md:27
msgid ""
"Ordinal theory also enables inscriptions, a protocol for attaching arbitrary content to individual satoshis, turning them into bitcoin-native digital artifacts."
msgstr ""
"Ordinal theory также позволяет создавать надписи - протокол для прикрепления произвольного содержимого к отдельным сатоши, превращая их в цифровые артефакты с "
"поддержкой биткоина."

#: src/faq.md:31
msgid "How does ordinal theory work?"
msgstr "Как работает ordinal theory?"

#: src/faq.md:34
msgid ""
"Ordinal numbers are assigned to satoshis in the order in which they are mined. The first satoshi in the first block has ordinal number 0, the second has "
"ordinal number 1, and the last satoshi of the first block has ordinal number 4,999,999,999."
msgstr ""
"Порядковые номера присваиваются сатоши в том порядке, в котором они добываются. Первый сатоши в первом блоке имеет порядковый номер 0, второй имеет порядковый "
"номер - 1, а последний сатоши первого блока имеет порядковый номер 4,999,999,999."

#: src/faq.md:39
msgid ""
"Satoshis live in outputs, but transactions destroy outputs and create new ones, so ordinal theory uses an algorithm to determine how satoshis hop from the "
"inputs of a transaction to its outputs."
msgstr ""
"Сатоши живут в выходах, но транзакции разрушают выходы и создают новые, поэтому порядковая теория использует алгоритм для определения того, как сатоши "
"переходят от входов транзакции к ее выходам."

#: src/faq.md:43
msgid "Fortunately, that algorithm is very simple."
msgstr "К счастью, этот алгоритм очень прост."

#: src/faq.md:45
msgid ""
"Satoshis transfer in first-in-first-out order. Think of the inputs to a transaction as being a list of satoshis, and the outputs as a list of slots, waiting to "
"receive a satoshi. To assign input satoshis to slots, go through each satoshi in the inputs in order, and assign each to the first available slot in the "
"outputs."
msgstr ""
"Сатоши передаются по принципу первым пришёл - первым вышел. Считайте, что на входе транзакции находится список сатоши, а на выходе - список слотов, ожидающих "
"получения сатоши. Чтобы распределить входные сатоши по слотам, переберите все сатоши на входах по порядку и распределите их по первому свободному слоту на "
"выходах."

#: src/faq.md:51
msgid ""
"Let's imagine a transaction with three inputs and two outputs. The inputs are on the left of the arrow and the outputs are on the right, all labeled with their "
"values:"
msgstr "Представим себе транзакцию с тремя входами и двумя выходами. Входы расположены слева от стрелки, а выходы - справа, все они помечены своими значениями:"

#: src/faq.md:55
msgid ""
"```\n"
"[2] [1] [3] → [4] [2]\n"
"```"
msgstr ""
"```\n"
"[2] [1] [3] → [4] [2]\n"
"```"

#: src/faq.md:57
msgid ""
"Now let's label the same transaction with the ordinal numbers of the satoshis that each input contains, and question marks for each output slot. Ordinal "
"numbers are large, so let's use letters to represent them:"
msgstr ""
"Теперь обозначим ту же операцию порядковыми номерами сатоши, которые содержит каждый вход, и вопросительными знаками - каждый выходной слот. Порядковые номера "
"большие, поэтому для их обозначения будем использовать буквы:"

#: src/faq.md:61
msgid ""
"```\n"
"[a b] [c] [d e f] → [? ? ? ?] [? ?]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [? ? ? ?] [? ?]\n"
"```"

#: src/faq.md:63
msgid "To figure out which satoshi goes to which output, go through the input satoshis in order and assign each to a question mark:"
msgstr "Чтобы выяснить, какой сатоши соответствует какому выходу, пройдите по порядку все входные сатоши и присвойте каждому из них вопросительный знак:"

#: src/faq.md:66
msgid ""
"```\n"
"[a b] [c] [d e f] → [a b c d] [e f]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [a b c d] [e f]\n"
"```"

#: src/faq.md:68
msgid ""
"What about fees, you might ask? Good question! Let's imagine the same transaction, this time with a two satoshi fee. Transactions with fees send more satoshis "
"in the inputs than are received by the outputs, so to make our transaction into one that pays fees, we'll remove the second output:"
msgstr ""
"А что насчет комиссий, спросите вы? Хороший вопрос! Представим себе ту же транзакцию, но с комиссией в два сатоши. Транзакции с комиссией отправляют на входы "
"больше сатоши, чем получают на выходы, поэтому, чтобы превратить нашу транзакцию в транзакцию которая оплачивает комиссию, мы удалим второй выход:"

#: src/faq.md:73
msgid ""
"```\n"
"[2] [1] [3] → [4]\n"
"```"
msgstr ""
"```\n"
"[2] [1] [3] → [4]\n"
"```"

#: src/faq.md:75
msgid "The satoshis "
msgstr "Сатоши "

#: src/faq.md:75
msgid "e"
msgstr "e"

#: src/faq.md:75
msgid " and "
msgstr " и "

#: src/faq.md:75
msgid "f"
msgstr "f"

#: src/faq.md:75
msgid " now have nowhere to go in the outputs:"
msgstr " теперь некуда девать на выходах:"

#: src/faq.md:78
msgid ""
"```\n"
"[a b] [c] [d e f] → [a b c d]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [a b c d]\n"
"```"

#: src/faq.md:80
msgid ""
"So they go to the miner who mined the block as fees. [The BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) has the details, but in short, fees "
"paid by transactions are treated as extra inputs to the coinbase transaction, and are ordered how their corresponding transactions are ordered in the block. "
"The coinbase transaction of the block might look like this:"
msgstr ""
"Поэтому они поступают майнеру, добывшему блок, в качестве вознаграждения. [В BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) описаны "
"подробности, но вкратце, комиссии, уплаченные транзакциями, рассматриваются как дополнительные входы в транзакцию coinbase и упорядочиваются так, как "
"упорядочены соответствующие им транзакции в блоке. Транзакция блока coinbase может выглядеть следующим образом:"

#: src/faq.md:87
msgid ""
"```\n"
"[SUBSIDY] [e f] → [SUBSIDY e f]\n"
"```"
msgstr ""
"```\n"
"[SUBSIDY] [e f] → [SUBSIDY e f]\n"
"```"

#: src/faq.md:89
msgid "Where can I find the nitty-gritty details?"
msgstr "Где я могу найти подробную информацию?"

#: src/faq.md:92
msgid "[The BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[В BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src/faq.md:94
msgid "Why are sat inscriptions called \"digital artifacts\" instead of \"NFTs\"?"
msgstr "Почему sat надписи называются \"цифровыми артефактами\", а не \"NFT\"?"

#: src/faq.md:97
msgid "An inscription is an NFT, but the term \"digital artifact\" is used instead, because it's simple, suggestive, and familiar."
msgstr "Надпись - это NFT, но вместо нее используется термин \"цифровой артефакт\", поскольку он прост, наводит на размышления и хорошо знаком."

#: src/faq.md:100
msgid ""
"The phrase \"digital artifact\" is highly suggestive, even to someone who has never heard the term before. In comparison, NFT is an acronym, and doesn't "
"provide any indication of what it means if you haven't heard the term before."
msgstr ""
"Фраза \"цифровой артефакт\" наводит на размышления даже тех, кто никогда раньше не слышал этого термина. Для сравнения, NFT - это аббревиатура, которая не дает "
"никаких указаний на то, что она означает, если вы не слышали этот термин раньше."

#: src/faq.md:104
msgid ""
"Additionally, \"NFT\" feels like financial terminology, and the both word \"fungible\" and sense of the word \"token\" as used in \"NFT\" is uncommon outside "
"of financial contexts."
msgstr ""
"Кроме того, в \"NFT\" чувствуется финансовая терминология, а слова \"fungible\" и смысл слова \"token\", используемые в \"NFT\", нередки вне финансового "
"контекста."

#: src/faq.md:108
msgid "How do sat inscriptions compare to…"
msgstr "Чем sat-надписи отличаются от…"

#: src/faq.md:111
msgid "Ethereum NFTs?"
msgstr "NFT на Ethereum?"

#: src/faq.md:113
msgid "_Inscriptions are always immutable._"
msgstr "_Надписи всегда неизменны._"

#: src/faq.md:115
msgid "There is simply no way to for the creator of an inscription, or the owner of an inscription, to modify it after it has been created."
msgstr "Для создателя надписи или ее владельца просто не существует возможности изменить ее после создания."

#: src/faq.md:118
msgid "Ethereum NFTs _can_ be immutable, but many are not, and can be changed or deleted by the NFT contract owner."
msgstr "Ethereum NFT _могут_ быть неизменяемыми, но многие из них не являются таковыми и могут быть изменены или удалены владельцем NFT контракта."

#: src/faq.md:121
msgid ""
"In order to make sure that a particular Ethereum NFT is immutable, the contract code must be audited, which requires detailed knowledge of the EVM and Solidity "
"semantics."
msgstr ""
"Для того чтобы убедиться в неизменяемости конкретного Ethereum NFT, необходимо провести аудит кода контракта, что требует детального знания семантики EVM и "
"Solidity."

#: src/faq.md:125
msgid ""
"It is very hard for a non-technical user to determine whether or not a given Ethereum NFT is mutable or immutable, and Ethereum NFT platforms make no effort to "
"distinguish whether an NFT is mutable or immutable, and whether the contract source code is available and has been audited."
msgstr ""
"Нетехническому пользователю очень сложно определить, является ли данный Ethereum NFT изменяемым или неизменяемым, а платформы Ethereum NFT не предпринимают "
"никаких усилий, чтобы отличить, является ли NFT изменяемым или неизменяемым, а также доступен ли исходный код контракта и прошел ли он аудит."

#: src/faq.md:130
msgid "_Inscription content is always on-chain._"
msgstr "_Содержимое надписей всегда находится on-chain._"

#: src/faq.md:132
msgid ""
"There is no way for an inscription to refer to off-chain content. This makes inscriptions more durable, because content cannot be lost, and scarcer, because "
"inscription creators must pay fees proportional to the size of the content."
msgstr ""
"Надпись не может ссылаться на off-chain контент. Это делает надписи более долговечными, поскольку контент не может быть потерян, и более редкими, поскольку "
"создатели надписей должны платить за них комиссию, пропорциональную размеру контента."

#: src/faq.md:136
msgid ""
"Some Ethereum NFT content is on-chain, but much is off-chain, and is stored on platforms like IPFS or Arweave, or on traditional, fully centralized web "
"servers. Content on IPFS is not guaranteed to continue to be available, and some NFT content stored on IPFS has already been lost. Platforms like Arweave rely "
"on weak economic assumptions, and will likely fail catastrophically when these economic assumptions are no longer met. Centralized web servers may disappear at "
"any time."
msgstr ""
"Часть контента Ethereum NFT находится on-chain, но большая часть - off-chain, и хранится на таких платформах, как IPFS или Arweave, или на традиционных, "
"полностью централизованных веб-серверах. Доступность контента на IPFS не гарантирована, а часть NFT-контента, хранящегося на IPFS, уже потеряна. Платформы типа "
"Arweave опираются на слабые экономические предположения и, скорее всего, потерпят катастрофический крах, когда эти экономические предположения перестанут "
"выполняться. Централизованные веб-серверы могут исчезнуть в любой момент."

#: src/faq.md:144
msgid "It is very hard for a non-technical user to determine where the content of a given Ethereum NFT is stored."
msgstr "Нетехническому пользователю очень сложно определить, где хранится содержимое того или иного Ethereum NFT."

#: src/faq.md:147
msgid "_Inscriptions are much simpler._"
msgstr "_Надписи гораздо проще._"

#: src/faq.md:149
msgid ""
"Ethereum NFTs depend on the Ethereum network and virtual machine, which are highly complex, constantly changing, and which introduce changes via backwards-"
"incompatible hard forks."
msgstr ""
"Ethereum NFT зависит от сети и виртуальной машины Ethereum, которые очень сложны, постоянно меняются и вносят изменения через обратно-несовместимые хард форки."

#: src/faq.md:153
msgid ""
"Inscriptions, on the other hand, depend on the Bitcoin blockchain, which is relatively simple and conservative, and which introduces changes via backwards-"
"compatible soft forks."
msgstr ""
"С другой стороны, надписи зависят от блокчейна Bitcoin, который относительно прост и консервативен, а изменения в него вносятся через обратно совместимые софт "
"форки."

#: src/faq.md:157
msgid "_Inscriptions are more secure._"
msgstr "_Надписи более надежны._"

#: src/faq.md:159
msgid ""
"Inscriptions inherit Bitcoin's transaction model, which allow a user to see exactly which inscriptions are being transferred by a transaction before they sign "
"it. Inscriptions can be offered for sale using partially signed transactions, which don't require allowing a third party, such as an exchange or marketplace, "
"to transfer them on the user's behalf."
msgstr ""
"Надписи наследуют модель Bitcoin транзакций, позволяющую пользователю до подписания транзакции видеть, какие именно надписи передаются по ней. Надписи могут "
"быть выставлены на продажу с помощью частично подписанных транзакций, которые не требуют разрешения третьей стороне, например бирже или торговой площадке, "
"передавать их от имени пользователя."

#: src/faq.md:165
msgid ""
"By comparison, Ethereum NFTs are plagued with end-user security vulnerabilities. It is commonplace to blind-sign transactions, grant third-party apps unlimited "
"permissions over a user's NFTs, and interact with complex and unpredictable smart contracts. This creates a minefield of hazards for Ethereum NFT users which "
"are simply not a concern for ordinal theorists."
msgstr ""
"Для сравнения, NFT на Ethereum страдают от уязвимостей безопасности конечных пользователей. Обычным явлением является слепая подпись транзакций, предоставление "
"сторонним приложениям неограниченных прав доступа к NFT пользователя, а также взаимодействие со сложными и непредсказуемыми смарт-контрактами. Это создает "
"минное поле опасностей для пользователей Ethereum NFT, которые просто не волнуют ordinal теоретиков."

#: src/faq.md:171
msgid "_Inscriptions are scarcer._"
msgstr "_Надписи встречаются все реже._"

#: src/faq.md:173
msgid ""
"Inscriptions require bitcoin to mint, transfer, and store. This seems like a downside on the surface, but the raison d'etre of digital artifacts is to be "
"scarce and thus valuable."
msgstr ""
"Для нанесения надписей, их передачи и хранения требуется биткоин. На первый взгляд, это недостаток, но смысл существования цифровых артефактов заключается в "
"том, чтобы быть дефицитными и, следовательно, ценными."

#: src/faq.md:177
msgid ""
"Ethereum NFTs, on the other hand, can be minted in virtually unlimited qualities with a single transaction, making them inherently less scarce, and thus, "
"potentially less valuable."
msgstr ""
"С другой стороны, Ethereum NFT можно майнить практически в неограниченном количестве за одну транзакцию, что делает их по своей сути менее дефицитными, а "
"значит, потенциально менее ценными."

#: src/faq.md:181
msgid "_Inscriptions do not pretend to support on-chain royalties._"
msgstr "_Надписи не претендуют на поддержку on-chain роялти._"

#: src/faq.md:183
msgid ""
"On-chain royalties are a good idea in theory but not in practice. Royalty payment cannot be enforced on-chain without complex and invasive restrictions. The "
"Ethereum NFT ecosystem is currently grappling with confusion around royalties, and is collectively coming to grips with the reality that on-chain royalties, "
"which were messaged to artists as an advantage of NFTs, are not possible, while platforms race to the bottom and remove royalty support."
msgstr ""
"On-chain Роялти - хорошая идея в теории, но не на практике. Выплата on-chain роялти не может быть обеспечена без сложных и инвазивных ограничений. Экосистема "
"Ethereum NFT в настоящее время борется с путаницей вокруг роялти и коллективно приходит к пониманию того, что on-chain роялти, которые были заявлены художникам "
"в качестве преимущества NFT, невозможны, в то время как платформы гонятся за дном и убирают поддержку роялти."

#: src/faq.md:190
msgid ""
"Inscriptions avoid this situation entirely by making no false promises of supporting royalties on-chain, thus avoiding the confusion, chaos, and negativity of "
"the Ethereum NFT situation."
msgstr ""
"Надписи полностью исключают подобную ситуацию, не давая ложных обещаний о поддержке on-chain роялти, что позволяет избежать путаницы, хаоса и негатива, "
"характерных для ситуации с NFT на Ethereum."

#: src/faq.md:194
msgid "_Inscriptions unlock new markets._"
msgstr "_Надписи открывают новые рынки._"

#: src/faq.md:196
msgid ""
"Bitcoin's market capitalization and liquidity are greater than Ethereum's by a large margin. Much of this liquidity is not available to Ethereum NFTs, since "
"many Bitcoiners prefer not to interact with the Ethereum ecosystem due to concerns related to simplicity, security, and decentralization."
msgstr ""
"Рыночная капитализация и ликвидность Bitcoin значительно превосходят Ethereum за счёт большей маржи. Значительная часть этой ликвидности недоступна для "
"Ethereum NFT, поскольку многие Bitcoiners предпочитают не взаимодействовать с экосистемой Ethereum из-за опасений, связанных с простотой, безопасностью и "
"децентрализацией."

#: src/faq.md:201
msgid "Such Bitcoiners may be more interested in inscriptions than Ethereum NFTs, unlocking new classes of collector."
msgstr "Такие Bitcoiners могут быть более заинтересованы в надписях, чем в Ethereum NFT, что открывает новые классы коллекционеров."

#: src/faq.md:204
msgid "_Inscriptions have a richer data model._"
msgstr "_Надписи имеют более богатую модель данных._"

#: src/faq.md:206
msgid ""
"Inscriptions consist of a content type, also known as a MIME type, and content, which is an arbitrary byte string. This is the same data model used by the web, "
"and allows inscription content to evolve with the web, and come to support any kind of content supported by web browsers, without requiring changes to the "
"underlying protocol."
msgstr ""
"Надписи состоят из типа содержимого, известного также как тип-MIME, и содержимого, представляющего собой произвольную байтовую строку. Это та же модель данных, "
"которая используется в web, и позволяет содержимому надписей развиваться вместе с web и поддерживать любой тип содержимого, поддерживаемый браузерами, не "
"требуя изменений в базовом протоколе."

#: src/faq.md:212
msgid "RGB and Taro assets?"
msgstr "RGB и активы Taro?"

#: src/faq.md:214
msgid "RGB and Taro are both second-layer asset protocols built on Bitcoin. Compared to inscriptions, they are much more complicated, but much more featureful."
msgstr ""
"RGB и Taro - протоколы активов второго уровня, построенные на базе Bitcoin. По сравнению с надписями они гораздо сложнее, но и гораздо более функциональны."

#: src/faq.md:217
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, whereas the primary use-case of RGB and Taro are fungible tokens, so the user "
"experience for inscriptions is likely to be simpler and more polished than the user experience for RGB and Taro NFTs."
msgstr ""
"Ordinal theory была разработана с нуля для цифровых артефактов, в то время как основной сферой использования RGB и Taro являются взаимозаменяемые токены, "
"поэтому пользовательский опыт для надписей, скорее всего, будет более простым и отточенным, чем пользовательский опыт для NFT RGB и Taro."

#: src/faq.md:222
msgid ""
"RGB and Taro both store content off-chain, which requires additional infrastructure, and which may be lost. By contrast, inscription content is stored on-"
"chain, and cannot be lost."
msgstr ""
"RGB и Taro хранят off-chain контент, что требует дополнительной инфраструктуры и может быть утеряно. В отличие от них, содержимое надписей хранится on-chain и "
"не может быть потеряно."

#: src/faq.md:226
msgid ""
"Ordinal theory, RGB, and Taro are all very early, so this is speculation, but ordinal theory's focus may give it the edge in terms of features for digital "
"artifacts, including a better content model, and features like globally unique symbols."
msgstr ""
"Ordinal theory, RGB и Taro находятся на очень ранних стадиях развития, поэтому это лишь предположение, но то, что ordinal theory уделяет больше внимания "
"цифровым артефактам, может дать ей преимущество в плане возможностей для них, включая лучшую модель контента и такие возможности, как глобально уникальные "
"символы."

#: src/faq.md:231
msgid "Counterparty assets?"
msgstr "Активы Counterparty?"

#: src/faq.md:233
msgid ""
"Counterparty has its own token, XCP, which is required for some functionality, which makes most bitcoiners regard it as an altcoin, and not an extension or "
"second layer for bitcoin."
msgstr ""
"У Counterparty есть собственный токен XCP, который необходим для работы с некоторыми функциями, что заставляет большинство bitcoiners рассматривать его как "
"альткоин, а не как расширение или второй уровень биткоина."

#: src/faq.md:237
msgid "Ordinal theory has been designed from the ground up for digital artifacts, whereas Counterparty was primarily designed for financial token issuance."
msgstr ""
"Ordinal theory была разработана с нуля для цифровых артефактов, в то время как Counterparty в первую очередь предназначалась для выпуска финансовых токенов."

#: src/faq.md:240
msgid "Inscriptions for…"
msgstr "Надписи для…"

#: src/faq.md:243
msgid "Artists"
msgstr "Художников"

#: src/faq.md:245
msgid ""
"_Inscriptions are on Bitcoin._ Bitcoin is the digital currency with the highest status and greatest chance of long-term survival. If you want to guarantee that "
"your art survives into the future, there is no better way to publish it than as inscriptions."
msgstr ""
"_Надписи на Bitcoin._ Bitcoin - это цифровая валюта с самым высоким статусом и наибольшими шансами на долгосрочное выживание. Если вы хотите гарантировать, что "
"ваше искусство сохранится в будущем, нет лучшего способа опубликовать его, чем в виде надписей."

#: src/faq.md:250
msgid ""
"_Cheaper on-chain storage._ At $20,000 per BTC and the minimum relay fee of 1 satoshi per vbyte, publishing inscription content costs $50 per 1 million bytes."
msgstr ""
"_Более дешевое on-chain хранение._ При цене $20 000 за BTC и минимальной комиссии за ретрансляцию в размере 1 сатоши за один байт публикация on-chain контента "
"обходится в $50 за 1 млн байт."

#: src/faq.md:254
msgid ""
"_Inscriptions are early!_ Inscriptions are still in development, and have not yet launched on mainnet. This gives you an opportunity to be an early adopter, "
"and explore the medium as it evolves."
msgstr ""
"_Надписи все еще являются ранними!_ Надписи находятся в стадии разработки и еще не запущены в основной сети. Это дает вам возможность стать ранним "
"последователем и изучать среду по мере ее развития."

#: src/faq.md:258
msgid "_Inscriptions are simple._ Inscriptions do not require writing or understanding smart contracts."
msgstr "_Надписи просты._ Надписи не требуют написания или понимания смарт-контрактов."

#: src/faq.md:261
msgid "_Inscriptions unlock new liquidity._ Inscriptions are more accessible and appealing to bitcoin holders, unlocking an entirely new class of collector."
msgstr ""
"_Надписи открывают новую ликвидность._ Надписи становятся более доступными и привлекательными для держателей биткоинов, открывая совершенно новый класс "
"коллекционеров."

#: src/faq.md:264
msgid ""
"_Inscriptions are designed for digital artifacts._ Inscriptions are designed from the ground up to support NFTs, and feature a better data model, and features "
"like globally unique symbols and enhanced provenance."
msgstr ""
"_Надписи предназначены для цифровых артефактов._ Надписи разработаны с нуля для поддержки NFT, имеют улучшенную модель данных, а также такие функции, как "
"глобально уникальные символы и улучшенное подтверждение подлинности."

#: src/faq.md:268
msgid ""
"_Inscriptions do not support on-chain royalties._ This is negative, but only depending on how you look at it. On-chain royalties have been a boon for creators, "
"but have also created a huge amount of confusion in the Ethereum NFT ecosystem. The ecosystem now grapples with this issue, and is engaged in a race to the "
"bottom, towards a royalties-optional future. Inscriptions have no support for on-chain royalties, because they are technically infeasible. If you choose to "
"create inscriptions, there are many ways you can work around this limitation: withhold a portion of your inscriptions for future sale, to benefit from future "
"appreciation, or perhaps offer perks for users who respect optional royalties."
msgstr ""
"_Надписи не поддерживают on-chain роялти._ Это негативный момент, но только в зависимости от того, как на него посмотреть. On-chain роялти стали благом для "
"создателей, но в то же время внесли огромную путаницу в экосистему NFT на Ethereum. В настоящее время экосистема решает эту проблему и участвует в гонке на "
"понижение, стремясь к будущему без роялти. Надписи не поддерживают on-chain роялти, поскольку это технически неосуществимо. Если вы решите создавать надписи, "
"есть много способов обойти это ограничение: придержать часть надписей для будущей продажи, чтобы получить выгоду от будущего роста стоимости, или, возможно, "
"предложить привилегии для пользователей, соблюдающих необязательные роялти."

#: src/faq.md:279
msgid "Collectors"
msgstr "Коллекционеры"

#: src/faq.md:281
msgid "_Inscriptions are simple, clear, and have no surprises._ They are always immutable and on-chain, with no special due diligence required."
msgstr "_Надписи просты, понятны и не содержат сюрпризов._ Они всегда неизменны и находятсяon-chain, не требуя специальной проверки."

#: src/faq.md:284
msgid "_Inscriptions are on Bitcoin._ You can verify the location and properties of inscriptions easily with Bitcoin full node that you control."
msgstr "_Надписи находятся на Bitcoin._ Вы можете легко проверить местоположение и свойства надписей с помощью Bitcoin fill node, который вы контролируете."

#: src/faq.md:287
msgid "Bitcoiners"
msgstr "Bitcoiners"

#: src/faq.md:289
msgid ""
"Let me begin this section by saying: the most important thing that the Bitcoin network does is decentralize money. All other use-cases are secondary, including "
"ordinal theory. The developers of ordinal theory understand and acknowledge this, and believe that ordinal theory helps, at least in a small way, Bitcoin's "
"primary mission."
msgstr ""
"Начну этот раздел с того, что самое главное, что делает сеть Bitcoin, - это децентрализация денег. Все остальные варианты использования, в том числе и ordinal "
"theory, являются вторичными. Разработчики ordinal theory понимают и признают это, и считают, что ordinal theory хотя бы в малой степени способствует выполнению "
"основной миссии Биткойна."

#: src/faq.md:295
msgid ""
"Unlike many other things in the altcoin space, digital artifacts have merit. There are, of course, a great deal of NFTs that are ugly, stupid, and fraudulent. "
"However, there are many that are fantastically creative, and creating and collecting art has been a part of the human story since its inception, and predates "
"even trade and money, which are also ancient technologies."
msgstr ""
"В отличие от многих других вещей в альткоин пространстве, цифровые артефакты имеют свои достоинства. Конечно, существует огромное количество NFT, которые "
"являются уродливыми, глупыми и мошенническими. Однако есть и множество фантастически креативных, а создание и коллекционирование произведений искусства было "
"частью истории человечества с момента его зарождения и предшествовало даже торговле и деньгам, которые также являются древними технологиями."

#: src/faq.md:302
msgid ""
"Bitcoin provides an amazing platform for creating and collecting digital artifacts in a secure, decentralized way, that protects users and artists in the same "
"way that it provides an amazing platform for sending and receiving value, and for all the same reasons."
msgstr ""
"Bitcoin предоставляет потрясающую платформу для создания и сбора цифровых артефактов безопасным, децентрализованным способом, который защищает пользователей и "
"художников точно так же, как и платформа для отправки и получения ценностей, и по всем тем же причинам."

#: src/faq.md:307
msgid ""
"Ordinals and inscriptions increase demand for Bitcoin block space, which increase Bitcoin's security budget, which is vital for safeguarding Bitcoin's "
"transition to a fee-dependent security model, as the block subsidy is halved into insignificance."
msgstr ""
"Ordinals и надписи увеличивают спрос на место в блоке Bitcoin, что увеличивает бюджет Bitcoin безопасности, который крайне важен для обеспечения перехода "
"Bitcoin на платно-зависимую модель безопасности, так как субсидия на блок сокращается вдвое и становится незначительной."

#: src/faq.md:312
msgid ""
"Inscription content is stored on-chain, and thus the demand for block space for use in inscriptions is unlimited. This creates a buyer of last resort for _all_ "
"Bitcoin block space. This will help support a robust fee market, which ensures that Bitcoin remains secure."
msgstr ""
"Содержимое надписей хранится on-chain, и поэтому спрос на блокчейн для использования в надписях неограничен. Это создает покупателя последней инстанции для "
"_всего_ блокчейна Bitcoin. Это поможет поддержать надежный рынок вознаграждений, что обеспечит безопасность биткойна."

#: src/faq.md:317
msgid ""
"Inscriptions also counter the narrative that Bitcoin cannot be extended or used for new use-cases. If you follow projects like DLCs, Fedimint, Lightning, Taro, "
"and RGB, you know that this narrative is false, but inscriptions provide a counter argument which is easy to understand, and which targets a popular and proven "
"use case, NFTs, which makes it highly legible."
msgstr ""
"Надписи также противостоят утверждению, что Bitcoin не может быть расширен или использован для новых целей. Если вы следите за такими проектами, как DLCs, "
"Fedimint, Lightning, Taro и RGB, вы знаете, что это утверждение неверно, но надписи предоставляют контраргумент, который легко понять, и который нацелен на "
"популярный и проверенный пример использования - NFT, что делает его весьма наглядным."

#: src/faq.md:323
msgid ""
"If inscriptions prove, as the authors hope, to be highly sought after digital artifacts with a rich history, they will serve as a powerful hook for Bitcoin "
"adoption: come for the fun, rich art, stay for the decentralized digital money."
msgstr ""
"Если надписи, как надеются авторы, окажутся востребованными цифровыми артефактами с богатой историей, они послужат мощным крючком для внедрения Bitcoin: "
"приходите за интересным, богатым искусством, оставайтесь за децентрализованными цифровыми деньгами."

#: src/faq.md:327
msgid ""
"Inscriptions are an extremely benign source of demand for block space. Unlike, for example, stablecoins, which potentially give large stablecoin issuers "
"influence over the future of Bitcoin development, or DeFi, which might centralize mining by introducing opportunities for MEV, digital art and collectables on "
"Bitcoin, are unlikely to produce individual entities with enough power to corrupt Bitcoin. Art is decentralized."
msgstr ""
"Надписи - это исключительно доброкачественный источник спроса на блокчейн. В отличие, например, от стабильных монеток, которые потенциально дают крупным "
"эмитентам стабильных монеток возможность влиять на будущее развитие Биткоина, или DeFi, которые могут централизовать майнинг, создавая возможности для MEV, "
"цифровое искусство и коллекционные предметы на Биткоине вряд ли приведут к появлению отдельных субъектов, обладающих достаточной властью, чтобы коррумпировать "
"Биткоин. Искусство децентрализовано."

#: src/faq.md:334
msgid ""
"Inscription users and service providers are incentivized to run Bitcoin full nodes, to publish and track inscriptions, and thus throw their economic weight "
"behind the honest chain."
msgstr ""
"Пользователи надписей и поставщики услуг получают стимул запускать Bitcoin full nodes, публиковать и отслеживать надписи и тем самым поддерживать честную "
"цепочку своим экономическим весом."

#: src/faq.md:338
msgid "Ordinal theory and inscriptions do not meaningfully affect Bitcoin's fungibility. Bitcoin users can ignore both and be unaffected."
msgstr ""
"Ordinal theory и надписи не оказывают существенного влияния на взаимозаменяемость Биткоина. Пользователи Биткоина могут игнорировать и то, и другое, и это их "
"не затронет."

#: src/faq.md:341
msgid ""
"We hope that ordinal theory strengthens and enriches bitcoin, and gives it another dimension of appeal and functionality, enabling it more effectively serve "
"its primary use case as humanity's decentralized store of value."
msgstr ""
"Мы надеемся, что ordinal theory укрепит и обогатит Биткоин, придаст ему еще одно измерение привлекательности и функциональности, это позволит ему более "
"эффективно выполнять свою основную функцию децентрализованного хранилища ценностей человечества."

#: src/contributing.md:1
msgid "Contributing to `ord`"
msgstr "Вклад в развитие `ord`"

#: src/contributing.md:4
msgid "Suggested Steps"
msgstr "Предлагаемые шаги"

#: src/contributing.md:7
msgid "Find an issue you want to work on."
msgstr "Найдите проблему, над которой вы хотите поработать."

#: src/contributing.md:8
msgid ""
"Figure out what would be a good first step towards resolving the issue. This could be in the form of code, research, a proposal, or suggesting that it be "
"closed, if it's out of date or not a good idea in the first place."
msgstr ""
"Определите, что будет хорошим первым шагом к решению проблемы. Это может быть в форме кода, исследование, коммерческое предложение или предложение закрыть "
"проблему, если она устарела или вообще не является хорошей идеей."

#: src/contributing.md:11
msgid ""
"Comment on the issue with an outline of your suggested first step, and asking for feedback. Of course, you can dive in and start writing code or tests "
"immediately, but this avoids potentially wasted effort, if the issue is out of date, not clearly specified, blocked on something else, or otherwise not ready "
"to implement."
msgstr ""
"Прокомментируйте проблему, изложив в общих чертах свой первый шаг, и попросите откликнуться. Конечно, можно сразу же приступить к написанию кода или тестов, но "
"это позволит избежать потенциально напрасных усилий, если проблема устарела, нечетко сформулирована, заблокирована на чем-то другом или иным образом не готова "
"к реализации."

#: src/contributing.md:16
msgid ""
"If the issue requires a code change or bugfix, open a draft PR with tests, and ask for feedback. This makes sure that everyone is on the same page about what "
"needs to be done, or what the first step in solving the issue should be. Also, since tests are required, writing the tests first makes it easy to confirm that "
"the change can be tested easily."
msgstr ""
"Если проблема требует изменения кода или исправления ошибки, откройте PR проект с тестами и попросите высказать свое мнение. Это позволит убедиться, что все "
"согласны с тем, что нужно сделать, или с тем, каким должен быть первый шаг в решении проблемы. Кроме того, поскольку тесты необходимы, написание тестов вначале "
"позволяет легко убедиться в том, что изменение можно легко протестировать."

#: src/contributing.md:21
msgid "Mash the keyboard randomly until the tests pass, and refactor until the code is ready to submit."
msgstr "Нажимайте на клавиатуру в произвольном порядке, пока тесты не пройдут, и дорабатывайте код до тех пор, пока он не будет готов к отправке."

#: src/contributing.md:23
msgid "Mark the PR as ready to review."
msgstr "Пометьте PR как готовый к рассмотрению."

#: src/contributing.md:24
msgid "Revise the PR as needed."
msgstr "Пересмотрите PR по мере необходимости."

#: src/contributing.md:25
msgid "And finally, mergies!"
msgstr "И, наконец, слияние!"

#: src/contributing.md:27
msgid "Start small"
msgstr "Начните с малого"

#: src/contributing.md:30
msgid "Small changes will allow you to make an impact quickly, and if you take the wrong tack, you won't have wasted much time."
msgstr "Небольшие изменения позволят вам быстро добиться результата, а если вы выберете неверный путь, то не потеряете много времени."

#: src/contributing.md:33
msgid "Ideas for small issues:"
msgstr "Идеи для решения мелких проблем:"

#: src/contributing.md:34
msgid "Add a new test or test case that increases test coverage"
msgstr "Добавление нового теста или тестового случая, увеличивающего площадь покрытия теста"

#: src/contributing.md:35
msgid "Add or improve documentation"
msgstr "Добавление или улучшение документации"

#: src/contributing.md:36
msgid "Find an issue that needs more research, and do that research and summarize it in a comment"
msgstr "Найдите проблему, которая нуждается в дополнительном исследовании, проведите это исследование и обобщите его в комментарии"

#: src/contributing.md:38
msgid "Find an out-of-date issue and comment that it can be closed"
msgstr "Найти устаревший вопрос и прокомментировать, что его можно закрыть"

#: src/contributing.md:39
msgid "Find an issue that shouldn't be done, and provide constructive feedback detailing why you think that is the case"
msgstr "Найдите проблему, которую не следует решать, и предоставьте конструктивный фидбек, подробно объяснив, почему вы так считаете"

#: src/contributing.md:42
msgid "Merge early and often"
msgstr "Слияние на ранней стадии и часто"

#: src/contributing.md:45
msgid ""
"Break up large tasks into multiple smaller steps that individually make progress. If there's a bug, you can open a PR that adds a failing ignored test. This "
"can be merged, and the next step can be to fix the bug and unignore the test. Do research or testing, and report on your results. Break a feature into small "
"sub-features, and implement them one at a time."
msgstr ""
"Разбивайте большие задачи на множество мелких шагов, которые по отдельности обеспечивают прогресс. Если есть ошибка, можно открыть PR с добавлением "
"игнорируемого теста. Он может быть объединен, и следующим шагом может быть исправление ошибки и снятие игнорирования теста. Проведите исследование или "
"тестирование и сообщите о результатах. Разбейте функцию на небольшие подфункции и реализуйте их по одной."

#: src/contributing.md:51
msgid ""
"Figuring out how to break down a larger PR into smaller PRs where each can be merged is an art form well-worth practicing. The hard part is that each PR must "
"itself be an improvement."
msgstr ""
"Понять, как разбить большой PR на более мелкие, чтобы каждый из них можно было объединить, - это целое искусство, которым стоит заниматься. Сложность "
"заключается в том, что каждый PR должен сам по себе быть улучшением."

#: src/contributing.md:55
msgid "I strive to follow this advice myself, and am always better off when I do."
msgstr "Я и сам стараюсь следовать этому совету и всегда чувствую себя лучше."

#: src/contributing.md:57
msgid ""
"Small changes are fast to write, review, and merge, which is much more fun than laboring over a single giant PR that takes forever to write, review, and merge. "
"Small changes don't take much time, so if you need to stop working on a small change, you won't have wasted much time as compared to a larger change that "
"represents many hours of work. Getting a PR in quickly improves the project a little bit immediately, instead of having to wait a long time for larger "
"improvement. Small changes are less likely to accumulate merge conflict. As the Athenians said: _The fast commit what they will, the slow merge what they must._"
msgstr ""
"Небольшие изменения быстро пишутся, рецензируются и объединяются, что гораздо интереснее, чем трудиться над одним огромным PR, на написание, рецензирование и "
"объединение которого уходит целая вечность. Небольшие изменения не занимают много времени, поэтому, если вам нужно прекратить работу над небольшим изменением, "
"вы не потеряете много времени, по сравнению с большим изменением, которое представляет собой многочасовую работу. Быстрое внесение PR улучшает проект сразу же, "
"вместо того чтобы долго ждать более значительных улучшений. Небольшие изменения с меньшей вероятностью приведут к конфликту в процессе слияния. Как говорили "
"Афиняне: _Быстрые фиксируют то, что хотят, медленные сливают то, что должны._"

#: src/contributing.md:67
msgid "Get help"
msgstr "Обратиться за помощью"

#: src/contributing.md:70
msgid "If you're stuck for more than 15 minutes, ask for help, like a Rust Discord, Stack Exchange, or in a project issue or discussion."
msgstr "Если вы застряли более чем на 15 минут, попросите помощи, например, в Rust Discord, Stack Exchange или в чате обсуждения проекта."

#: src/contributing.md:73
msgid "Practice hypothesis-driven debugging"
msgstr "Практика отладки, основанной на гипотезах"

#: src/contributing.md:76
msgid ""
"Formulate a hypothesis as to what is causing the problem. Figure out how to test that hypothesis. Perform that tests. If it works, great, you fixed the issue "
"or now you know how to fix the issue. If not, repeat with a new hypothesis."
msgstr ""
"Сформулируйте гипотезу о том, что является причиной проблемы. Придумайте, как проверить эту гипотезу. Проведите тестирование. Если она работает, то вы "
"устранили проблему или теперь знаете, как ее устранить. Если нет, повторите тест с новой гипотезой."

#: src/contributing.md:81
msgid "Pay attention to error messages"
msgstr "Обращайте внимание на сообщения об ошибке"

#: src/contributing.md:84
msgid "Read all error messages and don't tolerate warnings."
msgstr "Читайте все сообщения об ошибке и не миритесь с предупреждениями."

#: src/donate.md:4
msgid ""
"Ordinals is open-source and community funded. The current lead maintainer of `ord` is [raphjaph](https://github.com/raphjaph/). Raph's work on `ord` is "
"entirely funded by donations. If you can, please consider donating!"
msgstr ""
"Ordinals имеет открытый исходный код и финансируется сообществом. В настоящее время ведущий специалист по сопровождению `ord` это [raphjaph](https://github.com/"
"raphjaph/). Работа Рафа над `ord` полностью финансируется за счет пожертвований. Если вы можете, пожалуйста, рассмотрите возможность пожертвования!"

#: src/donate.md:8
msgid ""
"The donation address for Bitcoin is [**************************************************************](https://mempool.space/address/"
"**************************************************************). The donation address for inscriptions is "
"[**************************************************************](https://mempool.space/address/**************************************************************)."
msgstr ""
"Адрес для пожертвований в Bitcoin [**************************************************************](https://mempool.space/address/"
"**************************************************************). Адрес для пожертвований в виде надписей "
"[**************************************************************](https://mempool.space/address/**************************************************************)."

#: src/donate.md:11
msgid ""
"Both addresses are in a 2 of 4 multisig wallet with keys held by [raphjaph](https://twitter.com/raphjaph), [erin](https://twitter.com/realizingerin), [rodarmor]"
"(https://twitter.com/rodarmor), and [ordinally](https://twitter.com/veryordinally)."
msgstr ""
"Оба адреса находятся в 2 из 4 мультисиг кошельке, ключи от которого принадлежат [raphjaph](https://twitter.com/raphjaph), [erin](https://twitter.com/"
"realizingerin), [rodarmor](https://twitter.com/rodarmor), и [ordinally](https://twitter.com/veryordinally)."

#: src/donate.md:17
msgid "Donations received will go towards funding maintenance and development of `ord`, as well as hosting costs for [ordinals.com](https://ordinals.com)."
msgstr "Полученные пожертвования пойдут на поддержание и развитие `ord`, а также на оплату хостинга для [ordinals.com](https://ordinals.com)."

#: src/donate.md:20
msgid "Thank you for donating!"
msgstr "Благодарим Вас за пожертвование!"

#: src/guides.md:1
msgid "Ordinal Theory Guides"
msgstr "Руководство по ordinal theory"

#: src/guides.md:4
msgid "See the table of contents for a list of guides, including a guide to the explorer, a guide for sat hunters, and a guide to inscriptions."
msgstr "Список руководств приведен в оглавлении, включая руководство для обозревателя, руководство для охотников на sat и руководство по надписям."

#: src/guides/explorer.md:1
msgid "Ordinal Explorer"
msgstr "Ordinal обозреватель"

#: src/guides/explorer.md:4
msgid ""
"The `ord` binary includes a block explorer. We host a instance of the block explorer on mainnet at [ordinals.com](https://ordinals.com), and on signet at "
"[signet.ordinals.com](https://signet.ordinals.com)."
msgstr ""
"Двоичный код `ord` включает в себя обозреватель блоков. Мы размещаем экземпляр обозревателя блоков в mainnet по адресу [ordinals.com](https://ordinals.com), и "
"в signet по адресу [signet.ordinals.com](https://signet.ordinals.com)."

#: src/guides/explorer.md:8
msgid "Running The Explorer"
msgstr "Запуск обозревателя"

#: src/guides/explorer.md:9
msgid "The server can be run locally with:"
msgstr "Сервер может быть запущен локально с помощью команды:"

#: src/guides/explorer.md:11
msgid "`ord server`"
msgstr "`ord server`"

#: src/guides/explorer.md:13
msgid "To specify a port add the `--http-port` flag:"
msgstr "Для указания порта добавьте `--http-port` flag:"

#: src/guides/explorer.md:15
msgid "`ord server --http-port 8080`"
msgstr "`ord server --http-port 8080`"

#: src/guides/explorer.md:17
msgid "To enable the JSON-API endpoints add the `--enable-json-api` or `-e` flag:"
msgstr "Для включения конечных точек JSON-API добавьте `--enable-json-api` или `-e` flag:"

#: src/guides/explorer.md:19
msgid "`ord --enable-json-api server`"
msgstr "ord --enable-json-api server"

#: src/guides/explorer.md:21
msgid "To test how your inscriptions will look you can run:"
msgstr "Чтобы проверить, как будут выглядеть ваши надписи, вы можете запустить:"

#: src/guides/explorer.md:23
msgid "`ord preview <FILE1> <FILE2> ...`"
msgstr "`ord preview <FILE1> <FILE2> ...`"

#: src/guides/explorer.md:25
msgid "Search"
msgstr "Поиск"

#: src/guides/explorer.md:28
msgid "The search box accepts a variety of object representations."
msgstr "Поле поиска принимает различные представления объектов."

#: src/guides/explorer.md:30
msgid "Blocks"
msgstr "Блоки"

#: src/guides/explorer.md:32
msgid "Blocks can be searched by hash, for example, the genesis block:"
msgstr "Блоки можно искать по хэшу, например, блок genesis:"

#: src/guides/explorer.md:34
msgid ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://ordinals.com/search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"
msgstr ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://ordinals.com/search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"

#: src/guides/explorer.md:36
msgid "Transactions"
msgstr "Транзакции"

#: src/guides/explorer.md:38
msgid "Transactions can be searched by hash, for example, the genesis block coinbase transaction:"
msgstr "Транзакции можно искать по хэшу, например, транзакция coinbase на блоке genesis:"

#: src/guides/explorer.md:41
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"

#: src/guides/explorer.md:43
msgid "Outputs"
msgstr "Выходы"

#: src/guides/explorer.md:45
msgid "Transaction outputs can searched by outpoint, for example, the only output of the genesis block coinbase transaction:"
msgstr "Выходы транзакций могут искаться по выходной точке, например, единственный выход транзакции coinbase на блоке genesis:"

#: src/guides/explorer.md:48
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"

#: src/guides/explorer.md:50
msgid "Sats"
msgstr "Sats"

#: src/guides/explorer.md:52
msgid "Sats can be searched by integer, their position within the entire bitcoin supply:"
msgstr "Sats можно искать по целому числу, по их положению во всем запасе Биткоинов:"

#: src/guides/explorer.md:55
msgid "[2099994106992659](https://ordinals.com/search/2099994106992659)"
msgstr "[2099994106992659](https://ordinals.com/search/2099994106992659)"

#: src/guides/explorer.md:57
msgid "By decimal, their block and offset within that block:"
msgstr "По десятичному числу - по блоку и смещению внутри блока:"

#: src/guides/explorer.md:59
msgid "[481824.0](https://ordinals.com/search/481824.0)"
msgstr "[481824.0](https://ordinals.com/search/481824.0)"

#: src/guides/explorer.md:61
msgid "By degree, their cycle, blocks since the last halving, blocks since the last difficulty adjustment, and offset within their block:"
msgstr "По степени, их циклу, блокам с момента последнего халвинга, блокам с момента последней корректировки сложности и смещению внутри их блока:"

#: src/guides/explorer.md:64
msgid "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"
msgstr "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"

#: src/guides/explorer.md:66
msgid "By name, their base 26 representation using the letters \"a\" through \"z\":"
msgstr "По названию, их базовое представление 26 с использованием букв от \"a\" до \"z\":"

#: src/guides/explorer.md:68
msgid "[ahistorical](https://ordinals.com/search/ahistorical)"
msgstr "[внеисторический](https://ordinals.com/search/ahistorical)"

#: src/guides/explorer.md:70
msgid "Or by percentile, the percentage of bitcoin's supply that has been or will have been issued when they are mined:"
msgstr "Или по процентному соотношению - процент от количества Биткоинов, которые были или будут выпущены при их майнинге:"

#: src/guides/explorer.md:73
msgid "[100%](https://ordinals.com/search/100%)"
msgstr "[100%](https://ordinals.com/search/100%)"

#: src/guides/inscriptions.md:1
msgid "Ordinal Inscription Guide"
msgstr "Руководство по надписям ordinal"

#: src/guides/inscriptions.md:4
msgid ""
"Individual sats can be inscribed with arbitrary content, creating Bitcoin-native digital artifacts that can be held in a Bitcoin wallet and transferred using "
"Bitcoin transactions. Inscriptions are as durable, immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"На отдельные sats можно наносить надписи с произвольным содержанием, создавая цифровые артефакты, основанные на Биткоинах, которые можно хранить в биткоин-"
"кошельке и передавать с помощью биткоин-транзакций. Надписи столь же долговечны, неизменяемы, безопасны и децентрализованы, как и сам Биткоин."

#: src/guides/inscriptions.md:9
msgid ""
"Working with inscriptions requires a Bitcoin full node, to give you a view of the current state of the Bitcoin blockchain, and a wallet that can create "
"inscriptions and perform sat control when constructing transactions to send inscriptions to another wallet."
msgstr ""
"Для работы с надписями требуется Bitcoin full node, позволяющий видеть текущее состояние блокчейна Bitcoin, и кошелек, способный создавать надписи и "
"осуществлять sat контроль при построении транзакций для отправки надписей на другой кошелек."

#: src/guides/inscriptions.md:14
msgid "Bitcoin Core provides both a Bitcoin full node and wallet. However, the Bitcoin Core wallet cannot create inscriptions and does not perform sat control."
msgstr "Bitcoin Core предоставляет как Bitcoin full node, так и кошелек. Однако кошелек Bitcoin Core не может создавать надписи и не осуществляет sat контроль."

#: src/guides/inscriptions.md:17
msgid ""
"This requires [`ord`](https://github.com/ordinals/ord), the ordinal utility. `ord` doesn't implement its own wallet, so `ord wallet` subcommands interact with "
"Bitcoin Core wallets."
msgstr ""
"Для этого требуется [`ord`](https://github.com/ordinals/ord), ordinal utility. `ord` не реализует собственный кошелек, поэтому подкоманды `ord wallet` "
"взаимодействуют с кошельками Bitcoin Core."

#: src/guides/inscriptions.md:21
msgid "This guide covers:"
msgstr "В этом руководстве рассматриваются:"

#: src/guides/inscriptions.md:23 src/guides/inscriptions.md:39
msgid "Installing Bitcoin Core"
msgstr "Установка Bitcoin Core"

#: src/guides/inscriptions.md:24
msgid "Syncing the Bitcoin blockchain"
msgstr "Синхронизация блокчейна Bitcoin"

#: src/guides/inscriptions.md:25
msgid "Creating a Bitcoin Core wallet"
msgstr "Создание кошелька Bitcoin Core"

#: src/guides/inscriptions.md:26
msgid "Using `ord wallet receive` to receive sats"
msgstr "Использование `ord wallet receive` для получения sats"

#: src/guides/inscriptions.md:27
msgid "Creating inscriptions with `ord wallet inscribe`"
msgstr "Создание надписей с помощью `ord wallet inscribe`"

#: src/guides/inscriptions.md:28
msgid "Sending inscriptions with `ord wallet send`"
msgstr "Отправка надписей с помощью `ord wallet send`"

#: src/guides/inscriptions.md:29
msgid "Receiving inscriptions with `ord wallet receive`"
msgstr "Получение надписей с помощью `ord wallet receive`"

#: src/guides/inscriptions.md:31
msgid "Getting Help"
msgstr "Получение помощи"

#: src/guides/inscriptions.md:34
msgid ""
"If you get stuck, try asking for help on the [Ordinals Discord Server](https://discord.com/invite/87cjuz4FYg), or checking GitHub for relevant [issues](https://"
"github.com/ordinals/ord/issues) and [discussions](https://github.com/ordinals/ord/discussions)."
msgstr ""
"Если вы застряли, попробуйте обратиться за помощью к [Ordinals Discord Server](https://discord.com/invite/87cjuz4FYg), или проверить GitHub на наличие [проблем]"
"(https://github.com/ordinals/ord/issues) и для [обсуждений](https://github.com/ordinals/ord/discussions)."

#: src/guides/inscriptions.md:42
msgid "Bitcoin Core is available from [bitcoincore.org](https://bitcoincore.org/) on the [download page](https://bitcoincore.org/en/download/)."
msgstr "Установка Bitcoin Core доступна на сайте [bitcoincore.org](https://bitcoincore.org/) на [странице загрузки](https://bitcoincore.org/en/download/)."

#: src/guides/inscriptions.md:45
msgid "Making inscriptions requires Bitcoin Core 24 or newer."
msgstr "Для создания надписей требуется Bitcoin Core 24 или новее."

#: src/guides/inscriptions.md:47
msgid ""
"This guide does not cover installing Bitcoin Core in detail. Once Bitcoin Core is installed, you should be able to run `bitcoind -version` successfully from "
"the command line."
msgstr ""
"В этом руководстве установка Bitcoin Core подробно не рассматривается. Как только Bitcoin Core будет установлен, вы сможете успешно запустить`bitcoind -"
"version` из командной строки."

#: src/guides/inscriptions.md:51
msgid "Configuring Bitcoin Core"
msgstr "Настройка Bitcoin Core"

#: src/guides/inscriptions.md:54
msgid "`ord` requires Bitcoin Core's transaction index."
msgstr "Для выполнения `ord` требуется индекс транзакций Bitcoin Core."

#: src/guides/inscriptions.md:56
msgid "To configure your Bitcoin Core node to maintain a transaction index, add the following to your `bitcoin.conf`:"
msgstr "Чтобы настроить Bitcoin Core node на ведение индекса транзакций, добавьте следующее в `bitcoin.conf`:"

#: src/guides/inscriptions.md:59 src/guides/sat-hunting.md:30
msgid ""
"```\n"
"txindex=1\n"
"```"
msgstr ""
"```\n"
"txindex=1\n"
"```"

#: src/guides/inscriptions.md:63
msgid "Or, run `bitcoind` with `-txindex`:"
msgstr "Или запустите `bitcoind` с помощью `-txindex`:"

#: src/guides/inscriptions.md:65 src/guides/inscriptions.md:74
msgid ""
"```\n"
"bitcoind -txindex\n"
"```"
msgstr ""
"```\n"
"bitcoind -txindex\n"
"```"

#: src/guides/inscriptions.md:69
msgid "Syncing the Bitcoin Blockchain"
msgstr "Синхронизация блокчейна Bitcoin"

#: src/guides/inscriptions.md:72
msgid "To sync the chain, run:"
msgstr "Чтобы синхронизировать сеть, выполните команду:"

#: src/guides/inscriptions.md:78
msgid "…and leave it running until `getblockcount`:"
msgstr "...и оставьте его запущенным до получения `getblockcount`:"

#: src/guides/inscriptions.md:80
msgid ""
"```\n"
"bitcoin-cli getblockcount\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli getblockcount\n"
"```"

#: src/guides/inscriptions.md:84
msgid ""
"agrees with the block count on a block explorer like [the mempool.space block explorer](https://mempool.space/). `ord` interacts with `bitcoind`, so you should "
"leave `bitcoind` running in the background when you're using `ord`."
msgstr ""
"совпадает с количеством блоков в обозревателе блоков, например [в mempool.space block explorer](https://mempool.space/). `ord` взаимодействует с `bitcoind`, "
"поэтому вы должны оставить`bitcoind` включенным в фоновом режиме, пока используете `ord`."

#: src/guides/inscriptions.md:88
msgid "Installing `ord`"
msgstr "Установка `ord`"

#: src/guides/inscriptions.md:91
msgid ""
"The `ord` utility is written in Rust and can be built from [source](https://github.com/ordinals/ord). Pre-built binaries are available on the [releases page]"
"(https://github.com/ordinals/ord/releases)."
msgstr ""
"`ord` написана на языке Rust и может быть собрана из [исходного кода](https://github.com/ordinals/ord). Предварительно собранные бинарные файлы доступны на "
"[странице релизов](https://github.com/ordinals/ord/releases)."

#: src/guides/inscriptions.md:95
msgid "You can install the latest pre-built binary from the command line with:"
msgstr "Вы можете установить последний предварительно собранный двоичный файл из командной строки с помощью команды:"

#: src/guides/inscriptions.md:97
msgid ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash -s\n"
"```"
msgstr ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash -s\n"
"```"

#: src/guides/inscriptions.md:101
msgid "Once `ord` is installed, you should be able to run:"
msgstr "После установки `ord` вы сможете выполнить команду:"

#: src/guides/inscriptions.md:103
msgid ""
"```\n"
"ord --version\n"
"```"
msgstr ""
"```\n"
"ord --version\n"
"```"

#: src/guides/inscriptions.md:107
msgid "Which prints out `ord`'s version number."
msgstr "В результате которой будет выведен номер версии `ord`."

#: src/guides/inscriptions.md:109
msgid "Creating a Bitcoin Core Wallet"
msgstr "Создание кошелька Bitcoin Core"

#: src/guides/inscriptions.md:112
msgid "`ord` uses Bitcoin Core to manage private keys, sign transactions, and broadcast transactions to the Bitcoin network."
msgstr "Для управления закрытыми ключами, подписания транзакций и трансляции транзакций в сеть Биткоин, в `ord` используется Bitcoin Core."

#: src/guides/inscriptions.md:115
msgid "To create a Bitcoin Core wallet named `ord` for use with `ord`, run:"
msgstr "Чтобы создать кошелек Bitcoin Core с именем `ord` для использования с `ord`, выполните команду:"

#: src/guides/inscriptions.md:117
msgid ""
"```\n"
"ord wallet create\n"
"```"
msgstr ""
"```\n"
"ord wallet create\n"
"```"

#: src/guides/inscriptions.md:121
msgid "Receiving Sats"
msgstr "Получение sats"

#: src/guides/inscriptions.md:124
msgid "Inscriptions are made on individual sats, using normal Bitcoin transactions that pay fees in sats, so your wallet will need some sats."
msgstr ""
"Надписи делаются на отдельных sats с использованием обычных Биткоин-транзакций, комиссии которых оплачиваются в sats, поэтому вашему кошельку потребуется "
"некоторое количество sats."

#: src/guides/inscriptions.md:127
msgid "Get a new address from your `ord` wallet by running:"
msgstr "Получите новый адрес из кошелька `ord`, выполнив команду:"

#: src/guides/inscriptions.md:129 src/guides/inscriptions.md:212 src/guides/inscriptions.md:240
msgid ""
"```\n"
"ord wallet receive\n"
"```"
msgstr ""
"```\n"
"ord wallet receive\n"
"```"

#: src/guides/inscriptions.md:133
msgid "And send it some funds."
msgstr "И отправьте на него немного средств."

#: src/guides/inscriptions.md:135
msgid "You can see pending transactions with:"
msgstr "Вы можете посмотреть ожидающие транзакции, выполнив команду:"

#: src/guides/inscriptions.md:137 src/guides/inscriptions.md:224 src/guides/inscriptions.md:251
msgid ""
"```\n"
"ord wallet transactions\n"
"```"
msgstr ""
"```\n"
"ord wallet transactions\n"
"```"

#: src/guides/inscriptions.md:141
msgid "Once the transaction confirms, you should be able to see the transactions outputs with `ord wallet outputs`."
msgstr "Как только транзакция подтвердится, вы сможете увидеть выходные данные транзакций с помощью команды `ord wallet outputs`."

#: src/guides/inscriptions.md:144
msgid "Creating Inscription Content"
msgstr "Создание контента для надписи"

#: src/guides/inscriptions.md:147
msgid "Sats can be inscribed with any kind of content, but the `ord` wallet only supports content types that can be displayed by the `ord` block explorer."
msgstr "Sats могут содержать любые надписи, но кошелек `ord` поддерживает только те типы надписей, которые могут быть отображены в проводнике блоков `ord`."

#: src/guides/inscriptions.md:150
msgid "Additionally, inscriptions are included in transactions, so the larger the content, the higher the fee that the inscription transaction must pay."
msgstr "Кроме того, надписи включаются в транзакции, поэтому чем больше содержимое, тем выше комиссия за транзакцию с надписью."

#: src/guides/inscriptions.md:153
msgid ""
"Inscription content is included in transaction witnesses, which receive the witness discount. To calculate the approximate fee that an inscribe transaction "
"will pay, divide the content size by four and multiply by the fee rate."
msgstr ""
"Содержание надписи входит в состав свидетелей транзакций, которые получают льготу свидетеля. Чтобы рассчитать приблизительную комиссию, которую заплатит "
"транзакция inscribe, разделите размер надписи на четыре и умножьте на размер комиссии."

#: src/guides/inscriptions.md:157
msgid ""
"Inscription transactions must be less than 400,000 weight units, or they will not be relayed by Bitcoin Core. One byte of inscription content costs one weight "
"unit. Since an inscription transaction includes not just the inscription content, limit inscription content to less than 400,000 weight units. 390,000 weight "
"units should be safe."
msgstr ""
"Транзакции с надписями должны быть менее 400 000 весовых единиц, иначе они не будут переданы Bitcoin Core. Один байт содержимого надписи стоит одну весовую "
"единицу. Поскольку транзакция с надписью включает в себя не только содержимое надписи, ограничьте размер транзакции с надписью до менее чем 400 000 весовых "
"единиц. 390 000 весовых единиц должно быть безопасно."

#: src/guides/inscriptions.md:163
msgid "Creating Inscriptions"
msgstr "Создание надписей"

#: src/guides/inscriptions.md:166
msgid "To create an inscription with the contents of `FILE`, run:"
msgstr "Чтобы создать надпись с контентом `FILE`, выполните команду:"

#: src/guides/inscriptions.md:168
msgid ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE --file FILE\n"
"```"
msgstr ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE --file FILE\n"
"```"

#: src/guides/inscriptions.md:172
msgid ""
"Ord will output two transactions IDs, one for the commit transaction, and one for the reveal transaction, and the inscription ID. Inscription IDs are of the "
"form `TXIDiN`, where `TXID` is the transaction ID of the reveal transaction, and `N` is the index of the inscription in the reveal transaction."
msgstr ""
"Ord выдаст два ID транзакций, один для транзакции commit, другой для транзакции reveal, а также ID надписи. ID надписей имеют вид `TXIDiN`, где `TXID` - ID "
"транзакции reveal, а N - индекс надписи в транзакции reveal."

#: src/guides/inscriptions.md:177
msgid ""
"The commit transaction commits to a tapscript containing the content of the inscription, and the reveal transaction spends from that tapscript, revealing the "
"content on chain and inscribing it on the first sat of the input that contains the corresponding tapscript."
msgstr ""
"Транзакция commit фиксирует tapscript, содержащий содержимое надписи, а транзакция reveal использует этот tapscript, раскрывая содержимое по цепочке и "
"прописывая его на первом sat ввода, содержащем соответствующий tapscript."

#: src/guides/inscriptions.md:182
msgid ""
"Wait for the reveal transaction to be mined. You can check the status of the commit and reveal transactions using  [the mempool.space block explorer](https://"
"mempool.space/)."
msgstr ""
"Дождитесь, пока транзакция reveal будет добыта. Проверить статус транзакций commit и reveal можно с помощью [mempool.space block explorer](https://mempool."
"space/)."

#: src/guides/inscriptions.md:186
msgid "Once the reveal transaction has been mined, the inscription ID should be printed when you run:"
msgstr "После того как транзакция reveal будет добыта, при запуске должен быть выведен ID надписи:"

#: src/guides/inscriptions.md:189 src/guides/inscriptions.md:231 src/guides/inscriptions.md:257
msgid ""
"```\n"
"ord wallet inscriptions\n"
"```"
msgstr ""
"```\n"
"ord wallet inscriptions\n"
"```"

#: src/guides/inscriptions.md:193
msgid "Parent Child Inscriptions"
msgstr "Родительские и дочерние надписи"

#: src/guides/inscriptions.md:196
msgid "A child inscription is an inscription that is a child of another inscription. See [provenance](../inscriptions/provenance.md) for more information."
msgstr ""
"Дочерняя надпись - это надпись, которая является дочерней по отношению к другой надписи. Смотрите дополнительную информацию в разделе [происхождение](../"
"inscriptions/provenance.md)."

#: src/guides/inscriptions.md:198
msgid "get the parent inscription id `<PARENT_INSCRIPTION_ID>` from the output of `ord wallet inscriptions`"
msgstr "получить ID родительской надписи `<PARENT_INSCRIPTION_ID>` из выхода `ord wallet inscriptions`"

#: src/guides/inscriptions.md:200
msgid ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE --parent <PARENT_INSCRIPTION_ID> --file CHILD_FILE\"\n"
"```"
msgstr ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE --parent <PARENT_INSCRIPTION_ID> --file CHILD_FILE\"\n"
"```"

#: src/guides/inscriptions.md:204
msgid "And when you visit [the ordinals explorer](https://ordinals.com/) at `ordinals.com/inscription/INSCRIPTION_ID`."
msgstr "А при посещении [ordinals обзоревателя](https://ordinals.com/) в `ordinals.com/inscription/INSCRIPTION_ID`."

#: src/guides/inscriptions.md:207
msgid "Sending Inscriptions"
msgstr "Отправка надписей"

#: src/guides/inscriptions.md:210
msgid "Ask the recipient to generate a new address by running:"
msgstr "Попросите получателя сгенерировать новый адрес, выполнив команду:"

#: src/guides/inscriptions.md:216
msgid "Send the inscription by running:"
msgstr "Отправьте надпись запуском команды:"

#: src/guides/inscriptions.md:218
msgid ""
"```\n"
"ord wallet send --fee-rate <FEE_RATE> <ADDRESS> <INSCRIPTION_ID>\n"
"```"
msgstr ""
"```\n"
"ord wallet send --fee-rate <FEE_RATE> <ADDRESS> <INSCRIPTION_ID>\n"
"```"

#: src/guides/inscriptions.md:222 src/guides/inscriptions.md:250
msgid "See the pending transaction with:"
msgstr "Посмотреть транзакцию, находящуюся в ожидании, можно с помощью команды:"

#: src/guides/inscriptions.md:228
msgid "Once the send transaction confirms, the recipient can confirm receipt by running:"
msgstr "Как только транзакция отправки подтвердится, получатель может подтвердить получение, выполнив команду:"

#: src/guides/inscriptions.md:235
msgid "Receiving Inscriptions"
msgstr "Получение надписей"

#: src/guides/inscriptions.md:238
msgid "Generate a new receive address using:"
msgstr "Сгенерируйте новый адрес получения с помощью команды:"

#: src/guides/inscriptions.md:244
msgid "The sender can transfer the inscription to your address using:"
msgstr "Отправитель может передать надпись на ваш адрес с помощью:"

#: src/guides/inscriptions.md:246
msgid ""
"```\n"
"ord wallet send ADDRESS INSCRIPTION_ID\n"
"```"
msgstr ""
"```\n"
"ord wallet send ADDRESS INSCRIPTION_ID\n"
"```"

#: src/guides/inscriptions.md:255
msgid "Once the send transaction confirms, you can can confirm receipt by running:"
msgstr "После подтверждения транзакции отправки вы можете подтвердить ее получение, выполнив команду:"

#: src/guides/sat-hunting.md:4
msgid ""
"_This guide is out of date. Since it was written, the `ord` binary was changed to only build the full satoshi index when the `--index-sats` flag is supplied. "
"Additionally, `ord` now has a built-in wallet that wraps a Bitcoin Core wallet. See `ord wallet --help`._"
msgstr ""
"_Данное руководство устарело. С момента его написания бинарник `ord` был изменен таким образом, чтобы собирать полный индекс сатоши только при наличии флага `--"
"index-sats`. Кроме того, `ord` теперь имеет встроенный кошелек, который оборачивает кошелек Bitcoin Core. Смотрите раздел `ord wallet --help`._"

#: src/guides/sat-hunting.md:9
msgid ""
"Ordinal hunting is difficult but rewarding. The feeling of owning a wallet full of UTXOs, redolent with the scent of rare and exotic sats, is beyond compare."
msgstr ""
"Охота на ordinal - трудное, но вознаграждаемое занятие. Ощущения от обладания кошельком, полным UTXO, наполненным ароматом редких и экзотических сатов, вне "
"сравнения."

#: src/guides/sat-hunting.md:12
msgid "Ordinals are numbers for satoshis. Every satoshi has an ordinal number and every ordinal number has a satoshi."
msgstr "Ordinals - это числа для сатоши. Каждый сатоши имеет порядковый номер, а каждый порядковый номер имеет свой сатоши."

#: src/guides/sat-hunting.md:15
msgid "Preparation"
msgstr "Подготовка"

#: src/guides/sat-hunting.md:18
msgid "There are a few things you'll need before you start."
msgstr "Перед началом работы вам потребуется несколько вещей."

#: src/guides/sat-hunting.md:20
msgid "First, you'll need a synced Bitcoin Core node with a transaction index. To turn on transaction indexing, pass `-txindex` on the command-line:"
msgstr ""
"Во-первых, вам потребуется синхронизированный Bitcoin Core node с индексом транзакций. Чтобы включить индексирование транзакций, передайте в командной строке "
"команду `-txindex`:"

#: src/guides/sat-hunting.md:23
msgid ""
"```sh\n"
"bitcoind -txindex\n"
"```"
msgstr ""
"```sh\n"
"bitcoind -txindex\n"
"```"

#: src/guides/sat-hunting.md:27
msgid "Or put the following in your [Bitcoin configuration file](https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"
msgstr "Или поместите в [файл конфигурации Bitcoin](https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"

#: src/guides/sat-hunting.md:34
msgid "Launch it and wait for it to catch up to the chain tip, at which point the following command should print out the current block height:"
msgstr "Запустите его и дождитесь, пока он догонит вершину цепи, после чего следующая команда должна вывести текущую высоту блока:"

#: src/guides/sat-hunting.md:37
msgid ""
"```sh\n"
"bitcoin-cli getblockcount\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getblockcount\n"
"```"

#: src/guides/sat-hunting.md:41
msgid "Second, you'll need a synced `ord` index."
msgstr "Во-вторых, вам понадобится синхронизированный индекс `ord`."

#: src/guides/sat-hunting.md:43
msgid "Get a copy of `ord` from [the repo](https://github.com/ordinals/ord/)."
msgstr "Получить копию `ord` из [репозитория](https://github.com/ordinals/ord/)."

#: src/guides/sat-hunting.md:45
msgid "Run `RUST_LOG=info ord index`. It should connect to your bitcoin core node and start indexing."
msgstr "Выполните команду `RUST_LOG=info ord index`. Он должен подключиться к вашему bitcoin core node и начать индексирование."

#: src/guides/sat-hunting.md:48
msgid "Wait for it to finish indexing."
msgstr "Дождитесь окончания индексации."

#: src/guides/sat-hunting.md:50
msgid "Third, you'll need a wallet with UTXOs that you want to search."
msgstr "В-третьих, вам понадобится кошелек с UTXO, которые вы хотите найти."

#: src/guides/sat-hunting.md:52
msgid "Searching for Rare Ordinals"
msgstr "Поиск Rare Ordinals"

#: src/guides/sat-hunting.md:55
msgid "Searching for Rare Ordinals in a Bitcoin Core Wallet"
msgstr "Поиск Rare Ordinals в Bitcoin Core Wallet"

#: src/guides/sat-hunting.md:57
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so searching for rare ordinals in a Bitcoin Core wallet is Easy. Assuming your wallet "
"is named `foo`:"
msgstr ""
"Команда `ord wallet` - это просто обертка вокруг RPC API Bitcoin Core, поэтому найти rare ordinals в Bitcoin Core wallet довольно легко . Предположим, что ваш "
"кошелек имеет имя `foo`:"

#: src/guides/sat-hunting.md:61
msgid "Load your wallet:"
msgstr "Загрузите свой кошелек:"

#: src/guides/sat-hunting.md:63
msgid ""
"```sh\n"
"bitcoin-cli loadwallet foo\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli loadwallet foo\n"
"```"

#: src/guides/sat-hunting.md:67
msgid "Display any rare ordinals wallet `foo`'s UTXOs:"
msgstr "Отображение любых rare ordinals кошелька `foo` UTXOs:"

#: src/guides/sat-hunting.md:69 src/guides/sat-hunting.md:132 src/guides/sat-hunting.md:233
msgid ""
"```sh\n"
"ord wallet sats\n"
"```"
msgstr ""
"```sh\n"
"ord wallet sats\n"
"```"

#: src/guides/sat-hunting.md:73
msgid "Searching for Rare Ordinals in a Non-Bitcoin Core Wallet"
msgstr "Поиск Rare Ordinals в кошельке, не относящемся к Bitcoin Core Wallet"

#: src/guides/sat-hunting.md:75
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so to search for rare ordinals in a non-Bitcoin Core wallet, you'll need to import "
"your wallet's descriptors into Bitcoin Core."
msgstr ""
"Команда `ord wallet` является лишь оберткой для RPC API Bitcoin Core, поэтому для поиска rare ordinals в кошельке, не принадлежащем Bitcoin Core wallet, "
"необходимо импортировать дескрипторы кошелька в Bitcoin Core."

#: src/guides/sat-hunting.md:79
msgid "[Descriptors](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors.md) describe the ways that wallets generate private keys and public keys."
msgstr "[Дескрипторы](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors.md) описывают способы генерации кошельками закрытых и открытых ключей."

#: src/guides/sat-hunting.md:82
msgid "You should only import descriptors into Bitcoin Core for your wallet's public keys, not its private keys."
msgstr "В Bitcoin Core следует импортировать дескрипторы только для открытых ключей кошелька, но не для его закрытых ключей."

#: src/guides/sat-hunting.md:85
msgid "If your wallet's public key descriptor is compromised, an attacker will be able to see your wallet's addresses, but your funds will be safe."
msgstr ""
"Если дескриптор открытого ключа вашего кошелька будет скомпрометирован, злоумышленник сможет увидеть адреса вашего кошелька, но ваши средства будут в "
"безопасности."

#: src/guides/sat-hunting.md:88
msgid "If your wallet's private key descriptor is compromised, an attacker can drain your wallet of funds."
msgstr "Если дескриптор закрытого ключа вашего кошелька скомпрометирован, злоумышленник может вывести с него ваши средства."

#: src/guides/sat-hunting.md:91
msgid "Get the wallet descriptor from the wallet whose UTXOs you want to search for rare ordinals. It will look something like this:"
msgstr "Получите дескриптор кошелька, в кошельке которого вы хотите искать rare ordinals UTXO. Он будет выглядеть примерно так:"

#: src/guides/sat-hunting.md:94
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\n"
"```"

#: src/guides/sat-hunting.md:98
msgid "Create a watch-only wallet named `foo-watch-only`:"
msgstr "Создайте кошелек с именем `foo-watch-only`, предназначенный только для просмотра:"

#: src/guides/sat-hunting.md:100
msgid ""
"```sh\n"
"bitcoin-cli createwallet foo-watch-only true true\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli createwallet foo-watch-only true true\n"
"```"

#: src/guides/sat-hunting.md:104
msgid "Feel free to give it a better name than `foo-watch-only`!"
msgstr "Не стесняйтесь дать ему более подходящее название, чем `foo-watch-only`!"

#: src/guides/sat-hunting.md:106
msgid "Load the `foo-watch-only` wallet:"
msgstr "Загрузите кошелек `foo-watch-only`:"

#: src/guides/sat-hunting.md:108 src/guides/sat-hunting.md:199
msgid ""
"```sh\n"
"bitcoin-cli loadwallet foo-watch-only\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli loadwallet foo-watch-only\n"
"```"

#: src/guides/sat-hunting.md:112
msgid "Import your wallet descriptors into `foo-watch-only`:"
msgstr "Импортируйте дескрипторы вашего кошелька в `foo-watch-only`:"

#: src/guides/sat-hunting.md:114
msgid ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": \"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\", \"timestamp\":0 }]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": \"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\", \"timestamp\":0 }]'\n"
"```"

#: src/guides/sat-hunting.md:119
msgid ""
"If you know the Unix timestamp when your wallet first started receive transactions, you may use it for the value of `\"timestamp\"` instead of `0`. This will "
"reduce the time it takes for Bitcoin Core to search for your wallet's UTXOs."
msgstr ""
"Если вам известна временная метка Unix, когда ваш кошелек впервые начал принимать транзакции, вы можете использовать ее в качестве значения `\"timestamp\"` "
"вместо `0`. Это позволит сократить время поиска UTXO кошелька для Bitcoin Core."

#: src/guides/sat-hunting.md:124 src/guides/sat-hunting.md:225
msgid "Check that everything worked:"
msgstr "Проверьте, что все работает:"

#: src/guides/sat-hunting.md:126 src/guides/sat-hunting.md:227
msgid ""
"```sh\n"
"bitcoin-cli getwalletinfo\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getwalletinfo\n"
"```"

#: src/guides/sat-hunting.md:130 src/guides/sat-hunting.md:231
msgid "Display your wallet's rare ordinals:"
msgstr "Отображение rare ordinals в кошельке:"

#: src/guides/sat-hunting.md:136
msgid "Searching for Rare Ordinals in a Wallet that Exports Multi-path Descriptors"
msgstr "Поиск Rare Ordinals в кошельке, экспортирующем многопутевые дескрипторы"

#: src/guides/sat-hunting.md:138
msgid ""
"Some descriptors describe multiple paths in one descriptor using angle brackets, e.g., `<0;1>`. Multi-path descriptors are not yet supported by Bitcoin Core, "
"so you'll first need to convert them into multiple descriptors, and then import those multiple descriptors into Bitcoin Core."
msgstr ""
"Некоторые дескрипторы описывают несколько путей в одном дескрипторе с помощью угловых скобок, например,`<0;1>`. Bitcoin Core пока не поддерживает многопутевые "
"дескрипторы, поэтому сначала их необходимо преобразовать в многопутевые дескрипторы, а затем импортировать эти многопутевые дескрипторы в Bitcoin Core."

#: src/guides/sat-hunting.md:143
msgid "First get the multi-path descriptor from your wallet. It will look something like this:"
msgstr "Сначала получите многопутевой дескриптор из вашего кошелька. Он будет выглядеть примерно так:"

#: src/guides/sat-hunting.md:146
msgid ""
"```\n"
"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/<0;1>/*)#fw76ulgt\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/<0;1>/*)#fw76ulgt\n"
"```"

#: src/guides/sat-hunting.md:150
msgid "Create a descriptor for the receive address path:"
msgstr "Создайте дескриптор для пути адреса приема:"

#: src/guides/sat-hunting.md:152
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)\n"
"```"

#: src/guides/sat-hunting.md:156
msgid "And the change address path:"
msgstr "И путь смены адреса:"

#: src/guides/sat-hunting.md:158
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)\n"
"```"

#: src/guides/sat-hunting.md:162
msgid "Get and note the checksum for the receive address descriptor, in this case `tpnxnxax`:"
msgstr "Получите и запишите контрольную сумму для дескриптора адреса приема, в данном случае `tpnxnxax`:"

#: src/guides/sat-hunting.md:165
msgid ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)'\n"
"```"

#: src/guides/sat-hunting.md:170
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": \"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": \"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src/guides/sat-hunting.md:180
msgid "And for the change address descriptor, in this case `64k8wnd7`:"
msgstr "И для дескриптора адреса изменения, в данном случае `64k8wnd7`:"

#: src/guides/sat-hunting.md:182
msgid ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)'\n"
"```"

#: src/guides/sat-hunting.md:187
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": \"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": \"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src/guides/sat-hunting.md:197
msgid "Load the wallet you want to import the descriptors into:"
msgstr "Загрузите кошелек, в который вы хотите импортировать дескрипторы:"

#: src/guides/sat-hunting.md:203
msgid "Now import the descriptors, with the correct checksums, into Bitcoin Core."
msgstr "Теперь импортируйте дескрипторы с правильными контрольными суммами в Bitcoin Core."

#: src/guides/sat-hunting.md:205
msgid ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": \"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": \"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": \"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": \"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"

#: src/guides/sat-hunting.md:220
msgid ""
"If you know the Unix timestamp when your wallet first started receive transactions, you may use it for the value of the `\"timestamp\"` fields instead of `0`. "
"This will reduce the time it takes for Bitcoin Core to search for your wallet's UTXOs."
msgstr ""
"Если вам известна временная метка Unix, когда ваш кошелек впервые начал принимать транзакции, вы можете использовать ее в качестве значения `\"timestamp\"` "
"вместо `0`. Это позволит сократить время поиска UTXO кошелька для Bitcoin Core."

#: src/guides/sat-hunting.md:237
msgid "Exporting Descriptors"
msgstr "Экспорт дескрипторов"

#: src/guides/sat-hunting.md:241
msgid "Navigate to the `Settings` tab, then to `Script Policy`, and press the edit button to display the descriptor."
msgstr "Перейдите на вкладку `Settings`, затем на вкладку `Script Policy` и нажмите кнопку редактирования для отображения дескриптора."

#: src/guides/sat-hunting.md:244
msgid "Transferring Ordinals"
msgstr "Отправка Ordinals"

#: src/guides/sat-hunting.md:246
msgid ""
"The `ord` wallet supports transferring specific satoshis. You can also use `bitcoin-cli` commands `createrawtransaction`, `signrawtransactionwithwallet`, and "
"`sendrawtransaction`, how to do so is complex and outside the scope of this guide."
msgstr ""
"Кошелек `ord` поддерживает перевод определенных сатоши. Вы также можете использовать команды `bitcoin-cli` `createrawtransaction`, `signrawtransactionwwallet` "
"и `sendrawtransaction`, но их выполнение является сложным и не входит в рамки данного руководства."

#: src/guides/collecting.md:4
msgid ""
"Currently, [ord](https://github.com/ordinals/ord/) is the only wallet supporting sat-control and sat-selection, which are required to safely store and send "
"rare sats and inscriptions, hereafter ordinals."
msgstr ""
"В настоящее время, [ord](https://github.com/ordinals/ord/) является единственным кошельком, поддерживающим функции sat-control и sat-selection, необходимые для "
"безопасного хранения и отправки редких сатов и надписей, далее ordinals."

#: src/guides/collecting.md:8
msgid ""
"The recommended way to send, receive, and store ordinals is with `ord`, but if you are careful, it is possible to safely store, and in some cases send, "
"ordinals with other wallets."
msgstr ""
"Рекомендуемый способ отправки, получения и хранения ordinals - `ord`, но если быть осторожным, то можно безопасно хранить, а в некоторых случаях и отправлять "
"ordinals с помощью других кошельков."

#: src/guides/collecting.md:12
msgid ""
"As a general note, receiving ordinals in an unsupported wallet is not dangerous. Ordinals can be sent to any bitcoin address, and are safe as long as the UTXO "
"that contains them is not spent. However, if that wallet is then used to send bitcoin, it may select the UTXO containing the ordinal as an input, and send the "
"inscription or spend it to fees."
msgstr ""
"Как правило, получение ordinals на неподдерживаемый кошелек не представляет опасности. Ordinals могут быть отправлены на любой биткоин-адрес и безопасны до тех "
"пор, пока содержащий их UTXO не будет потрачен. Однако если этот кошелек будет использоваться для отправки биткоинов, он может выбрать UTXO, содержащий "
"ordinal, в качестве входного, и отправить надпись или потратить ее на оплату комиссии."

#: src/guides/collecting.md:18
msgid ""
"A [guide](./collecting/sparrow-wallet.md) to creating an `ord`\\-compatible wallet with [Sparrow Wallet](https://sparrowwallet.com/), is available in this "
"handbook."
msgstr ""
"В [руководстве](./collecting/sparrow-wallet.md) содержится информация по созданию `ord`\\-совместимого кошелька с [Sparrow Wallet](https://sparrowwallet.com/)."

#: src/guides/collecting.md:21
msgid ""
"Please note that if you follow this guide, you should not use the wallet you create to send BTC, unless you perform manual coin-selection to avoid sending "
"ordinals."
msgstr ""
"Обратите внимание, что если вы следуете этому руководству, то не должны использовать созданный вами кошелек для отправки BTC, если только вы не проводите "
"отправку вручную, чтобы избежать отправки ordinals."

#: src/guides/collecting/sparrow-wallet.md:1
msgid "Collecting Inscriptions and Ordinals with Sparrow Wallet"
msgstr "Коллекционирование надписей и Ordinals с помощью Sparrow Wallet"

#: src/guides/collecting/sparrow-wallet.md:4
msgid ""
"Users who cannot or have not yet set up the [ord](https://github.com/ordinals/ord) wallet can receive inscriptions and ordinals with alternative bitcoin "
"wallets, as long as they are _very_ careful about how they spend from that wallet."
msgstr ""
"Пользователи, которые не могут или не успели создать кошелек [ord](https://github.com/ordinals/ord), могут получать надписи и ordinals с помощью альтернативных "
"биткоин-кошельков, при условии, что они _очень_ внимательно относятся к тому, как тратят средства с этого кошелька."

#: src/guides/collecting/sparrow-wallet.md:6
msgid ""
"This guide gives some basic steps on how to create a wallet with [Sparrow Wallet](https://sparrowwallet.com/) which is compatible with `ord` and can be later "
"imported into `ord`"
msgstr ""
"В данном руководстве приведены основные шаги по созданию кошелька с помощью [Sparrow Wallet](https://sparrowwallet.com/), который совместим с `ord` и может "
"быть впоследствии импортирован в `ord`"

#: src/guides/collecting/sparrow-wallet.md:8
msgid "⚠️⚠️ Warning!! ⚠️⚠️"
msgstr "⚠️⚠️ Внимание!! ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:9
msgid "As a general rule if you take this approach, you should use this wallet with the Sparrow software as a receive-only wallet."
msgstr "Как правило, при таком подходе следует использовать этот кошелек с программой Sparrow в качестве кошелька только для приема транзакций."

#: src/guides/collecting/sparrow-wallet.md:11
msgid ""
"Do not spend any satoshis from this wallet unless you are sure you know what you are doing. You could very easily inadvertently lose access to your ordinals "
"and inscriptions if you don't heed this warning."
msgstr ""
"Не тратьте сатоши из этого кошелька, если не уверены, что знаете, что делаете. Если вы не прислушаетесь к этому предупреждению, то очень легко можете случайно "
"потерять доступ к своим ordinals и надписям."

#: src/guides/collecting/sparrow-wallet.md:13
msgid "Wallet Setup & Receiving"
msgstr "Установка кошелька и получение"

#: src/guides/collecting/sparrow-wallet.md:15
msgid "Download the Sparrow Wallet from the [releases page](https://sparrowwallet.com/download/) for your particular operating system."
msgstr "Загрузите Sparrow Wallet [со страницы](https://sparrowwallet.com/download/) для вашей конкретной операционной системы."

#: src/guides/collecting/sparrow-wallet.md:17
msgid "Select `File -> New Wallet` and create a new wallet called `ord`."
msgstr "Выберите `File -> New Wallet` и создайте новый кошелек с названием `ord`."

#: src/guides/collecting/sparrow-wallet.md:19
msgid "![](images/wallet_setup_01.png)"
msgstr "![](images/wallet_setup_01.png)"

#: src/guides/collecting/sparrow-wallet.md:21
msgid "Change the `Script Type` to `Taproot (P2TR)` and select the `New or Imported Software Wallet` option."
msgstr "Измените `Script Type` на `Taproot (P2TR)` и выберите опцию `New or Imported Software Wallet`."

#: src/guides/collecting/sparrow-wallet.md:23
msgid "![](images/wallet_setup_02.png)"
msgstr "![](images/wallet_setup_02.png)"

#: src/guides/collecting/sparrow-wallet.md:25
msgid "Select `Use 12 Words` and then click `Generate New`. Leave the passphrase blank."
msgstr "Выберите `Use 12 Words` и нажмите `Generate New`. Оставьте блан пароля пустой."

#: src/guides/collecting/sparrow-wallet.md:27
msgid "![](images/wallet_setup_03.png)"
msgstr "![](images/wallet_setup_03.png)"

#: src/guides/collecting/sparrow-wallet.md:29
msgid ""
"A new 12 word BIP39 seed phrase will be generated for you. Write this down somewhere safe as this is your backup to get access to your wallet. NEVER share or "
"show this seed phrase to anyone else."
msgstr ""
"Для вас будет сгенерирована новая начальная фраза BIP39 из 12 слов. Запишите ее в надежном месте, так как это ваш запасной вариант для получения доступа к "
"кошельку. НИКОГДА не сообщайте и не показывайте эту seed-фразу никому другому."

#: src/guides/collecting/sparrow-wallet.md:31
msgid "Once you have written down the seed phrase click `Confirm Backup`."
msgstr "После того как вы записали seed-фразу, нажмите кнопку `Confirm Backup`."

#: src/guides/collecting/sparrow-wallet.md:33
msgid "![](images/wallet_setup_04.png)"
msgstr "![](images/wallet_setup_04.png)"

#: src/guides/collecting/sparrow-wallet.md:35
msgid "Re-enter the seed phrase which you wrote down, and then click `Create Keystore`."
msgstr "Вновь введите записанную вами seed-фразу и нажмите кнопку `Create Keystore`."

#: src/guides/collecting/sparrow-wallet.md:37
msgid "![](images/wallet_setup_05.png)"
msgstr "![](images/wallet_setup_05.png)"

#: src/guides/collecting/sparrow-wallet.md:39
msgid "Click `Import Keystore`."
msgstr "Нажмите кнопку `Import Keystore`."

#: src/guides/collecting/sparrow-wallet.md:41
msgid "![](images/wallet_setup_06.png)"
msgstr "![](images/wallet_setup_06.png)"

#: src/guides/collecting/sparrow-wallet.md:43
msgid "Click `Apply`. Add a password for the wallet if you want to."
msgstr "Нажмите кнопку `Apply`. При необходимости добавьте пароль для кошелька."

#: src/guides/collecting/sparrow-wallet.md:45
msgid "![](images/wallet_setup_07.png)"
msgstr "![](images/wallet_setup_07.png)"

#: src/guides/collecting/sparrow-wallet.md:47
msgid ""
"You now have a wallet which is compatible with `ord`, and can be imported into `ord` using the BIP39 Seed Phrase. To receive ordinals or inscriptions, click on "
"the `Receive` tab and copy a new address."
msgstr ""
"Теперь у вас есть кошелек, который совместим с `ord` и может быть импортирован в `ord` с помощью seed-фразы BIP39. Чтобы получить ordinals или надписи, "
"перейдите на вкладку `Receive` и скопируйте новый адрес."

#: src/guides/collecting/sparrow-wallet.md:49
msgid "Each time you want to receive you should use a brand-new address, and not re-use existing addresses."
msgstr "Каждый раз для получения необходимо использовать совершенно новый адрес, а не повторно использовать существующие."

#: src/guides/collecting/sparrow-wallet.md:51
msgid ""
"Note that bitcoin is different to some other blockchain wallets, in that this wallet can generate an unlimited number of new addresses. You can generate a new "
"address by clicking on the `Get Next Address` button. You can see all of your addresses in the `Addresses` tab of the app."
msgstr ""
"Обратите внимание, что биткоин отличается от некоторых других блокчейн кошельков тем, что этот кошелек может генерировать неограниченное количество новых "
"адресов. Сгенерировать новый адрес можно, нажав на кнопку `Get Next Address`. Все адреса можно посмотреть на вкладке `Addresses` приложения."

#: src/guides/collecting/sparrow-wallet.md:53
msgid "You can add a label to each address, so you can keep track of what it was used for."
msgstr "К каждому адресу можно добавить лейбл, чтобы отслеживать, для чего он был использован."

#: src/guides/collecting/sparrow-wallet.md:55
msgid "![](images/wallet_setup_08.png)"
msgstr "![](images/wallet_setup_08.png)"

#: src/guides/collecting/sparrow-wallet.md:57
msgid "Validating / Viewing Received Inscriptions"
msgstr "Проверка/просмотр полученных надписей"

#: src/guides/collecting/sparrow-wallet.md:59
msgid "Once you have received an inscription you will see a new transaction in the `Transactions` tab of Sparrow, as well as a new UTXO in the `UTXOs` tab."
msgstr "После получения надписи вы увидите новую транзакцию на вкладке `Transactions` в Sparrow, а также новый UTXO на вкладке `UTXOs`."

#: src/guides/collecting/sparrow-wallet.md:61
msgid ""
"Initially this transaction may have an \"Unconfirmed\" status, and you will need to wait for it to be mined into a bitcoin block before it is fully received."
msgstr ""
"Первоначально эта транзакция может иметь статус \"Unconfirmed\", и вам придется подождать, пока она будет добыта в блокчейне биткоина, прежде чем она будет "
"полностью получена."

#: src/guides/collecting/sparrow-wallet.md:63
msgid "![](images/validating_viewing_01.png)"
msgstr "![](images/validating_viewing_01.png)"

#: src/guides/collecting/sparrow-wallet.md:65
msgid ""
"To track the status of your transaction you can right-click on it,  select `Copy Transaction ID` and then paste that transaction id into [mempool.space]"
"(https://mempool.space)."
msgstr ""
"Для отслеживания статуса транзакции можно щелкнуть на ней правой кнопкой мыши, выбрать `Copy Transaction ID` и затем вставить этот ID транзакции в [mempool."
"space](https://mempool.space)."

#: src/guides/collecting/sparrow-wallet.md:67
msgid "![](images/validating_viewing_02.png)"
msgstr "![](images/validating_viewing_02.png)"

#: src/guides/collecting/sparrow-wallet.md:69
msgid ""
"Once the transaction has confirmed, you can validate and view your inscription by heading over to the `UTXOs` tab, finding the UTXO you want to check, right-"
"clicking on the `Output` and selecting `Copy Transaction Output`. This transaction output id can then be pasted into the [ordinals.com](https://ordinals.com) "
"search."
msgstr ""
"После подтверждения транзакции вы можете проверить и просмотреть свою надпись, перейдя на вкладку `UTXOs`, найдя UTXO, который вы хотите проверить, щелкнув "
"правой кнопкой мыши на `Output` и выбрав `Copy Transaction Output`. Затем этот идентификатор транзакционного выхода можно вставить в поиск [ordinals.com]"
"(https://ordinals.com)."

#: src/guides/collecting/sparrow-wallet.md:72
msgid "Freezing UTXO's"
msgstr "Заморозка UTXO"

#: src/guides/collecting/sparrow-wallet.md:73
msgid ""
"As explained above, each of your inscriptions is stored in an Unspent Transaction Output (UTXO). You want to be very careful not to accidentally spend your "
"inscriptions, and one way to make it harder for this to happen is to freeze the UTXO."
msgstr ""
"Как объяснялось выше, каждая из ваших надписей хранится в Unspent Transaction Output (UTXO). Вы должны быть очень осторожны, чтобы случайно не потратить свои "
"надписи, и один из способов затруднить это - заморозить UTXO."

#: src/guides/collecting/sparrow-wallet.md:75
msgid "To do this, go to the `UTXOs` tab, find the UTXO you want to freeze, right-click on the `Output` and select `Freeze UTXO`."
msgstr "Для этого перейдите на вкладку `UTXOs`, найдите UTXO, который необходимо заморозить, щелкните правой кнопкой мыши на `Output` и выберите `Freeze UTXO`."

#: src/guides/collecting/sparrow-wallet.md:77
msgid "This UTXO (Inscription) is now un-spendable within the Sparrow Wallet until you unfreeze it."
msgstr "Этот UTXO (Inscription) теперь нельзя расходовать в Sparrow Wallet до тех пор, пока вы его не разморозите."

#: src/guides/collecting/sparrow-wallet.md:79
msgid "Importing into `ord` wallet"
msgstr "Импорт в кошелек `ord`"

#: src/guides/collecting/sparrow-wallet.md:81
msgid "For details on setting up Bitcoin Core and the `ord` wallet check out the [Inscriptions Guide](../inscriptions.md)"
msgstr "Подробнее о настройке Bitcoin Core и кошелька `ord` читайте в [руководстве по надписям](../inscriptions.md)"

#: src/guides/collecting/sparrow-wallet.md:83
msgid ""
"When setting up `ord`, instead of running `ord wallet create` to create a brand-new wallet, you can import your existing wallet using `ord wallet restore "
"\"BIP39 SEED PHRASE\"` using the seed phrase you generated with Sparrow Wallet."
msgstr ""
"При настройке `ord` вместо того, чтобы запускать команду `ord wallet create` для создания нового кошелька, можно импортировать существующий кошелек с помощью "
"команды `ord wallet restore \"BIP39 SEED PHRASE\"`, используя seed-фразу, созданную с помощью Sparrow Wallet."

#: src/guides/collecting/sparrow-wallet.md:85
msgid ""
"There is currently a [bug](https://github.com/ordinals/ord/issues/1589) which causes an imported wallet to not be automatically rescanned against the "
"blockchain. To work around this you will need to manually trigger a rescan using the bitcoin core cli: `bitcoin-cli -rpcwallet=ord rescanblockchain 767430`"
msgstr ""
"В настоящее время существует [ошибка](https://github.com/ordinals/ord/issues/1589) из-за которой импортированный кошелек не может быть автоматически "
"пересканирован на блокчейн. Чтобы решить эту проблему, необходимо вручную запустить повторное сканирование с помощью программы bitcoin core cli: `bitcoin-cli -"
"rpcwallet=ord rescanblockchain 767430`"

#: src/guides/collecting/sparrow-wallet.md:88
msgid "You can then check your wallet's inscriptions using `ord wallet inscriptions`"
msgstr "Затем вы можете проверить количество надписей в кошельке, используя команду `ord wallet inscriptions`"

#: src/guides/collecting/sparrow-wallet.md:90
msgid ""
"Note that if you have previously created a wallet with `ord`, then you will already have a wallet with the default name, and will need to give your imported "
"wallet a different name. You can use the `--wallet` parameter in all `ord` commands to reference a different wallet, eg:"
msgstr ""
"Обратите внимание, что если вы ранее создали кошелек с помощью `ord`, то у вас уже будет кошелек с именем по умолчанию, и вам необходимо будет дать "
"импортируемому кошельку другое имя. Для ссылки на другой кошелек можно использовать параметр `--wallet` во всех командах `ord`, например:"

#: src/guides/collecting/sparrow-wallet.md:92
msgid "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"
msgstr "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"

#: src/guides/collecting/sparrow-wallet.md:94
msgid "`ord --wallet ord_from_sparrow wallet inscriptions`"
msgstr "`ord --wallet ord_from_sparrow wallet inscriptions`"

#: src/guides/collecting/sparrow-wallet.md:96
msgid "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"
msgstr "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"

#: src/guides/collecting/sparrow-wallet.md:98
msgid "Sending inscriptions with Sparrow Wallet"
msgstr "Отправка надписей с помощью Sparrow Wallet"

#: src/guides/collecting/sparrow-wallet.md:100
msgid "⚠️⚠️ Warning ⚠️⚠️"
msgstr "⚠️⚠️ Внимание ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:101
msgid ""
"While it is highly recommended that you set up a bitcoin core node and run the `ord` software, there are certain limited ways you can send inscriptions out of "
"Sparrow Wallet in a safe way. Please note that this is not recommended, and you should only do this if you fully understand what you are doing."
msgstr ""
"Хотя настоятельно рекомендуется установить bitcoin core node и запустить программное обеспечение `ord`, существуют некоторые ограниченные способы безопасной "
"отправки надписей из Sparrow Wallet. Обратите внимание, что делать это не рекомендуется и следует только в том случае, если вы полностью понимаете, что делаете."

#: src/guides/collecting/sparrow-wallet.md:103
msgid ""
"Using the `ord` software will remove much of the complexity we are describing here, as it is able to automatically and safely handle sending inscriptions in an "
"easy way."
msgstr ""
"Использование программы `ord` устранит большую часть сложностей, которые мы здесь описываем, поскольку она способна автоматически и безопасно обрабатывать "
"отправку надписей в простой форме."

#: src/guides/collecting/sparrow-wallet.md:105
msgid "⚠️⚠️ Additional Warning ⚠️⚠️"
msgstr "⚠️⚠️ Дополнительное предупреждение ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:106
msgid ""
"Don't use your sparrow inscriptions wallet to do general sends of non-inscription bitcoin. You can setup a separate wallet in sparrow if you need to do normal "
"bitcoin transactions, and keep your inscriptions wallet separate."
msgstr ""
"Не используйте свой sparrow inscriptions wallet для общей отправки биткоинов. Вы можете создать отдельный кошелек в Sparrow, если вам нужно совершать обычные "
"транзакции с биткойнами, и держать кошелек для надписей отдельно."

#: src/guides/collecting/sparrow-wallet.md:108
msgid "Bitcoin's UTXO model"
msgstr "Модель UTXO биткоина"

#: src/guides/collecting/sparrow-wallet.md:109
msgid ""
"Before sending any transaction it's important that you have a good mental model for bitcoin's Unspent Transaction Output (UTXO) system. The way Bitcoin works "
"is fundamentally different to many other blockchains such as Ethereum. In Ethereum generally you have a single address in which you store ETH, and you cannot "
"differentiate between any of the ETH -  it is just all a single value of the total amount in that address. Bitcoin works very differently in that we generate a "
"new address in the wallet for each receive, and every time you receive sats to an address in your wallet you are creating a new UTXO. Each UTXO can be seen and "
"managed individually. You can select specific UTXO's which you want to spend, and you can choose not to spend certain UTXO's."
msgstr ""
"Перед отправкой любой транзакции важно хорошо представлять себе систему вывода Unspent Transaction Output (UTXO) в биткоине. Принцип работы биткоина "
"фундаментально отличается от многих других блокчейнов, таких как Ethereum. В Ethereum, как правило, у вас есть один адрес, на котором вы храните ETH, и вы не "
"можете различать ни один из ETH - все они просто представляют собой единое значение общей суммы, находящейся на этом адресе. Биткоин работает совсем по-"
"другому: для каждого приема мы генерируем новый адрес в кошельке, и каждый раз, когда вы получаете саты на адрес в кошельке, вы создаете новый UTXO. Каждый "
"UTXO можно видеть и управлять им индивидуально. Вы можете выбрать конкретные UTXO, которые хотите потратить, и не тратить определенные UTXO."

#: src/guides/collecting/sparrow-wallet.md:111
msgid ""
"Some Bitcoin wallets do not expose this level of detail, and they just show you a single summed up value of all the bitcoin in your wallet. However, when "
"sending inscriptions it is important that you use a wallet like Sparrow which allows for UTXO control."
msgstr ""
"Некоторые биткоин-кошельки не предоставляют такой детализации, а просто показывают суммарное значение всех биткоинов в кошельке. Однако при отправке надписей "
"важно использовать такой кошелек, как Sparrow, который позволяет контролировать UTXO."

#: src/guides/collecting/sparrow-wallet.md:113
msgid "Inspecting your inscription before sending"
msgstr "Проверка надписи перед отправкой"

#: src/guides/collecting/sparrow-wallet.md:114
msgid ""
"Like we have previously described inscriptions are inscribed onto sats, and sats are stored within UTXOs. UTXO's are a collection of satoshis with some "
"particular value of the number of satoshis (the output value). Usually (but not always) the inscription will be inscribed on the first satoshi in the UTXO."
msgstr ""
"Как мы уже описывали ранее, надписи наносятся на сатоши, а сатоши хранятся в UTXO. UTXO представляют собой набор сатоши с некоторым определенным значением "
"количества сатоши (выходное значение). Обычно (но не всегда) надпись наносится на первый сатоши в UTXO."

#: src/guides/collecting/sparrow-wallet.md:116
msgid "When inspecting your inscription before sending the main thing you will want to check is which satoshi in the UTXO your inscription is inscribed on."
msgstr "При проверке надписи перед отправкой главное, что вы захотите проверить, на каком сатоши в UTXO сделана ваша надпись."

#: src/guides/collecting/sparrow-wallet.md:118
msgid ""
"To do this, you can follow the [Validating / Viewing Received Inscriptions](./sparrow-wallet.md#validating--viewing-received-inscriptions) described above to "
"find the inscription page for your inscription on ordinals.com"
msgstr ""
"Для этого, следуя описанным выше инструкциям [Validating / Viewing Received Inscriptions](./sparrow-wallet.md#validating--viewing-received-inscriptions) можно "
"найти страницу надписи на сайте ordinals.com"

#: src/guides/collecting/sparrow-wallet.md:120
msgid "There you will find some metadata about your inscription which looks like the following:"
msgstr "Там вы найдете метаданные о вашей надписи, которые выглядят следующим образом:"

#: src/guides/collecting/sparrow-wallet.md:122
msgid "![](images/sending_01.png)"
msgstr "![](images/sending_01.png)"

#: src/guides/collecting/sparrow-wallet.md:124
msgid "There is a few of important things to check here:"
msgstr "Здесь необходимо проверить несколько важных моментов:"

#: src/guides/collecting/sparrow-wallet.md:125
msgid "The `output` identifier matches the identifier of the UTXO you are going to send"
msgstr "Идентификатор `output` совпадает с идентификатором UTXO, который вы собираетесь отправить"

#: src/guides/collecting/sparrow-wallet.md:126
msgid "The `offset` of the inscription is `0` (this means that the inscription is located on the first sat in the UTXO)"
msgstr "`offset` надписи равно `0` (это означает, что надпись расположена на первом сате в UTXO)"

#: src/guides/collecting/sparrow-wallet.md:127
msgid ""
"the `output_value` has enough sats to cover the transaction fee (postage) for sending the transaction. The exact amount you will need depends on the fee rate "
"you will select for the transaction"
msgstr ""
"`output_value` имеет достаточное количество sats, чтобы покрыть комиссию за отправку транзакции. Точная сумма, которая вам потребуется, зависит от выбранной "
"вами ставки комиссии за транзакцию"

#: src/guides/collecting/sparrow-wallet.md:129
msgid "If all of the above are true for your inscription, it should be safe for you to send it using the method below."
msgstr "Если все вышеперечисленное соответствует для вашей надписи, то ее можно смело отправлять указанным ниже способом."

#: src/guides/collecting/sparrow-wallet.md:131
msgid ""
"⚠️⚠️ Be very careful sending your inscription particularly if the `offset` value is not `0`. It is not recommended to use this method if that is the case, as "
"doing so you could accidentally send your inscription to a bitcoin miner unless you know what you are doing."
msgstr ""
"⚠️⚠️ Будьте очень осторожны при отправке надписи, особенно если значение `offset` не равно `0`. Не рекомендуется использовать этот метод, так как в этом случае "
"вы можете случайно отправить надпись майнеру биткоинов, если вы не знаете, что делаете."

#: src/guides/collecting/sparrow-wallet.md:133
msgid "Sending your inscription"
msgstr "Отправка вашей надписи"

#: src/guides/collecting/sparrow-wallet.md:134
msgid "To send an inscription navigate to the `UTXOs` tab, and find the UTXO which you previously validated contains your inscription."
msgstr "Для отправки надписи перейдите на вкладку `UTXOs` и найдите UTXO, который, как вы ранее подтвердили, содержит вашу надпись."

#: src/guides/collecting/sparrow-wallet.md:136
msgid "If you previously froze the UXTO you will need to right-click on it and unfreeze it."
msgstr "Если ранее UXTO был заморожен, необходимо щелкнуть на нем правой кнопкой мыши и разморозить его."

#: src/guides/collecting/sparrow-wallet.md:138
msgid ""
"Select the UTXO you want to send, and ensure that is the _only_ UTXO is selected. You should see `UTXOs 1/1` in the interface. Once you are sure this is the "
"case you can hit `Send Selected`."
msgstr ""
"Выберите UTXO, который вы хотите отправить, и убедитесь, что выбран _единственный_ UTXO. В интерфейсе должна появиться надпись `UTXOs 1/1`. Убедившись, что это "
"так, нажмите кнопку `Send Selected`."

#: src/guides/collecting/sparrow-wallet.md:140
msgid "![](images/sending_02.png)"
msgstr "![](images/sending_02.png)"

#: src/guides/collecting/sparrow-wallet.md:142
msgid ""
"You will then be presented with the transaction construction interface. There is a few things you need to check here to make sure that this is a safe send:"
msgstr "После этого появится интерфейс создания транзакции. Здесь необходимо проверить несколько моментов, чтобы убедиться в безопасности отправки:"

#: src/guides/collecting/sparrow-wallet.md:144
msgid "The transaction should have only 1 input, and this should be the UTXO with the label you want to send"
msgstr "Транзакция должна иметь только один вход, и это должен быть UTXO с лейблом, которую вы хотите отправить"

#: src/guides/collecting/sparrow-wallet.md:145
msgid "The transaction should have only 1 output, which is the address/label where you want to send the inscription"
msgstr "Транзакция должна иметь только один выход - адрес/лейбл, на который необходимо отправить надпись"

#: src/guides/collecting/sparrow-wallet.md:147
msgid ""
"If your transaction looks any different, for example you have multiple inputs, or multiple outputs then this may not be a safe transfer of your inscription, "
"and you should abandon sending until you understand more, or can import into the `ord` wallet."
msgstr ""
"Если ваша транзакция выглядит иначе, например, у вас несколько входов или несколько выходов, то это может быть небезопасной передачей вашей надписи, и вам "
"следует отказаться от отправки, пока вы не поймете больше или не сможете импортировать в кошелек `ord`."

#: src/guides/collecting/sparrow-wallet.md:149
msgid ""
"You should set an appropriate transaction fee, Sparrow will usually recommend a reasonable one, but you can also check [mempool.space](https://mempool.space) "
"to see what the recommended fee rate is for sending a transaction."
msgstr ""
"Вы должны установить соответствующую плату за транзакцию, обычно Sparrow рекомендует разумную, но вы также можете проверить [mempool.space](https://mempool."
"space), чтобы узнать, какова рекомендуемая ставка платы комиссии за отправку транзакции."

#: src/guides/collecting/sparrow-wallet.md:151
msgid "You should add a label for the recipient address, a label like `alice address for inscription #123` would be ideal."
msgstr "Необходимо добавить лейбл для адреса получателя, идеальным вариантом будет метка типа `alice address for inscription #123`."

#: src/guides/collecting/sparrow-wallet.md:153
msgid "Once you have checked the transaction is a safe transaction using the checks above, and you are confident to send it you can click `Create Transaction`."
msgstr ""
"После того как вы убедились в безопасности транзакции с помощью описанных выше проверок и уверены в ее отправке, вы можете нажать кнопку `Create Transaction`."

#: src/guides/collecting/sparrow-wallet.md:155
msgid "![](images/sending_03.png)"
msgstr "![](images/sending_03.png)"

#: src/guides/collecting/sparrow-wallet.md:157
msgid "Here again you can double check that your transaction looks safe, and once you are confident you can click `Finalize Transaction for Signing`."
msgstr "Здесь вы можете еще раз убедиться в безопасности транзакции и, убедившись в этом, нажать кнопку `Finalize Transaction for Signing`."

#: src/guides/collecting/sparrow-wallet.md:159
msgid "![](images/sending_04.png)"
msgstr "![](images/sending_04.png)"

#: src/guides/collecting/sparrow-wallet.md:161
msgid "Here you can triple check everything before hitting `Sign`."
msgstr "Здесь вы можете трижды проверить все, прежде чем нажать кнопку `Sign`."

#: src/guides/collecting/sparrow-wallet.md:163
msgid "![](images/sending_05.png)"
msgstr "![](images/sending_05.png)"

#: src/guides/collecting/sparrow-wallet.md:165
msgid ""
"And then actually you get very very last chance to check everything before hitting `Broadcast Transaction`. Once you broadcast the transaction it is sent to "
"the bitcoin network, and starts being propagated into the mempool."
msgstr ""
"И тут у вас появляется самый последний шанс все проверить, прежде чем нажать кнопку `Broadcast Transaction`. Как только вы передали транзакцию, она "
"отправляется в сеть bitcoin и начинает распространяться в mempool."

#: src/guides/collecting/sparrow-wallet.md:167
msgid "![](images/sending_06.png)"
msgstr "![](images/sending_06.png)"

#: src/guides/collecting/sparrow-wallet.md:169
msgid "If you want to track the status of your transaction you can copy the `Transaction Id (Txid)` and paste that into [mempool.space](https://mempool.space)"
msgstr "Если вы хотите отслеживать статус вашей транзакции, вы можете скопировать `Transaction Id (Txid)` и вставить его в [mempool.space](https://mempool.space)"

#: src/guides/collecting/sparrow-wallet.md:171
msgid ""
"Once the transaction has confirmed you can check the inscription page on [ordinals.com](https://ordinals.com) to validate that it has moved to the new output "
"location and address."
msgstr ""
"После подтверждения транзакции вы можете проверить страницу надписи на сайте [ordinals.com](https://ordinals.com), чтобы убедиться, что она переместилась на "
"новое место и адрес вывода."

#: src/guides/collecting/sparrow-wallet.md:173
msgid "Troubleshooting"
msgstr "Устранение проблем"

#: src/guides/collecting/sparrow-wallet.md:175
msgid "Sparrow wallet is not showing a transaction/UTXO, but I can see it on mempool.space!"
msgstr "Sparrow wallet не показывает транзакцию/UTXO, но я вижу ее в mempool.space!"

#: src/guides/collecting/sparrow-wallet.md:177
msgid ""
"Make sure that your wallet is connected to a bitcoin node. To validate this, head into the `Preferences`\\-> `Server` settings, and click `Edit Existing "
"Connection`."
msgstr ""
"Убедитесь, что ваш кошелек подключен к Bitcoin node. Чтобы проверить это, зайдите в настройки `Preferences`\\-> `Server` settings и нажмите кнопку `Edit "
"Existing Connection`."

#: src/guides/collecting/sparrow-wallet.md:179
msgid "![](images/troubleshooting_01.png)"
msgstr "![](images/troubleshooting_01.png)"

#: src/guides/collecting/sparrow-wallet.md:181
msgid "From there you can select a node and click `Test Connection` to validate that Sparrow is able to connect successfully."
msgstr "После этого можно выбрать node и нажать кнопку `Test Connection`, чтобы убедиться, что Sparrow успешно подключился."

#: src/guides/collecting/sparrow-wallet.md:183
msgid "![](images/troubleshooting_02.png)"
msgstr "![](images/troubleshooting_02.png)"

#: src/guides/testing.md:4
msgid ""
"Ord can be tested using the following flags to specify the test network. For more information on running Bitcoin Core for testing, see [Bitcoin's developer "
"documentation](https://developer.bitcoin.org/examples/testing.html)."
msgstr ""
"Ord можно протестировать, используя следующие флаги для указания тестовой сети. Более подробную информацию о запуске Bitcoin Core для тестирования смотрите в "
"[документации разработчика Bitcoin](https://developer.bitcoin.org/examples/testing.html)."

#: src/guides/testing.md:7
msgid "Most `ord` commands in [inscriptions](inscriptions.md) and [explorer](explorer.md) can be run with the following network flags:"
msgstr "Большинство команд `ord` в [inscriptions](inscriptions.md) и [explorer](explorer.md) могут быть запущены со следующими нетворк флагами:"

#: src/guides/testing.md:10
msgid "Network"
msgstr "Network"

#: src/guides/testing.md:10
msgid "Flag"
msgstr "Flag"

#: src/guides/testing.md:12
msgid "Testnet"
msgstr "Testnet"

#: src/guides/testing.md:12
msgid "`--testnet` or `-t`"
msgstr "`--testnet` or `-t`"

#: src/guides/testing.md:13
msgid "Signet"
msgstr "Signet"

#: src/guides/testing.md:13
msgid "`--signet` or `-s`"
msgstr "`--signet` or `-s`"

#: src/guides/testing.md:14
msgid "Regtest"
msgstr "Regtest"

#: src/guides/testing.md:14
msgid "`--regtest` or `-r`"
msgstr "`--regtest` or `-r`"

#: src/guides/testing.md:16
msgid "Regtest doesn't require downloading the blockchain or indexing ord."
msgstr "Regtest не требует загрузки блокчейна или индексации ord."

#: src/guides/testing.md:21
msgid "Run bitcoind in regtest with:"
msgstr "Запустите bitcoind в regtest с:"

#: src/guides/testing.md:22
msgid ""
"```\n"
"bitcoind -regtest -txindex\n"
"```"
msgstr ""
"```\n"
"bitcoind -regtest -txindex\n"
"```"

#: src/guides/testing.md:25
msgid "Create a wallet in regtest with:"
msgstr "Создать кошелек в regtest с:"

#: src/guides/testing.md:26
msgid ""
"```\n"
"ord -r wallet create\n"
"```"
msgstr ""
"```\n"
"ord -r wallet create\n"
"```"

#: src/guides/testing.md:29
msgid "Get a regtest receive address with:"
msgstr "Получите regtest адрес для приема с помощью:"

#: src/guides/testing.md:30
msgid ""
"```\n"
"ord -r wallet receive\n"
"```"
msgstr ""
"```\n"
"ord -r wallet receive\n"
"```"

#: src/guides/testing.md:33
msgid "Mine 101 blocks (to unlock the coinbase) with:"
msgstr "Добыть 101 блок (для разблокировки coinbase) с помощью:"

#: src/guides/testing.md:34
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 101 <receive address>\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 101 <receive address>\n"
"```"

#: src/guides/testing.md:37
msgid "Inscribe in regtest with:"
msgstr "Inscribe в regtest с:"

#: src/guides/testing.md:38
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file <file>\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file <file>\n"
"```"

#: src/guides/testing.md:41
msgid "Mine the inscription with:"
msgstr "Добыть надпись с:"

#: src/guides/testing.md:42
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 1 <receive address>\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 1 <receive address>\n"
"```"

#: src/guides/testing.md:45
msgid "View the inscription in the regtest explorer:"
msgstr "Посмотреть надпись в regtest explorer:"

#: src/guides/testing.md:46
msgid ""
"```\n"
"ord -r server\n"
"```"
msgstr ""
"```\n"
"ord -r server\n"
"```"

#: src/guides/testing.md:50
msgid "Testing Recursion"
msgstr "Тестирование рекурсии"

#: src/guides/testing.md:53
msgid "When testing out [recursion](../inscriptions/recursion.md), inscribe the dependencies first (example with [p5.js](https://p5js.org):"
msgstr "При тестировании [рекурсии](../inscriptions/recursion.md), сначала пропишите зависимости (пример с[p5.js](https://p5js.org):"

#: src/guides/testing.md:55
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file p5.js\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file p5.js\n"
"```"

#: src/guides/testing.md:58
msgid "This should return a `inscription_id` which you can then reference in your recursive inscription."
msgstr "Это должно вернуть значение `inscription_id`, на которое затем можно ссылаться в рекурсивной надписи."

#: src/guides/testing.md:61
msgid "ATTENTION: These ids will be different when inscribing on mainnet or signet, so be sure to change those in your recursive inscription for each chain."
msgstr "ВНИМАНИЕ: Эти ID будут отличаться при вписывании в mainnet и signet, поэтому не забудьте изменить их в рекурсивном вписывании для каждого чейна."

#: src/guides/testing.md:65
msgid "Then you can inscribe your recursive inscription with:"
msgstr "Затем можно нанести рекурсивную надпись с помощью:"

#: src/guides/testing.md:66
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file recursive-inscription.html\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file recursive-inscription.html\n"
"```"

#: src/guides/testing.md:69
msgid "Finally you will have to mine some blocks and start the server:"
msgstr "Наконец, нужно добыть несколько блоков и запустить сервер:"

#: src/guides/testing.md:70
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 6 <receive address>\n"
"ord -r server\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 6 <receive address>\n"
"ord -r server\n"
"```"

#: src/guides/moderation.md:4
msgid "`ord` includes a block explorer, which you can run locally with `ord server`."
msgstr "В состав `ord` входит обозреватель блоков, который можно запустить локально с помощью `ord server`."

#: src/guides/moderation.md:6
msgid "The block explorer allows viewing inscriptions. Inscriptions are user-generated content, which may be objectionable or unlawful."
msgstr "Обозреватель блоков позволяет просматривать надписи. Надписи - это пользовательский контент, который может быть нежелательным или противозаконным."

#: src/guides/moderation.md:9
msgid ""
"It is the responsibility of each individual who runs an ordinal block explorer instance to understand their responsibilities with respect to unlawful content, "
"and decide what moderation policy is appropriate for their instance."
msgstr ""
"Каждый пользователь, управляющий экземпляром ordinal block explorer, обязан понимать свою ответственность в отношении противоправного контента и решать, какая "
"политика модерирования подходит для его экземпляра."

#: src/guides/moderation.md:13
msgid ""
"In order to prevent particular inscriptions from being displayed on an `ord` instance, they can be included in a YAML config file, which is loaded with the `--"
"config` option."
msgstr ""
"Для того чтобы запретить отображение определенных надписей на экземпляре `ord`, их можно включить в конфиг файл YAML, который загружается с помощью опции `--"
"config`."

#: src/guides/moderation.md:17
msgid "To hide inscriptions, first create a config file, with the inscription ID you want to hide:"
msgstr "Чтобы скрыть надписи, сначала создайте файл конфиг с ID надписи, которую необходимо скрыть:"

#: src/guides/moderation.md:20
msgid ""
"```yaml\n"
"hidden:\n"
"- 0000000000000000000000000000000000000000000000000000000000000000i0\n"
"```"
msgstr ""
"```yaml\n"
"hidden:\n"
"- 0000000000000000000000000000000000000000000000000000000000000000i0\n"
"```"

#: src/guides/moderation.md:25
msgid "The suggested name for `ord` config files is `ord.yaml`, but any filename can be used."
msgstr "Предлагаемое имя конфиг файлов `ord` - `ord.yaml`, но можно использовать любое имя."

#: src/guides/moderation.md:28
msgid "Then pass the file to `--config` when starting the server:"
msgstr "Затем передайте этот файл в `--config` при запуске сервера:"

#: src/guides/moderation.md:30
msgid "`ord --config ord.yaml server`"
msgstr "`ord --config ord.yaml server`"

#: src/guides/moderation.md:32
msgid "Note that the `--config` option comes after `ord` but before the `server` subcommand."
msgstr "Обратите внимание, что опция `--config` идет после `ord`, но перед подкомандой `server`."

#: src/guides/moderation.md:35
msgid "`ord` must be restarted in to load changes to the config file."
msgstr "Для загрузки изменений в конфиг файл необходимо перезапустить `ord`."

#: src/guides/moderation.md:37
msgid "`ordinals.com`"
msgstr "`ordinals.com`"

#: src/guides/moderation.md:40
msgid "The `ordinals.com` instances use `systemd` to run the `ord server` service, which is called `ord`, with a config file located at `/var/lib/ord/ord.yaml`."
msgstr ""
"Экземпляры `ordinals.com` используют `systemd` для запуска службы `ord server`, которая называется `ord`, с файлом конфига, расположенным по адресу `/var/lib/"
"ord/ord.yaml`."

#: src/guides/moderation.md:43
msgid "To hide an inscription on `ordinals.com`:"
msgstr "Чтобы скрыть надпись на `ordinals.com`:"

#: src/guides/moderation.md:45
msgid "SSH into the server"
msgstr "SSH на сервер"

#: src/guides/moderation.md:46
msgid "Add the inscription ID to `/var/lib/ord/ord.yaml`"
msgstr "Добавьте ID надписи в `/var/lib/ord/ord.yaml`"

#: src/guides/moderation.md:47
msgid "Restart the service with `systemctl restart ord`"
msgstr "Перезапустите службу с помощью команды `systemctl restart ord`"

#: src/guides/moderation.md:48
msgid "Monitor the restart with `journalctl -u ord`"
msgstr "Мониторинг перезапуска с помощью команды `journalctl -u ord`"

#: src/guides/moderation.md:50
msgid "Currently, `ord` is slow to restart, so the site will not come back online immediately."
msgstr "В настоящее время `ord` медленно перезапускается, поэтому сайт вернется в онлайн не сразу."

#: src/guides/reindexing.md:4
msgid ""
"Sometimes the `ord` database must be reindexed, which means deleting the database and restarting the indexing process with either `ord index run` or `ord "
"server`. Reasons to reindex are:"
msgstr ""
"Иногда базу данных `ord` необходимо переиндексировать, что означает удаление базы данных и повторный запуск процесса индексирования с помощью `ord index run` "
"или `ord server`. Причинами для переиндексации являются:"

#: src/guides/reindexing.md:8
msgid "A new major release of ord, which changes the database scheme"
msgstr "Новый основной релиз ord, в котором изменена схема базы данных"

#: src/guides/reindexing.md:9
msgid "The database got corrupted somehow"
msgstr "База данных каким-то образом была повреждена"

#: src/guides/reindexing.md:11
msgid ""
"The database `ord` uses is called [redb](https://github.com/cberner/redb), so we give the index the default file name `index.redb`. By default we store this "
"file in different locations depending on your operating system."
msgstr ""
"База данных, которую использует `ord`, называется [redb](https://github.com/cberner/redb), поэтому по умолчанию мы присваиваем индексу имя файла `index.redb`. "
"По умолчанию мы храним этот файл в разных местах в зависимости от операционной системы."

#: src/guides/reindexing.md:15
msgid "Platform"
msgstr "OC"

#: src/guides/reindexing.md:15
msgid "Value"
msgstr "Value"

#: src/guides/reindexing.md:17
msgid "Linux"
msgstr "Linux"

#: src/guides/reindexing.md:17
msgid "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"
msgstr "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"

#: src/guides/reindexing.md:17
msgid "/home/<USER>/.local/share/ord"
msgstr "/home/<USER>/.local/share/ord"

#: src/guides/reindexing.md:18
msgid "macOS"
msgstr "macOS"

#: src/guides/reindexing.md:18
msgid "`$HOME`/Library/Application Support/ord"
msgstr "`$HOME`/Library/Application Support/ord"

#: src/guides/reindexing.md:18
msgid "/Users/<USER>/Library/Application Support/ord"
msgstr "/Users/<USER>/Library/Application Support/ord"

#: src/guides/reindexing.md:19
msgid "Windows"
msgstr "Windows"

#: src/guides/reindexing.md:19
msgid "`{FOLDERID_RoamingAppData}`\\\\ord"
msgstr "`{FOLDERID_RoamingAppData}`\\\\ord"

#: src/guides/reindexing.md:19
msgid "C:\\Users\\<USER>\\AppData\\Roaming\\ord"
msgstr "C:\\Users\\<USER>\\AppData\\Roaming\\ord"

#: src/guides/reindexing.md:21
msgid "So to delete the database and reindex on MacOS you would have to run the following commands in the terminal:"
msgstr "Поэтому для удаления базы данных и повторной индексации на MacOS необходимо выполнить в терминале следующие команды:"

#: src/guides/reindexing.md:24
msgid ""
"```bash\n"
"rm ~/Library/Application Support/ord/index.redb\n"
"ord index run\n"
"```"
msgstr ""
"```bash\n"
"rm ~/Library/Application Support/ord/index.redb\n"
"ord index run\n"
"```"

#: src/guides/reindexing.md:29
msgid ""
"You can of course also set the location of the data directory yourself with `ord --datadir <DIR> index run` or give it a specific filename and path with `ord "
"--index <FILENAME> index run`."
msgstr ""
"Конечно, можно также самостоятельно задать расположение директории данных командой `ord --datadir <DIR> index run` или указать ему конкретное имя файла и путь "
"к нему командой `ord --index <FILENAME> index run`."

#: src/bounties.md:1
msgid "Ordinal Bounty Hunting Hints"
msgstr "Подсказки по охоте на Ordinal"

#: src/bounties.md:4
msgid ""
"The `ord` wallet can send and receive specific satoshis. Additionally, ordinal theory is extremely simple. A clever hacker should be able to write code from "
"scratch to manipulate satoshis using ordinal theory in no time."
msgstr ""
"Кошелек `ord` может отправлять и получать определенные сатоши. Кроме того, ordinal theory чрезвычайно проста. Умный хакер сможет с нуля написать код для "
"манипуляций с сатоши с помощью ordinal theory в кратчайшие сроки."

#: src/bounties.md:8
msgid ""
"For more information about ordinal theory, check out the [FAQ](./faq.md) for an overview, the [BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) "
"for the technical details, and the [ord repo](https://github.com/ordinals/ord) for the `ord` wallet and block explorer."
msgstr ""
"Более подробную информацию о теории порядков можно найти в [ЧАВО](./faq.md) для общего обзора, в [BIP](https://github.com/ordinals/ord/blob/master/bip."
"mediawiki) для технических деталей, а также в [ord repo](https://github.com/ordinals/ord) для кошелька `ord` и обозревателя блоков."

#: src/bounties.md:14
msgid ""
"Satoshi was the original developer of ordinal theory. However, he knew that others would consider it heretical and dangerous, so he hid his knowledge, and it "
"was lost to the sands of time. This potent theory is only now being rediscovered. You can help by researching rare satoshis."
msgstr ""
"Сатоши Накамото был первым разработчиком ordinal theory. Однако он знал, что другие сочтут ее еретической и опасной, поэтому скрыл свои знания, и они были "
"утеряны в песках времени. Только сейчас эту мощную теорию открывают заново. Вы можете помочь в этом, исследуя редкие сатоши."

#: src/bounties.md:19
msgid "Good luck and godspeed!"
msgstr "Удачи, и да прибудет с вами сила!"

#: src/bounty/0.md:1
msgid "Ordinal Bounty 0"
msgstr "Ordinal вознаграждение 0"

#: src/bounty/0.md:4 src/bounty/1.md:4 src/bounty/2.md:4 src/bounty/3.md:4
msgid "Criteria"
msgstr "Критерии"

#: src/bounty/0.md:7
msgid "Send a sat whose ordinal number ends with a zero to the submission address:"
msgstr "Отправить sat, ordinal номер которого заканчивается нулем, по адресу отправки:"

#: src/bounty/0.md:9
msgid "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"
msgstr "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"

#: src/bounty/0.md:11
msgid "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"
msgstr "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"

#: src/bounty/0.md:13
msgid "The sat must be the first sat of the output you send."
msgstr "Значение sat должно быть первым sat в передаваемом выводе."

#: src/bounty/0.md:15 src/bounty/1.md:14 src/bounty/2.md:15 src/bounty/3.md:63
msgid "Reward"
msgstr "Награда"

#: src/bounty/0.md:18
msgid "100,000 sats"
msgstr "100,000 sats"

#: src/bounty/0.md:20 src/bounty/1.md:19 src/bounty/2.md:20 src/bounty/3.md:70
msgid "Submission Address"
msgstr "Адрес для отправки"

#: src/bounty/0.md:23
msgid "[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"
msgstr "[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"

#: src/bounty/0.md:25 src/bounty/1.md:24 src/bounty/2.md:25 src/bounty/3.md:75
msgid "Status"
msgstr "Статус"

#: src/bounty/0.md:28
msgid "Claimed by [@count_null](https://twitter.com/rodarmor/status/1560793241473400833)!"
msgstr "Собрано[@count_null](https://twitter.com/rodarmor/status/1560793241473400833)!"

#: src/bounty/1.md:1
msgid "Ordinal Bounty 1"
msgstr "Ordinal вознаграждение 1"

#: src/bounty/1.md:7
msgid "The transaction that submits a UTXO containing the oldest sat, i.e., that with the lowest number, amongst all submitted UTXOs will be judged the winner."
msgstr ""
"Победителем будет признана та сделка, которая представит UTXO, содержащий самый старый sat, т.е. имеющий наименьший номер, среди всех представленных UTXO."

#: src/bounty/1.md:10
msgid ""
"The bounty is open for submissions until block 753984—the first block of difficulty adjustment period 374. Submissions included in block 753984 or later will "
"not be considered."
msgstr ""
"Заявки на участие в конкурсе принимаются до блока 753984 - первого блока 374 периода корректировки сложности. Заявки, поданные в блок 753984 или позже, "
"рассматриваться не будут."

#: src/bounty/1.md:17
msgid "200,000 sats"
msgstr "200,000 sats"

#: src/bounty/1.md:22
msgid "[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"
msgstr "[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"

#: src/bounty/1.md:27
msgid "Claimed by [@ordinalsindex](https://twitter.com/rodarmor/status/1569883266508853251)!"
msgstr "Собрано [@ordinalsindex](https://twitter.com/rodarmor/status/1569883266508853251)!"

#: src/bounty/2.md:1
msgid "Ordinal Bounty 2"
msgstr "Ordinal вознаграждение 2"

#: src/bounty/2.md:7
msgid "Send an "
msgstr "Отправить "

#: src/bounty/2.md:7
msgid "uncommon"
msgstr "uncommon"

#: src/bounty/2.md:7
msgid " sat to the submission address:"
msgstr " sat на адрес для отправки:"

#: src/bounty/2.md:9
msgid "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"
msgstr "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"

#: src/bounty/2.md:11
msgid "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"
msgstr "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"

#: src/bounty/2.md:13
msgid "Confirm that the submission address has not received transactions before submitting your entry. Only the first successful submission will be rewarded."
msgstr "Перед отправкой заявки убедитесь в том, что по адресу отправки не было транзакций. Только первая успешная заявка будет вознаграждена."

#: src/bounty/2.md:18
msgid "300,000 sats"
msgstr "300,000 sats"

#: src/bounty/2.md:23
msgid "[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"
msgstr "[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"

#: src/bounty/2.md:28
msgid "Claimed by [@utxoset](https://twitter.com/rodarmor/status/1582424455615172608)!"
msgstr "Собрано [@utxoset](https://twitter.com/rodarmor/status/1582424455615172608)!"

#: src/bounty/3.md:1
msgid "Ordinal Bounty 3"
msgstr "Ordinal вознаграждение 3"

#: src/bounty/3.md:7
msgid ""
"Ordinal bounty 3 has two parts, both of which are based on _ordinal names_. Ordinal names are a modified base-26 encoding of ordinal numbers. To avoid locking "
"short names inside the unspendable genesis block coinbase reward, ordinal names get _shorter_ as the ordinal number gets _longer_. The name of sat 0, the first "
"sat to be mined is `nvtdijuwxlp` and the name of sat 2,099,999,997,689,999, the last sat to be mined, is `a`."
msgstr ""
"Ordinal вознаграждение 3 состоит из двух частей, обе из которых основаны на _ordinal именах_. Ordinal имена представляют собой модифицированную кодировку "
"порядковых чисел base-26. Чтобы избежать блокировки коротких имен внутри неизрасходованного вознаграждения coinbase блока genesis, порядковые имена становятся "
"_короче_ по мере того, как порядковый номер становится _длиннее_. Имя сата 0, первого sat, который будет добыт, - `nvtdijuwxlp`, а имя sat 2 099 999 997 689 "
"999, последнего sat, который будет добыт, - `a`."

#: src/bounty/3.md:14
msgid ""
"The bounty is open for submissions until block 840000—the first block after the fourth halvening. Submissions included in block 840000 or later will not be "
"considered."
msgstr ""
"Заявки на участие в конкурсе принимаются до блока 840000 - первого блока после четвертого халвинга. Заявки, включенные в блок 840000 или позже, рассматриваться "
"не будут."

#: src/bounty/3.md:18
msgid ""
"Both parts use [frequency.tsv](frequency.tsv), a list of words and the number of times they occur in the [Google Books Ngram dataset](http://storage.googleapis."
"com/books/ngrams/books/datasetsv2.html). filtered to only include the names of sats which will have been mined by the end of the submission period, that appear "
"at least 5000 times in the corpus."
msgstr ""
"В обеих частях используется [frequency.tsv](frequency.tsv) - список слов и количество их повторений в [Google Books Ngram dataset](http://storage.googleapis."
"com/books/ngrams/books/datasetsv2.html), отфильтрованный для включения только тех названий sats, которые будут добыты к концу периода подачи заявок и которые "
"встречаются в корпусе не менее 5000 раз."

#: src/bounty/3.md:24
msgid ""
"`frequency.tsv` is a file of tab-separated values. The first column is the word, and the second is the number of times it appears in the corpus. The entries "
"are sorted from least-frequently occurring to most-frequently occurring."
msgstr ""
"`frequency.tsv` представляет собой файл значений, разделенных табуляцией. Первый столбец - это слово, второй - количество раз, которое оно встречается в "
"корпусе. Записи отсортированы от наименее часто встречающегося к наиболее часто встречающемуся."

#: src/bounty/3.md:29
msgid "`frequency.tsv` was compiled using [this program](https://github.com/casey/onegrams)."
msgstr "`frequency.tsv` был скомпилирован с помощью [этой программы](https://github.com/casey/onegrams)."

#: src/bounty/3.md:32
msgid "To search an `ord` wallet for sats with a name in `frequency.tsv`, use the following [`ord`](https://github.com/ordinals/ord) command:"
msgstr "Для поиска в кошельке `ord` sats с именем в файле `frequency.tsv` используйте следующую команду [`ord`](https://github.com/ordinals/ord):"

#: src/bounty/3.md:35
msgid ""
"```\n"
"ord wallet sats --tsv frequency.tsv\n"
"```"
msgstr ""
"```\n"
"ord wallet sats --tsv frequency.tsv\n"
"```"

#: src/bounty/3.md:39
msgid "This command requires the sat index, so `--index-sats` must be passed to ord when first creating the index."
msgstr "Эта команда требует индекса sat, поэтому при первом создании индекса в ord необходимо передать `--index-sats`."

#: src/bounty/3.md:42
msgid "Part 0"
msgstr "Часть 0"

#: src/bounty/3.md:44
msgid "_Rare sats pair best with rare words._"
msgstr "_Пара редких sats лучше всего сочетаются с парой редких слов._"

#: src/bounty/3.md:46
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears with the lowest number of occurrences in `frequency.tsv` shall be the winner of "
"part 0."
msgstr ""
"Победителем 0 части становится транзакция, представившая UTXO, содержащий sat, название которого встречается в файле `frequency.tsv` с наименьшим числом "
"повторений."

#: src/bounty/3.md:50
msgid "Part 1"
msgstr "Часть 1"

#: src/bounty/3.md:52
msgid "_Popularity is the font of value._"
msgstr "_Популярность - это шрифт ценности._"

#: src/bounty/3.md:54
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears with the highest number of occurrences in `frequency.tsv` shall be the winner of "
"part 1."
msgstr ""
"Победителем в части 1 становится транзакция, представившая UTXO, содержащий sat, название которого встречается в файле `frequency.tsv` с наибольшим числом "
"повторений."

#: src/bounty/3.md:58
msgid "Tie Breaking"
msgstr "Разрыв равенства"

#: src/bounty/3.md:60
msgid "In the case of a tie, where two submissions occur with the same frequency, the earlier submission shall be the winner."
msgstr "В случае равенства, когда две заявки происходят с одинаковой частотой, победителем признается более ранняя заявка."

#: src/bounty/3.md:66
msgid "Part 0: 200,000 sats"
msgstr "Част 0: 200,000 sats"

#: src/bounty/3.md:67
msgid "Part 1: 200,000 sats"
msgstr "Часть 1: 200,000 sats"

#: src/bounty/3.md:68
msgid "Total: 400,000 sats"
msgstr "Итого: 400,000 sats"

#: src/bounty/3.md:73
msgid "[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"
msgstr "[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"

#: src/bounty/3.md:78
msgid "Unclaimed!"
msgstr "Не собрано!"
