[package]
name = "opi-runes-ord"
description = "◉ Ordinal wallet and block explorer"
version = "0.18.1-2"
license = "CC0-1.0"
edition = "2021"
autotests = false
homepage = "https://github.com/ordinals/ord"
repository = "https://github.com/ordinals/ord"
autobins = false
rust-version = "1.76.0"

[package.metadata.deb]
copyright = "The Ord Maintainers"
maintainer = "The Ord Maintainers"

[workspace]
members = [".", "crates/*"]

[dependencies]
anyhow = { version = "1.0.56", features = ["backtrace"] }
async-trait = "0.1.72"
axum = { version = "0.6.1", features = ["http2"] }
axum-server = "0.5.0"
base64 = "0.22.0"
bech32 = "0.11.0"
bip39 = "2.0.0"
bitcoin = { version = "0.30.1", features = ["rand"] }
boilerplate = { version = "1.0.0", features = ["axum"] }
brotli = "4.0.0"
chrono = { version = "0.4.19", features = ["serde"] }
ciborium = "0.2.1"
clap = { version = "4.4.2", features = ["derive"] }
colored = "2.0.4"
ctrlc = { version = "3.2.1", features = ["termination"] }
dirs = "5.0.0"
env_logger = "0.11.0"
futures = "0.3.21"
hex = "0.4.3"
html-escaper = "0.2.0"
http = "0.2.6"
humantime = "2.1.0"
hyper = { version = "0.14.24", features = ["client", "http2"] }
indicatif = "0.17.1"
lazy_static = "1.4.0"
log = "0.4.14"
mime = "0.3.16"
mime_guess = "2.0.4"
miniscript = "10.0.0"
mp4 = "0.14.0"
ord-bitcoincore-rpc = "0.17.2"
ordinals = { version = "0.0.7", path = "crates/ordinals" }
redb = "2.0.0"
regex = "1.6.0"
reqwest = { version = "0.11.23", features = ["blocking", "json"] }
rss = "2.0.1"
rust-embed = "8.0.0"
rustls = "0.22.0"
rustls-acme = { version = "0.8.1", features = ["axum"] }
serde = { version = "1.0.137", features = ["derive"] }
serde-hex = "0.1.0"
serde_json = { version = "1.0.81", features = ["preserve_order"] }
serde_with = "3.7.0"
serde_yaml = "0.9.17"
sha3 = "0.10.8"
sysinfo = "0.30.3"
tempfile = "3.2.0"
tokio = { version = "1.17.0", features = ["rt-multi-thread"] }
tokio-stream = "0.1.9"
tokio-util = {version = "0.7.3", features = ["compat"] }
tower-http = { version = "0.4.0", features = ["auth", "compression-br", "compression-gzip", "cors", "set-header"] }
urlencoding = "2.1.3"

[dev-dependencies]
criterion = "0.5.1"
executable-path = "1.0.0"
nix = { version = "0.28.0", features = ["signal"] }
pretty_assertions = "1.2.1"
reqwest = { version = "0.11.10", features = ["blocking", "brotli", "json"] }
mockcore = { path = "crates/mockcore" }
unindent = "0.2.1"

[[bin]]
name = "ord"
path = "src/bin/main.rs"

[lib]
name = "ord"
path = "src/lib.rs"

[[test]]
name = "integration"
path = "tests/lib.rs"

[build-dependencies]
pulldown-cmark = "0.10.0"
