create INPUT_TXID INPUT_VOUT OUTPUT_DESTINATION OUTPUT_AMOUNT:
  #!/usr/bin/env bash

  set -euxo pipefail

  bitcoin-cli createrawtransaction \
    '[
      {
        "txid": "{{INPUT_TXID}}",
        "vout": {{INPUT_VOUT}}
      }
    ]' \
    '[
      {
        "{{OUTPUT_DESTINATION}}": {{OUTPUT_AMOUNT}}
      }
    ]' \
    > raw.hex

sign WALLET_NAME:
  bitcoin-cli -rpcwallet={{WALLET_NAME}} signrawtransactionwithwallet `cat raw.hex` > signed.json

send:
  bitcoin-cli sendrawtransaction `cat signed.json | jq '.hex' --raw-output`
