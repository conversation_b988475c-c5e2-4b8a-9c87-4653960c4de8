# --------------------------------------------------------
# PostgreSQL 14 精简配置（128 GB RAM / 64 Core）
# --------------------------------------------------------

# --- 文件路径与集群标识 ---
data_directory      = '/home/<USER>'
hba_file            = '/etc/postgresql/14/main/pg_hba.conf'
ident_file          = '/etc/postgresql/14/main/pg_ident.conf'
external_pid_file   = '/var/run/postgresql/14-main.pid'
cluster_name        = '14/main'

# --- 监听与连接 ---
listen_addresses    = '*'          # 允许所有网卡
port                = 5432
max_connections     = 3000
unix_socket_directories = '/var/run/postgresql'

# --- 内存与并发 ---
shared_buffers        = 32GB       # ≈ 25% RAM
effective_cache_size  = 96GB       # ≈ 75% RAM
work_mem              = 64MB
maintenance_work_mem  = 4GB
shared_preload_libraries = 'pg_stat_statements'

# --- WAL & 检查点 ---
checkpoint_completion_target = 0.9
max_wal_size          = 1GB
min_wal_size          = 80MB

# --- 时区与日志 ---
timezone              = 'Asia/Shanghai'
log_timezone          = 'Asia/Shanghai'
log_line_prefix       = '%m [%p] %q%u@%d '

# --- 临时统计文件 ---
stats_temp_directory  = '/var/run/postgresql/14-main.pg_stat_tmp'

# --- 额外自定义配置目录 ---
include_dir           = 'conf.d'
