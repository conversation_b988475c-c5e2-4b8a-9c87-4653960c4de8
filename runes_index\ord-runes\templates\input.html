<h1>Input /{{self.path.0}}/{{self.path.1}}/{{self.path.2}}</h1>
<dl>
%% if !self.input.previous_output.is_null() {
  <dt>previous output</dt><dd class=monospace>{{self.input.previous_output}}</dd>
%% }
%% if self.input.sequence != Sequence::MAX {
  <dt>sequence</dt><dd>{{self.input.sequence}}</dd>
%% }
%% if !self.input.witness.is_empty() {
  <dt>witness</dt><dd class=monospace>{{hex::encode(consensus::serialize(&self.input.witness))}}</dd>
%% }
%% if !self.input.script_sig.is_empty() {
  <dt>script sig</dt><dd class=monospace>{{self.input.script_sig.to_asm_string()}}</dd>
  <dt>text</dt><dd>{{String::from_utf8_lossy(self.input.script_sig.as_bytes())}}</dd>
%% }
</dl>
