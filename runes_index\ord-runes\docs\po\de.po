msgid ""
msgstr ""
"Project-Id-Version: Ordinal Theory Handbook\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: 2023-09-09 14:25+0200\n"
"Last-Translator: EMAIL@ADDRESS\n"
"Language-Team: German\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.3.2\n"

#: src\SUMMARY.md:2 src\introduction.md:1
msgid "Introduction"
msgstr "Einführung"

#: src\SUMMARY.md:3
msgid "Overview"
msgstr "Übersicht"

#: src\SUMMARY.md:4 src\digital-artifacts.md:1
msgid "Digital Artifacts"
msgstr "Digitale Artefakte"

#: src\SUMMARY.md:5 src\SUMMARY.md:12 src\overview.md:221 src\inscriptions.md:1
msgid "Inscriptions"
msgstr "Inscriptions"

#: src\SUMMARY.md:6 src\inscriptions/recursion.md:1
msgid "Recursion"
msgstr "Rekursion"

#: src\SUMMARY.md:7
msgid "FAQ"
msgstr "Häufig gestellte Fragen"

#: src\SUMMARY.md:8
msgid "Contributing"
msgstr "Beitragen"

#: src\SUMMARY.md:9 src\donate.md:1
msgid "Donate"
msgstr "Spenden"

#: src\SUMMARY.md:10
msgid "Guides"
msgstr "Anleitungen"

#: src\SUMMARY.md:11
msgid "Explorer"
msgstr "Explorer"

#: src\SUMMARY.md:13 src\guides/sat-hunting.md:1
msgid "Sat Hunting"
msgstr "Sat-Suche"

#: src\SUMMARY.md:14 src\guides/collecting.md:1
msgid "Collecting"
msgstr "Sammeln"

#: src\SUMMARY.md:15 src\guides/sat-hunting.md:239
msgid "Sparrow Wallet"
msgstr "Sparrow Wallet"

#: src\SUMMARY.md:16 src\guides/testing.md:1
msgid "Testing"
msgstr "Testen"

#: src\SUMMARY.md:17 src\guides/moderation.md:1
msgid "Moderation"
msgstr "Moderation"

#: src\SUMMARY.md:18 src\guides/reindexing.md:1
msgid "Reindexing"
msgstr "Erneutes Indizieren"

#: src\SUMMARY.md:19
msgid "Bounties"
msgstr "Bounties"

#: src\SUMMARY.md:20
msgid "Bounty 0: 100,000 sats Claimed!"
msgstr "Bounty 0: 100.000 sats beansprucht!"

#: src\SUMMARY.md:21
msgid "Bounty 1: 200,000 sats Claimed!"
msgstr "Bounty 1: 200.000 sats beansprucht!"

#: src\SUMMARY.md:22
msgid "Bounty 2: 300,000 sats Claimed!"
msgstr "Bounty 2: 300.000 sats beansprucht!"

#: src\SUMMARY.md:23
msgid "Bounty 3: 400,000 sats"
msgstr "Bounty 3: 400.000 sats"

#: src\introduction.md:4
msgid ""
"This handbook is a guide to ordinal theory. Ordinal theory concerns itself "
"with satoshis, giving them individual identities and allowing them to be "
"tracked, transferred, and imbued with meaning."
msgstr ""
"Dieses Handbuch ist ein Leitfaden für die Ordinaltheorie. Die Ordinaltheorie "
"befasst sich mit Satoshis, verleiht ihnen individuelle Identitäten und "
"ermöglicht es, sie zu verfolgen, zu übertragen und mit Bedeutung zu versehen."

#: src\introduction.md:8
msgid ""
"Satoshis, not bitcoin, are the atomic, native currency of the Bitcoin "
"network. One bitcoin can be sub-divided into 100,000,000 satoshis, but no "
"further."
msgstr ""
"Satoshis, nicht Bitcoin, sind die atomare, native Währung des Bitcoin-"
"Netzwerks. Ein Bitcoin kann in 100.000.000 Satoshis unterteilt werden, "
"jedoch nicht weiter."

#: src\introduction.md:11
msgid ""
"Ordinal theory does not require a sidechain or token aside from Bitcoin, and "
"can be used without any changes to the Bitcoin network. It works right now."
msgstr ""
"Die Ordinaltheorie erfordert keine Sidechain oder Token neben Bitcoin und "
"kann ohne Änderungen am Bitcoin-Netzwerk verwendet werden. Sie funktioniert "
"bereits jetzt."

#: src\introduction.md:14
msgid ""
"Ordinal theory imbues satoshis with numismatic value, allowing them to be "
"collected and traded as curios."
msgstr ""
"Die Ordinaltheorie verleiht Satoshis numismatischen Wert, was es ermöglicht, "
"sie zu sammeln und als Kuriositäten zu handeln."

#: src\introduction.md:17
msgid ""
"Individual satoshis can be inscribed with arbitrary content, creating unique "
"Bitcoin-native digital artifacts that can be held in Bitcoin wallets and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"Einzelne Satoshis können mit beliebigem Inhalt beschriftet werden, wodurch "
"einzigartige, Bitcoin-native digitale Artefakte entstehen, die in Bitcoin-"
"Wallets aufbewahrt und mithilfe von Bitcoin-Transaktionen übertragen werden "
"können. Inschriften sind genauso haltbar, unveränderlich, sicher und "
"dezentralisiert wie Bitcoin selbst."

#: src\introduction.md:22
msgid ""
"Other, more unusual use-cases are possible: off-chain colored-coins, public "
"key infrastructure with key rotation, a decentralized replacement for the "
"DNS. For now though, such use-cases are speculative, and exist only in the "
"minds of fringe ordinal theorists."
msgstr ""
"Andere, ungewöhnlichere Anwendungsfälle sind möglich: Off-Chain-Farbmünzen, "
"Public-Key-Infrastruktur mit Schlüsselrotation, ein dezentralisierter Ersatz "
"für das DNS. Bisher sind solche Anwendungsfälle jedoch spekulativ und "
"existieren nur in den Köpfen randständiger Ordinaltheoretiker."

#: src\introduction.md:27
msgid "For more details on ordinal theory, see the [overview](overview.md)."
msgstr ""
"Für weitere details zur ordinal theory siehe die [Übersicht](overview.md)."

#: src\introduction.md:29
msgid "For more details on inscriptions, see [inscriptions](inscriptions.md)."
msgstr ""
"Für weitere details zu inscriptions siehe [inscriptions](inscriptions.md)."

#: src\introduction.md:31
msgid ""
"When you're ready to get your hands dirty, a good place to start is with "
"[inscriptions](guides/inscriptions.md), a curious species of digital "
"artifact enabled by ordinal theory."
msgstr ""
"Wenn Sie bereit sind, sich die Hände schmutzig zu machen, ist ein guter "
"Ausgangspunkt [inscriptions](guides/inscriptions.md), eine eigenartige Art "
"von digitaler Artefakten, die durch die ordinal theorie ermöglicht wird."

#: src\introduction.md:35
msgid "Links"
msgstr "Links"

#: src\introduction.md:38
msgid "[GitHub](https://github.com/ordinals/ord/)"
msgstr "[GitHub](https://github.com/ordinals/ord/)"

#: src\introduction.md:39
msgid "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src\introduction.md:40
msgid "[Discord](https://discord.gg/ordinals)"
msgstr "[Discord](https://discord.gg/ordinals)"

#: src\introduction.md:41
msgid "[Open Ordinals Institute Website](https://ordinals.org/)"
msgstr "[Open Ordinals Institute Website](https://ordinals.org/)"

#: src\introduction.md:42
msgid "[Open Ordinals Institute X](https://x.com/ordinalsorg)"
msgstr "[Open Ordinals Institute X](https://x.com/ordinalsorg)"

#: src\introduction.md:43
msgid "[Mainnet Block Explorer](https://ordinals.com)"
msgstr "[Mainnet Block Explorer](https://ordinals.com)"

#: src\introduction.md:44
msgid "[Signet Block Explorer](https://signet.ordinals.com)"
msgstr "[Signet Block Explorer](https://signet.ordinals.com)"

#: src\introduction.md:46
msgid "Videos"
msgstr "Videos"

#: src\introduction.md:49
msgid ""
"[Ordinal Theory Explained: Satoshi Serial Numbers and NFTs on Bitcoin]"
"(https://www.youtube.com/watch?v=rSS0O2KQpsI)"
msgstr ""
"[Ordinal Theory Explained: Satoshi Serial Numbers and NFTs on Bitcoin]"
"(https://www.youtube.com/watch?v=rSS0O2KQpsI)"

#: src\introduction.md:50
msgid ""
"[Ordinals Workshop with Rodarmor](https://www.youtube.com/watch?"
"v=MC_haVa6N3I)"
msgstr ""
"[Ordinals Workshop with Rodarmor](https://www.youtube.com/watch?"
"v=MC_haVa6N3I)"

#: src\introduction.md:51
msgid ""
"[Ordinal Art: Mint Your own NFTs on Bitcoin w/ @rodarmor](https://www."
"youtube.com/watch?v=j5V33kV3iqo)"
msgstr ""
"[Ordinal Art: Mint Your own NFTs on Bitcoin w/ @rodarmor](https://www."
"youtube.com/watch?v=j5V33kV3iqo)"

#: src\overview.md:1
msgid "Ordinal Theory Overview"
msgstr "Ordinal theorie Übersicht"

#: src\overview.md:4
msgid ""
"Ordinals are a numbering scheme for satoshis that allows tracking and "
"transferring individual sats. These numbers are called [ordinal numbers]"
"(https://ordinals.com). Satoshis are numbered in the order in which they're "
"mined, and transferred from transaction inputs to transaction outputs first-"
"in-first-out. Both the numbering scheme and the transfer scheme rely on "
"_order_, the numbering scheme on the _order_ in which satoshis are mined, "
"and the transfer scheme on the _order_ of transaction inputs and outputs. "
"Thus the name, _ordinals_."
msgstr ""
"Ordinals sind eine Nummerierung für Satoshis, die es ermöglicht, einzelne "
"Sats zu verfolgen und zu übertragen. Diese Zahlen werden als [Ordinalzahlen]"
"(https://ordinals.com) bezeichnet. Satoshis werden in der Reihenfolge "
"nummeriert, in der sie abgebaut werden, und von Transaktionseingängen zu "
"Transaktionsausgängen zuerst hereingegeben. Sowohl das Nummernschema als "
"auch das Übertragungsschema basieren auf der _Reihenfolge_, wobei das "
"Nummernschema auf der _Reihenfolge_ basiert, in der Satoshis abgebaut "
"werden, und das Übertragungsschema auf der _Reihenfolge_ von "
"Transaktionseingängen und -ausgängen. Daher der Name _Ordinals_."

#: src\overview.md:13
msgid ""
"Technical details are available in [the BIP](https://github.com/ordinals/ord/"
"blob/master/bip.mediawiki)."
msgstr ""
"Technische Details sind verfügbar unter [the BIP](https://github.com/"
"ordinals/ord/blob/master/bip.mediawiki)."

#: src\overview.md:16
msgid ""
"Ordinal theory does not require a separate token, another blockchain, or any "
"changes to Bitcoin. It works right now."
msgstr ""
"Die Ordinaltheorie erfordert keine separate Token, eine andere Blockchain "
"oder irgendwelche Änderungen an Bitcoin. Sie funktioniert jetzt bereits."

#: src\overview.md:19
msgid "Ordinal numbers have a few different representations:"
msgstr "Ordinal zahlen haben einige verschiedene Darstellungen:"

#: src\overview.md:21
msgid ""
"_Integer notation_: [`2099994106992659`](https://ordinals.com/"
"sat/2099994106992659) The ordinal number, assigned according to the order in "
"which the satoshi was mined."
msgstr ""
"_Ganzzahldarstellung_: [`2099994106992659`](https://ordinals.com/"
"sat/2099994106992659) Die Ordinalzahl, die gemäß der Reihenfolge, in der der "
"Satoshi abgebaut wurde, zugewiesen wurde."

#: src\overview.md:26
msgid ""
"_Decimal notation_: [`3891094.16797`](https://ordinals.com/"
"sat/3891094.16797) The first number is the block height in which the satoshi "
"was mined, the second the offset of the satoshi within the block."
msgstr ""
"_Dezi­mal­darstellung_: [`3891094.16797`](https://ordinals.com/"
"sat/3891094.16797) Die erste Zahl ist die Blockhöhe, in der der Satoshi "
"abgebaut wurde, die zweite die Verschiebung des Satoshis innerhalb des "
"Blocks."

#: src\overview.md:31
msgid ""
"_Degree notation_: [`3°111094′214″16797‴`](https://ordinals.com/"
"sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). We'll get to that in "
"a moment."
msgstr ""
"_Degree notation_: [`3°111094′214″16797‴`](https://ordinals.com/"
"sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). We'll get to that in "
"a moment."

#: src\overview.md:35
msgid ""
"_Percentile notation_: [`99.**************%`](https://ordinals.com/"
"sat/99.**************%25) . The satoshi's position in Bitcoin's supply, "
"expressed as a percentage."
msgstr ""
"_Prozentnotation_: [`99.**************%`](https://ordinals.com/"
"sat/99.**************%25). Die Position des Satoshis im Bitcoin-Vorrat, "
"ausgedrückt als Prozentsatz."

#: src\overview.md:39
msgid ""
"_Name_: [`satoshi`](https://ordinals.com/sat/satoshi). An encoding of the "
"ordinal number using the characters `a` through `z`."
msgstr ""
"_Name_: [`satoshi`](https://ordinals.com/sat/satoshi). Eine Codierung der "
"Ordnungszahl unter Verwendung der Zeichen `a` bis `z`."

#: src\overview.md:42
msgid ""
"Arbitrary assets, such as NFTs, security tokens, accounts, or stablecoins "
"can be attached to satoshis using ordinal numbers as stable identifiers."
msgstr ""
"Beliebige Vermögenswerte wie NFTs, Sicherheitstoken, Konten oder Stablecoins "
"können mit Hilfe von Ordnungszahlen als stabile Identifikatoren an Satoshis "
"angehängt werden."

#: src\overview.md:45
msgid ""
"Ordinals is an open-source project, developed [on GitHub](https://github.com/"
"ordinals/ord). The project consists of a BIP describing the ordinal scheme, "
"an index that communicates with a Bitcoin Core node to track the location of "
"all satoshis, a wallet that allows making ordinal-aware transactions, a "
"block explorer for interactive exploration of the blockchain, functionality "
"for inscribing satoshis with digital artifacts, and this manual."
msgstr ""
"Ordinals ist ein Open-Source-Projekt, das [auf GitHub](https://github.com/"
"ordinals/ord) entwickelt wird. Das Projekt umfasst ein BIP, das das "
"Ordnungsschema beschreibt, einen Index, der mit einem Bitcoin Core-Knoten "
"kommuniziert, um den Standort aller Satoshis zu verfolgen, eine Brieftasche, "
"die das Erstellen von Transaktionen mit Kenntnis der Ordnung ermöglicht, "
"einen Block-Explorer für die interaktive Erkundung der Blockchain, "
"Funktionen zum Einschreiben von Satoshis mit digitalen Artefakten und dieses "
"Handbuch."

#: src\overview.md:52
msgid "Rarity"
msgstr "Seltenheit"

#: src\overview.md:55
msgid ""
"Humans are collectors, and since satoshis can now be tracked and "
"transferred, people will naturally want to collect them. Ordinal theorists "
"can decide for themselves which sats are rare and desirable, but there are "
"some hints…"
msgstr ""
"Menschen sind Sammler, und da Satoshis jetzt verfolgt und übertragen werden "
"können, werden Menschen natürlich wollen, sie zu sammeln. Ordinaltheoretiker "
"können selbst entscheiden, welche Sats selten und begehrenswert sind, aber "
"es gibt einige Hinweise…"

#: src\overview.md:59
msgid ""
"Bitcoin has periodic events, some frequent, some more uncommon, and these "
"naturally lend themselves to a system of rarity. These periodic events are:"
msgstr ""
"Bitcoin hat periodische Ereignisse, einige davon häufig, andere weniger "
"häufig, und diese eignen sich naturgemäß für ein System der Seltenheit. "
"Diese periodischen Ereignisse sind:"

#: src\overview.md:62
msgid ""
"_Blocks_: A new block is mined approximately every 10 minutes, from now "
"until the end of time."
msgstr ""
"_Blöcke_: Ein neuer Block wird etwa alle 10 Minuten gemined, von jetzt bis "
"ans Ende aller Zeiten."

#: src\overview.md:65
msgid ""
"_Difficulty adjustments_: Every 2016 blocks, or approximately every two "
"weeks, the Bitcoin network responds to changes in hashrate by adjusting the "
"difficulty target which blocks must meet in order to be accepted."
msgstr ""
"_Schwierigkeitsanpassungen_: Alle 2016 Blöcke, oder etwa alle zwei Wochen, "
"passt das Bitcoin-Netzwerk die Schwierigkeitsziel an, das von den Blöcken "
"erfüllt werden muss, um akzeptiert zu werden, als Reaktion auf Veränderungen "
"in der Hashrate."

#: src\overview.md:69
msgid ""
"_Halvings_: Every 210,000 blocks, or roughly every four years, the amount of "
"new sats created in every block is cut in half."
msgstr ""
"_Halbierungen_: Alle 210.000 Blöcke, oder etwa alle vier Jahre, wird die "
"Menge an neuen Sats, die in jedem Block erstellt werden, halbiert."

#: src\overview.md:72
msgid ""
"_Cycles_: Every six halvings, something magical happens: the halving and the "
"difficulty adjustment coincide. This is called a conjunction, and the time "
"period between conjunctions a cycle. A conjunction occurs roughly every 24 "
"years. The first conjunction should happen sometime in 2032."
msgstr ""
"_Zyklen_: Alle sechs Halbierungen geschieht etwas Magisches: Die Halbierung "
"und die Schwierigkeitsanpassung fallen zusammen. Dies wird als Konjunktion "
"bezeichnet, und der Zeitraum zwischen den Konjunktionen wird als Zyklus "
"bezeichnet. Eine Konjunktion tritt ungefähr alle 24 Jahre auf. Die erste "
"Konjunktion sollte etwa im Jahr 2032 stattfinden."

#: src\overview.md:77
msgid "This gives us the following rarity levels:"
msgstr "Dies ergibt die folgenden Seltenheitsstufen:"

#: src\overview.md:79
msgid "`common`: Any sat that is not the first sat of its block"
msgstr "`gewöhnlich`: Jeder Sat, der nicht der erste Satz seines Blocks ist"

#: src\overview.md:80
msgid "`uncommon`: The first sat of each block"
msgstr "`ungewöhnlich`: Der erste Satz jedes Blocks"

#: src\overview.md:81
msgid "`rare`: The first sat of each difficulty adjustment period"
msgstr "`selten`: Der erste Satz jedes Schwierigkeitsanpassungszeitraums"

#: src\overview.md:82
msgid "`epic`: The first sat of each halving epoch"
msgstr "`episch`: Der erste Satz jedes Halbierungsjahrzehnts"

#: src\overview.md:83
msgid "`legendary`: The first sat of each cycle"
msgstr "`legendär`: Der erste Satz jedes Zyklus"

#: src\overview.md:84
msgid "`mythic`: The first sat of the genesis block"
msgstr "`mythisch`: Der erste Satz des Genesis-Blocks"

#: src\overview.md:86
msgid ""
"Which brings us to degree notation, which unambiguously represents an "
"ordinal number in a way that makes the rarity of a satoshi easy to see at a "
"glance:"
msgstr ""
"Das bringt uns zur Grad-Schreibweise, die eine Ordinalzahl eindeutig "
"darstellt und die Seltenheit eines Satoshis auf den ersten Blick leicht "
"erkennbar macht:"

#: src\overview.md:89
msgid ""
"```\n"
"A°B′C″D‴\n"
"│ │ │ ╰─ Index of sat in the block\n"
"│ │ ╰─── Index of block in difficulty adjustment period\n"
"│ ╰───── Index of block in halving epoch\n"
"╰─────── Cycle, numbered starting from 0\n"
"```"
msgstr ""
"A°B′C″D‴\n"
"│ │ │ ╰─ Index des Satoshis im Block\n"
"│ │ ╰─── Index des Blocks im Schwierigkeitsanpassungszeitraum\n"
"│ ╰───── Index des Blocks in der Halbierungsepoche\n"
"╰─────── Zyklus, nummeriert ab 0\n"
"```"

#: src\overview.md:97
msgid ""
"Ordinal theorists often use the terms \"hour\", \"minute\", \"second\", and "
"\"third\" for _A_, _B_, _C_, and _D_, respectively."
msgstr ""
"Ordinal-Theoretiker verwenden oft die Begriffe \"Stunde\", \"Minute\", "
"\"Sekunde\" und \"Dritte\" für _A_, _B_, _C_ und _D_."

#: src\overview.md:100
msgid "Now for some examples. This satoshi is common:"
msgstr "Jetzt einige Beispiele. Dieser Satoshi ist häufig:"

#: src\overview.md:102
msgid ""
"```\n"
"1°1′1″1‴\n"
"│ │ │ ╰─ Not first sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── Not first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"Dieser Satoshi ist ungewöhnlich:\n"
"\n"
"```\n"
"1°1′1″1‴\n"
"│ │ │ ╰─ Nicht der erste Sat in diesem Block\n"
"│ │ ╰─── Nicht der erste Block in diesem Schwierigkeitsanpassungszeitraum\n"
"│ ╰───── Nicht der erste Block in diesem Halbierungszyklus\n"
"╰─────── Zweiter Zyklus\n"
"```"

#: src\overview.md:111
msgid "This satoshi is uncommon:"
msgstr "Dieser Satoshi ist ungewöhnlich:"

#: src\overview.md:113
msgid ""
"```\n"
"1°1′1″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── Not first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″0‴\n"
"│ │ │ ╰─ Erster Sat in seinem Block\n"
"│ │ ╰─── Nicht der erste Block in diesem Schwierigkeitsanpassungszeitraum\n"
"│ ╰───── Nicht der erste Block in diesem Halbierungszyklus\n"
"╰─────── Erster Zyklus\n"
"```"

#: src\overview.md:121
msgid "This satoshi is rare:"
msgstr "Dieser Satoshi ist selten:"

#: src\overview.md:123
msgid ""
"```\n"
"1°1′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── Not the first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′0″1‴\n"
"│ │ ╰─ Erster Sat in seinem Schwierigkeitsanpassungszeitraum\n"
"│ ╰─── Nicht der erste Block in diesem Halbierungszyklus\n"
"╰───── Erster Zyklus\n"
"```"

#: src\overview.md:131
msgid "This satoshi is epic:"
msgstr "Dieser Satoshi ist episch:"

#: src\overview.md:133
msgid ""
"```\n"
"1°0′1″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"1°0′1″0‴\n"
"│ │ │ ╰─ Erster Sat in seinem Block\n"
"│ │ ╰─── Nicht der erste Block in seinem Schwierigkeitsanpassungszeitraum\n"
"│ ╰───── Erster Block in seinem Halbierungszyklus\n"
"╰─────── Zweiter Zyklus\n"
"```"

#: src\overview.md:141
msgid "This satoshi is legendary:"
msgstr "Dieser Satoshi ist legendär:"

#: src\overview.md:143
msgid ""
"```\n"
"1°0′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°0′0″0‴\n"
"│ │ │ ╰─ Erster Sat in seinem Block\n"
"│ │ ╰─── Erster Block in seinem Schwierigkeitsanpassungszeitraum\n"
"│ ╰───── Erster Block in seinem Halbierungszyklus\n"
"╰─────── Zweiter Zyklus\n"
"```"

#: src\overview.md:151
msgid "And this satoshi is mythic:"
msgstr "Dieser Satoshi ist mythisch:"

#: src\overview.md:153
msgid ""
"```\n"
"0°0′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── First cycle\n"
"```"
msgstr ""
"0°0′0″0‴\n"
"│ │ │ ╰─ Erster Sat in seinem Block\n"
"│ │ ╰─── Erster Block in seinem Schwierigkeitsanpassungszeitraum\n"
"│ ╰───── Erster Block in seinem Halbierungszyklus\n"
"╰─────── Erster Zyklus\n"
"```"

#: src\overview.md:161
msgid ""
"If the block offset is zero, it may be omitted. This is the uncommon satoshi "
"from above:"
msgstr ""
"Wenn der Block-Offset null ist, kann er weggelassen werden. Dies ist der "
"ungewöhnliche Satoshi von oben:"

#: src\overview.md:164
msgid ""
"```\n"
"1°1′1″\n"
"│ │ ╰─ Not first block in difficulty adjustment period\n"
"│ ╰─── Not first block in halving epoch\n"
"╰───── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″\n"
"│ │ ╰─ Nicht der erste Block im Schwierigkeitsanpassungszeitraum\n"
"│ ╰─── Nicht der erste Block im Halbierungszyklus\n"
"╰───── Zweiter Zyklus\n"
"```"

#: src\overview.md:171
msgid "Rare Satoshi Supply"
msgstr "Versorgung seltener Satoshis"

#: src\overview.md:174
msgid "Total Supply"
msgstr "Gesamtversorgung"

#: src\overview.md:176
msgid "`common`: 2.1 quadrillion"
msgstr "`gewöhnlich` 2.1 Billiarde"

#: src\overview.md:177
msgid "`uncommon`: 6,929,999"
msgstr "`ungewöhnlich`: 6,929,999"

#: src\overview.md:178
msgid "`rare`: 3437"
msgstr "`selten`: 3437"

#: src\overview.md:179
msgid "`epic`: 32"
msgstr "`episch`: 32"

#: src\overview.md:180
msgid "`legendary`: 5"
msgstr "`legendär`: 5"

#: src\overview.md:181 src\overview.md:190
msgid "`mythic`: 1"
msgstr "`mythisch`: 1"

#: src\overview.md:183
msgid "Current Supply"
msgstr "Aktuelle Versorgung"

#: src\overview.md:185
msgid "`common`: 1.9 quadrillion"
msgstr "`gewöhnlich` 1.9 Billiarde"

#: src\overview.md:186
msgid "`uncommon`: 745,855"
msgstr "`ungewöhnlich`: 745,855"

#: src\overview.md:187
msgid "`rare`: 369"
msgstr "`rare`: 369"

#: src\overview.md:188
msgid "`epic`: 3"
msgstr "`episch`: 3"

#: src\overview.md:189
msgid "`legendary`: 0"
msgstr "`legendary`: 0"

#: src\overview.md:192
msgid ""
"At the moment, even uncommon satoshis are quite rare. As of this writing, "
"745,855 uncommon satoshis have been mined - one per 25.6 bitcoin in "
"circulation."
msgstr ""
"Im Moment sind selbst ungewöhnliche Satoshis ziemlich selten. Zum jetzigen "
"Zeitpunkt wurden 745.855 ungewöhnliche Satoshis abgebaut – einer pro 25,6 "
"Bitcoin im Umlauf."

#: src\overview.md:196
msgid "Names"
msgstr "Namen"

#: src\overview.md:199
msgid ""
"Each satoshi has a name, consisting of the letters _A_ through _Z_, that get "
"shorter the further into the future the satoshi was mined. They could start "
"short and get longer, but then all the good, short names would be trapped in "
"the unspendable genesis block."
msgstr ""
"Jeder Satoshi hat einen Namen, der aus den Buchstaben _A_ bis _Z_ besteht "
"und umso kürzer wird, je weiter in der Zukunft der Satoshi abgebaut wurde. "
"Sie könnten kurz anfangen und dann länger werden, aber dann wären alle "
"guten, kurzen Namen im unvermeidlichen Genesis-Block gefangen."

#: src\overview.md:204
msgid ""
"As an example, 1905530482684727°'s name is \"iaiufjszmoba\". The name of the "
"last satoshi to be mined is \"a\". Every combination of 10 characters or "
"less is out there, or will be out there, someday."
msgstr ""
"Beispielsweise lautet der Name von 1905530482684727° „iaiufjszmoba“. Der "
"Name des letzten Satoshi, der abgebaut wurde, ist „a“. Jede Kombination aus "
"10 oder weniger Zeichen gibt es oder wird es eines Tages geben."

#: src\overview.md:208
msgid "Exotics"
msgstr "Exoten"

#: src\overview.md:211
msgid ""
"Satoshis may be prized for reasons other than their name or rarity. This "
"might be due to a quality of the number itself, like having an integer "
"square or cube root. Or it might be due to a connection to a historical "
"event, such as satoshis from block 477,120, the block in which SegWit "
"activated, or 2099999997689999°, the last satoshi that will ever be mined."
msgstr ""
"Satoshis können aus anderen Gründen als ihrem Namen oder ihrer Seltenheit "
"geschätzt werden. Dies kann auf die Qualität der Zahl selbst zurückzuführen "
"sein, beispielsweise darauf, dass sie eine ganzzahlige Quadrat- oder "
"Kubikwurzel ist. Oder es könnte an einer Verbindung zu einem historischen "
"Ereignis liegen, wie zum Beispiel Satoshis aus Block 477.120, dem Block, in "
"dem SegWit aktiviert wurde, oder 2099999997689999°, dem letzten Satoshi, der "
"jemals abgebaut wird."

#: src\overview.md:217
msgid ""
"Such satoshis are termed \"exotic\". Which satoshis are exotic and what "
"makes them so is subjective. Ordinal theorists are encouraged to seek out "
"exotics based on criteria of their own devising."
msgstr ""
"Solche Satoshis werden als „exotisch“ bezeichnet. Welche Satoshis exotisch "
"sind und was sie so macht, ist subjektiv. Ordinaltheoretiker werden "
"ermutigt, nach Exoten zu suchen, die auf Kriterien basieren, die sie selbst "
"entwickelt haben."

#: src\overview.md:224
msgid ""
"Satoshis can be inscribed with arbitrary content, creating Bitcoin-native "
"digital artifacts. Inscribing is done by sending the satoshi to be inscribed "
"in a transaction that reveals the inscription content on-chain. This content "
"is then inextricably linked to that satoshi, turning it into an immutable "
"digital artifact that can be tracked, transferred, hoarded, bought, sold, "
"lost, and rediscovered."
msgstr ""
"Satoshis können mit beliebigen Inhalten beschriftet werden, wodurch Bitcoin-"
"native digitale Artefakte entstehen. Die Beschriftung erfolgt durch Senden "
"des Satoshi zur Beschriftung in einer Transaktion, die den Inhalt der "
"Beschriftung in der Kette offenlegt. Dieser Inhalt ist dann untrennbar mit "
"diesem Satoshi verbunden und verwandelt ihn in ein unveränderliches "
"digitales Artefakt, das verfolgt, übertragen, gehortet, gekauft, verkauft, "
"verloren und wiederentdeckt werden kann."

#: src\overview.md:231
msgid "Archaeology"
msgstr "Archäologie"

#: src\overview.md:234
msgid ""
"A lively community of archaeologists devoted to cataloging and collecting "
"early NFTs has sprung up. [Here's a great summary of historical NFTs by "
"Chainleft.](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-"
"N29oF4iwCgX3lacrvaG9Kjko)"
msgstr ""
"Es ist eine lebendige Gemeinschaft von Archäologen entstanden, die sich der "
"Katalogisierung und Sammlung früher NFTs widmen. [Hier ist eine großartige "
"Zusammenfassung historischer NFTs von Chainleft.](https://mirror.xyz/"
"chainleft.eth/MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)"

#: src\overview.md:238
msgid ""
"A commonly accepted cut-off for early NFTs is March 19th, 2018, the date the "
"first ERC-721 contract, [SU SQUARES](https://tenthousandsu.com/), was "
"deployed on Ethereum."
msgstr ""
"Ein allgemein akzeptierter Stichtag für frühe NFTs ist der 19. März 2018, "
"das Datum, an dem der erste ERC-721-Vertrag, [SU SQUARES](https://"
"tenthousandsu.com/), auf Ethereum bereitgestellt wurde."

#: src\overview.md:242
msgid ""
"Whether or not ordinals are of interest to NFT archaeologists is an open "
"question! In one sense, ordinals were created in early 2022, when the "
"Ordinals specification was finalized. In this sense, they are not of "
"historical interest."
msgstr ""
"Ob ordinals für NFT-Archäologen von Interesse sind oder nicht, ist eine "
"offene Frage! In gewisser Hinsicht wurden ordinals Anfang 2022 erstellt, als "
"die Ordinals-Spezifikation fertiggestellt wurde. In diesem Sinne sind sie "
"nicht von historischem Interesse."

#: src\overview.md:247
msgid ""
"In another sense though, ordinals were in fact created by Satoshi Nakamoto "
"in 2009 when he mined the Bitcoin genesis block. In this sense, ordinals, "
"and especially early ordinals, are certainly of historical interest."
msgstr ""
"In einem anderen Sinne wurden ordinals jedoch tatsächlich von Satoshi "
"Nakamoto im Jahr 2009 erstellt, als er den Bitcoin-Genesis-Block schürfte. "
"In diesem Sinne sind Ordnungszahlen und insbesondere frühe Ordnungszahlen "
"sicherlich von historischem Interesse."

#: src\overview.md:251
msgid ""
"Many ordinal theorists favor the latter view. This is not least because the "
"ordinals were independently discovered on at least two separate occasions, "
"long before the era of modern NFTs began."
msgstr ""
"Viele Ordinal theoretiker befürworten die letztere Ansicht. Dies liegt nicht "
"zuletzt daran, dass die ordinals bei mindestens zwei verschiedenen "
"Gelegenheiten unabhängig voneinander entdeckt wurden, lange bevor die Ära "
"moderner NFTs begann."

#: src\overview.md:255
msgid ""
"On August 21st, 2012, Charlie Lee [posted a proposal to add proof-of-stake "
"to Bitcoin to the Bitcoin Talk forum](https://bitcointalk.org/index.php?"
"topic=102355.0). This wasn't an asset scheme, but did use the ordinal "
"algorithm, and was implemented but never deployed."
msgstr ""
"Am 21. August 2012 veröffentlichte Charlie Lee im Bitcoin Talk-Forum einen "
"Vorschlag, einen Proof-of-Stake zu Bitcoin hinzuzufügen (https://bitcointalk."
"org/index.php?topic=102355.0). Dies war kein Asset-Schema, sondern nutzte "
"den Ordinal algorithmus und wurde implementiert, aber nie bereitgestellt."

#: src\overview.md:261
msgid ""
"On October 8th, 2012, jl2012 [posted a scheme to the same forum](https://"
"bitcointalk.org/index.php?topic=117224.0) which uses decimal notation and "
"has all the important properties of ordinals. The scheme was discussed but "
"never implemented."
msgstr ""
"Am 8. Oktober 2012 hat jl2012 [im selben Forum ein Schema gepostet](https://"
"bitcointalk.org/index.php?topic=117224.0), das die Dezimalschreibweise "
"verwendet und alle wichtigen Eigenschaften von Ordinals aufweist. Der Plan "
"wurde diskutiert, aber nie umgesetzt."

#: src\overview.md:266
msgid ""
"These independent inventions of ordinals indicate in some way that ordinals "
"were discovered, or rediscovered, and not invented. The ordinals are an "
"inevitability of the mathematics of Bitcoin, stemming not from their modern "
"documentation, but from their ancient genesis. They are the culmination of a "
"sequence of events set in motion with the mining of the first block, so many "
"years ago."
msgstr ""
"Diese unabhängigen Erfindungen von Ordnungszahlen weisen in gewisser Weise "
"darauf hin, dass ordinals entdeckt oder wiederentdeckt und nicht erfunden "
"wurden. Die ordinals sind eine Unvermeidlichkeit der Mathematik von Bitcoin "
"und ergeben sich nicht aus ihrer modernen Dokumentation, sondern aus ihrer "
"antiken Entstehung. Sie sind der Höhepunkt einer Reihe von Ereignissen, die "
"vor so vielen Jahren mit dem Abbau des ersten Blocks in Gang gesetzt wurden."

#: src\digital-artifacts.md:4
msgid ""
"Imagine a physical artifact. A rare coin, say, held safe for untold years in "
"the dark, secret clutch of a Viking hoard, now dug from the earth by your "
"grasping hands. It…"
msgstr ""
"Stellen Sie sich ein physisches Artefakt vor. Eine seltene Münze zum "
"Beispiel, die unzählige Jahre lang sicher im dunklen, geheimen Schatz eines "
"Wikingerschatzes aufbewahrt wurde, der jetzt von Ihren griffigen Händen aus "
"der Erde gegraben wurde. Es…"

#: src\digital-artifacts.md:8
msgid ""
"…has an owner. You. As long as you keep it safe, nobody can take it from you."
msgstr ""
"…hat einen Besitzer. Du. Solange Sie es sicher aufbewahren, kann es Ihnen "
"niemand wegnehmen."

#: src\digital-artifacts.md:10
msgid "…is complete. It has no missing parts."
msgstr "…ist komplett. Es fehlen keine Teile."

#: src\digital-artifacts.md:12
msgid ""
"…can only be changed by you. If you were a trader, and you made your way to "
"18th century China, none but you could stamp it with your chop-mark."
msgstr ""
"…kann nur von Ihnen geändert werden. Wenn Sie ein Händler waren und sich auf "
"den Weg ins China des 18. Jahrhunderts machten, konnte niemand außer Ihnen "
"Ihr Stempelzeichen aufbringen."

#: src\digital-artifacts.md:15
msgid ""
"…can only be disposed of by you. The sale, trade, or gift is yours to make, "
"to whomever you wish."
msgstr ""
"…kann nur von Ihnen selbst entsorgt werden. Der Verkauf, Tausch oder die "
"Schenkung liegt bei Ihnen, an wen auch immer Sie möchten."

#: src\digital-artifacts.md:18
msgid ""
"What are digital artifacts? Simply put, they are the digital equivalent of "
"physical artifacts."
msgstr ""
"Was sind digitale Artefakte? Einfach ausgedrückt sind sie das digitale "
"Äquivalent physischer Artefakte."

#: src\digital-artifacts.md:21
msgid ""
"For a digital thing to be a digital artifact, it must be like that coin of "
"yours:"
msgstr ""
"Damit ein digitales Ding ein digitales Artefakt ist, muss es wie Ihre Münze "
"sein:"

#: src\digital-artifacts.md:24
msgid ""
"Digital artifacts can have owners. A number is not a digital artifact, "
"because nobody can own it."
msgstr ""
"Digitale Artefakte können Besitzer haben. Eine Nummer ist kein digitales "
"Artefakt, denn niemand kann sie besitzen."

#: src\digital-artifacts.md:27
msgid ""
"Digital artifacts are complete. An NFT that points to off-chain content on "
"IPFS or Arweave is incomplete, and thus not a digital artifact."
msgstr ""
"Die digitalen Artefakte sind vollständig. Ein NFT, der auf Off-Chain-Inhalte "
"auf IPFS oder Arweave verweist, ist unvollständig und daher kein digitales "
"Artefakt."

#: src\digital-artifacts.md:30
msgid ""
"Digital artifacts are permissionless. An NFT which cannot be sold without "
"paying a royalty is not permissionless, and thus not a digital artifact."
msgstr ""
"Digitale Artefakte sind erlaubnisfrei. Ein NFT, der nicht ohne Zahlung einer "
"Lizenzgebühr verkauft werden kann, ist nicht erlaubnislos und daher kein "
"digitales Artefakt."

#: src\digital-artifacts.md:33
msgid ""
"Digital artifacts are uncensorable. Perhaps you can change a database entry "
"on a centralized ledger today, but maybe not tomorrow, and thus one cannot "
"be a digital artifact."
msgstr ""
"Digitale Artefakte sind unzensierbar. Vielleicht können Sie heute einen "
"Datenbankeintrag in einem zentralen Hauptbuch ändern, aber vielleicht nicht "
"morgen, und daher kann es sich nicht um ein digitales Artefakt handeln."

#: src\digital-artifacts.md:37
msgid ""
"Digital artifacts are immutable. An NFT with an upgrade key is not a digital "
"artifact."
msgstr ""
"Digitale Artefakte sind unveränderlich. Ein NFT mit einem Upgrade-Schlüssel "
"ist kein digitales Artefakt."

#: src\digital-artifacts.md:40
msgid ""
"The definition of a digital artifact is intended to reflect what NFTs "
"_should_ be, sometimes are, and what inscriptions _always_ are, by their "
"very nature."
msgstr ""
"Die Definition eines digitalen Artefakts soll widerspiegeln, was NFTs sein "
"_sollten_, manchmal sind und was inscriptions ihrer Natur nach _immer_ sind."

#: src\inscriptions.md:4
msgid ""
"Inscriptions inscribe sats with arbitrary content, creating bitcoin-native "
"digital artifacts, more commonly known as NFTs. Inscriptions do not require "
"a sidechain or separate token."
msgstr ""
"Inscriptions beschriften Sats mit beliebigen Inhalten und erzeugen Bitcoin-"
"native digitale Artefakte, besser bekannt als NFTs. Für Inscriptions "
"erfordern keine Sidechain oder separaten Token."

#: src\inscriptions.md:8
msgid ""
"These inscribed sats can then be transferred using bitcoin transactions, "
"sent to bitcoin addresses, and held in bitcoin UTXOs. These transactions, "
"addresses, and UTXOs are normal bitcoin transactions, addresses, and UTXOS "
"in all respects, with the exception that in order to send individual sats, "
"transactions must control the order and value of inputs and outputs "
"according to ordinal theory."
msgstr ""
"Diese eingeschriebenen Sats können dann mithilfe von Bitcoin-Transaktionen "
"übertragen, an Bitcoin-Adressen gesendet und in Bitcoin-UTXOs gespeichert "
"werden. Diese Transaktionen, Adressen und UTXOs sind in jeder Hinsicht "
"normale Bitcoin-Transaktionen, Adressen und UTXOS, mit der Ausnahme, dass "
"Transaktionen zum Senden einzelner Sats die Reihenfolge und den Wert der "
"Ein- und Ausgänge gemäß der Ordinal theorie steuern müssen."

#: src\inscriptions.md:15
msgid ""
"The inscription content model is that of the web. An inscription consists of "
"a content type, also known as a MIME type, and the content itself, which is "
"a byte string. This allows inscription content to be returned from a web "
"server, and for creating HTML inscriptions that use and remix the content of "
"other inscriptions."
msgstr ""
"Das Inhaltsmodell der inscription ist das des Webs. Eine inscription besteht "
"aus einem Inhaltstyp, auch MIME-Typ genannt, und dem Inhalt selbst, bei dem "
"es sich um eine Bytefolge handelt. Dies ermöglicht die Rückgabe von "
"inscriptions von einem Webserver und die Erstellung von HTML-inscriptions , "
"die den Inhalt anderer inscription verwenden und neu mischen."

#: src\inscriptions.md:21
msgid ""
"Inscription content is entirely on-chain, stored in taproot script-path "
"spend scripts. Taproot scripts have very few restrictions on their content, "
"and additionally receive the witness discount, making inscription content "
"storage relatively economical."
msgstr ""
"Der Inhalt der Inscription ist vollständig in der Kette und wird in Taproot-"
"Skriptpfad-Ausgabeskripten gespeichert. Für Taproot-Skripte gelten nur sehr "
"wenige Einschränkungen hinsichtlich ihres Inhalts und sie erhalten "
"zusätzlich den Witness-Rabatt, was die Speicherung von Inscription-Inhalten "
"relativ kostengünstig macht."

#: src\inscriptions.md:26
msgid ""
"Since taproot script spends can only be made from existing taproot outputs, "
"inscriptions are made using a two-phase commit/reveal procedure. First, in "
"the commit transaction, a taproot output committing to a script containing "
"the inscription content is created. Second, in the reveal transaction, the "
"output created by the commit transaction is spent, revealing the inscription "
"content on-chain."
msgstr ""
"Da Ausgaben für Taproot-Skripts nur aus vorhandenen Taproot-Ausgaben "
"getätigt werden können, werden inscriptions mithilfe eines zweiphasigen "
"Commit/Reveal-Verfahrens vorgenommen. Zunächst wird in der Commit-"
"Transaktion eine Taproot-Ausgabe erstellt, die ein Commit für ein Skript mit "
"dem Inhalt der Inschrift durchführt. Zweitens wird bei der "
"Offenlegungstransaktion die durch die Festschreibungstransaktion erzeugte "
"Ausgabe ausgegeben, um den Inhalt der Inschrift in der chain preiszugeben."

#: src\inscriptions.md:33
msgid ""
"Inscription content is serialized using data pushes within unexecuted "
"conditionals, called \"envelopes\". Envelopes consist of an `OP_FALSE OP_IF "
"… OP_ENDIF` wrapping any number of data pushes. Because envelopes are "
"effectively no-ops, they do not change the semantics of the script in which "
"they are included, and can be combined with any other locking script."
msgstr ""
"Der Inhalt der Inscription wird mithilfe von Daten-Pushs innerhalb nicht "
"ausgeführter Bedingungen, sogenannter „Umschläge“, serialisiert. Umschläge "
"bestehen aus einem `OP_FALSE OP_IF … OP_ENDIF `, das eine beliebige Anzahl "
"von Daten-Pushs umschließt. Da Umschläge praktisch No-Ops sind, ändern sie "
"nicht die Semantik des Skripts, in dem sie enthalten sind, und können mit "
"jedem anderen Sperrskript kombiniert werden."

#: src\inscriptions.md:39
msgid ""
"A text inscription containing the string \"Hello, world!\" is serialized as "
"follows:"
msgstr ""
"Eine inscription mit der Zeichenfolge \"Hello, world!\" wird wie folgt "
"serialisiert:"

#: src\inscriptions.md:42
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src\inscriptions.md:53
msgid ""
"First the string `ord` is pushed, to disambiguate inscriptions from other "
"uses of envelopes."
msgstr ""
"Zuerst wird die Zeichenfolge `ord` gedrückt, um inscriptions von anderen "
"Verwendungszwecken von Umschlägen zu unterscheiden."

#: src\inscriptions.md:56
msgid ""
"`OP_PUSH 1` indicates that the next push contains the content type, and "
"`OP_PUSH 0` indicates that subsequent data pushes contain the content "
"itself. Multiple data pushes must be used for large inscriptions, as one of "
"taproot's few restrictions is that individual data pushes may not be larger "
"than 520 bytes."
msgstr ""
"`OP_PUSH 1` gibt an, dass der nächste Push den Inhaltstyp enthält, und "
"`OP_PUSH 0` gibt an, dass nachfolgende Datenpushs den Inhalt selbst "
"enthalten. Für große inscriptions müssen mehrere Daten-Pushes verwendet "
"werden, da eine der wenigen Einschränkungen von Taproot darin besteht, dass "
"einzelne Daten-Pushes nicht größer als 520 Bytes sein dürfen."

#: src\inscriptions.md:61
msgid ""
"The inscription content is contained within the input of a reveal "
"transaction, and the inscription is made on the first sat of its input. This "
"sat can then be tracked using the familiar rules of ordinal theory, allowing "
"it to be transferred, bought, sold, lost to fees, and recovered."
msgstr ""
"Der Inhalt der inscription ist in der Eingabe einer Enthüllungstransaktion "
"enthalten und die inscription erfolgt am ersten sat ihrer Eingang (input). "
"Dieser Sat kann dann mithilfe der bekannten Regeln der Ordinaltheorie "
"verfolgt werden, sodass er übertragen, gekauft, verkauft, durch Gebühren "
"verloren und wiederhergestellt werden kann."

#: src\inscriptions.md:66
msgid "Content"
msgstr "Inhalt"

#: src\inscriptions.md:69
msgid ""
"The data model of inscriptions is that of a HTTP response, allowing "
"inscription content to be served by a web server and viewed in a web browser."
msgstr ""
"Das Datenmodell von inscriptions ist das einer HTTP-Antwort, sodass "
"inscription inhalte von einem Webserver bereitgestellt und in einem "
"Webbrowser angezeigt werden können."

#: src\inscriptions.md:72
msgid "Fields"
msgstr "Felder"

#: src\inscriptions.md:75
msgid ""
"Inscriptions may include fields before an optional body. Each field consists "
"of two data pushes, a tag and a value."
msgstr ""
"Inscriptions können Felder vor einem optionalen Text enthalten. Jedes Feld "
"besteht aus zwei Daten-Pushs, einem Tag und einem Wert."

#: src\inscriptions.md:78
msgid ""
"Currently, the only defined field is `content-type`, with a tag of `1`, "
"whose value is the MIME type of the body."
msgstr ""
"Derzeit ist das einzige definierte Feld `content-type` mit dem Tag `1`, "
"dessen Wert der MIME-Typ des Körpers ist."

#: src\inscriptions.md:81
msgid ""
"The beginning of the body and end of fields is indicated with an empty data "
"push."
msgstr ""
"Der Anfang des Hauptteils und das Ende der Felder werden durch einen leeren "
"Daten-Push angezeigt."

#: src\inscriptions.md:84
msgid ""
"Unrecognized tags are interpreted differently depending on whether they are "
"even or odd, following the \"it's okay to be odd\" rule used by the "
"Lightning Network."
msgstr ""
"Nicht erkannte Tags werden unterschiedlich interpretiert, je nachdem, ob sie "
"gerade oder ungerade sind. Dabei gilt die vom Lightning Network verwendete "
"Regel \"Es ist in Ordnung, ungerade zu sein\"."

#: src\inscriptions.md:88
msgid ""
"Even tags are used for fields which may affect creation, initial assignment, "
"or transfer of an inscription. Thus, inscriptions with unrecognized even "
"fields must be displayed as \"unbound\", that is, without a location."
msgstr ""
"Sogar Tags werden für Felder verwendet, die sich auf die Erstellung, "
"Erstzuweisung oder Übertragung einer inscription auswirken können. So müssen "
"inscription mit nicht erkannten geraden Feldern als \"ungebunden“, also ohne "
"Ortsangabe, angezeigt werden."

#: src\inscriptions.md:92
msgid ""
"Odd tags are used for fields which do not affect creation, initial "
"assignment, or transfer, such as additional metadata, and thus are safe to "
"ignore."
msgstr ""
"Ungerade Tags werden für Felder verwendet, die sich nicht auf die "
"Erstellung, anfängliche Zuweisung oder Übertragung auswirken, wie z. B. "
"zusätzliche Metadaten, und daher sicher ignoriert werden können."

#: src\inscriptions.md:95
msgid "Inscription IDs"
msgstr "Inscription IDs"

#: src\inscriptions.md:98
msgid ""
"The inscriptions are contained within the inputs of a reveal transaction. In "
"order to uniquely identify them they are assigned an ID of the form:"
msgstr ""
"Die inscriptions sind in den Eingaben einer Enthüllungstransaktion "
"enthalten. Um sie eindeutig zu identifizieren, wird ihnen eine ID der Form "
"zugewiesen:"

#: src\inscriptions.md:101
msgid "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"
msgstr "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"

#: src\inscriptions.md:103
msgid ""
"The part in front of the `i` is the transaction ID (`txid`) of the reveal "
"transaction. The number after the `i` defines the index (starting at 0) of "
"new inscriptions being inscribed in the reveal transaction."
msgstr ""
"Der Teil vor dem „i“ ist die Transaktions-ID (`txid`) der "
"Offenlegungstransaktion. Die Zahl nach dem `i` definiert den Index "
"(beginnend bei 0) der neuen inscriptions, die in die Offenlegungstransaktion "
"eingeschrieben werden."

#: src\inscriptions.md:107
msgid ""
"Inscriptions can either be located in different inputs, within the same "
"input or a combination of both. In any case the ordering is clear, since a "
"parser would go through the inputs consecutively and look for all "
"inscription `envelopes`."
msgstr ""
"Inscriptions können sich entweder in verschiedenen Eingaben (inputs), "
"innerhalb derselben Eingabe oder in einer Kombination aus beiden befinden. "
"In jedem Fall ist die Reihenfolge klar, da ein Parser die Eingaben "
"nacheinander durchgehen und nach allen inscription `envelopes` suchen würde."

#: src\inscriptions.md:111
msgid "Input"
msgstr "Eingang"

#: src\inscriptions.md:111
msgid "Inscription Count"
msgstr "Inscription Zählen"

#: src\inscriptions.md:111
msgid "Indices"
msgstr "Indices"

#: src\inscriptions.md:113 src\inscriptions.md:116
msgid "0"
msgstr "0"

#: src\inscriptions.md:113 src\inscriptions.md:115
msgid "2"
msgstr "2"

#: src\inscriptions.md:113
msgid "i0, i1"
msgstr "i0, i1"

#: src\inscriptions.md:114 src\inscriptions.md:117
msgid "1"
msgstr "1"

#: src\inscriptions.md:114
msgid "i2"
msgstr "i2"

#: src\inscriptions.md:115 src\inscriptions.md:116
msgid "3"
msgstr "3"

#: src\inscriptions.md:115
msgid "i3, i4, i5"
msgstr "i3, i4, i5"

#: src\inscriptions.md:117
msgid "4"
msgstr "4"

#: src\inscriptions.md:117
msgid "i6"
msgstr "i6"

#: src\inscriptions.md:119
msgid "Sandboxing"
msgstr "Sandboxen"

#: src\inscriptions.md:122
msgid ""
"HTML and SVG inscriptions are sandboxed in order to prevent references to "
"off-chain content, thus keeping inscriptions immutable and self-contained."
msgstr ""
"HTML- und SVG-inscriptions werden in einer Sandbox gespeichert, um Verweise "
"auf Inhalte außerhalb der chain zu verhindern, sodass die inscriptions "
"unveränderlich und in sich geschlossen bleiben."

#: src\inscriptions.md:125
msgid ""
"This is accomplished by loading HTML and SVG inscriptions inside `iframes` "
"with the `sandbox` attribute, as well as serving inscription content with "
"`Content-Security-Policy` headers."
msgstr ""
"Dies wird erreicht, indem HTML und SVG-Inschriften in `iframes` mit dem "
"`Sandbox` Attribut geladen werden und inscriptions inhalte mit `Content-"
"Security-Policy` Headern bereitgestellt werden."

#: src\inscriptions/recursion.md:4
msgid ""
"An important exception to [sandboxing](../inscriptions.md#sandboxing) is "
"recursion: access to `ord`'s `/content` endpoint is permitted, allowing "
"inscriptions to access the content of other inscriptions by requesting `/"
"content/<INSCRIPTION_ID>`."
msgstr ""
"Eine wichtige Ausnahme von [Sandboxing](../inscriptions.md#sandboxing) ist "
"die Rekursion: Der Zugriff auf den `ord`'s `/content` ist zulässig, sodass "
"Inschriften auf den Inhalt anderer Inschriften zugreifen können, indem sie `/"
"content/<INSCRIPTION_ID>`."

#: src\inscriptions/recursion.md:8
msgid "This has a number of interesting use-cases:"
msgstr "Dies hat eine Reihe interessanter Anwendungsfälle:"

#: src\inscriptions/recursion.md:10
msgid "Remixing the content of existing inscriptions."
msgstr "Neumischung des Inhalts bestehender inscriptions."

#: src\inscriptions/recursion.md:12
msgid ""
"Publishing snippets of code, images, audio, or stylesheets as shared public "
"resources."
msgstr ""
"Veröffentlichen von Code, Bild, Audio oder Stylesheet-Schnipseln als "
"gemeinsam genutzte öffentliche Ressourcen."

#: src\inscriptions/recursion.md:15
msgid ""
"Generative art collections where an algorithm is inscribed as JavaScript, "
"and instantiated from multiple inscriptions with unique seeds."
msgstr ""
"Generative Kunstsammlungen, bei denen ein Algorithmus als JavaScript "
"inscribed und aus mehreren inscriptions mit einzigartigen Seeds instanziiert "
"wird."

#: src\inscriptions/recursion.md:18
msgid ""
"Generative profile picture collections where accessories and attributes are "
"inscribed as individual images, or in a shared texture atlas, and then "
"combined, collage-style, in unique combinations in multiple inscriptions."
msgstr ""
"Generative Profilbildsammlungen, bei denen Accessoires und Attribute als "
"einzelne Bilder oder in einen gemeinsamen Texturatlas inscribed und dann im "
"Collagenstil in einzigartigen Kombinationen in mehreren inscriptions "
"kombiniert werden."

#: src\inscriptions/recursion.md:22
msgid "A few other endpoints that inscriptions may access are the following:"
msgstr ""
"Einige andere Endpunkte, auf die inscriptions zugreifen können, sind die "
"folgenden:"

#: src\inscriptions/recursion.md:24
msgid "`/blockheight`: latest block height."
msgstr "`/blockheight`: neueste Blockhöhe."

#: src\inscriptions/recursion.md:25
msgid "`/blockhash`: latest block hash."
msgstr "`/blockhash`: aktueller Block-Hash."

#: src\inscriptions/recursion.md:26
msgid "`/blockhash/<HEIGHT>`: block hash at given block height."
msgstr "`/blockhash/<HEIGHT>`: Block-Hash bei gegebener Blockhöhe."

#: src\inscriptions/recursion.md:27
msgid "`/blocktime`: UNIX time stamp of latest block."
msgstr "`/blocktime`: UNIX-Zeitstempel des letzten Blocks."

#: src\faq.md:1
msgid "Ordinal Theory FAQ"
msgstr "Ordinal theorie FAQ"

#: src\faq.md:4
msgid "What is ordinal theory?"
msgstr "Was ist ordinal theorie?"

#: src\faq.md:7
msgid ""
"Ordinal theory is a protocol for assigning serial numbers to satoshis, the "
"smallest subdivision of a bitcoin, and tracking those satoshis as they are "
"spent by transactions."
msgstr ""
"Die Ordinal theorie ist ein Protokoll zum Zuweisen von Seriennummern zu "
"Satoshis, der kleinsten Unterteilung eines Bitcoin, und zum Verfolgen dieser "
"Satoshis, während sie für Transaktionen ausgegeben werden."

#: src\faq.md:11
msgid ""
"These serial numbers are large numbers, like this 804766073970493. Every "
"satoshi, which is ¹⁄₁₀₀₀₀₀₀₀₀ of a bitcoin, has an ordinal number."
msgstr ""
"Diese Seriennummern sind große Zahlen, wie diese 804766073970493. Jeder "
"Satoshi, der ¹⁄₁₀₀₀₀₀₀₀₀ eines bitcoins ist, hat eine Ordnungszahl."

#: src\faq.md:14
msgid ""
"Does ordinal theory require a side chain, a separate token, or changes to "
"Bitcoin?"
msgstr ""
"Erfordert die Ordinaltheorie eine Seiten chain, einen separaten Token oder "
"Änderungen an Bitcoin?"

#: src\faq.md:17
msgid ""
"Nope! Ordinal theory works right now, without a side chain, and the only "
"token needed is bitcoin itself."
msgstr ""
"Nein! Die Ordinaltheorie funktioniert derzeit ohne Seiten chain, und der "
"einzige benötigte Token ist Bitcoin selbst."

#: src\faq.md:20
msgid "What is ordinal theory good for?"
msgstr "Wofür ist die Ordinaltheorie gut?"

#: src\faq.md:23
msgid ""
"Collecting, trading, and scheming. Ordinal theory assigns identities to "
"individual satoshis, allowing them to be individually tracked and traded, as "
"curios and for numismatic value."
msgstr ""
"Sammeln, Handeln und Intrigieren. Die Ordinaltheorie weist einzelnen "
"Satoshis Identitäten zu, sodass sie einzeln verfolgt und gehandelt werden "
"können, als Kuriositäten und für numismatischen Wert."

#: src\faq.md:27
msgid ""
"Ordinal theory also enables inscriptions, a protocol for attaching arbitrary "
"content to individual satoshis, turning them into bitcoin-native digital "
"artifacts."
msgstr ""
"Die Ordinaltheorie ermöglicht auch inscriptions, ein Protokoll zum Anhängen "
"beliebiger Inhalte an einzelne Satoshis und deren Umwandlung in Bitcoin-"
"native digitale Artefakte."

#: src\faq.md:31
msgid "How does ordinal theory work?"
msgstr "Wie funktioniert die Ordinaltheorie?"

#: src\faq.md:34
msgid ""
"Ordinal numbers are assigned to satoshis in the order in which they are "
"mined. The first satoshi in the first block has ordinal number 0, the second "
"has ordinal number 1, and the last satoshi of the first block has ordinal "
"number 4,999,999,999."
msgstr ""
"Ordinalzahlen werden den Satoshis in der Reihenfolge zugewiesen, in der sie "
"geschürft werden. Der erste Satoshi im ersten Block hat die Ordinal zahl 0, "
"der zweite hat die Ordinal zahl 1 und der letzte Satoshi des ersten Blocks "
"hat die Ordinal zahl 4.999.999.999."

#: src\faq.md:39
msgid ""
"Satoshis live in outputs, but transactions destroy outputs and create new "
"ones, so ordinal theory uses an algorithm to determine how satoshis hop from "
"the inputs of a transaction to its outputs."
msgstr ""
"Satoshis leben in Outputs, aber Transaktionen zerstören Outputs und "
"erstellen neue. Daher verwendet die Ordinaltheorie einen Algorithmus, um zu "
"bestimmen, wie Satoshis von den Inputs einer Transaktion zu ihren Outputs "
"springen."

#: src\faq.md:43
msgid "Fortunately, that algorithm is very simple."
msgstr "Glücklicherweise ist dieser Algorithmus sehr einfach."

#: src\faq.md:45
msgid ""
"Satoshis transfer in first-in-first-out order. Think of the inputs to a "
"transaction as being a list of satoshis, and the outputs as a list of slots, "
"waiting to receive a satoshi. To assign input satoshis to slots, go through "
"each satoshi in the inputs in order, and assign each to the first available "
"slot in the outputs."
msgstr ""
"Satoshis werden in der Reihenfolge \"first-in-first-out\" übertragen. "
"Stellen Sie sich die Inputs einer Transaktion als eine Liste von Satoshis "
"vor und die Outputs als eine Liste von Slots, die darauf warten, einen "
"Satoshi zu erhalten. Um Eingabe-Satoshis den Slots zuzuweisen, gehen Sie "
"nacheinander durch jeden Satoshi in den Inputs und weisen Sie ihn dem ersten "
"verfügbaren Slot in den Outputs zu."

#: src\faq.md:51
msgid ""
"Let's imagine a transaction with three inputs and two outputs. The inputs "
"are on the left of the arrow and the outputs are on the right, all labeled "
"with their values:"
msgstr ""
"Stellen Sie sich eine Transaktion mit drei Inputs und zwei Outputs vor. Die "
"Inputs befinden sich auf der linken Seite des Pfeils, und die Outputs auf "
"der rechten Seite, jeweils mit ihren Werten beschriftet:"

#: src\faq.md:55
msgid ""
"```\n"
"[2] [1] [3] → [4] [2]\n"
"```"
msgstr ""
"```\n"
"[2] [1] [3] → [4] [2]\n"
"```"

#: src\faq.md:57
msgid ""
"Now let's label the same transaction with the ordinal numbers of the "
"satoshis that each input contains, and question marks for each output slot. "
"Ordinal numbers are large, so let's use letters to represent them:"
msgstr ""
"Nun beschriften wir dieselbe Transaktion mit den Ordnungszahlen der "
"Satoshis, die sich in jedem Input befinden, und verwenden Fragezeichen für "
"jeden Output-Slot. Ordnungszahlen sind groß, daher verwenden wir Buchstaben, "
"um sie darzustellen:"

#: src\faq.md:61
msgid ""
"```\n"
"[a b] [c] [d e f] → [? ? ? ?] [? ?]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [? ? ? ?] [? ?]\n"
"```"

#: src\faq.md:63
msgid ""
"To figure out which satoshi goes to which output, go through the input "
"satoshis in order and assign each to a question mark:"
msgstr ""
"Um herauszufinden, welcher Satoshi zu welchem Output gehört, gehen Sie die "
"Input-Satoshis der Reihe nach durch und weisen jedem ein Fragezeichen zu:"

#: src\faq.md:66
msgid ""
"```\n"
"[a b] [c] [d e f] → [a b c d] [e f]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [a b c d] [e f]\n"
"```"

#: src\faq.md:68
msgid ""
"What about fees, you might ask? Good question! Let's imagine the same "
"transaction, this time with a two satoshi fee. Transactions with fees send "
"more satoshis in the inputs than are received by the outputs, so to make our "
"transaction into one that pays fees, we'll remove the second output:"
msgstr ""
"Was ist mit Gebühren, könnten Sie fragen? Gute Frage! Stellen Sie sich "
"dieselbe Transaktion vor, diesmal jedoch mit einer Gebühr von zwei Satoshis. "
"Transaktionen mit Gebühren senden mehr Satoshis in den Inputs als in den "
"Outputs empfangen werden. Um unsere Transaktion in eine Transaktion mit "
"Gebühren zu verwandeln, werden wir den zweiten Output entfernen:"

#: src\faq.md:73
msgid ""
"```\n"
"[2] [1] [3] → [4]\n"
"```"
msgstr ""
"```\n"
"[2] [1] [3] → [4]\n"
"```"

#: src\faq.md:75
msgid "The satoshis "
msgstr "Die satoshis "

#: src\faq.md:75
msgid "e"
msgstr "e"

#: src\faq.md:75
msgid " and "
msgstr " und "

#: src\faq.md:75
msgid "f"
msgstr "f"

#: src\faq.md:75
msgid " now have nowhere to go in the outputs:"
msgstr " Jetzt haben sie keine passenden Outputs mehr:"

#: src\faq.md:78
msgid ""
"```\n"
"[a b] [c] [d e f] → [a b c d]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [a b c d]\n"
"```"

#: src\faq.md:80
msgid ""
"So they go to the miner who mined the block as fees. [The BIP](https://"
"github.com/ordinals/ord/blob/master/bip.mediawiki) has the details, but in "
"short, fees paid by transactions are treated as extra inputs to the coinbase "
"transaction, and are ordered how their corresponding transactions are "
"ordered in the block. The coinbase transaction of the block might look like "
"this:"
msgstr ""
"Die Gebühren werden dem Miner, der den Block geschürft hat, als Gebühren "
"gutgeschrieben. [Das BIP](https://github.com/ordinals/ord/blob/master/bip."
"mediawiki) enthält die Details, aber kurz gesagt werden die Gebühren, die "
"von den Transaktionen gezahlt werden, als zusätzliche Inputs zur Coinbase-"
"Transaktion behandelt und entsprechend der Reihenfolge der zugehörigen "
"Transaktionen im Block angeordnet. Die Coinbase-Transaktion des Blocks "
"könnte wie folgt aussehen:"

#: src\faq.md:87
msgid ""
"```\n"
"[SUBSIDY] [e f] → [SUBSIDY e f]\n"
"```"
msgstr ""
"```\n"
"[SUBSIDY] [e f] → [SUBSIDY e f]\n"
"```"

#: src\faq.md:89
msgid "Where can I find the nitty-gritty details?"
msgstr "Wo finde ich die wichtigsten Details?"

#: src\faq.md:92
msgid "[The BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[The BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src\faq.md:94
msgid ""
"Why are sat inscriptions called \"digital artifacts\" instead of \"NFTs\"?"
msgstr ""
"Warum werden Sat-inscriptions \"digitale Artefakte“ statt \"NFTs“ genannt?"

#: src\faq.md:97
msgid ""
"An inscription is an NFT, but the term \"digital artifact\" is used instead, "
"because it's simple, suggestive, and familiar."
msgstr ""
"Eine inscription ist ein NFT (Non-Fungible Token), aber der Begriff "
"\"digitales Artefakt\" wird stattdessen verwendet, weil er einfach, "
"anschaulich und vertraut ist."

#: src\faq.md:100
msgid ""
"The phrase \"digital artifact\" is highly suggestive, even to someone who "
"has never heard the term before. In comparison, NFT is an acronym, and "
"doesn't provide any indication of what it means if you haven't heard the "
"term before."
msgstr ""
"Der Ausdruck \"digitales Artefakt\" ist äußerst anschaulich, selbst für "
"jemanden, der den Begriff zuvor noch nie gehört hat. Im Vergleich dazu ist "
"NFT ein Akronym und gibt keine Hinweise darauf, was es bedeutet, wenn man "
"den Begriff zuvor noch nie gehört hat."

#: src\faq.md:104
msgid ""
"Additionally, \"NFT\" feels like financial terminology, and the both word "
"\"fungible\" and sense of the word \"token\" as used in \"NFT\" is uncommon "
"outside of financial contexts."
msgstr ""
"Außerdem klingt \"NFT\" nach Finanzterminologie, und sowohl das Wort "
"\"fungibel\" als auch die Bedeutung des Wortes \"Token\" im Kontext von "
"\"NFT\" sind außerhalb finanzieller Zusammenhänge unüblich."

#: src\faq.md:108
msgid "How do sat inscriptions compare to…"
msgstr "Wie vergleichen sich Sat-inscriptions mit…"

#: src\faq.md:111
msgid "Ethereum NFTs?"
msgstr "Ethereum NFTs?"

#: src\faq.md:113
msgid "_Inscriptions are always immutable._"
msgstr "_Inscriptions sind immer unveränderlich._"

#: src\faq.md:115
msgid ""
"There is simply no way to for the creator of an inscription, or the owner of "
"an inscription, to modify it after it has been created."
msgstr ""
"Es gibt schlichtweg keine Möglichkeit für den Schöpfer oder den Besitzer "
"einer Inschrift, diese nach ihrer Erstellung zu ändern."

#: src\faq.md:118
msgid ""
"Ethereum NFTs _can_ be immutable, but many are not, and can be changed or "
"deleted by the NFT contract owner."
msgstr ""
"Ethereum NFTs _können_ unveränderlich sein, aber viele sind es nicht und "
"können vom Besitzer des NFT-Vertrags geändert oder gelöscht werden."

#: src\faq.md:121
msgid ""
"In order to make sure that a particular Ethereum NFT is immutable, the "
"contract code must be audited, which requires detailed knowledge of the EVM "
"and Solidity semantics."
msgstr ""
"Um sicherzustellen, dass ein bestimmtes Ethereum NFT unveränderlich ist, "
"muss der Vertragscode überprüft werden. Dies erfordert ein detailliertes "
"Verständnis der EVM- und Solidity-Semantik."

#: src\faq.md:125
msgid ""
"It is very hard for a non-technical user to determine whether or not a given "
"Ethereum NFT is mutable or immutable, and Ethereum NFT platforms make no "
"effort to distinguish whether an NFT is mutable or immutable, and whether "
"the contract source code is available and has been audited."
msgstr ""
"Es ist für einen nicht-technischen Benutzer sehr schwer festzustellen, ob "
"ein bestimmtes Ethereum NFT veränderbar oder unveränderlich ist. Ethereum "
"NFT-Plattformen unternehmen keine Anstrengungen, um zu unterscheiden, ob ein "
"NFT veränderbar oder unveränderlich ist und ob der Vertragsquellcode "
"verfügbar ist und überprüft wurde."

#: src\faq.md:130
msgid "_Inscription content is always on-chain._"
msgstr "_Die Inhalt das Inscription ist immer in der chain._"

#: src\faq.md:132
msgid ""
"There is no way for an inscription to refer to off-chain content. This makes "
"inscriptions more durable, because content cannot be lost, and scarcer, "
"because inscription creators must pay fees proportional to the size of the "
"content."
msgstr ""
"Es gibt keine Möglichkeit für eine Inschrift, auf Off-Chain-Inhalte zu "
"verweisen. Dies macht Inschriften langlebiger, da Inhalte nicht verloren "
"gehen können, und knapper, da die Ersteller von Inschriften Gebühren in "
"Abhängigkeit von der Größe des Inhalts zahlen müssen."

#: src\faq.md:136
msgid ""
"Some Ethereum NFT content is on-chain, but much is off-chain, and is stored "
"on platforms like IPFS or Arweave, or on traditional, fully centralized web "
"servers. Content on IPFS is not guaranteed to continue to be available, and "
"some NFT content stored on IPFS has already been lost. Platforms like "
"Arweave rely on weak economic assumptions, and will likely fail "
"catastrophically when these economic assumptions are no longer met. "
"Centralized web servers may disappear at any time."
msgstr ""
"Einige Ethereum NFT-Inhalte sind in der Blockchain gespeichert, aber ein "
"Großteil befindet sich außerhalb der Blockchain und wird auf Plattformen wie "
"IPFS oder Arweave oder auf traditionellen, vollständig zentralisierten "
"Webservern gespeichert. Inhalte auf IPFS sind nicht garantiert dauerhaft "
"verfügbar, und einige NFT-Inhalte, die auf IPFS gespeichert sind, gingen "
"bereits verloren. Plattformen wie Arweave stützen sich auf schwache "
"wirtschaftliche Annahmen und werden wahrscheinlich katastrophal scheitern, "
"wenn diese wirtschaftlichen Annahmen nicht mehr erfüllt sind. Zentralisierte "
"Webserver können jederzeit verschwinden."

#: src\faq.md:144
msgid ""
"It is very hard for a non-technical user to determine where the content of a "
"given Ethereum NFT is stored."
msgstr ""
"Es ist für einen nicht-technischen Benutzer sehr schwer zu bestimmen, wo der "
"Inhalt eines bestimmten Ethereum NFT gespeichert ist."

#: src\faq.md:147
msgid "_Inscriptions are much simpler._"
msgstr "_Inscriptions sind viel einfacher._"

#: src\faq.md:149
msgid ""
"Ethereum NFTs depend on the Ethereum network and virtual machine, which are "
"highly complex, constantly changing, and which introduce changes via "
"backwards-incompatible hard forks."
msgstr ""
"Ethereum NFTs sind abhängig vom Ethereum-Netzwerk und der virtuellen "
"Maschine, die äußerst komplex und ständigen Veränderungen unterworfen sind. "
"Diese Änderungen erfolgen oft durch rückwärtsinkompatible Hardforks."

#: src\faq.md:153
msgid ""
"Inscriptions, on the other hand, depend on the Bitcoin blockchain, which is "
"relatively simple and conservative, and which introduces changes via "
"backwards-compatible soft forks."
msgstr ""
"Inscriptions hingegen sind auf die Bitcoin-Blockchain angewiesen, die "
"vergleichsweise einfach und konservativ ist und Änderungen durch "
"rückwärtskompatible Softforks einführt."

#: src\faq.md:157
msgid "_Inscriptions are more secure._"
msgstr "_Inscriptions sind sicherer._"

#: src\faq.md:159
msgid ""
"Inscriptions inherit Bitcoin's transaction model, which allow a user to see "
"exactly which inscriptions are being transferred by a transaction before "
"they sign it. Inscriptions can be offered for sale using partially signed "
"transactions, which don't require allowing a third party, such as an "
"exchange or marketplace, to transfer them on the user's behalf."
msgstr ""
"Inscriptions erben das Transaktionsmodell von Bitcoin, das es einem Benutzer "
"ermöglicht, genau zu sehen, welche Inschriften durch eine Transaktion "
"übertragen werden, bevor er sie signiert. Inschriften können zum Verkauf "
"angeboten werden, indem teilweise signierte Transaktionen verwendet werden, "
"die keine Übertragung durch Dritte wie eine Börse oder einen Marktplatz im "
"Auftrag des Benutzers erfordern."

#: src\faq.md:165
msgid ""
"By comparison, Ethereum NFTs are plagued with end-user security "
"vulnerabilities. It is commonplace to blind-sign transactions, grant third-"
"party apps unlimited permissions over a user's NFTs, and interact with "
"complex and unpredictable smart contracts. This creates a minefield of "
"hazards for Ethereum NFT users which are simply not a concern for ordinal "
"theorists."
msgstr ""
"Inscriptions erben das Transaktionsmodell von Bitcoin, das es einem Benutzer "
"ermöglicht, genau zu sehen, welche Inschriften durch eine Transaktion "
"übertragen werden, bevor er sie signiert. Inschriften können zum Verkauf "
"angeboten werden, indem teilweise signierte Transaktionen verwendet werden, "
"die keine Übertragung durch Dritte wie eine Börse oder einen Marktplatz im "
"Auftrag des Benutzers erfordern."

#: src\faq.md:171
msgid "_Inscriptions are scarcer._"
msgstr "_Inscriptions sind seltener._"

#: src\faq.md:173
msgid ""
"Inscriptions require bitcoin to mint, transfer, and store. This seems like a "
"downside on the surface, but the raison d'etre of digital artifacts is to be "
"scarce and thus valuable."
msgstr ""
"Inscriptions erfordern Bitcoin, um geprägt, übertragen und gespeichert zu "
"werden. Dies scheint auf den ersten Blick nachteilig zu sein, aber der Grund "
"für das Dasein digitaler Artefakte besteht darin, knapp und daher wertvoll "
"zu sein."

#: src\faq.md:177
msgid ""
"Ethereum NFTs, on the other hand, can be minted in virtually unlimited "
"qualities with a single transaction, making them inherently less scarce, and "
"thus, potentially less valuable."
msgstr ""
"Auf der anderen Seite können Ethereum NFTs praktisch in unbegrenzten Mengen "
"mit einer einzigen Transaktion geprägt werden, was sie von Natur aus weniger "
"knapp macht und somit potenziell weniger wertvoll."

#: src\faq.md:181
msgid "_Inscriptions do not pretend to support on-chain royalties._"
msgstr ""
"_Inscriptions geben nicht vor, On-Chain-Lizenzgebühren zu unterstützen._"

#: src\faq.md:183
msgid ""
"On-chain royalties are a good idea in theory but not in practice. Royalty "
"payment cannot be enforced on-chain without complex and invasive "
"restrictions. The Ethereum NFT ecosystem is currently grappling with "
"confusion around royalties, and is collectively coming to grips with the "
"reality that on-chain royalties, which were messaged to artists as an "
"advantage of NFTs, are not possible, while platforms race to the bottom and "
"remove royalty support."
msgstr ""
"On-Chain-Royalties sind in der Theorie eine gute Idee, aber in der Praxis "
"nicht umsetzbar. Die Zahlung von Lizenzgebühren kann nicht ohne komplexe und "
"invasive Beschränkungen auf der Blockchain durchgesetzt werden. Das Ethereum-"
"NFT-ecosystem kämpft derzeit mit Verwirrung im Zusammenhang mit "
"Lizenzgebühren und kommt kollektiv zu der Erkenntnis, dass On-Chain-"
"Royalties, die Künstlern als Vorteil von NFTs präsentiert wurden, nicht "
"möglich sind. Inzwischen entfernen Plattformen die Unterstützung für "
"Lizenzgebühren und konkurrieren auf dem Markt."

#: src\faq.md:190
msgid ""
"Inscriptions avoid this situation entirely by making no false promises of "
"supporting royalties on-chain, thus avoiding the confusion, chaos, and "
"negativity of the Ethereum NFT situation."
msgstr ""
"Inscriptions umgehen diese Situation vollständig, indem sie keine falschen "
"Versprechen bezüglich der Unterstützung von On-Chain-Royalties abgeben. "
"Dadurch vermeiden sie die Verwirrung, das Chaos und die Negativität, die im "
"Ethereum NFT-Bereich aufgetreten sind."

#: src\faq.md:194
msgid "_Inscriptions unlock new markets._"
msgstr "_Inscriptions erschließen neue Märkte._"

#: src\faq.md:196
msgid ""
"Bitcoin's market capitalization and liquidity are greater than Ethereum's by "
"a large margin. Much of this liquidity is not available to Ethereum NFTs, "
"since many Bitcoiners prefer not to interact with the Ethereum ecosystem due "
"to concerns related to simplicity, security, and decentralization."
msgstr ""
"Die Marktkapitalisierung und Liquidität von Bitcoin sind bei weitem größer "
"als die von Ethereum. Ein Großteil dieser Liquidität steht Ethereum NFTs "
"nicht zur Verfügung, da viele Bitcoin-Nutzer aufgrund von Bedenken in Bezug "
"auf Einfachheit, Sicherheit und Dezentralisierung lieber nicht mit dem "
"Ethereum-ecosystem interagieren möchten."

#: src\faq.md:201
msgid ""
"Such Bitcoiners may be more interested in inscriptions than Ethereum NFTs, "
"unlocking new classes of collector."
msgstr ""
"Solche Bitcoin-Nutzer könnten möglicherweise mehr Interesse an inscriptions "
"als an Ethereum NFTs haben, was neue Sammlerklassen erschließen könnte."

#: src\faq.md:204
msgid "_Inscriptions have a richer data model._"
msgstr "_Inscriptions haben ein umfangreicheres Datenmodell._"

#: src\faq.md:206
msgid ""
"Inscriptions consist of a content type, also known as a MIME type, and "
"content, which is an arbitrary byte string. This is the same data model used "
"by the web, and allows inscription content to evolve with the web, and come "
"to support any kind of content supported by web browsers, without requiring "
"changes to the underlying protocol."
msgstr ""
"Inscriptions bestehen aus einem Inhaltstyp, auch als MIME-Typ bekannt, und "
"einem Inhalt, der eine beliebige Byte-Folge ist. Dies ist das gleiche "
"Datenmodell, das vom Web verwendet wird, und ermöglicht es, dass sich der "
"inscription inhalt mit dem Web weiterentwickelt und jede Art von Inhalt "
"unterstützt, die von Webbrowsern unterstützt wird, ohne Änderungen am "
"zugrunde liegenden Protokoll zu erfordern."

#: src\faq.md:212
msgid "RGB and Taro assets?"
msgstr "RGB und Taro Assets?"

#: src\faq.md:214
msgid ""
"RGB and Taro are both second-layer asset protocols built on Bitcoin. "
"Compared to inscriptions, they are much more complicated, but much more "
"featureful."
msgstr ""
"RGB und Taro sind beide Asset-Protokolle der zweiten Ebene, die auf Bitcoin "
"aufbauen. Im Vergleich zu inscriptions sind sie wesentlich komplizierter, "
"aber bieten auch wesentlich mehr Funktionen."

#: src\faq.md:217
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas the primary use-case of RGB and Taro are fungible tokens, so the "
"user experience for inscriptions is likely to be simpler and more polished "
"than the user experience for RGB and Taro NFTs."
msgstr ""
"Ordinal theory wurde von Grund auf für digitale Artefakte entwickelt, "
"während der Hauptanwendungsfall von RGB und Taro fungible Tokens sind. Daher "
"dürfte die Benutzererfahrung für inscriptions wahrscheinlich einfacher und "
"ausgereifter sein als die Benutzererfahrung für RGB und Taro NFTs."

#: src\faq.md:222
msgid ""
"RGB and Taro both store content off-chain, which requires additional "
"infrastructure, and which may be lost. By contrast, inscription content is "
"stored on-chain, and cannot be lost."
msgstr ""
"RGB und Taro speichern beide Inhalte außerhalb der Blockchain, was "
"zusätzliche Infrastruktur erfordert und verloren gehen kann. Im Gegensatz "
"dazu werden inscription inhalte in der Blockchain gespeichert und können "
"nicht verloren gehen."

#: src\faq.md:226
msgid ""
"Ordinal theory, RGB, and Taro are all very early, so this is speculation, "
"but ordinal theory's focus may give it the edge in terms of features for "
"digital artifacts, including a better content model, and features like "
"globally unique symbols."
msgstr ""
"Ordinal theory, RGB und Taro sind alle noch sehr früh in ihrer Entwicklung, "
"daher handelt es sich hierbei um Spekulationen. Ordinal Theory könnte jedoch "
"aufgrund seines Schwerpunkts auf digitalen Artefakten Vorteile in Bezug auf "
"Funktionen für digitale Artefakte haben, einschließlich eines besseren "
"Inhaltsmodells und Funktionen wie global eindeutige Symbole."

#: src\faq.md:231
msgid "Counterparty assets?"
msgstr "Counterparty vermögenswerte?"

#: src\faq.md:233
msgid ""
"Counterparty has its own token, XCP, which is required for some "
"functionality, which makes most bitcoiners regard it as an altcoin, and not "
"an extension or second layer for bitcoin."
msgstr ""
"Counterparty hat seine eigene Token, XCP, die für einige Funktionen "
"erforderlich sind. Dies führt dazu, dass die meisten Bitcoin-Nutzer es als "
"Altcoin betrachten und nicht als Erweiterung oder Second Layer für Bitcoin."

#: src\faq.md:237
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas Counterparty was primarily designed for financial token issuance."
msgstr ""
"Ordinaltheorie wurde von Grund auf für digitale Artefakte entwickelt, "
"während Counterparty in erster Linie für die Ausgabe von Finanztokens "
"konzipiert wurde."

#: src\faq.md:240
msgid "Inscriptions for…"
msgstr "Inscriptions für…"

#: src\faq.md:243
msgid "Artists"
msgstr "Künstler"

#: src\faq.md:245
msgid ""
"_Inscriptions are on Bitcoin._ Bitcoin is the digital currency with the "
"highest status and greatest chance of long-term survival. If you want to "
"guarantee that your art survives into the future, there is no better way to "
"publish it than as inscriptions."
msgstr ""
"_Inscriptions existieren auf Bitcoin._ Bitcoin ist die digitale Währung mit "
"dem höchsten Status und den größten Überlebenschancen auf lange Sicht. Wenn "
"Sie sicherstellen möchten, dass Ihre Kunst in die Zukunft überdauert, gibt "
"es keine bessere Möglichkeit, sie als inscriptions zu veröffentlichen."

#: src\faq.md:250
msgid ""
"_Cheaper on-chain storage._ At $20,000 per BTC and the minimum relay fee of "
"1 satoshi per vbyte, publishing inscription content costs $50 per 1 million "
"bytes."
msgstr ""
"_Günstigere On-Chain-Speicherung._ Bei $20,000 pro BTC und der Mindest-Relay-"
"Gebühr von 1 Sat pro Vbyte kostet die Veröffentlichung von inscription $50 "
"pro 1 Million Bytes."

#: src\faq.md:254
msgid ""
"_Inscriptions are early!_ Inscriptions are still in development, and have "
"not yet launched on mainnet. This gives you an opportunity to be an early "
"adopter, and explore the medium as it evolves."
msgstr ""
"_Inscriptions sind noch in der Frühphase!_ Inscriptions befinden sich noch "
"in der Entwicklung und wurden noch nicht auf Mainnet gestartet. Dies gibt "
"Ihnen die Möglichkeit, ein früher Anwender zu sein und das Medium zu "
"erkunden, während es sich weiterentwickelt."

#: src\faq.md:258
msgid ""
"_Inscriptions are simple._ Inscriptions do not require writing or "
"understanding smart contracts."
msgstr ""
"_Inscriptions sind einfach._ Für Inscriptions ist es nicht erforderlich, "
"Smart Contracts zu schreiben oder zu verstehen."

#: src\faq.md:261
msgid ""
"_Inscriptions unlock new liquidity._ Inscriptions are more accessible and "
"appealing to bitcoin holders, unlocking an entirely new class of collector."
msgstr ""
"_Inscriptions erschließen neue Liquidität._ Inscriptions sind für Bitcoin-"
"Inhaber zugänglicher und attraktiver und eröffnen damit eine völlig neue "
"Sammlerklasse."

#: src\faq.md:264
msgid ""
"_Inscriptions are designed for digital artifacts._ Inscriptions are designed "
"from the ground up to support NFTs, and feature a better data model, and "
"features like globally unique symbols and enhanced provenance."
msgstr ""
"_Inscriptions sind für digitale Artefakte konzipiert._ Inscriptions sind von "
"Grund auf so konzipiert, dass sie NFTs unterstützen, und bieten ein besseres "
"Datenmodell sowie Funktionen wie global eindeutige Symbole und erweiterte "
"Herkunftsnachweise."

#: src\faq.md:268
msgid ""
"_Inscriptions do not support on-chain royalties._ This is negative, but only "
"depending on how you look at it. On-chain royalties have been a boon for "
"creators, but have also created a huge amount of confusion in the Ethereum "
"NFT ecosystem. The ecosystem now grapples with this issue, and is engaged in "
"a race to the bottom, towards a royalties-optional future. Inscriptions have "
"no support for on-chain royalties, because they are technically infeasible. "
"If you choose to create inscriptions, there are many ways you can work "
"around this limitation: withhold a portion of your inscriptions for future "
"sale, to benefit from future appreciation, or perhaps offer perks for users "
"who respect optional royalties."
msgstr ""
"_Inschriften unterstützen keine On-Chain-Royalties._ Das kann sowohl negativ "
"als auch positiv betrachtet werden. On-Chain-Royalties haben Künstlern "
"geholfen, haben jedoch auch für Verwirrung im Ethereum NFT-Ökosystem "
"gesorgt. Das Ökosystem sieht sich nun mit diesem Problem konfrontiert und "
"bewegt sich in Richtung einer Zukunft ohne verpflichtende Royalties. "
"Inschriften unterstützen keine On-Chain-Royalties, da sie technisch nicht "
"umsetzbar sind. Wenn Sie sich dafür entscheiden, Inschriften zu erstellen, "
"gibt es viele Möglichkeiten, diese Einschränkung zu umgehen: Behalten Sie "
"einen Teil Ihrer Inschriften für den zukünftigen Verkauf zurück, um von "
"zukünftiger Wertsteigerung zu profitieren, oder bieten Sie vielleicht "
"Vorteile für Benutzer an, die optionale Royalties respektieren."

#: src\faq.md:279
msgid "Collectors"
msgstr "Sammler"

#: src\faq.md:281
msgid ""
"_Inscriptions are simple, clear, and have no surprises._ They are always "
"immutable and on-chain, with no special due diligence required."
msgstr ""
"_Inscriptions sind einfach, klar und ohne Überraschungen._ Sie sind immer "
"unveränderlich und auf der Blockchain, ohne besondere Sorgfaltspflichten zu "
"erfordern."

#: src\faq.md:284
msgid ""
"_Inscriptions are on Bitcoin._ You can verify the location and properties of "
"inscriptions easily with Bitcoin full node that you control."
msgstr ""
"_Inscriptions sind auf Bitcoin._ Sie können den Standort und die "
"Eigenschaften von Inscriptions leicht mit einem von Ihnen kontrollierten "
"Bitcoin-Full-node überprüfen."

#: src\faq.md:287
msgid "Bitcoiners"
msgstr "Bitcoiners"

#: src\faq.md:289
msgid ""
"Let me begin this section by saying: the most important thing that the "
"Bitcoin network does is decentralize money. All other use-cases are "
"secondary, including ordinal theory. The developers of ordinal theory "
"understand and acknowledge this, and believe that ordinal theory helps, at "
"least in a small way, Bitcoin's primary mission."
msgstr ""
"Lassen Sie mich diesen Abschnitt mit den Worten beginnen: Das Wichtigste, "
"was das Bitcoin-Netzwerk tut, ist die Dezentralisierung von Geld. Alle "
"anderen Anwendungsfälle sind sekundär, einschließlich der Ordinaltheorie. "
"Die Entwickler der Ordinaltheorie verstehen und erkennen dies an und "
"glauben, dass die Ordinaltheorie zumindest in geringem Maße dazu beiträgt, "
"die primäre Mission von Bitcoin zu unterstützen."

#: src\faq.md:295
msgid ""
"Unlike many other things in the altcoin space, digital artifacts have merit. "
"There are, of course, a great deal of NFTs that are ugly, stupid, and "
"fraudulent. However, there are many that are fantastically creative, and "
"creating and collecting art has been a part of the human story since its "
"inception, and predates even trade and money, which are also ancient "
"technologies."
msgstr ""
"Anders als viele andere Dinge im Altcoin-Bereich haben digitale Artefakte "
"ihren Wert. Es gibt natürlich viele NFTs, die hässlich, dumm und "
"betrügerisch sind. Es gibt jedoch viele, die fantastisch kreativ sind, und "
"das Schaffen und Sammeln von Kunst gehört seit Anbeginn der "
"Menschheitsgeschichte dazu und geht sogar der Handels- und Geldwirtschaft "
"voraus, die ebenfalls alte Technologien sind."

#: src\faq.md:302
msgid ""
"Bitcoin provides an amazing platform for creating and collecting digital "
"artifacts in a secure, decentralized way, that protects users and artists in "
"the same way that it provides an amazing platform for sending and receiving "
"value, and for all the same reasons."
msgstr ""
"Bitcoin bietet eine erstaunliche Plattform für die Erstellung und Sammlung "
"von digitalen Artefakten in einer sicheren, dezentralen Weise, die Benutzer "
"und Künstler auf die gleiche Weise schützt, wie es eine erstaunliche "
"Plattform für das Senden und Empfangen von Wert bietet, und aus denselben "
"Gründen."

#: src\faq.md:307
msgid ""
"Ordinals and inscriptions increase demand for Bitcoin block space, which "
"increase Bitcoin's security budget, which is vital for safeguarding "
"Bitcoin's transition to a fee-dependent security model, as the block subsidy "
"is halved into insignificance."
msgstr ""
"Ordinals und inscriptions erhöhen die Nachfrage nach Bitcoin-Blockplatz, was "
"das Sicherheitsbudget von Bitcoin erhöht, was für die Sicherung des "
"Übergangs von Bitcoin zu einem gebührenabhängigen Sicherheitsmodell von "
"entscheidender Bedeutung ist, da die Block-Subvention in die "
"Bedeutungslosigkeit halbiert wird."

#: src\faq.md:312
msgid ""
"Inscription content is stored on-chain, and thus the demand for block space "
"for use in inscriptions is unlimited. This creates a buyer of last resort "
"for _all_ Bitcoin block space. This will help support a robust fee market, "
"which ensures that Bitcoin remains secure."
msgstr ""
"Der Inhalt der Inscription wird auf der Blockchain gespeichert, und daher "
"ist die Nachfrage nach Blockplatz für Inscription unbegrenzt. Dies schafft "
"einen Käufer letzter Instanz für _allen_ Bitcoin-Blockplatz. Dies wird dazu "
"beitragen, einen robusten Gebührenmarkt zu unterstützen, der sicherstellt, "
"dass Bitcoin sicher bleibt."

#: src\faq.md:317
msgid ""
"Inscriptions also counter the narrative that Bitcoin cannot be extended or "
"used for new use-cases. If you follow projects like DLCs, Fedimint, "
"Lightning, Taro, and RGB, you know that this narrative is false, but "
"inscriptions provide a counter argument which is easy to understand, and "
"which targets a popular and proven use case, NFTs, which makes it highly "
"legible."
msgstr ""
"Inscriptions stehen auch im Widerspruch zur Erzählung, dass Bitcoin nicht "
"erweitert oder für neue Anwendungsfälle verwendet werden kann. Wenn Sie "
"Projekte wie DLCs, Fedimint, Lightning, Taro und RGB verfolgen, wissen Sie, "
"dass diese Erzählung falsch ist, aber inscriptions liefern ein "
"Gegenargument, das leicht verständlich ist und auf einen beliebten und "
"bewährten Anwendungsfall abzielt: NFTs, was es äußerst verständlich macht."

#: src\faq.md:323
msgid ""
"If inscriptions prove, as the authors hope, to be highly sought after "
"digital artifacts with a rich history, they will serve as a powerful hook "
"for Bitcoin adoption: come for the fun, rich art, stay for the decentralized "
"digital money."
msgstr ""
"Wenn sich inscriptions, wie die Autoren hoffen, als sehr begehrte digitale "
"Artefakte mit einer reichen Geschichte erweisen, werden sie als ein "
"mächtiger Köder für die Bitcoin-Adoption dienen: Kommen Sie wegen des Spaßes "
"und der reichen Kunst, bleiben Sie für das dezentralisierte digitale Geld."

#: src\faq.md:327
msgid ""
"Inscriptions are an extremely benign source of demand for block space. "
"Unlike, for example, stablecoins, which potentially give large stablecoin "
"issuers influence over the future of Bitcoin development, or DeFi, which "
"might centralize mining by introducing opportunities for MEV, digital art "
"and collectables on Bitcoin, are unlikely to produce individual entities "
"with enough power to corrupt Bitcoin. Art is decentralized."
msgstr ""
"Inscriptions sind eine äußerst harmlose Quelle für die Nachfrage nach "
"Blockplatz. Im Gegensatz zu stabilen Münzen, die potenziell großen "
"Emittenten von stabilen Münzen Einfluss auf die Zukunft der Bitcoin-"
"Entwicklung geben könnten, oder DeFi, das durch die Einführung von "
"Möglichkeiten für MEV das Mining möglicherweise zentralisiert, sind digitale "
"Kunst und Sammlerstücke auf Bitcoin unwahrscheinlich, einzelne Entitäten mit "
"genug Macht zu erzeugen, um Bitcoin zu korrumpieren. Kunst ist "
"dezentralisiert."

#: src\faq.md:334
msgid ""
"Inscription users and service providers are incentivized to run Bitcoin full "
"nodes, to publish and track inscriptions, and thus throw their economic "
"weight behind the honest chain."
msgstr ""
"Nutzer von Inscription und Dienstleister werden dazu angeregt, Bitcoin-Full-"
"Nodes zu betreiben, um Inscription zu veröffentlichen und zu verfolgen, und "
"somit ihr wirtschaftliches Gewicht hinter die ehrliche Blockchain zu legen."

#: src\faq.md:338
msgid ""
"Ordinal theory and inscriptions do not meaningfully affect Bitcoin's "
"fungibility. Bitcoin users can ignore both and be unaffected."
msgstr ""
"Die Ordinaltheorie und inscriptions beeinflussen die Fungibilität von "
"Bitcoin nicht in bedeutender Weise. Bitcoin-Nutzer können beide ignorieren "
"und bleiben unberührt."

#: src\faq.md:341
msgid ""
"We hope that ordinal theory strengthens and enriches bitcoin, and gives it "
"another dimension of appeal and functionality, enabling it more effectively "
"serve its primary use case as humanity's decentralized store of value."
msgstr ""
"Wir hoffen, dass die Ordinaltheorie Bitcoin stärkt und bereichert und ihm "
"eine weitere Dimension von Attraktivität und Funktionalität verleiht, damit "
"es seine Hauptanwendung als dezentrale Wertspeicherung der Menschheit "
"effektiver erfüllen kann."

#: src\contributing.md:1
msgid "Contributing to `ord`"
msgstr "Beitrag zu `ord`"

#: src\contributing.md:4
msgid "Suggested Steps"
msgstr "Vorgeschlagene Schritte"

#: src\contributing.md:7
msgid "Find an issue you want to work on."
msgstr "Finden Sie ein Problem, an dem Sie arbeiten möchten."

#: src\contributing.md:8
msgid ""
"Figure out what would be a good first step towards resolving the issue. This "
"could be in the form of code, research, a proposal, or suggesting that it be "
"closed, if it's out of date or not a good idea in the first place."
msgstr ""
"Überlegen Sie, was der erste Schritt zur Lösung des Problems sein könnte. "
"Dies könnte in Form von Code, Forschung, einem Vorschlag oder der Empfehlung "
"erfolgen, es zu schließen, wenn es veraltet ist oder von vornherein keine "
"gute Idee ist."

#: src\contributing.md:11
msgid ""
"Comment on the issue with an outline of your suggested first step, and "
"asking for feedback. Of course, you can dive in and start writing code or "
"tests immediately, but this avoids potentially wasted effort, if the issue "
"is out of date, not clearly specified, blocked on something else, or "
"otherwise not ready to implement."
msgstr ""
"Kommentieren Sie das Problem, skizzieren Sie Ihren vorgeschlagenen ersten "
"Schritt und bitten Sie um Feedback. Natürlich können Sie sofort loslegen und "
"mit dem Schreiben von Code oder Tests beginnen, aber das vermeidet "
"potenziell verschwendeten Aufwand, wenn das Problem veraltet, nicht klar "
"spezifiziert, an etwas anderem blockiert oder aus anderen Gründen nicht zur "
"Implementierung bereit ist."

#: src\contributing.md:16
msgid ""
"If the issue requires a code change or bugfix, open a draft PR with tests, "
"and ask for feedback. This makes sure that everyone is on the same page "
"about what needs to be done, or what the first step in solving the issue "
"should be. Also, since tests are required, writing the tests first makes it "
"easy to confirm that the change can be tested easily."
msgstr ""
"Wenn das Problem eine Code-Änderung oder Fehlerbehebung erfordert, öffnen "
"Sie einen Entwurfs-PR mit Tests und bitten Sie um Feedback. Dies stellt "
"sicher, dass alle auf derselben Seite darüber sind, was getan werden muss, "
"oder was der erste Schritt zur Lösung des Problems sein sollte. Da Tests "
"erforderlich sind, erleichtert das Schreiben der Tests zuerst die "
"Bestätigung, dass die Änderung leicht getestet werden kann."

#: src\contributing.md:21
msgid ""
"Mash the keyboard randomly until the tests pass, and refactor until the code "
"is ready to submit."
msgstr ""
"Mischen Sie die Tastatur nach dem Zufallsprinzip, bis die Tests bestanden "
"sind, und überarbeiten Sie sie, bis der Code zur Übermittlung bereit ist."

#: src\contributing.md:23
msgid "Mark the PR as ready to review."
msgstr "Markieren Sie die PR als zur Überprüfung bereit."

#: src\contributing.md:24
msgid "Revise the PR as needed."
msgstr "Überarbeiten Sie die PR nach Bedarf."

#: src\contributing.md:25
msgid "And finally, mergies!"
msgstr "Und schließlich: mergies!"

#: src\contributing.md:27
msgid "Start small"
msgstr "Fangen Sie klein an"

#: src\contributing.md:30
msgid ""
"Small changes will allow you to make an impact quickly, and if you take the "
"wrong tack, you won't have wasted much time."
msgstr ""
"Mit kleinen Änderungen können Sie schnell Wirkung erzielen, und wenn Sie den "
"falschen Weg einschlagen, haben Sie nicht viel Zeit verschwendet."

#: src\contributing.md:33
msgid "Ideas for small issues:"
msgstr "Ideen für kleine Probleme:"

#: src\contributing.md:34
msgid "Add a new test or test case that increases test coverage"
msgstr ""
"Fügen Sie einen neuen Test oder Testfall hinzu, der die Testabdeckung erhöht"

#: src\contributing.md:35
msgid "Add or improve documentation"
msgstr "Dokumentation hinzufügen oder verbessern"

#: src\contributing.md:36
msgid ""
"Find an issue that needs more research, and do that research and summarize "
"it in a comment"
msgstr ""
"Finden Sie ein Problem, das mehr Recherche erfordert, führen Sie diese "
"Recherche durch und fassen Sie sie in einem Kommentar zusammen"

#: src\contributing.md:38
msgid "Find an out-of-date issue and comment that it can be closed"
msgstr ""
"Finden Sie ein veraltetes Problem und kommentieren Sie, dass es geschlossen "
"werden kann"

#: src\contributing.md:39
msgid ""
"Find an issue that shouldn't be done, and provide constructive feedback "
"detailing why you think that is the case"
msgstr ""
"Finden Sie ein Problem, das nicht behoben werden sollte, und geben Sie "
"konstruktives Feedback, in dem Sie darlegen, warum dies Ihrer Meinung nach "
"der Fall ist"

#: src\contributing.md:42
msgid "Merge early and often"
msgstr "Mergen Sie frühzeitig und häufig"

#: src\contributing.md:45
msgid ""
"Break up large tasks into multiple smaller steps that individually make "
"progress. If there's a bug, you can open a PR that adds a failing ignored "
"test. This can be merged, and the next step can be to fix the bug and "
"unignore the test. Do research or testing, and report on your results. Break "
"a feature into small sub-features, and implement them one at a time."
msgstr ""
"Teilen Sie große Aufgaben in mehrere kleinere Schritte auf, die jeweils "
"Fortschritte machen. Wenn es einen Fehler gibt, können Sie eine Pull-Anfrage "
"(PR) öffnen, die einen fehlgeschlagenen, ignorierten Test hinzufügt. Diese "
"PR kann zusammengeführt werden, und der nächste Schritt könnte darin "
"bestehen, den Fehler zu beheben und den Test nicht mehr zu ignorieren. "
"Führen Sie umfangreiche Recherchen oder Tests durch und berichten Sie über "
"Ihre Ergebnisse. Zerlegen Sie ein Feature in kleine Unterfeatures und "
"implementieren Sie sie nacheinander."

#: src\contributing.md:51
msgid ""
"Figuring out how to break down a larger PR into smaller PRs where each can "
"be merged is an art form well-worth practicing. The hard part is that each "
"PR must itself be an improvement."
msgstr ""
"Herauszufinden, wie man eine größere PR in kleinere PRs aufteilt, die "
"jeweils zusammengeführt werden können, ist eine Kunstform, die es wert ist, "
"geübt zu werden. Das Schwierige daran ist, dass jede PR selbst eine "
"Verbesserung darstellen muss."

#: src\contributing.md:55
msgid ""
"I strive to follow this advice myself, and am always better off when I do."
msgstr ""
"Ich bemühe mich, diesen Rat selbst zu befolgen, und es geht mir immer "
"besser, wenn ich das tue."

#: src\contributing.md:57
msgid ""
"Small changes are fast to write, review, and merge, which is much more fun "
"than laboring over a single giant PR that takes forever to write, review, "
"and merge. Small changes don't take much time, so if you need to stop "
"working on a small change, you won't have wasted much time as compared to a "
"larger change that represents many hours of work. Getting a PR in quickly "
"improves the project a little bit immediately, instead of having to wait a "
"long time for larger improvement. Small changes are less likely to "
"accumulate merge conflict. As the Athenians said: _The fast commit what they "
"will, the slow merge what they must._"
msgstr ""
"Kleine Änderungen lassen sich schnell schreiben, überprüfen und "
"zusammenführen, was viel mehr Spaß macht als an einer einzigen riesigen Pull-"
"Anfrage (PR) zu arbeiten, die ewig dauert, um geschrieben, überprüft und "
"zusammengeführt zu werden. Kleine Änderungen benötigen nicht viel Zeit, "
"sodass Sie bei Bedarf aufhören können, an einer kleinen Änderung zu "
"arbeiten, ohne viel Zeit zu verschwenden, im Vergleich zu einer größeren "
"Änderung, die viele Stunden Arbeit repräsentiert. Wenn eine PR schnell "
"eingereicht wird, verbessert dies das Projekt sofort ein wenig, anstatt "
"lange auf größere Verbesserungen warten zu müssen. Kleine Änderungen sind "
"weniger wahrscheinlich, Merge-Konflikte zu verursachen. Wie die Athener "
"sagten: _Die Schnellen commit , was sie wollen, die Langsamen merge was sie "
"müssen._"

#: src\contributing.md:67
msgid "Get help"
msgstr "Hilfe bekommen"

#: src\contributing.md:70
msgid ""
"If you're stuck for more than 15 minutes, ask for help, like a Rust Discord, "
"Stack Exchange, or in a project issue or discussion."
msgstr ""
"Wenn Sie länger als 15 Minuten nicht weiterkommen, bitten Sie um Hilfe, z. "
"B. bei Rust Discord, Stack Exchange oder bei einem Projektproblem oder einer "
"Diskussion."

#: src\contributing.md:73
msgid "Practice hypothesis-driven debugging"
msgstr "Üben Sie das hypothesis-driven debugging"

#: src\contributing.md:76
msgid ""
"Formulate a hypothesis as to what is causing the problem. Figure out how to "
"test that hypothesis. Perform that tests. If it works, great, you fixed the "
"issue or now you know how to fix the issue. If not, repeat with a new "
"hypothesis."
msgstr ""
"Formulieren Sie eine Hypothese darüber, was das Problem verursacht. "
"Überlegen Sie, wie Sie diese Hypothese testen können. Führen Sie diese Tests "
"durch. Wenn es funktioniert, großartig, Sie haben das Problem behoben oder "
"wissen jetzt, wie Sie es beheben können. Wenn nicht, wiederholen Sie den "
"Vorgang mit einer neuen Hypothese."

#: src\contributing.md:81
msgid "Pay attention to error messages"
msgstr "Achten Sie auf Fehlermeldungen"

#: src\contributing.md:84
msgid "Read all error messages and don't tolerate warnings."
msgstr "Lesen Sie alle Fehlermeldungen und dulden Sie keine Warnungen."

#: src\donate.md:4
msgid ""
"Ordinals is open-source and community funded. The current lead maintainer of "
"`ord` is [raphjaph](https://github.com/raphjaph/). Raph's work on `ord` is "
"entirely funded by donations. If you can, please consider donating!"
msgstr ""
"Ordinals ist Open Source und wird von die community finanziert. Der aktuelle "
"Hauptbetreuer von \"ord\" ist [raphjaph](https://github.com/raphjaph/). "
"Raphs Arbeit an \"ord\" wird ausschließlich durch Spenden finanziert. Wenn "
"möglich, erwägen Sie bitte eine Spende!"

#: src\donate.md:8
msgid ""
"The donation address for Bitcoin is "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************). The "
"donation address for inscriptions is "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************)."
msgstr ""
"Die Spendenadresse für Bitcoin lautet "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************). Die "
"Spendenadresse für inscriptions lautet "
"[**************************************************************](https://"
"mempool.space/address/"
"**************************************************************)."

#: src\donate.md:11
msgid ""
"Both addresses are in a 2 of 4 multisig wallet with keys held by [raphjaph]"
"(https://twitter.com/raphjaph), [erin](https://twitter.com/realizingerin), "
"[rodarmor](https://twitter.com/rodarmor), and [ordinally](https://twitter."
"com/veryordinally)."
msgstr ""
"Beide Adressen befinden sich in einer 2-aus-4 Multisig-Wallet, bei der die "
"Schlüssel von [raphjaph](https://twitter.com/raphjaph), [erin](https://"
"twitter.com/realizingerin), [rodarmor](https://twitter.com/rodarmor) und "
"[ordinally](https://twitter.com/veryordinally) gehalten werden."

#: src\donate.md:17
msgid ""
"Donations received will go towards funding maintenance and development of "
"`ord`, as well as hosting costs for [ordinals.com](https://ordinals.com)."
msgstr ""
"Die erhaltenen Spenden werden für die Finanzierung der Wartung und "
"Entwicklung von `ord` sowie für die Hosting-Kosten von [ordinals.com]"
"(https://ordinals.com) verwendet."

#: src\donate.md:20
msgid "Thank you for donating!"
msgstr "Vielen Dank für Ihre Spende!"

#: src\guides.md:1
msgid "Ordinal Theory Guides"
msgstr "Ordinal Theorie Anleitungen"

#: src\guides.md:4
msgid ""
"See the table of contents for a list of guides, including a guide to the "
"explorer, a guide for sat hunters, and a guide to inscriptions."
msgstr ""
"Sehen Sie das Inhaltsverzeichnis für eine Liste von Anleitungen, "
"einschließlich einer Anleitung zum Explorer, einer Anleitung für "
"Satoshijäger und einer Anleitung zu inscriptions."

#: src\guides/explorer.md:1
msgid "Ordinal Explorer"
msgstr "Ordinal Explorer"

#: src\guides/explorer.md:4
msgid ""
"The `ord` binary includes a block explorer. We host a instance of the block "
"explorer on mainnet at [ordinals.com](https://ordinals.com), and on signet "
"at [signet.ordinals.com](https://signet.ordinals.com)."
msgstr ""
"Die `ord`-Binärdatei enthält einen Block-Explorer. Wir hosten eine Instanz "
"des Block-Explorers auf Mainnet unter [ordinals.com](https://ordinals.com) "
"und auf Signet unter [signet.ordinals.com](https://signet.ordinals.com)."

#: src\guides/explorer.md:8
msgid "Running The Explorer"
msgstr "Ausführen des Explorers"

#: src\guides/explorer.md:9
msgid "The server can be run locally with:"
msgstr "Der Server kann lokal betrieben werden mit:"

#: src\guides/explorer.md:11
msgid "`ord server`"
msgstr "`ord server`"

#: src\guides/explorer.md:13
msgid "To specify a port add the `--http-port` flag:"
msgstr "Um einen Port anzugeben, fügen Sie Folgendes `--http-port` flag:"

#: src\guides/explorer.md:15
msgid "`ord server --http-port 8080`"
msgstr "`ord server --http-port 8080`"

#: src\guides/explorer.md:17
msgid "To test how your inscriptions will look you can run:"
msgstr ""
"Um zu testen, wie Ihre inscription aussehen werden, können Sie Folgendes "
"ausführen:"

#: src\guides/explorer.md:19
msgid "`ord preview <FILE1> <FILE2> ...`"
msgstr "`ord preview <FILE1> <FILE2> ...`"

#: src\guides/explorer.md:21
msgid "Search"
msgstr "Suchen"

#: src\guides/explorer.md:24
msgid "The search box accepts a variety of object representations."
msgstr "Das Suchfeld akzeptiert eine Vielzahl von Objektdarstellungen."

#: src\guides/explorer.md:26
msgid "Blocks"
msgstr "Blöcke"

#: src\guides/explorer.md:28
msgid "Blocks can be searched by hash, for example, the genesis block:"
msgstr ""
"Blöcke können per Hash durchsucht werden, zum Beispiel der Genesis-Block:"

#: src\guides/explorer.md:30
msgid ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://"
"ordinals.com/"
"search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"
msgstr ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://"
"ordinals.com/"
"search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"

#: src\guides/explorer.md:32
msgid "Transactions"
msgstr "Transaktionen"

#: src\guides/explorer.md:34
msgid ""
"Transactions can be searched by hash, for example, the genesis block "
"coinbase transaction:"
msgstr ""
"Transaktionen können nach Hash durchsucht werden, zum Beispiel die Genesis "
"Block Coinbase Transaktion:"

#: src\guides/explorer.md:37
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"

#: src\guides/explorer.md:39
msgid "Outputs"
msgstr "Outputs"

#: src\guides/explorer.md:41
msgid ""
"Transaction outputs can searched by outpoint, for example, the only output "
"of the genesis block coinbase transaction:"
msgstr ""
"Transaktions outputs können nach Outpoint durchsucht werden, zum Beispiel "
"die einzige outputs der Genesis-Block-Coinbase-Transaktion:"

#: src\guides/explorer.md:44
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://"
"ordinals.com/"
"search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"

#: src\guides/explorer.md:46
msgid "Sats"
msgstr "Sats"

#: src\guides/explorer.md:48
msgid ""
"Sats can be searched by integer, their position within the entire bitcoin "
"supply:"
msgstr ""
"Sats können nach ihrer Ganzzahl, ihrer Position im gesamten Bitcoin-Vorrat, "
"gesucht werden:"

#: src\guides/explorer.md:51
msgid "[2099994106992659](https://ordinals.com/search/2099994106992659)"
msgstr "[2099994106992659](https://ordinals.com/search/2099994106992659)"

#: src\guides/explorer.md:53
msgid "By decimal, their block and offset within that block:"
msgstr ""
"Nach Dezimalstellen, ihrem Block und der Position innerhalb dieses Blocks:"

#: src\guides/explorer.md:55
msgid "[481824.0](https://ordinals.com/search/481824.0)"
msgstr "[481824.0](https://ordinals.com/search/481824.0)"

#: src\guides/explorer.md:57
msgid ""
"By degree, their cycle, blocks since the last halving, blocks since the last "
"difficulty adjustment, and offset within their block:"
msgstr ""
"Nach Grad, ihrem Zyklus, den Blöcken seit der letzten Halbierung, den "
"Blöcken seit der letzten Schwierigkeitsanpassung und der Position in ihrem "
"Block:"

#: src\guides/explorer.md:60
msgid "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"
msgstr "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"

#: src\guides/explorer.md:62
msgid ""
"By name, their base 26 representation using the letters \"a\" through \"z\":"
msgstr ""
"Nach Namen, ihrer Basis-26-Darstellung mit den Buchstaben \"a\" bis \"z\":"

#: src\guides/explorer.md:64
msgid "[ahistorical](https://ordinals.com/search/ahistorical)"
msgstr "[ahistorical](https://ordinals.com/search/ahistorical)"

#: src\guides/explorer.md:66
msgid ""
"Or by percentile, the percentage of bitcoin's supply that has been or will "
"have been issued when they are mined:"
msgstr ""
"Oder nach Prozentil, dem Prozentsatz des Bitcoin-Angebots, das bereits "
"emittiert wurde oder emittiert wird, wenn sie abgebaut werden:"

#: src\guides/explorer.md:69
msgid "[100%](https://ordinals.com/search/100%)"
msgstr "[100%](https://ordinals.com/search/100%)"

#: src\guides/inscriptions.md:1
msgid "Ordinal Inscription Guide"
msgstr "Ordinal Inscription Anleitungen"

#: src\guides/inscriptions.md:4
msgid ""
"Individual sats can be inscribed with arbitrary content, creating Bitcoin-"
"native digital artifacts that can be held in a Bitcoin wallet and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"Einzelsats können mit beliebigem Inhalt beschriftet werden, wodurch Bitcoin-"
"native digitale Artefakte entstehen, die in einer Bitcoin-Brieftasche "
"aufbewahrt und mit Bitcoin-Transaktionen übertragen werden können. "
"Inscriptions sind genauso langlebig, unveränderlich, sicher und "
"dezentralisiert wie Bitcoin selbst."

#: src\guides/inscriptions.md:9
msgid ""
"Working with inscriptions requires a Bitcoin full node, to give you a view "
"of the current state of the Bitcoin blockchain, and a wallet that can create "
"inscriptions and perform sat control when constructing transactions to send "
"inscriptions to another wallet."
msgstr ""
"Die Arbeit mit inscriptions erfordert einen Bitcoin-Full-Node, um Ihnen eine "
"Ansicht des aktuellen Zustands der Bitcoin-Blockchain zu geben, sowie eine "
"Brieftasche, die inscription erstellen und Satoshis kontrollieren kann, wenn "
"Transaktionen erstellt werden, um Inschriften an eine andere Brieftasche zu "
"senden."

#: src\guides/inscriptions.md:14
msgid ""
"Bitcoin Core provides both a Bitcoin full node and wallet. However, the "
"Bitcoin Core wallet cannot create inscriptions and does not perform sat "
"control."
msgstr ""
"Bitcoin Core bietet sowohl einen Bitcoin-Full-Node als auch eine "
"Brieftasche. Die Bitcoin Core-Brieftasche kann jedoch keine inscriptions "
"erstellen und führt keine Satoshis-Kontrolle durch."

#: src\guides/inscriptions.md:17
msgid ""
"This requires [`ord`](https://github.com/ordinals/ord), the ordinal utility. "
"`ord` doesn't implement its own wallet, so `ord wallet` subcommands interact "
"with Bitcoin Core wallets."
msgstr ""
"Dies erfordert [`ord`](https://github.com/ordinals/ord), das Ordinal-"
"utility. `ord` implementiert keine eigene Brieftasche, daher interagieren "
"`ord wallet`-Befehle mit Bitcoin Core-Brieftaschen."

#: src\guides/inscriptions.md:21
msgid "This guide covers:"
msgstr "Dieser Anleitungen behandelt:"

#: src\guides/inscriptions.md:23 src\guides/inscriptions.md:39
msgid "Installing Bitcoin Core"
msgstr "Bitcoin Core installieren"

#: src\guides/inscriptions.md:24
msgid "Syncing the Bitcoin blockchain"
msgstr "Synchronisierung der Bitcoin-Blockchain"

#: src\guides/inscriptions.md:25
msgid "Creating a Bitcoin Core wallet"
msgstr "Erstellen einer Bitcoin Core-Wallet"

#: src\guides/inscriptions.md:26
msgid "Using `ord wallet receive` to receive sats"
msgstr "Verwenden Sie `ord wallet receive` , um sats zu empfangen"

#: src\guides/inscriptions.md:27
msgid "Creating inscriptions with `ord wallet inscribe`"
msgstr "Inscriptions erstellen mit `ord wallet inscribe`"

#: src\guides/inscriptions.md:28
msgid "Sending inscriptions with `ord wallet send`"
msgstr "Versenden von inscriptions mit `ord wallet send`"

#: src\guides/inscriptions.md:29
msgid "Receiving inscriptions with `ord wallet receive`"
msgstr "Empfangen von inscriptions mit `ord wallet receive`"

#: src\guides/inscriptions.md:31
msgid "Getting Help"
msgstr "Hilfe bekommen"

#: src\guides/inscriptions.md:34
msgid ""
"If you get stuck, try asking for help on the [Ordinals Discord Server]"
"(https://discord.com/invite/87cjuz4FYg), or checking GitHub for relevant "
"[issues](https://github.com/ordinals/ord/issues) and [discussions](https://"
"github.com/ordinals/ord/discussions)."
msgstr ""
"Wenn Sie nicht weiterkommen, versuchen Sie, um Hilfe auf dem [Ordinals "
"Discord Server](https://discord.com/invite/87cjuz4FYg) zu fragen, oder "
"überprüfen Sie GitHub nach relevanten [Issues](https://github.com/ordinals/"
"ord/issues) und [Diskussionen](https://github.com/ordinals/ord/discussions)."

#: src\guides/inscriptions.md:42
msgid ""
"Bitcoin Core is available from [bitcoincore.org](https://bitcoincore.org/) "
"on the [download page](https://bitcoincore.org/en/download/)."
msgstr ""
"Bitcoin Core ist auf [bitcoincore.org](https://bitcoincore.org/) auf der "
"[Download-Seite](https://bitcoincore.org/en/download/) verfügbar."

#: src\guides/inscriptions.md:45
msgid "Making inscriptions requires Bitcoin Core 24 or newer."
msgstr ""
"Für die Erstellung von inscriptions ist Bitcoin Core 24 oder neuer "
"erforderlich."

#: src\guides/inscriptions.md:47
msgid ""
"This guide does not cover installing Bitcoin Core in detail. Once Bitcoin "
"Core is installed, you should be able to run `bitcoind -version` "
"successfully from the command line."
msgstr ""
"Dieser Leitfaden behandelt die Installation von Bitcoin Core nicht im "
"Detail. Sobald Bitcoin Core installiert ist, sollten Sie in der Lage sein, "
"`bitcoind -version` erfolgreich von der Befehlszeile auszuführen."

#: src\guides/inscriptions.md:51
msgid "Configuring Bitcoin Core"
msgstr "Bitcoin Core konfigurieren"

#: src\guides/inscriptions.md:54
msgid "`ord` requires Bitcoin Core's transaction index."
msgstr "`ord` erfordert den Transaktionsindex von Bitcoin Core."

#: src\guides/inscriptions.md:56
msgid ""
"To configure your Bitcoin Core node to maintain a transaction index, add the "
"following to your `bitcoin.conf`:"
msgstr ""
"Um Ihren Bitcoin Core-Node so zu konfigurieren, dass er einen "
"Transaktionsindex pflegt, fügen Sie das folgende zu Ihrer `bitcoin.conf` "
"hinzu:"

#: src\guides/inscriptions.md:59 src\guides/sat-hunting.md:30
msgid ""
"```\n"
"txindex=1\n"
"```"
msgstr ""
"```\n"
"txindex=1\n"
"```"

#: src\guides/inscriptions.md:63
msgid "Or, run `bitcoind` with `-txindex`:"
msgstr "Oder führen Sie `bitcoind` mit `-txindex`:"

#: src\guides/inscriptions.md:65 src\guides/inscriptions.md:74
msgid ""
"```\n"
"bitcoind -txindex\n"
"```"
msgstr ""
"```\n"
"bitcoind -txindex\n"
"```"

#: src\guides/inscriptions.md:69
msgid "Syncing the Bitcoin Blockchain"
msgstr "Synchronisierung der Bitcoin-Blockchain"

#: src\guides/inscriptions.md:72
msgid "To sync the chain, run:"
msgstr "Um die chain zu synchronisieren, führen Sie Folgendes aus:"

#: src\guides/inscriptions.md:78
msgid "…and leave it running until `getblockcount`:"
msgstr "…und lassen Sie es laufen, bis `getblockcount`:"

#: src\guides/inscriptions.md:80
msgid ""
"```\n"
"bitcoin-cli getblockcount\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli getblockcount\n"
"```"

#: src\guides/inscriptions.md:84
msgid ""
"agrees with the block count on a block explorer like [the mempool.space "
"block explorer](https://mempool.space/). `ord` interacts with `bitcoind`, so "
"you should leave `bitcoind` running in the background when you're using "
"`ord`."
msgstr ""
"Es stimmt mit der Blockanzahl auf einem Block-Explorer wie [dem mempool."
"space Block Explorer](https://mempool.space/) überein. `ord` interagiert mit "
"`bitcoind`, daher sollten Sie `bitcoind` im Hintergrund laufen lassen, wenn "
"Sie `ord` verwenden."

#: src\guides/inscriptions.md:88
msgid "Installing `ord`"
msgstr "Installieren `ord`"

#: src\guides/inscriptions.md:91
msgid ""
"The `ord` utility is written in Rust and can be built from [source](https://"
"github.com/ordinals/ord). Pre-built binaries are available on the [releases "
"page](https://github.com/ordinals/ord/releases)."
msgstr ""
"Das Dienstprogramm \"ord\" ist in Rust geschrieben und kann aus [Quelle]"
"(https://github.com/ordinals/ord) erstellt werden. Vorgefertigte "
"Binärdateien sind auf der [Releases-Seite](https://github.com/ordinals/ord/"
"releases) verfügbar."

#: src\guides/inscriptions.md:95
msgid "You can install the latest pre-built binary from the command line with:"
msgstr ""
"Sie können die neueste vorgefertigte Binärdatei über die Befehlszeile "
"installieren mit:"

#: src\guides/inscriptions.md:97
msgid ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"
msgstr ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"

#: src\guides/inscriptions.md:101
msgid "Once `ord` is installed, you should be able to run:"
msgstr "Sobald `ord`  installiert ist, sollten Sie Folgendes ausführen können:"

#: src\guides/inscriptions.md:103
msgid ""
"```\n"
"ord --version\n"
"```"
msgstr ""
"```\n"
"ord --version\n"
"```"

#: src\guides/inscriptions.md:107
msgid "Which prints out `ord`'s version number."
msgstr "Dadurch wird die Versionsnummer von `ord` ausgedruckt."

#: src\guides/inscriptions.md:109
msgid "Creating a Bitcoin Core Wallet"
msgstr "Erstellen einer Bitcoin Core Wallet"

#: src\guides/inscriptions.md:112
msgid ""
"`ord` uses Bitcoin Core to manage private keys, sign transactions, and "
"broadcast transactions to the Bitcoin network."
msgstr ""
"`ord` verwendet Bitcoin Core, um private Schlüssel zu verwalten, "
"Transaktionen zu signieren und Transaktionen an das Bitcoin-Netzwerk zu "
"senden."

#: src\guides/inscriptions.md:115
msgid "To create a Bitcoin Core wallet named `ord` for use with `ord`, run:"
msgstr ""
"Um eine Bitcoin Core-Wallet mit dem Namen `ord` zur Verwendung mit `ord` zu "
"erstellen, führen Sie Folgendes aus:"

#: src\guides/inscriptions.md:117
msgid ""
"```\n"
"ord wallet create\n"
"```"
msgstr ""
"```\n"
"ord wallet create\n"
"```"

#: src\guides/inscriptions.md:121
msgid "Receiving Sats"
msgstr "Sats empfangen"

#: src\guides/inscriptions.md:124
msgid ""
"Inscriptions are made on individual sats, using normal Bitcoin transactions "
"that pay fees in sats, so your wallet will need some sats."
msgstr ""
"Inscriptions werden auf einzelnen Sats erstellt, indem normale Bitcoin-"
"Transaktionen verwendet werden, die Gebühren in Sats zahlen. Daher benötigt "
"Ihre Wallet einige Sats."

#: src\guides/inscriptions.md:127
msgid "Get a new address from your `ord` wallet by running:"
msgstr ""
"Holen Sie sich eine neue Adresse aus Ihrem `ord` Wallet, indem Sie Folgendes "
"ausführen:"

#: src\guides/inscriptions.md:129 src\guides/inscriptions.md:201
#: src\guides/inscriptions.md:229
msgid ""
"```\n"
"ord wallet receive\n"
"```"
msgstr ""
"```\n"
"ord wallet receive\n"
"```"

#: src\guides/inscriptions.md:133
msgid "And send it some funds."
msgstr "Und schick ihm etwas Geld."

#: src\guides/inscriptions.md:135
msgid "You can see pending transactions with:"
msgstr "Ausstehende Transaktionen können Sie mit einsehen:"

#: src\guides/inscriptions.md:137 src\guides/inscriptions.md:213
#: src\guides/inscriptions.md:240
msgid ""
"```\n"
"ord wallet transactions\n"
"```"
msgstr ""
"```\n"
"ord wallet transactions\n"
"```"

#: src\guides/inscriptions.md:141
msgid ""
"Once the transaction confirms, you should be able to see the transactions "
"outputs with `ord wallet outputs`."
msgstr ""
"Sobald die Transaktion bestätigt ist, sollten Sie die Transaktionsausgaben "
"mit `ord wallet outputs` sehen können."

#: src\guides/inscriptions.md:144
msgid "Creating Inscription Content"
msgstr "Inscription inhalte erstellen"

#: src\guides/inscriptions.md:147
msgid ""
"Sats can be inscribed with any kind of content, but the `ord` wallet only "
"supports content types that can be displayed by the `ord` block explorer."
msgstr ""
"Sats können mit beliebigen Inhalten versehen werden, aber die `ord` Wallet "
"unterstützt nur Content-Typen, die vom `ord` Block-Explorer angezeigt werden "
"können."

#: src\guides/inscriptions.md:150
msgid ""
"Additionally, inscriptions are included in transactions, so the larger the "
"content, the higher the fee that the inscription transaction must pay."
msgstr ""
"Zusätzlich werden inscriptions in Transaktionen aufgenommen, daher muss je "
"nach Größe des Inhalts auch eine höhere Gebühr für die inscriptions "
"transaktion gezahlt werden."

#: src\guides/inscriptions.md:153
msgid ""
"Inscription content is included in transaction witnesses, which receive the "
"witness discount. To calculate the approximate fee that an inscribe "
"transaction will pay, divide the content size by four and multiply by the "
"fee rate."
msgstr ""
"Der Inhalt der Inscription wird in Transaktionszeugnissen aufgenommen, die "
"den Zeugnisrabatt erhalten. Um die ungefähre Gebühr für eine Inscription "
"transaktion zu berechnen, teilen Sie die Größe des Inhalts durch vier und "
"multiplizieren Sie sie mit dem Gebührensatz."

#: src\guides/inscriptions.md:157
msgid ""
"Inscription transactions must be less than 400,000 weight units, or they "
"will not be relayed by Bitcoin Core. One byte of inscription content costs "
"one weight unit. Since an inscription transaction includes not just the "
"inscription content, limit inscription content to less than 400,000 weight "
"units. 390,000 weight units should be safe."
msgstr ""
"Inscription transaktion müssen weniger als 400.000 Gewichtseinheiten "
"betragen, da sie ansonsten von Bitcoin Core nicht weitergeleitet werden. Ein "
"Byte inscription inhalt kostet eine Gewichtseinheit. Da eine inscription "
"transaktion nicht nur den inscription inhalt enthält, sollte der inscription "
"inhalt auf weniger als 400.000 Gewichtseinheiten begrenzt werden. 390.000 "
"Gewichtseinheiten sollten sicher sein."

#: src\guides/inscriptions.md:163
msgid "Creating Inscriptions"
msgstr "Inscription erstellen"

#: src\guides/inscriptions.md:166
msgid "To create an inscription with the contents of `FILE`, run:"
msgstr "Um eine Inschrift mit dem Inhalt von `FILE` zu erstellen, führen:"

#: src\guides/inscriptions.md:168
msgid ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE --file FILE\n"
"```"
msgstr ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE --file FILE\n"
"```"

#: src\guides/inscriptions.md:172
msgid ""
"Ord will output two transactions IDs, one for the commit transaction, and "
"one for the reveal transaction, and the inscription ID. Inscription IDs are "
"of the form `TXIDiN`, where `TXID` is the transaction ID of the reveal "
"transaction, and `N` is the index of the inscription in the reveal "
"transaction."
msgstr ""
"Ord gibt zwei Transaktions-IDs aus, eine für die Commit-Transaktion und eine "
"für die Reveal-Transaktion, sowie die inscription -ID. inscription -IDs "
"haben das Format `TXIDiN`, wobei `TXID` die Transaktions-ID der Reveal-"
"Transaktion ist und `N` der Index der inscription in der Reveal-Transaktion "
"ist."

#: src\guides/inscriptions.md:177
msgid ""
"The commit transaction commits to a tapscript containing the content of the "
"inscription, and the reveal transaction spends from that tapscript, "
"revealing the content on chain and inscribing it on the first sat of the "
"input that contains the corresponding tapscript."
msgstr ""
"Die Commit-Transaktion verpflichtet sich zu einem Tapscript, das den Inhalt "
"der inscription enthält, und die Reveal-Transaktion gibt von diesem "
"Tapscript aus und enthüllt den Inhalt auf der Blockchain, indem sie ihn auf "
"die erste Satoshis des Inputs schreibt, der das entsprechende Tapscript "
"enthält."

#: src\guides/inscriptions.md:182
msgid ""
"Wait for the reveal transaction to be mined. You can check the status of the "
"commit and reveal transactions using  [the mempool.space block explorer]"
"(https://mempool.space/)."
msgstr ""
"Warten Sie, bis die Reveal-Transaktion gemined wurde. Sie können den Status "
"der Commit- und Reveal-Transaktionen mit dem [Block Explorer von mempool."
"space](https://mempool.space/) überprüfen."

#: src\guides/inscriptions.md:186
msgid ""
"Once the reveal transaction has been mined, the inscription ID should be "
"printed when you run:"
msgstr ""
"Sobald die Reveal-Transaktion gemined wurde, sollte die Inscription-ID "
"angezeigt werden, wenn Sie Folgendes ausführen:"

#: src\guides/inscriptions.md:189 src\guides/inscriptions.md:220
#: src\guides/inscriptions.md:246
msgid ""
"```\n"
"ord wallet inscriptions\n"
"```"
msgstr ""
"```\n"
"ord wallet inscriptions\n"
"```"

#: src\guides/inscriptions.md:193
msgid ""
"And when you visit [the ordinals explorer](https://ordinals.com/) at "
"`ordinals.com/inscription/INSCRIPTION_ID`."
msgstr ""
"Und wenn Sie [the ordinals explorer](https://ordinals.com/) at `ordinals.com/"
"inscription/INSCRIPTION_ID` besuchen."

#: src\guides/inscriptions.md:196
msgid "Sending Inscriptions"
msgstr "Inscriptions Senden"

#: src\guides/inscriptions.md:199
msgid "Ask the recipient to generate a new address by running:"
msgstr ""
"Bitten Sie den Empfänger, eine neue Adresse zu generieren, indem Sie "
"Folgendes ausführen:"

#: src\guides/inscriptions.md:205
msgid "Send the inscription by running:"
msgstr "Senden Sie die inscription, indem Sie Folgendes ausführen:"

#: src\guides/inscriptions.md:207
msgid ""
"```\n"
"ord wallet send --fee-rate <FEE_RATE> <ADDRESS> <INSCRIPTION_ID>\n"
"```"
msgstr ""
"```\n"
"ord wallet send --fee-rate <FEE_RATE> <ADDRESS> <INSCRIPTION_ID>\n"
"```"

#: src\guides/inscriptions.md:211 src\guides/inscriptions.md:239
msgid "See the pending transaction with:"
msgstr "Sehen Sie sich die ausstehende Transaktion an mit:"

#: src\guides/inscriptions.md:217
msgid ""
"Once the send transaction confirms, the recipient can confirm receipt by "
"running:"
msgstr ""
"Sobald die Sendetransaktion bestätigt ist, kann der Empfänger den Empfang "
"bestätigen, indem er Folgendes ausführt:"

#: src\guides/inscriptions.md:224
msgid "Receiving Inscriptions"
msgstr "Empfang von Inscriptions"

#: src\guides/inscriptions.md:227
msgid "Generate a new receive address using:"
msgstr "Erzeugen Sie eine neue Empfangsadresse mit:"

#: src\guides/inscriptions.md:233
msgid "The sender can transfer the inscription to your address using:"
msgstr "Der Absender kann die inscriptions an Ihre Adresse übertragen mit:"

#: src\guides/inscriptions.md:235
msgid ""
"```\n"
"ord wallet send ADDRESS INSCRIPTION_ID\n"
"```"
msgstr ""
"```\n"
"ord wallet send ADDRESS INSCRIPTION_ID\n"
"```"

#: src\guides/inscriptions.md:244
msgid ""
"Once the send transaction confirms, you can can confirm receipt by running:"
msgstr ""
"Sobald die Send-Transaktion bestätigt ist, können Sie den Empfang "
"bestätigen, indem Sie Folgendes ausführen:"

#: src\guides/sat-hunting.md:4
msgid ""
"_This guide is out of date. Since it was written, the `ord` binary was "
"changed to only build the full satoshi index when the `--index-sats` flag is "
"supplied. Additionally, `ord` now has a built-in wallet that wraps a Bitcoin "
"Core wallet. See `ord wallet --help`._"
msgstr ""
"_Dieser Leitfaden ist veraltet. Seit seiner Erstellung wurde die `ord`-"
"Binärdatei geändert, um den vollständigen Satoshi-Index nur dann zu "
"erstellen, wenn die `--index-sats`-Flagge angegeben ist. Darüber hinaus "
"verfügt `ord` jetzt über eine integrierte Geldbörse, die eine Bitcoin Core-"
"Geldbörse umgibt. Sehen Sie sich `ord wallet --help` an._"

#: src\guides/sat-hunting.md:9
msgid ""
"Ordinal hunting is difficult but rewarding. The feeling of owning a wallet "
"full of UTXOs, redolent with the scent of rare and exotic sats, is beyond "
"compare."
msgstr ""
"Die Jagd auf Ordinals ist schwierig, aber lohnend. Das Gefühl, eine "
"Geldbörse voller UTXOs zu besitzen, die nach seltenen und exotischen "
"Satoshis duften, ist unvergleichlich."

#: src\guides/sat-hunting.md:12
msgid ""
"Ordinals are numbers for satoshis. Every satoshi has an ordinal number and "
"every ordinal number has a satoshi."
msgstr ""
"Ordinals sind Zahlen für Satoshis. Jeder Satoshi hat eine Ordnungszahl und "
"jede Ordnungszahl gehört zu einem Satoshi."

#: src\guides/sat-hunting.md:15
msgid "Preparation"
msgstr "Vorbereitung"

#: src\guides/sat-hunting.md:18
msgid "There are a few things you'll need before you start."
msgstr "Es gibt ein paar Dinge, die Sie benötigen, bevor Sie beginnen."

#: src\guides/sat-hunting.md:20
msgid ""
"First, you'll need a synced Bitcoin Core node with a transaction index. To "
"turn on transaction indexing, pass `-txindex` on the command-line:"
msgstr ""
"Zuerst benötigen Sie einen synchronisierten Bitcoin Core-Node mit einem "
"Transaktionsindex. Um die Transaktionsindexierung zu aktivieren, geben Sie `-"
"txindex` auf der Befehlszeile an:"

#: src\guides/sat-hunting.md:23
msgid ""
"```sh\n"
"bitcoind -txindex\n"
"```"
msgstr ""
"```sh\n"
"bitcoind -txindex\n"
"```"

#: src\guides/sat-hunting.md:27
msgid ""
"Or put the following in your [Bitcoin configuration file](https://github.com/"
"bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"
msgstr ""
"Oder fügen Sie Folgendes in Ihr Konto ein [Bitcoin configuration file]"
"(https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf."
"md#configuration-file-path):"

#: src\guides/sat-hunting.md:34
msgid ""
"Launch it and wait for it to catch up to the chain tip, at which point the "
"following command should print out the current block height:"
msgstr ""
"Starten Sie es und warten Sie, bis es zum aktuellen Blockspitze "
"aufgeschlossen hat. An diesem Punkt sollte der folgende Befehl die aktuelle "
"Blockhöhe ausgeben:"

#: src\guides/sat-hunting.md:37
msgid ""
"```sh\n"
"bitcoin-cli getblockcount\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getblockcount\n"
"```"

#: src\guides/sat-hunting.md:41
msgid "Second, you'll need a synced `ord` index."
msgstr "Zweitens benötigen Sie einen synchronisierten `ord` Index."

#: src\guides/sat-hunting.md:43
msgid "Get a copy of `ord` from [the repo](https://github.com/ordinals/ord/)."
msgstr ""
"Holen Sie sich eine Kopie von `ord` von [the repo](https://github.com/"
"ordinals/ord/)."

#: src\guides/sat-hunting.md:45
msgid ""
"Run `RUST_LOG=info ord index`. It should connect to your bitcoin core node "
"and start indexing."
msgstr ""
"Führen Sie `RUST_LOG=info ord index` aus. Es sollte eine Verbindung zu Ihrem "
"Bitcoin-Core-Knoten herstellen und mit der Indexierung beginnen."

#: src\guides/sat-hunting.md:48
msgid "Wait for it to finish indexing."
msgstr "Warten Sie, bis die Indizierung abgeschlossen ist."

#: src\guides/sat-hunting.md:50
msgid "Third, you'll need a wallet with UTXOs that you want to search."
msgstr ""
"Drittens benötigen Sie eine Wallet mit UTXOs, die Sie durchsuchen möchten."

#: src\guides/sat-hunting.md:52
msgid "Searching for Rare Ordinals"
msgstr "Suche nach seltenen Ordnungs"

#: src\guides/sat-hunting.md:55
msgid "Searching for Rare Ordinals in a Bitcoin Core Wallet"
msgstr "Suche nach seltenen Ordnungs in einer Bitcoin Core Wallet"

#: src\guides/sat-hunting.md:57
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"searching for rare ordinals in a Bitcoin Core wallet is Easy. Assuming your "
"wallet is named `foo`:"
msgstr ""
"Der befehl `ord wallet` ist nur eine Wrapper um Bitcoin Cores RPC-API, daher "
"ist die Suche nach seltenen ordinals in einer Bitcoin Core Wallet einfach. "
"Angenommen, Ihre Wallet heißt `foo`:"

#: src\guides/sat-hunting.md:61
msgid "Load your wallet:"
msgstr "Laden Sie Ihr Wallet:"

#: src\guides/sat-hunting.md:63
msgid ""
"```sh\n"
"bitcoin-cli loadwallet foo\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli loadwallet foo\n"
"```"

#: src\guides/sat-hunting.md:67
msgid "Display any rare ordinals wallet `foo`'s UTXOs:"
msgstr "Zeigt alle seltenen ordinal wallet-UTXOs von `foo`'s an:"

#: src\guides/sat-hunting.md:69 src\guides/sat-hunting.md:132
#: src\guides/sat-hunting.md:233
msgid ""
"```sh\n"
"ord wallet sats\n"
"```"
msgstr ""
"```sh\n"
"ord wallet sats\n"
"```"

#: src\guides/sat-hunting.md:73
msgid "Searching for Rare Ordinals in a Non-Bitcoin Core Wallet"
msgstr "Suche nach seltenen Ordnungs in einer Nicht-Bitcoin-Core-Wallet"

#: src\guides/sat-hunting.md:75
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"to search for rare ordinals in a non-Bitcoin Core wallet, you'll need to "
"import your wallet's descriptors into Bitcoin Core."
msgstr ""
"Der `ord wallet` Befehl ist nur eine Hülle um die RPC-API von Bitcoin Core. "
"Um nach seltenen Ordinals in einer Nicht-Bitcoin-Core-Wallet zu suchen, "
"müssen Sie die Deskriptoren Ihrer Wallet in Bitcoin Core importieren."

#: src\guides/sat-hunting.md:79
msgid ""
"[Descriptors](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors."
"md) describe the ways that wallets generate private keys and public keys."
msgstr ""
"[Descriptors](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors."
"md) describe the ways that wallets generate private keys and public keys."

#: src\guides/sat-hunting.md:82
msgid ""
"You should only import descriptors into Bitcoin Core for your wallet's "
"public keys, not its private keys."
msgstr ""
"Sie sollten nur Deskriptoren für die öffentlichen Schlüssel Ihrer Wallet in "
"Bitcoin Core importieren, nicht für die privaten Schlüssel."

#: src\guides/sat-hunting.md:85
msgid ""
"If your wallet's public key descriptor is compromised, an attacker will be "
"able to see your wallet's addresses, but your funds will be safe."
msgstr ""
"Wenn der öffentliche Schlüssel-Deskriptor Ihrer Wallet kompromittiert wird, "
"kann ein Angreifer die Adressen Ihrer Wallet sehen, aber Ihre Gelder werden "
"sicher sein."

#: src\guides/sat-hunting.md:88
msgid ""
"If your wallet's private key descriptor is compromised, an attacker can "
"drain your wallet of funds."
msgstr ""
"Wenn der Deskriptor des privaten Schlüssels Ihrer Wallet kompromittiert "
"wird, kann ein Angreifer Ihr Wallet entleeren."

#: src\guides/sat-hunting.md:91
msgid ""
"Get the wallet descriptor from the wallet whose UTXOs you want to search for "
"rare ordinals. It will look something like this:"
msgstr ""
"Holen Sie sich den Wallet-Deskriptor von der Wallet, deren UTXOs Sie nach "
"seltenen Ordinals durchsuchen möchten. Es wird ungefähr so aussehen:"

#: src\guides/sat-hunting.md:94
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\n"
"```"

#: src\guides/sat-hunting.md:98
msgid "Create a watch-only wallet named `foo-watch-only`:"
msgstr "Erstellen Sie eine reine Uhren-Wallet mit dem Namen `foo-watch-only`:"

#: src\guides/sat-hunting.md:100
msgid ""
"```sh\n"
"bitcoin-cli createwallet foo-watch-only true true\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli createwallet foo-watch-only true true\n"
"```"

#: src\guides/sat-hunting.md:104
msgid "Feel free to give it a better name than `foo-watch-only`!"
msgstr "Geben Sie ihm gerne einen besseren Namen als `foo-watch-only`!"

#: src\guides/sat-hunting.md:106
msgid "Load the `foo-watch-only` wallet:"
msgstr "Laden Sie die `foo-watch-only` Wallet:"

#: src\guides/sat-hunting.md:108 src\guides/sat-hunting.md:199
msgid ""
"```sh\n"
"bitcoin-cli loadwallet foo-watch-only\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli loadwallet foo-watch-only\n"
"```"

#: src\guides/sat-hunting.md:112
msgid "Import your wallet descriptors into `foo-watch-only`:"
msgstr "Importieren Sie Ihre Wallet-Deskriptoren in `foo-watch-only`:"

#: src\guides/sat-hunting.md:114
msgid ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\", \"timestamp\":0 }]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\", \"timestamp\":0 }]'\n"
"```"

#: src\guides/sat-hunting.md:119
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of `\"timestamp\"` instead of "
"`0`. This will reduce the time it takes for Bitcoin Core to search for your "
"wallet's UTXOs."
msgstr ""
"Wenn Sie den Unix-Zeitstempel kennen, zu dem Ihre Wallet zum ersten Mal "
"Transaktionen empfangen hat, können Sie diesen für den Wert von "
"`\"timestamp\"` verwenden, anstelle von `0`. Dadurch wird die Zeit "
"reduziert, die Bitcoin Core benötigt, um nach den UTXOs Ihrer Wallet zu "
"suchen."

#: src\guides/sat-hunting.md:124 src\guides/sat-hunting.md:225
msgid "Check that everything worked:"
msgstr "Überprüfen Sie, ob alles funktioniert hat:"

#: src\guides/sat-hunting.md:126 src\guides/sat-hunting.md:227
msgid ""
"```sh\n"
"bitcoin-cli getwalletinfo\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getwalletinfo\n"
"```"

#: src\guides/sat-hunting.md:130 src\guides/sat-hunting.md:231
msgid "Display your wallet's rare ordinals:"
msgstr "Zeigen Sie die seltenen Ordnungs Ihrer Brieftasche an:"

#: src\guides/sat-hunting.md:136
msgid ""
"Searching for Rare Ordinals in a Wallet that Exports Multi-path Descriptors"
msgstr ""
"Suchen nach seltenen Ordinals in einer Wallet, die Mehrweg-Deskriptoren "
"exportiert"

#: src\guides/sat-hunting.md:138
msgid ""
"Some descriptors describe multiple paths in one descriptor using angle "
"brackets, e.g., `<0;1>`. Multi-path descriptors are not yet supported by "
"Bitcoin Core, so you'll first need to convert them into multiple "
"descriptors, and then import those multiple descriptors into Bitcoin Core."
msgstr ""
"Einige Deskriptoren beschreiben mehrere Pfade in einem Deskriptor unter "
"Verwendung von spitzen Klammern, z.B. `<0;1>`. Multi-Pfad-Deskriptoren "
"werden von Bitcoin Core derzeit nicht unterstützt. Daher müssen Sie sie "
"zuerst in mehrere Deskriptoren umwandeln und diese dann in Bitcoin Core "
"importieren."

#: src\guides/sat-hunting.md:143
msgid ""
"First get the multi-path descriptor from your wallet. It will look something "
"like this:"
msgstr ""
"Holen Sie sich zunächst den Multipath-Deskriptor aus Ihrem Wallet. Es wird "
"ungefähr so aussehen:"

#: src\guides/sat-hunting.md:146
msgid ""
"```\n"
"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/"
"<0;1>/*)#fw76ulgt\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/"
"<0;1>/*)#fw76ulgt\n"
"```"

#: src\guides/sat-hunting.md:150
msgid "Create a descriptor for the receive address path:"
msgstr "Erstellen Sie einen Deskriptor für den Empfangsadress path:"

#: src\guides/sat-hunting.md:152
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)\n"
"```"

#: src\guides/sat-hunting.md:156
msgid "And the change address path:"
msgstr "Und der Adress path ändern:"

#: src\guides/sat-hunting.md:158
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)\n"
"```"

#: src\guides/sat-hunting.md:162
msgid ""
"Get and note the checksum for the receive address descriptor, in this case "
"`tpnxnxax`:"
msgstr ""
"Rufen Sie die Prüfsumme für den Empfangsadressdeskriptor ab und notieren Sie "
"sie, in diesem Fall `tpnxnxax`:"

#: src\guides/sat-hunting.md:165
msgid ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)'\n"
"```"

#: src\guides/sat-hunting.md:170
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src\guides/sat-hunting.md:180
msgid "And for the change address descriptor, in this case `64k8wnd7`:"
msgstr "Und für den Änderungsadressdeskriptor, in diesem Fall `64k8wnd7`:"

#: src\guides/sat-hunting.md:182
msgid ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)'\n"
"```"

#: src\guides/sat-hunting.md:187
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src\guides/sat-hunting.md:197
msgid "Load the wallet you want to import the descriptors into:"
msgstr "Laden Sie die Wallet, in die Sie die Deskriptoren importieren möchten:"

#: src\guides/sat-hunting.md:203
msgid ""
"Now import the descriptors, with the correct checksums, into Bitcoin Core."
msgstr ""
"Importieren Sie nun die Deskriptoren mit den richtigen Prüfsummen in Bitcoin "
"Core."

#: src\guides/sat-hunting.md:205
msgid ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/"
"*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/"
"*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"

#: src\guides/sat-hunting.md:220
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of the `\"timestamp\"` fields "
"instead of `0`. This will reduce the time it takes for Bitcoin Core to "
"search for your wallet's UTXOs."
msgstr ""
"Wenn Sie den Unix-Zeitstempel kennen, zu dem Ihre Wallet erstmals "
"Transaktionen empfangen hat, können Sie ihn anstelle von `0` für die Werte "
"der `\"timestamp\"`-Felder verwenden. Dadurch wird die Zeit verkürzt, die "
"Bitcoin Core benötigt, um nach den UTXOs Ihrer Wallet zu suchen."

#: src\guides/sat-hunting.md:237
msgid "Exporting Descriptors"
msgstr "Deskriptoren exportieren"

#: src\guides/sat-hunting.md:241
msgid ""
"Navigate to the `Settings` tab, then to `Script Policy`, and press the edit "
"button to display the descriptor."
msgstr ""
"Navigieren Sie zur Registerkarte `Einstellungen`, dann zu `Skriptrichtlinie` "
"und klicken Sie auf die Schaltfläche `Bearbeiten`, um den Deskriptor "
"anzuzeigen."

#: src\guides/sat-hunting.md:244
msgid "Transferring Ordinals"
msgstr "Ordinals übertragen"

#: src\guides/sat-hunting.md:246
msgid ""
"The `ord` wallet supports transferring specific satoshis. You can also use "
"`bitcoin-cli` commands `createrawtransaction`, "
"`signrawtransactionwithwallet`, and `sendrawtransaction`, how to do so is "
"complex and outside the scope of this guide."
msgstr ""
"Die `ord`-Wallet unterstützt die Übertragung bestimmter Satoshis. Sie können "
"auch `bitcoin-cli`-Befehle wie `createrawtransaction`, "
"`signrawtransactionwithwallet` und `sendrawtransaction` verwenden, wie dies "
"geschieht, ist komplex und fällt nicht in den Rahmen dieses Leitfadens."

#: src\guides/collecting.md:4
msgid ""
"Currently, [ord](https://github.com/ordinals/ord/) is the only wallet "
"supporting sat-control and sat-selection, which are required to safely store "
"and send rare sats and inscriptions, hereafter ordinals."
msgstr ""
"Derzeit ist [ord](https://github.com/ordinals/ord/) die einzige Wallet, die "
"Sat-Kontrolle und Sat-Auswahl unterstützt, die erforderlich sind, um seltene "
"Sats und inscriptions, im Folgenden Ordinals genannt, sicher zu speichern "
"und zu senden."

#: src\guides/collecting.md:8
msgid ""
"The recommended way to send, receive, and store ordinals is with `ord`, but "
"if you are careful, it is possible to safely store, and in some cases send, "
"ordinals with other wallets."
msgstr ""
"Die empfohlene Methode, um Ordinals zu senden, zu empfangen und zu "
"speichern, ist die Verwendung von `ord`. Es ist jedoch möglich, Ordinals "
"sicher mit anderen Wallets zu speichern und in einigen Fällen zu senden, "
"wenn Sie vorsichtig sind."

#: src\guides/collecting.md:12
msgid ""
"As a general note, receiving ordinals in an unsupported wallet is not "
"dangerous. Ordinals can be sent to any bitcoin address, and are safe as long "
"as the UTXO that contains them is not spent. However, if that wallet is then "
"used to send bitcoin, it may select the UTXO containing the ordinal as an "
"input, and send the inscription or spend it to fees."
msgstr ""
"Im Allgemeinen ist das Empfangen von Ordinals in einer nicht unterstützten "
"Wallet nicht gefährlich. Ordinals können an jede Bitcoin-Adresse gesendet "
"werden und sind sicher, solange die UTXO, die sie enthält, nicht ausgegeben "
"wird. Allerdings könnte, wenn Sie diese Wallet später verwenden, um Bitcoin "
"zu senden, die Wallet die UTXO auswählen, die den Ordinal enthält, als "
"Eingabe verwenden und die Inschrift oder Gebühren damit ausgeben."

#: src\guides/collecting.md:18
msgid ""
"A [guide](./collecting/sparrow-wallet.md) to creating an `ord`\\-compatible "
"wallet with [Sparrow Wallet](https://sparrowwallet.com/), is available in "
"this handbook."
msgstr ""
"Ein [Anleitungen](./collecting/sparrow-wallet.md) zur Erstellung eines `ord`-"
"kompatiblen Wallets mit [Sparrow Wallet](https://sparrowwallet.com/) steht "
"in diesem Handbuch zur Verfügung."

#: src\guides/collecting.md:21
msgid ""
"Please note that if you follow this guide, you should not use the wallet you "
"create to send BTC, unless you perform manual coin-selection to avoid "
"sending ordinals."
msgstr ""
"Bitte beachten Sie, dass Sie, wenn Sie diesem Leitfaden folgen, das "
"erstellte Wallet nicht verwenden sollten, um BTC zu senden, es sei denn, Sie "
"führen eine manuelle Auswahl der Coins durch, um das Senden von Ordinals zu "
"vermeiden."

#: src\guides/collecting/sparrow-wallet.md:1
msgid "Collecting Inscriptions and Ordinals with Sparrow Wallet"
msgstr "Sammeln von Inscriptions und Ordinals mit Sparrow Wallet"

#: src\guides/collecting/sparrow-wallet.md:4
msgid ""
"Users who cannot or have not yet set up the [ord](https://github.com/"
"ordinals/ord) wallet can receive inscriptions and ordinals with alternative "
"bitcoin wallets, as long as they are _very_ careful about how they spend "
"from that wallet."
msgstr ""
"Benutzer, die das [ord](https://github.com/ordinals/ord) nicht einrichten "
"können oder noch nicht eingerichtet haben, können inscriptions und Ordinals "
"mit alternativen Bitcoin-Wallets empfangen, solange sie _sehr_ vorsichtig "
"sind, wie sie aus diesem Wallet ausgeben."

#: src\guides/collecting/sparrow-wallet.md:6
msgid ""
"This guide gives some basic steps on how to create a wallet with [Sparrow "
"Wallet](https://sparrowwallet.com/) which is compatible with `ord` and can "
"be later imported into `ord`"
msgstr ""
"Dieser Leitfaden gibt einige grundlegende Schritte an, wie Sie ein Wallet "
"mit [Sparrow Wallet](https://sparrowwallet.com/) erstellen können, das mit "
"`ord` kompatibel ist und später in `ord` importiert werden kann"

#: src\guides/collecting/sparrow-wallet.md:8
msgid "⚠️⚠️ Warning!! ⚠️⚠️"
msgstr "⚠️⚠️ Achtung!! ⚠️⚠️"

#: src\guides/collecting/sparrow-wallet.md:9
msgid ""
"As a general rule if you take this approach, you should use this wallet with "
"the Sparrow software as a receive-only wallet."
msgstr ""
"Im Allgemeinen sollten Sie, wenn Sie diesen Ansatz wählen, dieses Wallet mit "
"der Sparrow-Software nur als Empfangswallet verwenden."

#: src\guides/collecting/sparrow-wallet.md:11
msgid ""
"Do not spend any satoshis from this wallet unless you are sure you know what "
"you are doing. You could very easily inadvertently lose access to your "
"ordinals and inscriptions if you don't heed this warning."
msgstr ""
"Geben Sie keine Satoshis aus diesem Wallet aus, es sei denn, Sie sind "
"sicher, dass Sie wissen, was Sie tun. Sie könnten leicht unbeabsichtigt den "
"Zugriff auf Ihre Ordinals und inscriptions verlieren, wenn Sie dieser "
"Warnung nicht folgen."

#: src\guides/collecting/sparrow-wallet.md:13
msgid "Wallet Setup & Receiving"
msgstr "Wallet-Einrichtung und -Empfang"

#: src\guides/collecting/sparrow-wallet.md:15
msgid ""
"Download the Sparrow Wallet from the [releases page](https://sparrowwallet."
"com/download/) for your particular operating system."
msgstr ""
"Laden Sie die Sparrow Wallet von der [Download-Seite](https://sparrowwallet."
"com/download/) für Ihr jeweiliges Betriebssystem herunter."

#: src\guides/collecting/sparrow-wallet.md:17
msgid "Select `File -> New Wallet` and create a new wallet called `ord`."
msgstr ""
"Wählen Sie `File -> New Wallet` und erstellen Sie eine neue Wallet namens "
"`ord`."

#: src\guides/collecting/sparrow-wallet.md:19
msgid "![](images/wallet_setup_01.png)"
msgstr "![](images/wallet_setup_01.png)"

#: src\guides/collecting/sparrow-wallet.md:21
msgid ""
"Change the `Script Type` to `Taproot (P2TR)` and select the `New or Imported "
"Software Wallet` option."
msgstr ""
"Ändern Sie den \"Skripttyp\" in \"Taproot (P2TR)\" und wählen Sie die Option "
"\"Neue oder importierte Software-Wallet\" aus."

#: src\guides/collecting/sparrow-wallet.md:23
msgid "![](images/wallet_setup_02.png)"
msgstr "![](images/wallet_setup_02.png)"

#: src\guides/collecting/sparrow-wallet.md:25
msgid ""
"Select `Use 12 Words` and then click `Generate New`. Leave the passphrase "
"blank."
msgstr ""
"Wählen Sie `Verwenden von 12 Wörtern` aus und klicken Sie dann auf `Neue "
"generieren`. Lassen Sie das Passwortfeld leer."

#: src\guides/collecting/sparrow-wallet.md:27
msgid "![](images/wallet_setup_03.png)"
msgstr "![](images/wallet_setup_03.png)"

#: src\guides/collecting/sparrow-wallet.md:29
msgid ""
"A new 12 word BIP39 seed phrase will be generated for you. Write this down "
"somewhere safe as this is your backup to get access to your wallet. NEVER "
"share or show this seed phrase to anyone else."
msgstr ""
"Es wird eine neue 12-Wort-BIP39-Sicherungsphrase für Sie generiert. "
"Schreiben Sie diese an einem sicheren Ort auf, da dies Ihre Sicherung ist, "
"um Zugriff auf Ihre Brieftasche zu erhalten. Teilen Sie diese "
"Sicherungsphrase NIEMALS mit jemand anderem oder zeigen Sie sie niemandem."

#: src\guides/collecting/sparrow-wallet.md:31
msgid "Once you have written down the seed phrase click `Confirm Backup`."
msgstr ""
"Sobald Sie die Sicherungsphrase notiert haben, klicken Sie auf `Confirm "
"Backup`."

#: src\guides/collecting/sparrow-wallet.md:33
msgid "![](images/wallet_setup_04.png)"
msgstr "![](images/wallet_setup_04.png)"

#: src\guides/collecting/sparrow-wallet.md:35
msgid ""
"Re-enter the seed phrase which you wrote down, and then click `Create "
"Keystore`."
msgstr ""
"Geben Sie die zuvor notierte Sicherungsphrase erneut ein und klicken Sie "
"dann auf `Create Keystore`."

#: src\guides/collecting/sparrow-wallet.md:37
msgid "![](images/wallet_setup_05.png)"
msgstr "![](images/wallet_setup_05.png)"

#: src\guides/collecting/sparrow-wallet.md:39
msgid "Click `Import Keystore`."
msgstr "Klicken Sie auf `Import Keystore`."

#: src\guides/collecting/sparrow-wallet.md:41
msgid "![](images/wallet_setup_06.png)"
msgstr "![](images/wallet_setup_06.png)"

#: src\guides/collecting/sparrow-wallet.md:43
msgid "Click `Apply`. Add a password for the wallet if you want to."
msgstr ""
"Klicken Sie auf `Apply`. Wenn Sie möchten, können Sie dem Wallet ein "
"Passwort hinzufügen."

#: src\guides/collecting/sparrow-wallet.md:45
msgid "![](images/wallet_setup_07.png)"
msgstr "![](images/wallet_setup_07.png)"

#: src\guides/collecting/sparrow-wallet.md:47
msgid ""
"You now have a wallet which is compatible with `ord`, and can be imported "
"into `ord` using the BIP39 Seed Phrase. To receive ordinals or inscriptions, "
"click on the `Receive` tab and copy a new address."
msgstr ""
"Sie haben jetzt ein Wallet, das mit `ord` kompatibel ist und mit Hilfe des "
"BIP39 Seed Phrase in `ord` importiert werden kann. Um Ordinals oder "
"Inschriften zu empfangen, klicken Sie auf die Registerkarte `Receive` und "
"kopieren Sie eine neue Adresse."

#: src\guides/collecting/sparrow-wallet.md:49
msgid ""
"Each time you want to receive you should use a brand-new address, and not re-"
"use existing addresses."
msgstr ""
"Jedes Mal, wenn Sie empfangen möchten, sollten Sie eine brandneue Adresse "
"verwenden und keine vorhandenen Adressen erneut verwenden."

#: src\guides/collecting/sparrow-wallet.md:51
msgid ""
"Note that bitcoin is different to some other blockchain wallets, in that "
"this wallet can generate an unlimited number of new addresses. You can "
"generate a new address by clicking on the `Get Next Address` button. You can "
"see all of your addresses in the `Addresses` tab of the app."
msgstr ""
"Beachten Sie, dass Bitcoin sich von einigen anderen Blockchain-Wallets "
"dadurch unterscheidet, dass dieses Wallet eine unbegrenzte Anzahl neuer "
"Adressen generieren kann. Sie können eine neue Adresse generieren, indem Sie "
"auf die Schaltfläche `Get Next Address` klicken. Sie können alle Ihre "
"Adressen im Reiter `Addresses` der App sehen."

#: src\guides/collecting/sparrow-wallet.md:53
msgid ""
"You can add a label to each address, so you can keep track of what it was "
"used for."
msgstr ""
"Sie können jeder Adresse ein Etikett hinzufügen, um den Überblick darüber zu "
"behalten, wofür sie verwendet wurde."

#: src\guides/collecting/sparrow-wallet.md:55
msgid "![](images/wallet_setup_08.png)"
msgstr "![](images/wallet_setup_08.png)"

#: src\guides/collecting/sparrow-wallet.md:57
msgid "Validating / Viewing Received Inscriptions"
msgstr "Überprüfung / Anzeige empfangener Inscriptions"

#: src\guides/collecting/sparrow-wallet.md:59
msgid ""
"Once you have received an inscription you will see a new transaction in the "
"`Transactions` tab of Sparrow, as well as a new UTXO in the `UTXOs` tab."
msgstr ""
"Sobald Sie eine Anmeldung erhalten haben, sehen Sie eine neue Transaktion "
"auf der Registerkarte `Transactions` von Sparrow sowie ein neues UTXO auf "
"der Registerkarte `UTXOs`."

#: src\guides/collecting/sparrow-wallet.md:61
msgid ""
"Initially this transaction may have an \"Unconfirmed\" status, and you will "
"need to wait for it to be mined into a bitcoin block before it is fully "
"received."
msgstr ""
"Anfänglich hat diese Transaktion möglicherweise den Status \"Unconfirmed\" "
"und Sie müssen warten, bis sie in einen Bitcoin-Block umgewandelt wurde, "
"bevor sie vollständig empfangen wird."

#: src\guides/collecting/sparrow-wallet.md:63
msgid "![](images/validating_viewing_01.png)"
msgstr "![](images/validating_viewing_01.png)"

#: src\guides/collecting/sparrow-wallet.md:65
msgid ""
"To track the status of your transaction you can right-click on it,  select "
"`Copy Transaction ID` and then paste that transaction id into [mempool.space]"
"(https://mempool.space)."
msgstr ""
"Um den Status Ihrer Transaktion zu verfolgen, können Sie mit der rechten "
"Maustaste darauf klicken, `Copy Transaction ID` auswählen und diese "
"Transaktions-ID dann in [mempool.space](https://mempool.space) einfügen."

#: src\guides/collecting/sparrow-wallet.md:67
msgid "![](images/validating_viewing_02.png)"
msgstr "![](images/validating_viewing_02.png)"

#: src\guides/collecting/sparrow-wallet.md:69
msgid ""
"Once the transaction has confirmed, you can validate and view your "
"inscription by heading over to the `UTXOs` tab, finding the UTXO you want to "
"check, right-clicking on the `Output` and selecting `Copy Transaction "
"Output`. This transaction output id can then be pasted into the [ordinals."
"com](https://ordinals.com) search."
msgstr ""
"Sobald die Transaktion bestätigt wurde, können Sie Ihre inscription "
"validieren und anzeigen, indem Sie zur Registerkarte `UTXOs` gehen, das UTXO "
"suchen, das Sie überprüfen möchten, mit der rechten Maustaste auf `Output` "
"klicken und  `Copy Transaction Output` auswählen. Diese Transaktionsausgabe-"
"ID kann dann in die Suche von [ordinals.com](https://ordinals.com) eingefügt "
"werden."

#: src\guides/collecting/sparrow-wallet.md:72
msgid "Freezing UTXO's"
msgstr "Einfrieren von UTXOs"

#: src\guides/collecting/sparrow-wallet.md:73
msgid ""
"As explained above, each of your inscriptions is stored in an Unspent "
"Transaction Output (UTXO). You want to be very careful not to accidentally "
"spend your inscriptions, and one way to make it harder for this to happen is "
"to freeze the UTXO."
msgstr ""
"Wie oben erläutert, wird jede Ihrer inscription in einem Unspent Transaction "
"Output (UTXO) gespeichert. Sie sollten sehr darauf achten, Ihre inscription "
"nicht versehentlich auszugeben. Eine Möglichkeit, dies zu erschweren, "
"besteht darin, den UTXO einzufrieren."

#: src\guides/collecting/sparrow-wallet.md:75
msgid ""
"To do this, go to the `UTXOs` tab, find the UTXO you want to freeze, right-"
"click on the `Output` and select `Freeze UTXO`."
msgstr ""
"Gehen Sie dazu zur Registerkarte `UTXOs`, suchen Sie das UTXO, das Sie "
"einfrieren möchten, klicken Sie mit der rechten Maustaste auf `Output` und "
"wählen Sie `Freeze UTXO`."

#: src\guides/collecting/sparrow-wallet.md:77
msgid ""
"This UTXO (Inscription) is now un-spendable within the Sparrow Wallet until "
"you unfreeze it."
msgstr ""
"Dieses UTXO (Inscription) kann jetzt nicht mehr in der Sparrow-Wallet "
"ausgegeben werden, bis Sie es entsperren."

#: src\guides/collecting/sparrow-wallet.md:79
msgid "Importing into `ord` wallet"
msgstr "Importieren in die `ord` wallet"

#: src\guides/collecting/sparrow-wallet.md:81
msgid ""
"For details on setting up Bitcoin Core and the `ord` wallet check out the "
"[Inscriptions Guide](../inscriptions.md)"
msgstr ""
"Einzelheiten zum Einrichten von Bitcoin Core und der `ord` Wallet finden Sie "
"im [Inscriptions Guide](../inscriptions.md)"

#: src\guides/collecting/sparrow-wallet.md:83
msgid ""
"When setting up `ord`, instead of running `ord wallet create` to create a "
"brand-new wallet, you can import your existing wallet using `ord wallet "
"restore \"BIP39 SEED PHRASE\"` using the seed phrase you generated with "
"Sparrow Wallet."
msgstr ""
"Wenn Sie `ord` einrichten, können Sie, anstatt `ord wallet create` "
"auszuführen, um ein brandneues Wallet zu erstellen, Ihr vorhandenes Wallet "
"mit `ord wallet restore \"BIP39 SEED PHRASE\"` importieren und dabei die "
"Seed-Phrase verwenden, die Sie mit Sparrow Wallet generiert haben."

#: src\guides/collecting/sparrow-wallet.md:85
msgid ""
"There is currently a [bug](https://github.com/ordinals/ord/issues/1589) "
"which causes an imported wallet to not be automatically rescanned against "
"the blockchain. To work around this you will need to manually trigger a "
"rescan using the bitcoin core cli: `bitcoin-cli -rpcwallet=ord "
"rescanblockchain 767430`"
msgstr ""
"Derzeit gibt es einen [Fehler](https://github.com/ordinals/ord/issues/1589), "
"der dazu führt, dass eine importierte Wallet nicht automatisch erneut anhand "
"der Blockchain gescannt wird. Um dies zu umgehen, müssen Sie manuell einen "
"erneuten Scan mit der Bitcoin-Core-CLI auslösen: `bitcoin-cli -rpcwallet=ord "
"rescanblockchain 767430`"

#: src\guides/collecting/sparrow-wallet.md:88
msgid ""
"You can then check your wallet's inscriptions using `ord wallet inscriptions`"
msgstr ""
"Anschließend können Sie die Beschriftung Ihrer Brieftasche mithilfe von `ord "
"wallet inscriptions` überprüfen"

#: src\guides/collecting/sparrow-wallet.md:90
msgid ""
"Note that if you have previously created a wallet with `ord`, then you will "
"already have a wallet with the default name, and will need to give your "
"imported wallet a different name. You can use the `--wallet` parameter in "
"all `ord` commands to reference a different wallet, eg:"
msgstr ""
"Beachten Sie, dass Sie, wenn Sie zuvor ein Wallet mit `ord` erstellt haben, "
"bereits über ein Wallet mit dem Standardnamen verfügen und Ihrem "
"importierten Wallet einen anderen Namen geben müssen. Sie können den "
"Parameter `--wallet` in allen `ord` -Befehlen verwenden, um auf eine andere "
"Wallet zu verweisen, z. B.:"

#: src\guides/collecting/sparrow-wallet.md:92
msgid "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"
msgstr "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"

#: src\guides/collecting/sparrow-wallet.md:94
msgid "`ord --wallet ord_from_sparrow wallet inscriptions`"
msgstr "`ord --wallet ord_from_sparrow wallet inscriptions`"

#: src\guides/collecting/sparrow-wallet.md:96
msgid "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"
msgstr "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"

#: src\guides/collecting/sparrow-wallet.md:98
msgid "Sending inscriptions with Sparrow Wallet"
msgstr "Inscriptions mit Sparrow Wallet versenden"

#: src\guides/collecting/sparrow-wallet.md:100
msgid "⚠️⚠️ Warning ⚠️⚠️"
msgstr "⚠️⚠️ Achtung ⚠️⚠️"

#: src\guides/collecting/sparrow-wallet.md:101
msgid ""
"While it is highly recommended that you set up a bitcoin core node and run "
"the `ord` software, there are certain limited ways you can send inscriptions "
"out of Sparrow Wallet in a safe way. Please note that this is not "
"recommended, and you should only do this if you fully understand what you "
"are doing."
msgstr ""
"Es wird zwar dringend empfohlen, einen Bitcoin-Kernknoten einzurichten und "
"die `ord` Software auszuführen, es gibt jedoch bestimmte eingeschränkte "
"Möglichkeiten, inscriptions aus Sparrow Wallet auf sichere Weise zu "
"versenden. Bitte beachten Sie, dass dies nicht empfohlen wird und Sie dies "
"nur tun sollten, wenn Sie vollständig verstehen, was Sie tun."

#: src\guides/collecting/sparrow-wallet.md:103
msgid ""
"Using the `ord` software will remove much of the complexity we are "
"describing here, as it is able to automatically and safely handle sending "
"inscriptions in an easy way."
msgstr ""
"Der Einsatz der `ord` Software wird einen Großteil der hier beschriebenen "
"Komplexität beseitigen, da sie in der Lage ist, das Versenden von "
"inscription auf einfache Weise automatisch und sicher abzuwickeln."

#: src\guides/collecting/sparrow-wallet.md:105
msgid "⚠️⚠️ Additional Warning ⚠️⚠️"
msgstr "⚠️⚠️ Zusätzliche Warnung ⚠️⚠️"

#: src\guides/collecting/sparrow-wallet.md:106
msgid ""
"Don't use your sparrow inscriptions wallet to do general sends of non-"
"inscription bitcoin. You can setup a separate wallet in sparrow if you need "
"to do normal bitcoin transactions, and keep your inscriptions wallet "
"separate."
msgstr ""
"Verwenden Sie Ihre Sparrow-Inscriptions-Wallet nicht für allgemeine "
"Versendungen von Bitcoin ohne Inschrift. Sie können in Sparrow ein separates "
"Wallet einrichten, wenn Sie normale Bitcoin-Transaktionen durchführen "
"müssen, und Ihr Inscriptions Wallet separat aufbewahren."

#: src\guides/collecting/sparrow-wallet.md:108
msgid "Bitcoin's UTXO model"
msgstr "Das UTXO-Modell von Bitcoin"

#: src\guides/collecting/sparrow-wallet.md:109
msgid ""
"Before sending any transaction it's important that you have a good mental "
"model for bitcoin's Unspent Transaction Output (UTXO) system. The way "
"Bitcoin works is fundamentally different to many other blockchains such as "
"Ethereum. In Ethereum generally you have a single address in which you store "
"ETH, and you cannot differentiate between any of the ETH -  it is just all a "
"single value of the total amount in that address. Bitcoin works very "
"differently in that we generate a new address in the wallet for each "
"receive, and every time you receive sats to an address in your wallet you "
"are creating a new UTXO. Each UTXO can be seen and managed individually. You "
"can select specific UTXO's which you want to spend, and you can choose not "
"to spend certain UTXO's."
msgstr ""
"Bevor Sie eine Transaktion senden, ist es wichtig, dass Sie ein gutes "
"mentales Modell für das Unspent Transaction Output (UTXO) -System von "
"Bitcoin haben. Die Funktionsweise von Bitcoin unterscheidet sich grundlegend "
"von vielen anderen Blockchains wie Ethereum. In Ethereum haben Sie in der "
"Regel eine einzelne Adresse, auf der Sie ETH speichern, und Sie können "
"zwischen keinem der ETH unterscheiden - es handelt sich einfach um einen "
"einzigen Wert des Gesamtbetrags auf dieser Adresse. Bitcoin funktioniert "
"sehr unterschiedlich, indem wir für jede empfangene Transaktion eine neue "
"Adresse im Wallet generieren, und jedes Mal, wenn Sie Sats an eine Adresse "
"in Ihrem Wallet empfangen, erstellen Sie ein neues UTXO. Jeder UTXO kann "
"einzeln betrachtet und verwaltet werden. Sie können bestimmte UTXOs "
"auswählen, die Sie ausgeben möchten, und Sie können wählen, bestimmte UTXOs "
"nicht auszugeben."

#: src\guides/collecting/sparrow-wallet.md:111
msgid ""
"Some Bitcoin wallets do not expose this level of detail, and they just show "
"you a single summed up value of all the bitcoin in your wallet. However, "
"when sending inscriptions it is important that you use a wallet like Sparrow "
"which allows for UTXO control."
msgstr ""
"Einige Bitcoin-Wallets bieten diese Ebene des Detailreichtums nicht an und "
"zeigen Ihnen lediglich einen einzigen zusammengefassten Wert aller Bitcoins "
"in Ihrem Wallet an. Wenn Sie jedoch inscriptions senden, ist es wichtig, "
"eine Wallet wie Sparrow zu verwenden, die die Kontrolle über UTXOs "
"ermöglicht."

#: src\guides/collecting/sparrow-wallet.md:113
msgid "Inspecting your inscription before sending"
msgstr "Überprüfen Sie Ihre inscription vor dem Absenden"

#: src\guides/collecting/sparrow-wallet.md:114
msgid ""
"Like we have previously described inscriptions are inscribed onto sats, and "
"sats are stored within UTXOs. UTXO's are a collection of satoshis with some "
"particular value of the number of satoshis (the output value). Usually (but "
"not always) the inscription will be inscribed on the first satoshi in the "
"UTXO."
msgstr ""
"Wie zuvor beschrieben, werden inscriptions auf Satoshis graviert, und "
"Satoshis werden in UTXOs (Unspent Transaction Outputs) gespeichert. UTXOs "
"sind eine Sammlung von Satoshis mit einem bestimmten Wert der Anzahl von "
"Satoshis (the output value). Normalerweise (aber nicht immer) wird die "
"inscribed auf dem ersten Satoshi im UTXO graviert."

#: src\guides/collecting/sparrow-wallet.md:116
msgid ""
"When inspecting your inscription before sending the main thing you will want "
"to check is which satoshi in the UTXO your inscription is inscribed on."
msgstr ""
"Wenn Sie Ihre inscription vor dem Versenden überprüfen, sollten Sie vor "
"allem prüfen, auf welchem Satoshi im UTXO Ihre Inschrift eingetragen ist."

#: src\guides/collecting/sparrow-wallet.md:118
msgid ""
"To do this, you can follow the [Validating / Viewing Received Inscriptions]"
"(./sparrow-wallet.md#validating--viewing-received-inscriptions) described "
"above to find the inscription page for your inscription on ordinals.com"
msgstr ""
"Um dies zu tun, können Sie dem oben beschriebenen [Validating / Viewing "
"Received Inscriptions](./sparrow-wallet.md#validating--viewing-received-"
"inscriptions) folgen, um die inscription seite für Ihre inscription auf "
"ordinals.com zu finden"

#: src\guides/collecting/sparrow-wallet.md:120
msgid ""
"There you will find some metadata about your inscription which looks like "
"the following:"
msgstr ""
"Dort finden Sie einige Metadaten zu Ihrer Inschrift, die wie folgt aussehen:"

#: src\guides/collecting/sparrow-wallet.md:122
msgid "![](images/sending_01.png)"
msgstr "![](images/sending_01.png)"

#: src\guides/collecting/sparrow-wallet.md:124
msgid "There is a few of important things to check here:"
msgstr "Hier gibt es einige wichtige Dinge zu überprüfen:"

#: src\guides/collecting/sparrow-wallet.md:125
msgid ""
"The `output` identifier matches the identifier of the UTXO you are going to "
"send"
msgstr ""
"Die `output` Kennung entspricht der Kennung des UTXO, das Sie senden möchten"

#: src\guides/collecting/sparrow-wallet.md:126
msgid ""
"The `offset` of the inscription is `0` (this means that the inscription is "
"located on the first sat in the UTXO)"
msgstr ""
"Der `offset` der Inschrift ist `0` (das bedeutet, dass sich die inscription "
"auf dem ersten Platz im UTXO befindet)"

#: src\guides/collecting/sparrow-wallet.md:127
msgid ""
"the `output_value` has enough sats to cover the transaction fee (postage) "
"for sending the transaction. The exact amount you will need depends on the "
"fee rate you will select for the transaction"
msgstr ""
"Der `output_value` verfügt über genügend Sats, um die Transaktionsgebühr "
"(Porto) für den Versand der Transaktion zu decken. Der genaue Betrag, den "
"Sie benötigen, hängt von dem Gebührensatz ab, den Sie für die Transaktion "
"wählen"

#: src\guides/collecting/sparrow-wallet.md:129
msgid ""
"If all of the above are true for your inscription, it should be safe for you "
"to send it using the method below."
msgstr ""
"Wenn alle oben genannten Punkte auf Ihre inscription zutreffen, sollten Sie "
"sie sicher mit der unten aufgeführten Methode versenden."

#: src\guides/collecting/sparrow-wallet.md:131
msgid ""
"⚠️⚠️ Be very careful sending your inscription particularly if the `offset` "
"value is not `0`. It is not recommended to use this method if that is the "
"case, as doing so you could accidentally send your inscription to a bitcoin "
"miner unless you know what you are doing."
msgstr ""
"⚠️⚠️ Seien Sie äußerst vorsichtig, wenn Sie Ihre inscription senden, "
"insbesondere wenn der Wert von `offset` nicht `0` ist. Es wird nicht "
"empfohlen, diese Methode zu verwenden, wenn dies der Fall ist, da Sie "
"andernfalls Ihre inscription versehentlich an einen Bitcoin-Miner senden "
"könnten, es sei denn, Sie wissen genau, was Sie tun."

#: src\guides/collecting/sparrow-wallet.md:133
msgid "Sending your inscription"
msgstr "Senden Ihrer inscription"

#: src\guides/collecting/sparrow-wallet.md:134
msgid ""
"To send an inscription navigate to the `UTXOs` tab, and find the UTXO which "
"you previously validated contains your inscription."
msgstr ""
"Um eine inscription zu senden, wechseln Sie zum Tab `UTXOs` und suchen Sie "
"die UTXO, bei der Sie zuvor überprüft haben, dass sie Ihre inscription "
"enthält."

#: src\guides/collecting/sparrow-wallet.md:136
msgid ""
"If you previously froze the UXTO you will need to right-click on it and "
"unfreeze it."
msgstr ""
"Wenn Sie das UXTO zuvor eingefroren haben, müssen Sie mit der rechten "
"Maustaste darauf klicken und es entsperren."

#: src\guides/collecting/sparrow-wallet.md:138
msgid ""
"Select the UTXO you want to send, and ensure that is the _only_ UTXO is "
"selected. You should see `UTXOs 1/1` in the interface. Once you are sure "
"this is the case you can hit `Send Selected`."
msgstr ""
"Wählen Sie die UTXO aus, die Sie senden möchten, und stellen Sie sicher, "
"dass dies die _einzige_ ausgewählte UTXO ist. Sie sollten in der "
"Benutzeroberfläche `UTXOs 1/1` sehen. Sobald Sie sicher sind, dass dies der "
"Fall ist, können Sie auf `Send Selected` klicken."

#: src\guides/collecting/sparrow-wallet.md:140
msgid "![](images/sending_02.png)"
msgstr "![](images/sending_02.png)"

#: src\guides/collecting/sparrow-wallet.md:142
msgid ""
"You will then be presented with the transaction construction interface. "
"There is a few things you need to check here to make sure that this is a "
"safe send:"
msgstr ""
"Anschließend wird Ihnen die Schnittstelle zur Transaktionskonstruktion "
"angezeigt. Hier müssen Sie einige Dinge überprüfen, um sicherzustellen, dass "
"es sich um einen sicheren Versand handelt:"

#: src\guides/collecting/sparrow-wallet.md:144
msgid ""
"The transaction should have only 1 input, and this should be the UTXO with "
"the label you want to send"
msgstr ""
"Die Transaktion sollte nur eine Eingabe haben, und dies sollte das UTXO mit "
"der Bezeichnung sein, die Sie senden möchten"

#: src\guides/collecting/sparrow-wallet.md:145
msgid ""
"The transaction should have only 1 output, which is the address/label where "
"you want to send the inscription"
msgstr ""
"Die Transaktion sollte nur eine Ausgabe haben, nämlich die Adresse/Etikett, "
"an die Sie die Inschrift senden möchten"

#: src\guides/collecting/sparrow-wallet.md:147
msgid ""
"If your transaction looks any different, for example you have multiple "
"inputs, or multiple outputs then this may not be a safe transfer of your "
"inscription, and you should abandon sending until you understand more, or "
"can import into the `ord` wallet."
msgstr ""
"Wenn Ihre Transaktion anders aussieht, z. B. wenn Sie mehrere Eingaben oder "
"Ausgaben haben, handelt es sich möglicherweise nicht um eine sichere "
"Übertragung Ihrer inscription, und Sie sollten das Senden abbrechen, bis Sie "
"mehr verstehen oder sie in das `ord` Wallet importieren können."

#: src\guides/collecting/sparrow-wallet.md:149
msgid ""
"You should set an appropriate transaction fee, Sparrow will usually "
"recommend a reasonable one, but you can also check [mempool.space](https://"
"mempool.space) to see what the recommended fee rate is for sending a "
"transaction."
msgstr ""
"Sie sollten eine angemessene Transaktionsgebühr festlegen. Sparrow wird in "
"der Regel eine vernünftige Gebühr empfehlen, aber Sie können auch auf "
"[mempool.space](https://mempool.space) nachsehen, um zu erfahren, was die "
"empfohlene Gebührenrate für das Senden einer Transaktion ist."

#: src\guides/collecting/sparrow-wallet.md:151
msgid ""
"You should add a label for the recipient address, a label like `alice "
"address for inscription #123` would be ideal."
msgstr ""
"Sie sollten eine Beschriftung für die Empfängeradresse hinzufügen, ideal "
"wäre eine Beschriftung wie `alice address for inscription #123`."

#: src\guides/collecting/sparrow-wallet.md:153
msgid ""
"Once you have checked the transaction is a safe transaction using the checks "
"above, and you are confident to send it you can click `Create Transaction`."
msgstr ""
"Sobald Sie mithilfe der oben genannten Prüfungen überprüft haben, dass es "
"sich bei der Transaktion um eine sichere Transaktion handelt, und Sie sicher "
"sind, dass Sie sie senden können, können Sie auf `Create Transaction` "
"klicken."

#: src\guides/collecting/sparrow-wallet.md:155
msgid "![](images/sending_03.png)"
msgstr "![](images/sending_03.png)"

#: src\guides/collecting/sparrow-wallet.md:157
msgid ""
"Here again you can double check that your transaction looks safe, and once "
"you are confident you can click `Finalize Transaction for Signing`."
msgstr ""
"Auch hier können Sie noch einmal überprüfen, ob Ihre Transaktion sicher "
"aussieht, und wenn Sie sicher sind, können Sie auf `Finalize Transaction for "
"Signing` klicken."

#: src\guides/collecting/sparrow-wallet.md:159
msgid "![](images/sending_04.png)"
msgstr "![](images/sending_04.png)"

#: src\guides/collecting/sparrow-wallet.md:161
msgid "Here you can triple check everything before hitting `Sign`."
msgstr ""
"Hier können Sie alles noch einmal überprüfen, bevor Sie auf `Sign` klicken."

#: src\guides/collecting/sparrow-wallet.md:163
msgid "![](images/sending_05.png)"
msgstr "![](images/sending_05.png)"

#: src\guides/collecting/sparrow-wallet.md:165
msgid ""
"And then actually you get very very last chance to check everything before "
"hitting `Broadcast Transaction`. Once you broadcast the transaction it is "
"sent to the bitcoin network, and starts being propagated into the mempool."
msgstr ""
"Und dann haben Sie tatsächlich die allerletzte Chance, alles zu überprüfen, "
"bevor Sie auf `Broadcast Transaction` klicken. Sobald Sie die Transaktion "
"übertragen, wird sie an das Bitcoin-Netzwerk gesendet und beginnt mit der "
"Weitergabe an den Mempool."

#: src\guides/collecting/sparrow-wallet.md:167
msgid "![](images/sending_06.png)"
msgstr "![](images/sending_06.png)"

#: src\guides/collecting/sparrow-wallet.md:169
msgid ""
"If you want to track the status of your transaction you can copy the "
"`Transaction Id (Txid)` and paste that into [mempool.space](https://mempool."
"space)"
msgstr ""
"Wenn Sie den Status Ihrer Transaktion verfolgen möchten, können Sie die "
"`Transaction Id (Txid)` kopieren und in [mempool.space](https://mempool."
"space) einfügen."

#: src\guides/collecting/sparrow-wallet.md:171
msgid ""
"Once the transaction has confirmed you can check the inscription page on "
"[ordinals.com](https://ordinals.com) to validate that it has moved to the "
"new output location and address."
msgstr ""
"Nachdem die Transaktion bestätigt wurde, können Sie die inscription seite "
"auf [ordinals.com](https://ordinals.com) überprüfen, um sicherzustellen, "
"dass sie an den neuen Ausgabeort und die neue Adresse verschoben wurde."

#: src\guides/collecting/sparrow-wallet.md:173
msgid "Troubleshooting"
msgstr "Störungssuche"

#: src\guides/collecting/sparrow-wallet.md:175
msgid ""
"Sparrow wallet is not showing a transaction/UTXO, but I can see it on "
"mempool.space!"
msgstr ""
"Wenn die Sparrow-Wallet eine Transaktion oder UTXO nicht anzeigt, Sie sie "
"aber auf mempool.space sehen können!"

#: src\guides/collecting/sparrow-wallet.md:177
msgid ""
"Make sure that your wallet is connected to a bitcoin node. To validate this, "
"head into the `Preferences`\\-> `Server` settings, and click `Edit Existing "
"Connection`."
msgstr ""
"Um sicherzustellen, dass Ihre Wallet mit einem Bitcoin-Knoten (Node) "
"verbunden ist, befolgen Sie diese Schritte `Preferences`\\-> `Server` "
"settings, and click `Edit Existing Connection`."

#: src\guides/collecting/sparrow-wallet.md:179
msgid "![](images/troubleshooting_01.png)"
msgstr "![](images/troubleshooting_01.png)"

#: src\guides/collecting/sparrow-wallet.md:181
msgid ""
"From there you can select a node and click `Test Connection` to validate "
"that Sparrow is able to connect successfully."
msgstr ""
"Von dort aus können Sie einen Knoten auswählen und auf `Test Connection` "
"klicken, um zu überprüfen, ob Sparrow erfolgreich eine Verbindung herstellen "
"kann."

#: src\guides/collecting/sparrow-wallet.md:183
msgid "![](images/troubleshooting_02.png)"
msgstr "![](images/troubleshooting_02.png)"

#: src\guides/testing.md:4
msgid ""
"Ord can be tested using the following flags to specify the test network. For "
"more information on running Bitcoin Core for testing, see [Bitcoin's "
"developer documentation](https://developer.bitcoin.org/examples/testing."
"html)."
msgstr ""
"Ord kann mithilfe der folgenden Flags getestet werden, um das Testnetzwerk "
"anzugeben. Weitere Informationen zum Ausführen von Bitcoin Core zu "
"Testzwecken finden Sie in der [Entwicklerdokumentation von Bitcoin](https://"
"developer.bitcoin.org/examples/testing.html)."

#: src\guides/testing.md:7
msgid ""
"Most `ord` commands in [inscriptions](inscriptions.md) and [explorer]"
"(explorer.md) can be run with the following network flags:"
msgstr ""
"Die meisten `ord` Befehle in [Inscriptions](inscriptions.md) und [Explorer]"
"(explorer.md) können mit den folgenden Netzwerkflags ausgeführt werden:"

#: src\guides/testing.md:10
msgid "Network"
msgstr "Network"

#: src\guides/testing.md:10
msgid "Flag"
msgstr "Flag"

#: src\guides/testing.md:12
msgid "Testnet"
msgstr "Testnet"

#: src\guides/testing.md:12
msgid "`--testnet` or `-t`"
msgstr "`--testnet` or `-t`"

#: src\guides/testing.md:13
msgid "Signet"
msgstr "Signet"

#: src\guides/testing.md:13
msgid "`--signet` or `-s`"
msgstr "`--signet` or `-s`"

#: src\guides/testing.md:14
msgid "Regtest"
msgstr "Regtest"

#: src\guides/testing.md:14
msgid "`--regtest` or `-r`"
msgstr "`--regtest` or `-r`"

#: src\guides/testing.md:16
msgid "Regtest doesn't require downloading the blockchain or indexing ord."
msgstr ""
"Für den Regtest ist kein Herunterladen der Blockchain oder Indexierungs das "
"ord erforderlich."

#: src\guides/testing.md:18 src\guides/reindexing.md:15
msgid "Example"
msgstr "Beispiel"

#: src\guides/testing.md:21
msgid "Run bitcoind in regtest with:"
msgstr "Führen Sie Bitcoind im Regtest aus mit:"

#: src\guides/testing.md:22
msgid ""
"```\n"
"bitcoind -regtest -txindex\n"
"```"
msgstr ""
"```\n"
"bitcoind -regtest -txindex\n"
"```"

#: src\guides/testing.md:25
msgid "Create a wallet in regtest with:"
msgstr "Erstellen Sie im Regtest ein Wallet mit:"

#: src\guides/testing.md:26
msgid ""
"```\n"
"ord -r wallet create\n"
"```"
msgstr ""
"```\n"
"ord -r wallet create\n"
"```"

#: src\guides/testing.md:29
msgid "Get a regtest receive address with:"
msgstr "Erhalten Sie eine Regtest-Empfangsadresse mit:"

#: src\guides/testing.md:30
msgid ""
"```\n"
"ord -r wallet receive\n"
"```"
msgstr ""
"```\n"
"ord -r wallet receive\n"
"```"

#: src\guides/testing.md:33
msgid "Mine 101 blocks (to unlock the coinbase) with:"
msgstr "Minen Sie 101 Blöcke (um die coinbase freizuschalten) mit:"

#: src\guides/testing.md:34
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 101 <receive address>\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 101 <receive address>\n"
"```"

#: src\guides/testing.md:37
msgid "Inscribe in regtest with:"
msgstr "Inscribe im Regtest mit:"

#: src\guides/testing.md:38
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file <file>\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file <file>\n"
"```"

#: src\guides/testing.md:41
msgid "Mine the inscription with:"
msgstr "Mine die inscription mit:"

#: src\guides/testing.md:42
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 1 <receive address>\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 1 <receive address>\n"
"```"

#: src\guides/testing.md:45
msgid "View the inscription in the regtest explorer:"
msgstr "Sehen Sie sich die inscription im Regtest-Explorer an:"

#: src\guides/testing.md:46
msgid ""
"```\n"
"ord -r server\n"
"```"
msgstr ""
"```\n"
"ord -r server\n"
"```"

#: src\guides/testing.md:50
msgid "Testing Recursion"
msgstr "Testen der Rekursion"

#: src\guides/testing.md:53
msgid ""
"When testing out [recursion](../inscriptions/recursion.md), inscribe the "
"dependencies first (example with [p5.js](https://p5js.org):"
msgstr ""
"Wenn Sie [Rekursion](../inscriptions/recursion.md) testen möchten, "
"inskribieren Sie zuerst die Abhängigkeiten (Beispiel mit [p5.js](https://"
"p5js.org)):"

#: src\guides/testing.md:55
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file p5.js\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file p5.js\n"
"```"

#: src\guides/testing.md:58
msgid ""
"This should return a `inscription_id` which you can then reference in your "
"recursive inscription."
msgstr ""
"Dies sollte eine `inscription_id` zurückgeben, auf die Sie dann in Ihrer "
"rekursiven inscription verweisen können."

#: src\guides/testing.md:61
msgid ""
"ATTENTION: These ids will be different when inscribing on mainnet or signet, "
"so be sure to change those in your recursive inscription for each chain."
msgstr ""
"ACHTUNG: Diese IDs werden unterschiedlich sein, wenn Sie auf Mainnet oder "
"Signet inskribieren. Stellen Sie sicher, dass Sie sie für jede Kette in "
"Ihrer rekursiven inscription ändern."

#: src\guides/testing.md:65
msgid "Then you can inscribe your recursive inscription with:"
msgstr "Dann können Sie Ihre rekursive inscribe einschreiben mit:"

#: src\guides/testing.md:66
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file recursive-inscription.html\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file recursive-inscription.html\n"
"```"

#: src\guides/testing.md:69
msgid "Finally you will have to mine some blocks and start the server:"
msgstr "Schließlich müssen Sie einige Blöcke minen und den Server starten:"

#: src\guides/testing.md:70
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 6 <receive address>\n"
"ord -r server\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 6 <receive address>\n"
"ord -r server\n"
"```"

#: src\guides/moderation.md:4
msgid ""
"`ord` includes a block explorer, which you can run locally with `ord server`."
msgstr ""
"`ord` enthält einen Block-Explorer, den Sie lokal mit `ord server` ausführen "
"können."

#: src\guides/moderation.md:6
msgid ""
"The block explorer allows viewing inscriptions. Inscriptions are user-"
"generated content, which may be objectionable or unlawful."
msgstr ""
"Der Block-Explorer ermöglicht das Anzeigen von Inscriptions. Inscriptions "
"sind von Benutzern generierte Inhalte, die möglicherweise anstößig oder "
"rechtswidrig sein können."

#: src\guides/moderation.md:9
msgid ""
"It is the responsibility of each individual who runs an ordinal block "
"explorer instance to understand their responsibilities with respect to "
"unlawful content, and decide what moderation policy is appropriate for their "
"instance."
msgstr ""
"Es liegt in der Verantwortung jeder Person, die eine Instanz eines ordinal "
"Block-Explorers betreibt, zu verstehen, welche Verantwortlichkeiten in Bezug "
"auf rechtswidrige Inhalte bestehen, und zu entscheiden, welche "
"Moderationsrichtlinien für ihre Instanz angemessen sind."

#: src\guides/moderation.md:13
msgid ""
"In order to prevent particular inscriptions from being displayed on an `ord` "
"instance, they can be included in a YAML config file, which is loaded with "
"the `--config` option."
msgstr ""
"Um bestimmte inscriptions davor zu schützen, auf einer `ord`-Instanz "
"angezeigt zu werden, können sie in einer YAML-Konfigurationsdatei aufgeführt "
"werden, die mit der `--config`-Option geladen wird."

#: src\guides/moderation.md:17
msgid ""
"To hide inscriptions, first create a config file, with the inscription ID "
"you want to hide:"
msgstr ""
"Um Inscriptions zu verbergen, erstellen Sie zunächst eine "
"Konfigurationsdatei mit der Inscription-ID, die Sie ausblenden möchten:"

#: src\guides/moderation.md:20
msgid ""
"```yaml\n"
"hidden:\n"
"- 0000000000000000000000000000000000000000000000000000000000000000i0\n"
"```"
msgstr ""
"```yaml\n"
"hidden:\n"
"- 0000000000000000000000000000000000000000000000000000000000000000i0\n"
"```"

#: src\guides/moderation.md:25
msgid ""
"The suggested name for `ord` config files is `ord.yaml`, but any filename "
"can be used."
msgstr ""
"Der vorgeschlagene Name für `ord` Konfigurationsdateien ist `ord.yaml`, aber "
"Sie können jeden Dateinamen verwenden."

#: src\guides/moderation.md:28
msgid "Then pass the file to `--config` when starting the server:"
msgstr ""
"Dann übergeben Sie die Datei mit `--config`, wenn Sie den Server starten:"

#: src\guides/moderation.md:30
msgid "`ord --config ord.yaml server`"
msgstr "`ord --config ord.yaml server`"

#: src\guides/moderation.md:32
msgid ""
"Note that the `--config` option comes after `ord` but before the `server` "
"subcommand."
msgstr ""
"Beachten Sie, dass die Option `--config` nach `ord`, aber vor dem `server` "
"Unterbefehl kommt."

#: src\guides/moderation.md:35
msgid "`ord` must be restarted in to load changes to the config file."
msgstr ""
"`ord` muss neu gestartet werden, um Änderungen an der Konfigurationsdatei zu "
"laden."

#: src\guides/moderation.md:37
msgid "`ordinals.com`"
msgstr "`ordinals.com`"

#: src\guides/moderation.md:40
msgid ""
"The `ordinals.com` instances use `systemd` to run the `ord server` service, "
"which is called `ord`, with a config file located at `/var/lib/ord/ord.yaml`."
msgstr ""
"Die `ordinals.com`-Instanzen verwenden `systemd`, um den Dienst `ord server` "
"auszuführen, der `ord` genannt wird und eine Konfigurationsdatei unter `/var/"
"lib/ord/ord.yaml`."

#: src\guides/moderation.md:43
msgid "To hide an inscription on `ordinals.com`:"
msgstr "So verbergen Sie eine inscription auf „ordinals.com“:"

#: src\guides/moderation.md:45
msgid "SSH into the server"
msgstr "SSH in den Server"

#: src\guides/moderation.md:46
msgid "Add the inscription ID to `/var/lib/ord/ord.yaml`"
msgstr "Fügen Sie die inscription-ID zu `/var/lib/ord/ord.yaml`"

#: src\guides/moderation.md:47
msgid "Restart the service with `systemctl restart ord`"
msgstr "Starten Sie den Dienst mit `systemctl restart ord` neu an"

#: src\guides/moderation.md:48
msgid "Monitor the restart with `journalctl -u ord`"
msgstr "Überwachen Sie den Neustart mit `journalctl -u ord`"

#: src\guides/moderation.md:50
msgid ""
"Currently, `ord` is slow to restart, so the site will not come back online "
"immediately."
msgstr ""
"Derzeit startet `ord` langsam neu, sodass die Website nicht sofort wieder "
"online ist."

#: src\guides/reindexing.md:4
msgid ""
"Sometimes the `ord` database must be reindexed, which means deleting the "
"database and restarting the indexing process with either `ord index run` or "
"`ord server`. Reasons to reindex are:"
msgstr ""
"Manchmal muss die `ord`-Datenbank neu indexiert werden, was bedeutet, die "
"Datenbank zu löschen und den Indexierungsprozess mit `ord index run` oder "
"`ord server` erneut zu starten. Gründe für eine Neuerstellung des Index "
"können sein:"

#: src\guides/reindexing.md:8
msgid "A new major release of ord, which changes the database scheme"
msgstr "Eine neue Hauptversion von ord, die das Datenbankschema ändert"

#: src\guides/reindexing.md:9
msgid "The database got corrupted somehow"
msgstr "Die Datenbank wurde irgendwie beschädigt"

#: src\guides/reindexing.md:11
msgid ""
"The database `ord` uses is called [redb](https://github.com/cberner/redb), "
"so we give the index the default file name `index.redb`. By default we store "
"this file in different locations depending on your operating system."
msgstr ""
"Die von `ord` verwendete Datenbank heißt [redb](https://github.com/cberner/"
"redb), daher verwenden wir den Standarddateinamen `index.redb` für den "
"Index. Standardmäßig wird diese Datei an unterschiedlichen Speicherorten "
"abhängig von Ihrem Betriebssystem gespeichert."

#: src\guides/reindexing.md:15
msgid "Platform"
msgstr "Platform"

#: src\guides/reindexing.md:15
msgid "Value"
msgstr "Value"

#: src\guides/reindexing.md:17
msgid "Linux"
msgstr "Linux"

#: src\guides/reindexing.md:17
msgid "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"
msgstr "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"

#: src\guides/reindexing.md:17
msgid "/home/<USER>/.local/share/ord"
msgstr "/home/<USER>/.local/share/ord"

#: src\guides/reindexing.md:18
msgid "macOS"
msgstr "macOS"

#: src\guides/reindexing.md:18
msgid "`$HOME`/Library/Application Support/ord"
msgstr "`$HOME`/Library/Application Support/ord"

#: src\guides/reindexing.md:18
msgid "/Users/<USER>/Library/Application Support/ord"
msgstr "/Users/<USER>/Library/Application Support/ord"

#: src\guides/reindexing.md:19
msgid "Windows"
msgstr "Windows"

#: src\guides/reindexing.md:19
msgid "`{FOLDERID_RoamingAppData}`\\\\ord"
msgstr "`{FOLDERID_RoamingAppData}`\\\\ord"

#: src\guides/reindexing.md:19
msgid "C:\\Users\\<USER>\\AppData\\Roaming\\ord"
msgstr "C:\\Users\\<USER>\\AppData\\Roaming\\ord"

#: src\guides/reindexing.md:21
msgid ""
"So to delete the database and reindex on MacOS you would have to run the "
"following commands in the terminal:"
msgstr ""
"Um die Datenbank zu löschen und erneut zu indizieren, müssen Sie auf MacOS "
"die folgenden Befehle im Terminal ausführen:"

#: src\guides/reindexing.md:24
msgid ""
"```bash\n"
"rm ~/Library/Application Support/ord/index.redb\n"
"ord index run\n"
"```"
msgstr ""
"```bash\n"
"rm ~/Library/Application Support/ord/index.redb\n"
"ord index run\n"
"```"

#: src\guides/reindexing.md:29
msgid ""
"You can of course also set the location of the data directory yourself with "
"`ord --datadir <DIR> index run` or give it a specific filename and path "
"with `ord --index <FILENAME> index run`."
msgstr ""
"Sie können natürlich auch den Speicherort des Dataverzeichnisses selbst "
"festlegen, indem Sie `ord --datadir <DIR> index run` verwenden oder ihm "
"einen bestimmten Dateinamen und path mit `ord --index <DATEINAME> index run` "
"zuweisen."

#: src\bounties.md:1
msgid "Ordinal Bounty Hunting Hints"
msgstr "Hinweise zur Ordinal Bounty jagen"

#: src\bounties.md:4
msgid ""
"The `ord` wallet can send and receive specific satoshis. Additionally, "
"ordinal theory is extremely simple. A clever hacker should be able to write "
"code from scratch to manipulate satoshis using ordinal theory in no time."
msgstr ""
"Die `ord`-Wallet kann bestimmte Satoshis senden und empfangen. Außerdem ist "
"die Ordinaltheorie äußerst einfach. Ein geschickter Hacker sollte in "
"kürzester Zeit Code von Grund auf schreiben können, um Satoshis mithilfe der "
"Ordinaltheorie zu manipulieren."

#: src\bounties.md:8
msgid ""
"For more information about ordinal theory, check out the [FAQ](./faq.md) for "
"an overview, the [BIP](https://github.com/ordinals/ord/blob/master/bip."
"mediawiki) for the technical details, and the [ord repo](https://github.com/"
"ordinals/ord) for the `ord` wallet and block explorer."
msgstr ""
"Für weitere Informationen zur ordinaltheorie sieh dir das [FAQ](./faq.md) "
"für einen Überblick, das [BIP](https://github.com/ordinals/ord/blob/master/"
"bip.mediawiki) für die technischen Details und das [ord-Repository](https://"
"github.com/ordinals/ord) für die `ord` Wallet und den Block-Explorer an."

#: src\bounties.md:14
msgid ""
"Satoshi was the original developer of ordinal theory. However, he knew that "
"others would consider it heretical and dangerous, so he hid his knowledge, "
"and it was lost to the sands of time. This potent theory is only now being "
"rediscovered. You can help by researching rare satoshis."
msgstr ""
"Satoshi war der ursprüngliche Entwickler der Ordinaltheorie. Allerdings "
"wusste er, dass andere es als ketzerisch und gefährlich betrachten würden, "
"deshalb verbarg er sein Wissen, und es ging in den Wirren der Zeit verloren. "
"Diese mächtige Theorie wird erst jetzt wiederentdeckt. Du kannst helfen, "
"indem du seltene Satoshis erforschst.."

#: src\bounties.md:19
msgid "Good luck and godspeed!"
msgstr "Viel Glück und gute Fahrt!"

#: src\bounty/0.md:1
msgid "Ordinal Bounty 0"
msgstr "Ordinal Bounty 0"

#: src\bounty/0.md:4 src\bounty/1.md:4 src\bounty/2.md:4 src\bounty/3.md:4
msgid "Criteria"
msgstr "Kriterien"

#: src\bounty/0.md:7
msgid ""
"Send a sat whose ordinal number ends with a zero to the submission address:"
msgstr ""
"Senden Sie einen Sat, dessen Ordnungszahl mit einer Null endet, an die "
"Übermittlungsadresse:"

#: src\bounty/0.md:9
msgid "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"
msgstr "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"

#: src\bounty/0.md:11
msgid "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"
msgstr "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"

#: src\bounty/0.md:13
msgid "The sat must be the first sat of the output you send."
msgstr ""
"Der Sat muss der erste Sat der von Ihnen gesendeten Ausgabe (output) sein."

#: src\bounty/0.md:15 src\bounty/1.md:14 src\bounty/2.md:15 src\bounty/3.md:63
msgid "Reward"
msgstr "Belohnen"

#: src\bounty/0.md:18
msgid "100,000 sats"
msgstr "100,000 sats"

#: src\bounty/0.md:20 src\bounty/1.md:19 src\bounty/2.md:20 src\bounty/3.md:70
msgid "Submission Address"
msgstr "Einsendeadresse"

#: src\bounty/0.md:23
msgid ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/"
"address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"
msgstr ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/"
"address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"

#: src\bounty/0.md:25 src\bounty/1.md:24 src\bounty/2.md:25 src\bounty/3.md:75
msgid "Status"
msgstr "Status"

#: src\bounty/0.md:28
msgid ""
"Claimed by [@count_null](https://twitter.com/rodarmor/"
"status/1560793241473400833)!"
msgstr ""
"Beansprucht von [@count_null](https://twitter.com/rodarmor/"
"status/1560793241473400833)!"

#: src\bounty/1.md:1
msgid "Ordinal Bounty 1"
msgstr "Ordinal Belohnung 1"

#: src\bounty/1.md:7
msgid ""
"The transaction that submits a UTXO containing the oldest sat, i.e., that "
"with the lowest number, amongst all submitted UTXOs will be judged the "
"winner."
msgstr ""
"Die Transaktion, die eine UTXO enthält, die den ältesten sat repräsentiert, "
"das heißt denjenigen mit der niedrigsten Zahl, unter allen eingereichten "
"UTXOs, wird als gewinner betrachtet."

#: src\bounty/1.md:10
msgid ""
"The bounty is open for submissions until block 753984—the first block of "
"difficulty adjustment period 374. Submissions included in block 753984 or "
"later will not be considered."
msgstr ""
"Die belohnung ist für Einreichungen bis zum Block 753984— geöffnet dem "
"ersten Block des Schwierigkeitsanpassungszeitraums 374. Einreichungen, die "
"in Block 753984 oder später enthalten sind, werden nicht berücksichtigt."

#: src\bounty/1.md:17
msgid "200,000 sats"
msgstr "200,000 sats"

#: src\bounty/1.md:22
msgid ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/"
"address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"
msgstr ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/"
"address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"

#: src\bounty/1.md:27
msgid ""
"Claimed by [@ordinalsindex](https://twitter.com/rodarmor/"
"status/1569883266508853251)!"
msgstr ""
"Eingefordert von [@ordinalsindex](https://twitter.com/rodarmor/"
"status/1569883266508853251)!"

#: src\bounty/2.md:1
msgid "Ordinal Bounty 2"
msgstr "Ordinal Belohnung 2"

#: src\bounty/2.md:7
msgid "Send an "
msgstr "Senden an "

#: src\bounty/2.md:7
msgid "uncommon"
msgstr "ungewöhnlich"

#: src\bounty/2.md:7
msgid " sat to the submission address:"
msgstr " sats an die Einreichungsadresse:"

#: src\bounty/2.md:9
msgid "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"
msgstr "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"

#: src\bounty/2.md:11
msgid "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"
msgstr "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"

#: src\bounty/2.md:13
msgid ""
"Confirm that the submission address has not received transactions before "
"submitting your entry. Only the first successful submission will be rewarded."
msgstr ""
"Bestätigen Sie, dass die Einreichungsadresse keine Transaktionen erhalten "
"hat, bevor Sie Ihren Beitrag einreichen. Nur die erste erfolgreiche "
"Einreichung wird belohnt."

#: src\bounty/2.md:18
msgid "300,000 sats"
msgstr "300,000 sats"

#: src\bounty/2.md:23
msgid ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/"
"address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"
msgstr ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/"
"address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"

#: src\bounty/2.md:28
msgid ""
"Claimed by [@utxoset](https://twitter.com/rodarmor/"
"status/1582424455615172608)!"
msgstr ""
"Eingefordert von [@utxoset](https://twitter.com/rodarmor/"
"status/1582424455615172608)!"

#: src\bounty/3.md:1
msgid "Ordinal Bounty 3"
msgstr "Ordinal Belohnung 3"

#: src\bounty/3.md:7
msgid ""
"Ordinal bounty 3 has two parts, both of which are based on _ordinal names_. "
"Ordinal names are a modified base-26 encoding of ordinal numbers. To avoid "
"locking short names inside the unspendable genesis block coinbase reward, "
"ordinal names get _shorter_ as the ordinal number gets _longer_. The name of "
"sat 0, the first sat to be mined is `nvtdijuwxlp` and the name of sat "
"2,099,999,997,689,999, the last sat to be mined, is `a`."
msgstr ""
"\"Ordinal Belohnung 3\" hat zwei Teile, die beide auf _ordinalen Namen_ "
"basieren. Ordinale Namen sind eine modifizierte Basis-26-Codierung von "
"ordinal Zahlen. Um kurze Namen nicht im nicht ausgebuchten Genesis-Block-"
"Coinbase-Reward einzusperren, werden ordinale Namen _kürzer_, je länger die "
"ordinal Nummer wird. Der Name von Sat 0, dem ersten Sat, der gemint wurde, "
"lautet `nvtdijuwxlp`, und der Name von Sat 2.099.999.997.689.999, dem "
"letzten geminten sat, lautet `a`."

#: src\bounty/3.md:14
msgid ""
"The bounty is open for submissions until block 840000—the first block after "
"the fourth halvening. Submissions included in block 840000 or later will not "
"be considered."
msgstr ""
"Die Belohnung ist für Einreichungen bis zum Block 840.000 geöffnet, das ist "
"der erste Block nach der vierten Halbierung. Einreichungen, die in Block "
"840.000 oder später enthalten sind, werden nicht berücksichtigt."

#: src\bounty/3.md:18
msgid ""
"Both parts use [frequency.tsv](frequency.tsv), a list of words and the "
"number of times they occur in the [Google Books Ngram dataset](http://"
"storage.googleapis.com/books/ngrams/books/datasetsv2.html). filtered to only "
"include the names of sats which will have been mined by the end of the "
"submission period, that appear at least 5000 times in the corpus."
msgstr ""
"Beide Teile verwenden [frequency.tsv](frequency.tsv), eine Liste von Wörtern "
"und der Anzahl ihrer Vorkommen im [Google Books Ngram-Datensatz](http://"
"storage.googleapis.com/books/ngrams/books/datasetsv2.html), wobei nur die "
"Namen der Sats eingeschlossen sind, die bis zum Ende des "
"Einreichungszeitraums abgebaut wurden und mindestens 5000 Mal im Korpus "
"vorkommen."

#: src\bounty/3.md:24
msgid ""
"`frequency.tsv` is a file of tab-separated values. The first column is the "
"word, and the second is the number of times it appears in the corpus. The "
"entries are sorted from least-frequently occurring to most-frequently "
"occurring."
msgstr ""
"`frequency.tsv` ist eine Datei mit tabulatorgetrennten Werten. Die erste "
"Spalte ist das Wort, und die zweite Spalte zeigt an, wie oft es im Korpus "
"vorkommt. Die Einträge sind von am wenigsten häufig vorkommend bis am "
"häufigsten vorkommend sortiert."

#: src\bounty/3.md:29
msgid ""
"`frequency.tsv` was compiled using [this program](https://github.com/casey/"
"onegrams)."
msgstr ""
"`frequency.tsv` wurde mit kompiliert [dieses Programm](https://github.com/"
"casey/onegrams)."

#: src\bounty/3.md:32
msgid ""
"To search an `ord` wallet for sats with a name in `frequency.tsv`, use the "
"following [`ord`](https://github.com/ordinals/ord) command:"
msgstr ""
"Um in einem `ord` wallet nach sats mit einem Namen in `frequency.tsv` zu "
"suchen, verwenden Sie den folgenden [`ord`](https://github.com/ordinals/ord) "
"befehl:"

#: src\bounty/3.md:35
msgid ""
"```\n"
"ord wallet sats --tsv frequency.tsv\n"
"```"
msgstr ""
"```\n"
"ord wallet sats --tsv frequency.tsv\n"
"```"

#: src\bounty/3.md:39
msgid ""
"This command requires the sat index, so `--index-sats` must be passed to ord "
"when first creating the index."
msgstr ""
"Dieser befehl erfordert den sat-Index, daher muss `--index-sats` an `ord` "
"übergeben werden, wenn der Index zum ersten mal erstellt wird."

#: src\bounty/3.md:42
msgid "Part 0"
msgstr "Teil 0"

#: src\bounty/3.md:44
msgid "_Rare sats pair best with rare words._"
msgstr "_Seltene sats passen am besten zu seltenen Wörtern._"

#: src\bounty/3.md:46
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the lowest number of occurrences in `frequency.tsv` shall be the winner "
"of part 0."
msgstr ""
"Die Transaktion, die den UTXO einreicht, der den Sat enthält, dessen Name in "
"`frequency.tsv` mit der niedrigsten Anzahl von Vorkommnissen erscheint, wird "
"der Gewinner von Teil 0 sein."

#: src\bounty/3.md:50
msgid "Part 1"
msgstr "Teil 1"

#: src\bounty/3.md:52
msgid "_Popularity is the font of value._"
msgstr "_Beliebtheit ist die Quelle des Werts._"

#: src\bounty/3.md:54
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the highest number of occurrences in `frequency.tsv` shall be the "
"winner of part 1."
msgstr ""
"Die Transaktion, die die UTXO mit dem Namen des Sats enthält, der in "
"`frequency.tsv` am häufigsten vorkommt, wird der Gewinner von Teil 1 sein."

#: src\bounty/3.md:58
msgid "Tie Breaking"
msgstr "Entscheidung bei Unentschieden"

#: src\bounty/3.md:60
msgid ""
"In the case of a tie, where two submissions occur with the same frequency, "
"the earlier submission shall be the winner."
msgstr ""
"Im Falle eines Unentschiedens, bei dem zwei Einsendungen mit der gleichen "
"Häufigkeit erfolgen, geht die frühere Einsendung als Sieger hervor."

#: src\bounty/3.md:66
msgid "Part 0: 200,000 sats"
msgstr "Teil 0: 200,000 sats"

#: src\bounty/3.md:67
msgid "Part 1: 200,000 sats"
msgstr "Teil 1: 200,000 sats"

#: src\bounty/3.md:68
msgid "Total: 400,000 sats"
msgstr "Gesamt: 400,000 sats"

#: src\bounty/3.md:73
msgid ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/"
"address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"
msgstr ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/"
"address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"

#: src\bounty/3.md:78
msgid "Unclaimed!"
msgstr "Unbeansprucht!"
