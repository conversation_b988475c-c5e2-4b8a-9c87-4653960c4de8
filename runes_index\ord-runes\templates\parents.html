<h1><a href=/inscription/{{ self.id }}>Inscription {{ self.number }}</a> Parents</h1>
%% if self.parents.is_empty() {
<h3>No parents</h3>
%% } else {
<div class=thumbnails>
%% for id in &self.parents {
  {{ Iframe::thumbnail(*id) }}
%% }
</div>
<div class=center>
%% if let Some(prev_page) = &self.prev_page {
  <a class=prev href=/parents/{{ self.id }}/{{ prev_page }}>prev</a>
%% } else {
prev
%% }
%% if let Some(next_page) = &self.next_page {
  <a class=next href=/parents/{{ self.id }}/{{ next_page }}>next</a>
%% } else {
next
%% }
</div>
%% }
