<h1>Inscription {{ self.number }}</h1>
<div class=inscription>
%% if let Some(previous) = self.previous {
<a class=prev href=/inscription/{{previous}}>❮</a>
%% } else {
<div>❮</div>
%% }
{{Iframe::main(self.id)}}
%% if let Some(next) = self.next {
<a class=next href=/inscription/{{next}}>❯</a>
%% } else {
<div>❯</div>
%% }
</div>
<dl>
%% if !&self.parents.is_empty() {
  <dt>parents</dt>
  <dd>
    <div class=thumbnails>
%% for parent in &self.parents {
      {{Iframe::thumbnail(*parent)}}
%% }
    </div>
    <div class=center>
      <a href=/parents/{{self.id}}>all</a>
    </div>
  </dd>
%% }
%% if !self.children.is_empty() {
  <dt>children</dt>
  <dd>
    <div class=thumbnails>
%% for id in &self.children {
      {{Iframe::thumbnail(*id)}}
%% }
    </div>
    <div class=center>
      <a href=/children/{{self.id}}>all</a>
    </div>
  </dd>
%% }
  <dt>id</dt>
  <dd class=monospace>{{ self.id }}</dd>
%% if self.charms != 0 {
  <dt>charms</dt>
  <dd>
%% for charm in Charm::ALL {
%%   if charm.is_set(self.charms) {
    <span title={{charm}}>{{charm.icon()}}</span>
%%   }
%% }
  </dd>
%% }
%% if let Some(metadata) = self.inscription.metadata() {
  <dt>metadata</dt>
  <dd>
    {{ Trusted(MetadataHtml(&metadata)) }}
  </dd>
%% }
%% if let Some(output) = &self.output {
%% if let Ok(address) = self.chain.address_from_script(&output.script_pubkey ) {
  <dt>address</dt>
  <dd class=monospace>{{ address }}</dd>
%% }
  <dt>value</dt>
  <dd>{{ output.value }}</dd>
%% }
%% if let Some(sat) = self.sat {
  <dt>sat</dt>
  <dd><a href=/sat/{{sat}}>{{sat}}</a></dd>
%% }
%% if let Some(metaprotocol) = self.inscription.metaprotocol() {
  <dt>metaprotocol</dt>
  <dd>{{ metaprotocol }}</dd>
%% }
%% if self.inscription.content_length().is_some() || self.inscription.delegate().is_some() {
%% if let Some(delegate) = self.inscription.delegate() {
  <dt>delegate</dt>
  <dd><a href=/inscription/{{ delegate }}>{{ delegate }}</a></dd>
%% }
  <dt>preview</dt>
  <dd><a href=/preview/{{self.id}}>link</a></dd>
  <dt>content</dt>
  <dd><a href=/content/{{self.id}}>link</a></dd>
%% if let Some(content_length) = self.inscription.content_length() {
  <dt>content length</dt>
  <dd>{{ content_length }} bytes</dd>
%% }
%% }
%% if let Some(content_type) = self.inscription.content_type() {
  <dt>content type</dt>
  <dd>{{ content_type }}</dd>
%% }
%% if let Some(content_encoding) = self.inscription.content_encoding() {
  <dt>content encoding</dt>
  <dd>{{ content_encoding.to_str().unwrap_or_default() }}</dd>
%% }
  <dt>timestamp</dt>
  <dd><time>{{ self.timestamp }}</time></dd>
  <dt>height</dt>
  <dd><a href=/block/{{ self.height }}>{{ self.height }}</a></dd>
  <dt>fee</dt>
  <dd>{{ self.fee }}</dd>
  <dt>reveal transaction</dt>
  <dd><a class=monospace href=/tx/{{ self.id.txid }}>{{ self.id.txid }}</a></dd>
  <dt>location</dt>
  <dd class=monospace>{{ self.satpoint }}</dd>
  <dt>output</dt>
  <dd><a class=monospace href=/output/{{ self.satpoint.outpoint }}>{{ self.satpoint.outpoint }}</a></dd>
  <dt>offset</dt>
  <dd>{{ self.satpoint.offset }}</dd>
  <dt>ethereum teleburn address</dt>
  <dd>{{ teleburn::Ethereum::from(self.id) }}</dd>
%% if let Some(rune) = self.rune {
  <dt>rune</dt>
  <dd><a href=/rune/{{ rune }}>{{ rune }}</a></dd>
%% }
</dl>
