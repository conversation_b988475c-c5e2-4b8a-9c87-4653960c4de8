msgid ""
msgstr ""
"Project-Id-Version: Manual da Teoria Ordinal\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: 2023-09-26 10:13-0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> @namcio<PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: src/SUMMARY.md:2
#: src/introduction.md:1
msgid "Introduction"
msgstr "Introdução"

#: src/SUMMARY.md:3
msgid "Overview"
msgstr "Descrição Geral"

#: src/SUMMARY.md:4
#: src/digital-artifacts.md:1
msgid "Digital Artifacts"
msgstr "Artefatos Digitais"

#: src/SUMMARY.md:5
#: src/SUMMARY.md:13
#: src/overview.md:221
#: src/inscriptions.md:1
msgid "Inscriptions"
msgstr "Inscrições"

#: src/SUMMARY.md:6
#: src/inscriptions/provenance.md:1
msgid "Provenance"
msgstr "Procedência"

#: src/SUMMARY.md:7
#: src/inscriptions/recursion.md:1
msgid "Recursion"
msgstr "Recursão"

#: src/SUMMARY.md:8
msgid "FAQ"
msgstr "Perguntas Frequentes"

#: src/SUMMARY.md:9
msgid "Contributing"
msgstr "Contribuir"

#: src/SUMMARY.md:10
#: src/donate.md:1
msgid "Donate"
msgstr "Doações"

#: src/SUMMARY.md:11
msgid "Guides"
msgstr "Guias"

#: src/SUMMARY.md:12
msgid "Explorer"
msgstr "Explorador"

#: src/SUMMARY.md:14
#: src/guides/sat-hunting.md:1
msgid "Sat Hunting"
msgstr "Caçando Sats"

#: src/SUMMARY.md:15
#: src/guides/collecting.md:1
msgid "Collecting"
msgstr "Colecionando"

#: src/SUMMARY.md:16
#: src/guides/sat-hunting.md:239
msgid "Sparrow Wallet"
msgstr "Carteira Sparrow"

#: src/SUMMARY.md:17
#: src/guides/testing.md:1
msgid "Testing"
msgstr "Testando"

#: src/SUMMARY.md:18
#: src/guides/moderation.md:1
msgid "Moderation"
msgstr "Moderação"

#: src/SUMMARY.md:19
#: src/guides/reindexing.md:1
msgid "Reindexing"
msgstr "Reindexação"

#: src/SUMMARY.md:20
msgid "Bounties"
msgstr "Recompensas"

#: src/SUMMARY.md:21
msgid "Bounty 0: 100,000 sats Claimed!"
msgstr "Recompensa 0: 100,000 sats reivindicados!"

#: src/SUMMARY.md:22
msgid "Bounty 1: 200,000 sats Claimed!"
msgstr "Recompensa 1: 200,000 sats reivindicados!"

#: src/SUMMARY.md:23
msgid "Bounty 2: 300,000 sats Claimed!"
msgstr "Recompensa 2: 300,000 sats reivindicados!"

#: src/SUMMARY.md:24
msgid "Bounty 3: 400,000 sats"
msgstr "Recompensa 3: 400,000 sats"

#: src/introduction.md:4
msgid ""
"This handbook is a guide to ordinal theory. Ordinal theory concerns itself "
"with satoshis, giving them individual identities and allowing them to be "
"tracked, transferred, and imbued with meaning."
msgstr ""
"Este manual é um guia para a teoria ordinal. A teoria ordinal se preocupa "
"com satoshis, dando-lhes identidades individuais e permitindo que sejam "
"rastreadoes, transferidos e imbuídos de significado. "

#: src/introduction.md:8
msgid ""
"Satoshis, not bitcoin, are the atomic, native currency of the Bitcoin "
"network. One bitcoin can be sub-divided into 100,000,000 satoshis, but no "
"further."
msgstr ""
"Os satoshis, e não os bitcoins, são as moedas atômicas nativas da rede "
"Bitcoin. Um bitcoin pode ser subdividido em 100 milhões de satoshis, mas não "
"mais do que isso."

#: src/introduction.md:11
msgid ""
"Ordinal theory does not require a sidechain or token aside from Bitcoin, and "
"can be used without any changes to the Bitcoin network. It works right now."
msgstr ""
"A teoria ordinal não requer uma sidechain ou um token além do bitcoin, e "
"e pode ser usada sem quaisquer alterações na rede Bitcoin. Ela functiona agora mesmo."

#: src/introduction.md:14
msgid ""
"Ordinal theory imbues satoshis with numismatic value, allowing them to be "
"collected and traded as curios."
msgstr ""
"A teoria ordinal confere aos satoshis valor numismático, permitindo "
"que sejam coletados e comercializados como itens de colectionador."

#: src/introduction.md:17
msgid ""
"Individual satoshis can be inscribed with arbitrary content, creating unique "
"Bitcoin-native digital artifacts that can be held in Bitcoin wallets and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"Satoshis individuais podem ser inscritos com conteúdo arbitrário, criando "
"artefatos digitais únicos e nativos do Bitcoin que podem que podem ser "
"mantidos em carteiras de bitcoin e transferidos usando transações de Bitcoin. "
"Essas inscrições são tão duráveis, imutáveis, seguras e descentralizadas quanto "
"o próprio Bitcoin."

#: src/introduction.md:22
msgid ""
"Other, more unusual use-cases are possible: off-chain colored-coins, public "
"key infrastructure with key rotation, a decentralized replacement for the "
"DNS. For now though, such use-cases are speculative, and exist only in the "
"minds of fringe ordinal theorists."
msgstr ""
"Outros casos de uso mais incomuns são possíveis: moedas coloridas (colored-coins) "
"fora da cadeia, infraestrutura de chave pública com rotação de chaves, "
"um substituto descentralizado para o DNS. Por enquanto, porém, tais casos de uso "
"são especulativos e existem apenas nas mentes dos teóricos ordinais."

#: src/introduction.md:27
msgid "For more details on ordinal theory, see the [overview](overview.md)."
msgstr "Para obter mais detalhes sobre a teoria ordinal, consulte a [visão geral](overview.md)."

#: src/introduction.md:29
msgid "For more details on inscriptions, see [inscriptions](inscriptions.md)."
msgstr "Para obter mais detalhes sobre as inscrições, consulte [inscrições](inscriptions.md)."

#: src/introduction.md:31
msgid ""
"When you're ready to get your hands dirty, a good place to start is with "
"[inscriptions](guides/inscriptions.md), a curious species of digital "
"artifact enabled by ordinal theory."
msgstr ""
"Quando estiver pronto para pôr a mão na obra, um bom lugar para começar "
"é na página de [inscrições](guides/inscriptions.md), um tipo de artefato "
"digital possibilitado pela teoria ordinal."

#: src/introduction.md:35
msgid "Links"
msgstr "Links"

#: src/introduction.md:38
msgid "[GitHub](https://github.com/ordinals/ord/)"
msgstr "[GitHub](https://github.com/ordinals/ord/)"

#: src/introduction.md:39
msgid "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src/introduction.md:40
msgid "[Discord](https://discord.gg/ordinals)"
msgstr "[Discord](https://discord.gg/ordinals)"

#: src/introduction.md:41
msgid "[Open Ordinals Institute Website](https://ordinals.org/)"
msgstr "[Site do Instituto Open Ordinals](https://ordinals.org/)"

#: src/introduction.md:42
msgid "[Open Ordinals Institute X](https://x.com/ordinalsorg)"
msgstr "[X (Twitter) do Instituto Open Ordinals](https://x.com/ordinalsorg)"

#: src/introduction.md:43
msgid "[Mainnet Block Explorer](https://ordinals.com)"
msgstr "[Explorador de Blocos Mainnet](https://ordinals.com)"

#: src/introduction.md:44
msgid "[Signet Block Explorer](https://signet.ordinals.com)"
msgstr "[Explorador de Blocos Signet](https://signet.ordinals.com)"

#: src/introduction.md:46
msgid "Videos"
msgstr "Vídeos"

#: src/introduction.md:49
msgid ""
"[Ordinal Theory Explained: Satoshi Serial Numbers and NFTs on "
"Bitcoin](https://www.youtube.com/watch?v=rSS0O2KQpsI)"
msgstr ""
"[Teoria Ordinal Explicada: Números de Série de Satoshis e NFTs no "
"Bitcoin](https://www.youtube.com/watch?v=rSS0O2KQpsI)"

#: src/introduction.md:50
msgid ""
"[Ordinals Workshop with "
"Rodarmor](https://www.youtube.com/watch?v=MC_haVa6N3I)"
msgstr "[Workshop de Ordinals com "
"Rodarmor](https://www.youtube.com/watch?v=MC_haVa6N3I&ab_channel=PlebLab)"

#: src/introduction.md:51
msgid ""
"[Ordinal Art: Mint Your own NFTs on Bitcoin w/ "
"@rodarmor](https://www.youtube.com/watch?v=j5V33kV3iqo)"
msgstr ""
"[Arte Ordinal: Crie suas próprias NFTs no Bitcoin com "
"@rodarmor](https://www.youtube.com/watch?v=j5V33kV3iqo)"

#: src/overview.md:1
msgid "Ordinal Theory Overview"
msgstr "Visão Geral da Teoria Ordinal"

#: src/overview.md:4
msgid ""
"Ordinals are a numbering scheme for satoshis that allows tracking and "
"transferring individual sats. These numbers are called [ordinal "
"numbers](https://ordinals.com). Satoshis are numbered in the order in which "
"they're mined, and transferred from transaction inputs to transaction "
"outputs first-in-first-out. Both the numbering scheme and the transfer "
"scheme rely on _order_, the numbering scheme on the _order_ in which "
"satoshis are mined, and the transfer scheme on the _order_ of transaction "
"inputs and outputs. Thus the name, _ordinals_."
msgstr ""
"Os Ordinals, ou Ordinais, são um esquema de numeração para satoshis que "
"permite rastrear e transferir sats individuais. Esses números são chamados "
"[números ordinais](https://ordinals.com/). Os satoshis são numerados na ordem "
"em que são minerados, e quando transferidos das entradas de uma transação para as saídas,"
"nos baseamos no modelo FIFO (First In, First Out). Tanto o esquema de numeração quanto "
"o esquema de transferência dependem da _ordem_; o esquema de numeração se baseia na _ordem_ "
"em que os satoshis são minerados, enquanto que o esquema de transferência se baseia na _ordem_ "
"das entradas e saídas das transações. Daí o nome, _Ordinals_."

#: src/overview.md:13
msgid ""
"Technical details are available in [the "
"BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)."
msgstr ""
"Os detalhes técnicos estão disponíveis no "
"[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki)."

#: src/overview.md:16
msgid ""
"Ordinal theory does not require a separate token, another blockchain, or any "
"changes to Bitcoin. It works right now."
msgstr ""
"A teoria ordinal funciona não requer um token separado, outra blockchain, ou "
"qualquer mudança ao Bitcoin. Ela funciona agora mesmo."

#: src/overview.md:19
msgid "Ordinal numbers have a few different representations:"
msgstr "Os números ordinais têm várias representações:"

#: src/overview.md:21
msgid ""
"_Integer notation_: "
"[`2099994106992659`](https://ordinals.com/sat/2099994106992659) The ordinal "
"number, assigned according to the order in which the satoshi was mined."
msgstr "_Notação de Inteiro (Integer)_: "
"[`2099994106992659`](https://ordinals.com/sat/2099994106992659) O número "
"ordinal, atribuído de acordo com a ordem em que o satoshi foi minerado."

#: src/overview.md:26
msgid ""
"_Decimal notation_: "
"[`3891094.16797`](https://ordinals.com/sat/3891094.16797) The first number "
"is the block height in which the satoshi was mined, the second the offset of "
"the satoshi within the block."
msgstr ""
"_Notação decimal_: "
"[` 3891094.16797`](https://ordinals.com/sat/3891094.16797) O primeiro número "
"é a altura do bloco em que o satoshi foi minerado, o segundo é o deslocamento "
"do satoshi dentro do bloco."

#: src/overview.md:31
msgid ""
"_Degree notation_: "
"[`3°111094′214″16797‴`](https://ordinals.com/sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). "
"We'll get to that in a moment."
msgstr ""
"_Notação de grau_: "
"[`3°111094′214″16797‴`](https://ordinals.com/sat/3%C2%B0111094%E2%80%B2214%E2%80%B316797%E2%80%B4). "
"Falaremos sobre isso mais tarde."


#: src/overview.md:35
msgid ""
"_Percentile notation_: "
"[`99.**************%`](https://ordinals.com/sat/99.**************%25) . The "
"satoshi's position in Bitcoin's supply, expressed as a percentage."
msgstr ""
"_Notação percentil_: "
"[`99.**************%`](https://ordinals.com/sat/99.**************%25) . A "
"posição do satoshi na oferta total de bitcoin, expressa em porcentagem."

#: src/overview.md:39
msgid ""
"_Name_: [`satoshi`](https://ordinals.com/sat/satoshi). An encoding of the "
"ordinal number using the characters `a` through `z`."
msgstr ""
"_Nome_: [`satoshi`](https://ordinals.com/sat/satoshi). Uma codificação do "
"número ordinal usando os caracteres `a` até `z`."

#: src/overview.md:42
msgid ""
"Arbitrary assets, such as NFTs, security tokens, accounts, or stablecoins "
"can be attached to satoshis using ordinal numbers as stable identifiers."
msgstr ""
"AAtivos arbitrários, como NFTs, tokens de segurança, contas ou stablecoins "
"podem ser anexados a satoshis usando números ordinais como identificadores estáveis."

#: src/overview.md:45
msgid ""
"Ordinals is an open-source project, developed [on "
"GitHub](https://github.com/ordinals/ord). The project consists of a BIP "
"describing the ordinal scheme, an index that communicates with a Bitcoin "
"Core node to track the location of all satoshis, a wallet that allows making "
"ordinal-aware transactions, a block explorer for interactive exploration of "
"the blockchain, functionality for inscribing satoshis with digital "
"artifacts, and this manual."
msgstr ""
"Ordinals é um projeto de código aberto, desenvolvido [no "
"GitHub](https://github.com/ordinals/ord). O projeto consiste em um BIP "
"que descreve o esquema ordinal, um índice que se comunica com um nó "
"Bitcoin Core para rastrear a localização de todos os satoshis, uma carteira que permite "
"fazer transações com reconhecimento de Ordinals, um explorador de blocos para "
"exploração interativa da blockchain, funcionalidade para inscrever artefatos digitais "
"nos satoshis e este manual."

#: src/overview.md:52
msgid "Rarity"
msgstr "Raridade"

#: src/overview.md:55
msgid ""
"Humans are collectors, and since satoshis can now be tracked and "
"transferred, people will naturally want to collect them. Ordinal theorists "
"can decide for themselves which sats are rare and desirable, but there are "
"some hints…"
msgstr ""
"Os humanos são colecionadores e, como os satoshis agora podem ser rastreados e "
“transferidos, as pessoas naturalmente desejarão coletá-los. Teóricos de Ordinals "
"podem decidir por si mesmos quais sats são raros e desejáveis, mas existem"
"algumas dicas..."

#: src/overview.md:59
msgid ""
"Bitcoin has periodic events, some frequent, some more uncommon, and these "
"naturally lend themselves to a system of rarity. These periodic events are:"
msgstr ""
“O Bitcoin tem eventos periódicos, alguns frequentes, outros mais incomuns, e estes "
"naturalmente se prestam a um sistema de raridade. Esses eventos periódicos são:"

#: src/overview.md:62
msgid ""
"_Blocks_: A new block is mined approximately every 10 minutes, from now "
"until the end of time."
msgstr ""
"_Blocos_: Um novo bloco é minerado aproximadamente a cada 10 minutos, a partir de agora "
"e até o fim dos tempos."

#: src/overview.md:65
msgid ""
"_Difficulty adjustments_: Every 2016 blocks, or approximately every two "
"weeks, the Bitcoin network responds to changes in hashrate by adjusting the "
"difficulty target which blocks must meet in order to be accepted."
msgstr ""
"_Ajustes de dificuldade_: A cada 2016 blocos, ou aproximadamente a cada duas "
"semanas, a rede Bitcoin responde às mudanças no hashrate ajustando o "
"alvo de dificuldade que os blocos devem atingir para serem aceitos."

#: src/overview.md:69
msgid ""
"_Halvings_: Every 210,000 blocks, or roughly every four years, the amount of "
"new sats created in every block is cut in half."
msgstr ""
"_Halvings_: A cada 210.000 blocos, ou aproximadamente a cada quatro anos, a quantidade de "
"novos sats criados em cada bloco é cortada pela metade."

#: src/overview.md:72
msgid ""
"_Cycles_: Every six halvings, something magical happens: the halving and the "
"difficulty adjustment coincide. This is called a conjunction, and the time "
"period between conjunctions a cycle. A conjunction occurs roughly every 24 "
"years. The first conjunction should happen sometime in 2032."
msgstr ""
"_Ciclos_: A cada seis halvings, algo mágico acontece: o halving e o "
"ajuste de dificuldade coincidem. Isso é chamado de conjunção, e o período "
"de tempo entre conjunções, um ciclo. Uma conjunção ocorre aproximadamente a cada 24 "
"anos. A primeira conjunção deve acontecer em algum momento em 2032."

#: src/overview.md:77
msgid "This gives us the following rarity levels:"
msgstr "Isso nos dá os seguintes níveis de raridade:"

#: src/overview.md:79
msgid "`common`: Any sat that is not the first sat of its block"
msgstr "`comum`: Qualquer sat que não seja o primeiro de seu bloco"

#: src/overview.md:80
msgid "`uncommon`: The first sat of each block"
msgstr "`incomum`: O primeiro sat de cada bloco"

#: src/overview.md:81
msgid "`rare`: The first sat of each difficulty adjustment period"
msgstr "`raro`: O primeiro sat de cada período de ajuste de dificuldade"

#: src/overview.md:82
msgid "`epic`: The first sat of each halving epoch"
msgstr "`épico`: O primeiro satoshi de cada época do halving"

#: src/overview.md:83
msgid "`legendary`: The first sat of each cycle"
msgstr "`lendário`: O primeiro sat de cada ciclo"

#: src/overview.md:84
msgid "`mythic`: The first sat of the genesis block"
msgstr "`mítico`: O primeiro sat do bloco Gênesis"

#: src/overview.md:86
msgid ""
"Which brings us to degree notation, which unambiguously represents an "
"ordinal number in a way that makes the rarity of a satoshi easy to see at a "
"glance:"
msgstr ""
"O que nos leva à notação de grau, que representa inequivocamente um "
"número ordinal de uma forma que torna fácil ver a raridade de um satoshi:"

#: src/overview.md:89
msgid ""
"```\n"
"A°B′C″D‴\n"
"│ │ │ ╰─ Index of sat in the block\n"
"│ │ ╰─── Index of block in difficulty adjustment period\n"
"│ ╰───── Index of block in halving epoch\n"
"╰─────── Cycle, numbered starting from 0\n"
"```"
msgstr ""
"```\n"
"A°B′C″D‴\n"
"│ │ │ ╰─ Índice do sat no bloco\n"
"│ │ ╰─── Índice do bloco no período de ajuste de dificuldade\n"
"│ ╰───── Índice do bloco na época do halving\n"
"╰─────── Ciclo, numerados começando em zero\n"
"```"

#: src/overview.md:97
msgid ""
"Ordinal theorists often use the terms \"hour\", \"minute\", \"second\", and "
"\"third\" for _A_, _B_, _C_, and _D_, respectively."
msgstr ""
"Os teóricos de Ordinals costumam usar os termos \"hora\", \"minuto\", \"segundo\" e "
"\"terceiro\" para _A_, _B_, _C_ e _D_, respectivamente."

#: src/overview.md:100
msgid "Now for some examples. This satoshi is common:"
msgstr "Agora vejamos alguns exemplos. Este satoshi é comum:"

#: src/overview.md:102
msgid ""
"```\n"
"1°1′1″1‴\n"
"│ │ │ ╰─ Not first sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── Not first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″1‴\n"
"│ │ │ ╰─ Não é o primeiro sat do bloco\n"
"│ │ ╰─── Não é o primeiro bloco no período de ajuste de dificuldade\n"
"│ ╰───── Não é o primeiro bloco na época do halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:111
msgid "This satoshi is uncommon:"
msgstr "Este satoshi é incomum:"

#: src/overview.md:113
msgid ""
"```\n"
"1°1′1″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── Not first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″0‴\n"
"│ │ │ ╰─ Primeiro sat do bloco\n"
"│ │ ╰─── Não é o primeiro bloco no período de ajuste de dificuldade\n"
"│ ╰───── Não é o primeiro bloco na época do halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:121
msgid "This satoshi is rare:"
msgstr "Este satoshi é raro:"

#: src/overview.md:123
msgid ""
"```\n"
"1°1′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── Not the first block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′0″0‴\n"
"│ │ │ ╰─ Primeiro sat do bloco\n"
"│ │ ╰─── Primeiro bloco no período de ajuste de dificuldade\n"
"│ ╰───── Não é o primeiro bloco na época do halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:131
msgid "This satoshi is epic:"
msgstr "Este satoshi é épico:"

#: src/overview.md:133
msgid ""
"```\n"
"1°0′1″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── Not first block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°0′1″0‴\n"
"│ │ │ ╰─ Primeiro sat do bloco\n"
"│ │ ╰─── Não é o primeiro bloco no período de ajuste de dificuldade\n"
"│ ╰───── Primeiro bloco na época do halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:141
msgid "This satoshi is legendary:"
msgstr "Este satoshi é lendário:"

#: src/overview.md:143
msgid ""
"```\n"
"1°0′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°0′0″0‴\n"
"│ │ │ ╰─ Primeiro sat do bloco\n"
"│ │ ╰─── Primeiro bloco no período de ajuste de dificuldade\n"
"│ ╰───── Primeiro bloco na época do halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:151
msgid "And this satoshi is mythic:"
msgstr "Este satoshi é mítico:"

#: src/overview.md:153
msgid ""
"```\n"
"0°0′0″0‴\n"
"│ │ │ ╰─ First sat in block\n"
"│ │ ╰─── First block in difficulty adjustment period\n"
"│ ╰───── First block in halving epoch\n"
"╰─────── First cycle\n"
"```"
msgstr ""
"```\n"
"0°0′0″0‴\n"
"│ │ │ ╰─ Primeiro sat do bloco\n"
"│ │ ╰─── Primeiro bloco no período de ajuste de dificuldade\n"
"│ ╰───── Primeiro bloco na época do halving\n"
"╰─────── Primeiro ciclo\n"
"```"


#: src/overview.md:161
msgid ""
"If the block offset is zero, it may be omitted. This is the uncommon satoshi "
"from above:"
msgstr ""
"Se o deslocamento do bloco for zero, ele pode ser omitido. Esse é o satoshi incomum"
"de cima:"

#: src/overview.md:164
msgid ""
"```\n"
"1°1′1″\n"
"│ │ ╰─ Not first block in difficulty adjustment period\n"
"│ ╰─── Not first block in halving epoch\n"
"╰───── Second cycle\n"
"```"
msgstr ""
"```\n"
"1°1′1″\n"
"│ │ ╰─── Não é o primeiro bloco no período de ajuste de dificuldade\n"
"│ ╰───── Não é o primeiro bloco na época do halving\n"
"╰─────── Segundo ciclo\n"
"```"

#: src/overview.md:171
msgid "Rare Satoshi Supply"
msgstr "Oferta de Satoshis Raros"

#: src/overview.md:174
msgid "Total Supply"
msgstr "Oferta Total"

#: src/overview.md:176
msgid "`common`: 2.1 quadrillion"
msgstr "`comum`: 2.1 quatrilhões"

#: src/overview.md:177
msgid "`uncommon`: 6,929,999"
msgstr "`incomum`: 6.929.999"

#: src/overview.md:178
msgid "`rare`: 3437"
msgstr "`raro`: 3437"

#: src/overview.md:179
msgid "`epic`: 32"
msgstr "`épico`: 32"

#: src/overview.md:180
msgid "`legendary`: 5"
msgstr "`lendário`: 5"

#: src/overview.md:181
#: src/overview.md:190
msgid "`mythic`: 1"
msgstr "`mítico`: 1"

#: src/overview.md:183
msgid "Current Supply"
msgstr "Oferta Atual:"

#: src/overview.md:185
msgid "`common`: 1.9 quadrillion"
msgstr "`comum`: 1.9 quatrilhões"

#: src/overview.md:186
msgid "`uncommon`: 745,855"
msgstr "`incomum`: 745.855"

#: src/overview.md:187
msgid "`rare`: 369"
msgstr "`raro`: 369"

#: src/overview.md:188
msgid "`epic`: 3"
msgstr "`épico`: 3"

#: src/overview.md:189
msgid "`legendary`: 0"
msgstr "`lendário`: 0"

#: src/overview.md:192
msgid ""
"At the moment, even uncommon satoshis are quite rare. As of this writing, "
"745,855 uncommon satoshis have been mined - one per 25.6 bitcoin in "
"circulation."
msgstr ""
“No momento, mesmo os satoshis incomuns são bastante raros. No momento em que este manual foi escrito, “
"745.855 satoshis incomuns haviam sido minerados - 1 para cada 25,6 bitcoins em "
"circulação."

#: src/overview.md:196
msgid "Names"
msgstr "Nomes "

#: src/overview.md:199
msgid ""
"Each satoshi has a name, consisting of the letters _A_ through _Z_, that get "
"shorter the further into the future the satoshi was mined. They could start "
"short and get longer, but then all the good, short names would be trapped in "
"the unspendable genesis block."
msgstr ""
"Cada satoshi tem um nome, composto pelas letras de _A_ a _Z_, que ficam "
"mais curtos quanto mais no futuro o satoshi foi minerado. Eles poderiam começar "
"curtos e ficarem mais longos, mas então todos os nomes bons e curtos ficariam presos no "
"bloco Gênesis, satoshis do qual são impossíveis de gastar."

#: src/overview.md:204
msgid ""
"As an example, 1905530482684727°'s name is \"iaiufjszmoba\". The name of the "
"last satoshi to be mined is \"a\". Every combination of 10 characters or "
"less is out there, or will be out there, someday."
msgstr ""
"Por exemplo, o nome de 1905530482684727° é \"iaiufjszmoba\". O nome do "
"último satoshi a ser minerado é \"a\". Cada combinação de 10 caracteres ou "
"menos já existe, ou existirá algum dia."

#: src/overview.md:208
msgid "Exotics"
msgstr "Exóticos"

#: src/overview.md:211
msgid ""
"Satoshis may be prized for reasons other than their name or rarity. This "
"might be due to a quality of the number itself, like having an integer "
"square or cube root. Or it might be due to a connection to a historical "
"event, such as satoshis from block 477,120, the block in which SegWit "
"activated, or 2099999997689999°, the last satoshi that will ever be mined."
msgstr ""
"Satoshis podem ser valorizados por outras razões além do nome ou raridade. Isto "
"pode ser devido a uma qualidade do próprio número, como ter um número inteiro, "
"raiz quadrada ou cúbica. Ou pode ser devido a uma conexão com um evento "
"histórico, como satoshis do bloco 477.120, o bloco em que o SegWit "
"foi ativado, ou 2099999997689999°, o último satoshi que será minerado."

#: src/overview.md:217
msgid ""
"Such satoshis are termed \"exotic\". Which satoshis are exotic and what "
"makes them so is subjective. Ordinal theorists are encouraged to seek out "
"exotics based on criteria of their own devising."
msgstr ""
"Tais satoshis são denominados \"exóticos\". Quais satoshis são exóticos e o quê "
"torna-os assim é subjetivo. Os teóricos de Ordinals são encorajados a procurar "
"exóticos baseados em critérios de sua própria concepção."

#: src/overview.md:224
msgid ""
"Satoshis can be inscribed with arbitrary content, creating Bitcoin-native "
"digital artifacts. Inscribing is done by sending the satoshi to be inscribed "
"in a transaction that reveals the inscription content on-chain. This content "
"is then inextricably linked to that satoshi, turning it into an immutable "
"digital artifact that can be tracked, transferred, hoarded, bought, sold, "
"lost, and rediscovered."
msgstr ""
"Satoshis podem ser inscritos com conteúdo arbitrário, criando artefatos digitais "
"nativos do Bitcoin. A inscrição é feita enviando o satoshi para ser inscrito "
"em uma transação que revela o conteúdo da inscrição na cadeia. Este conteúdo "
"está então inextricavelmente ligado a esse satoshi, transformando-o em um artefato digital "
"imutável que pode ser rastreado, transferido, acumulado, comprado, vendido, "
"perdido e redescoberto."

#: src/overview.md:231
msgid "Archaeology"
msgstr "Arqueologia"

#: src/overview.md:234
msgid ""
"A lively community of archaeologists devoted to cataloging and collecting "
"early NFTs has sprung up. [Here's a great summary of historical NFTs by "
"Chainleft.](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)"
msgstr ""
"Uma comunidade animada de arqueólogos dedicados à catalogação e coleta "
"dos primeiros NFTs surgiu. [Aqui está um ótimo resumo dos NFTs históricos por "
"Chainleft](https://mirror.xyz/chainleft.eth/MzPWRsesC9mQflxlLo-N29oF4iwCgX3lacrvaG9Kjko)."

#: src/overview.md:238
msgid ""
"A commonly accepted cut-off for early NFTs is March 19th, 2018, the date the "
"first ERC-721 contract, [SU SQUARES](https://tenthousandsu.com/), was "
"deployed on Ethereum."
msgstr ""
"Um limite comumente aceito nos primeiros NFTs é 19 de março de 2018, a data em que"
"o primeiro contrato ERC-721, [SU SQUARES](https://tenthousandsu.com/), foi"
"implantado no Ethereum."

#: src/overview.md:242
msgid ""
"Whether or not ordinals are of interest to NFT archaeologists is an open "
"question! In one sense, ordinals were created in early 2022, when the "
"Ordinals specification was finalized. In this sense, they are not of "
"historical interest."
msgstr ""
"Se os Ordinals são ou não de interesse para os arqueólogos de NFT é uma questão "
"em aberto! De certo modo, os Ordinals foram criados no início de 2022, quando "
"a sua especificação foi finalizada. Nesse sentido, eles não são de "
"interesse histórico."

#: src/overview.md:247
msgid ""
"In another sense though, ordinals were in fact created by Satoshi Nakamoto "
"in 2009 when he mined the Bitcoin genesis block. In this sense, ordinals, "
"and especially early ordinals, are certainly of historical interest."
msgstr ""
"Por outro lado, porém, os Ordinals foram de fato criados por Satoshi Nakamoto "
"em 2009, quando ele extraiu o bloco Gênesis do Bitcoin. Nesse sentido, Ordinals, ",
"e especialmente os primeiros Ordinals, são certamente de interesse histórico."

#: src/overview.md:251
msgid ""
"Many ordinal theorists favor the latter view. This is not least because the "
"ordinals were independently discovered on at least two separate occasions, "
"long before the era of modern NFTs began."
msgstr ""
"Muitos teóricos de Ordinals favorecem este ângulo. Até porque os "
"Ordinals foram descobertos independentemente em pelo menos duas ocasiões distintas, "
"muito antes do início da era dos NFTs modernos."

#: src/overview.md:255
msgid ""
"On August 21st, 2012, Charlie Lee [posted a proposal to add proof-of-stake "
"to Bitcoin to the Bitcoin Talk "
"forum](https://bitcointalk.org/index.php?topic=102355.0). This wasn't an "
"asset scheme, but did use the ordinal algorithm, and was implemented but "
"never deployed."
msgstr ""
"Em 21 de agosto de 2012, Charlie Lee [publicou no fórum Bitcoin Talk uma proposta "
"para adicionar Proof of Stake ao Bitcoin](https://bitcointalk.org/index.php?topic=102355.0). "
"Este não era um esquema de ativos, mas usou o algoritmo Ordinal e foi implementado, mas"
"nunca implantado."

#: src/overview.md:261
msgid ""
"On October 8th, 2012, jl2012 [posted a scheme to the same "
"forum](https://bitcointalk.org/index.php?topic=117224.0) which uses decimal "
"notation and has all the important properties of ordinals. The scheme was "
"discussed but never implemented."
msgstr ""
"Em 8 de outubro de 2012, jl2012 [publicou um esquema no mesmo"
"fórum](https://bitcointalk.org/index.php?topic=117224.0) que usa notação "
"decimal e tem todas as propriedades importantes dos Ordinals. O esquema foi "
"discutido, mas nunca implementado."

#: src/overview.md:266
msgid ""
"These independent inventions of ordinals indicate in some way that ordinals "
"were discovered, or rediscovered, and not invented. The ordinals are an "
"inevitability of the mathematics of Bitcoin, stemming not from their modern "
"documentation, but from their ancient genesis. They are the culmination of a "
"sequence of events set in motion with the mining of the first block, so many "
"years ago."
msgstr ""
"Essas invenções independentes de Ordinals indicam de alguma forma que os Ordinals"
"foram descobertos, ou redescobertos, e não inventados. Os Ordinals são uma "
"inevitabilidade da matemática do Bitcoin, não decorrente de sua "
"documentação moderna, mas desde a sua gênese antiga. São o resultado de uma "
"sequência de eventos desencadeados com a mineração do primeiro bloco, tantos "
"anos atrás."

#: src/digital-artifacts.md:4
msgid ""
"Imagine a physical artifact. A rare coin, say, held safe for untold years in "
"the dark, secret clutch of a Viking hoard, now dug from the earth by your "
"grasping hands. It…"
msgstr ""
"Imagine um artefato físico. Uma moeda rara, digamos, mantida em segurança por incontáveis anos em "
"um esconderijo escuro e secreto de um tesouro viking, agora escavado na terra pelas suas"
"mãos. Esta moeda..."

#: src/digital-artifacts.md:8
msgid ""
"…has an owner. You. As long as you keep it safe, nobody can take it from you."
msgstr ""
"...tem um dono. Você. Contanto que você a mantenha segura, ninguém poderá tirá-la de você."

#: src/digital-artifacts.md:10
msgid "…is complete. It has no missing parts."
msgstr "...está completa. Não falta nenhuma peça."

#: src/digital-artifacts.md:12
msgid ""
"…can only be changed by you. If you were a trader, and you made your way to "
"18th century China, none but you could stamp it with your chop-mark."
msgstr ""
"...só pode ser alterada por você. Se você fosse um comerciante e fizesse o seu caminho para "
"a China do século XVIII, ninguém além de você poderia carimbá-la com sua marca."

#: src/digital-artifacts.md:15
msgid ""
"…can only be disposed of by you. The sale, trade, or gift is yours to make, "
"to whomever you wish."
msgstr ""
"...só pode ser descartada por você. A venda, troca ou presente é sua decisão, "
"e para quem você quiser."

#: src/digital-artifacts.md:18
msgid ""
"What are digital artifacts? Simply put, they are the digital equivalent of "
"physical artifacts."
msgstr ""
"O que são artefatos digitais? Simplificando, eles são o equivalente digital de "
"artefatos físicos."

#: src/digital-artifacts.md:21
msgid ""
"For a digital thing to be a digital artifact, it must be like that coin of "
"yours:"
msgstr ""
"Para que uma coisa digital seja um artefato digital, deve ser como aquela moeda "
"sua:"

#: src/digital-artifacts.md:24
msgid ""
"Digital artifacts can have owners. A number is not a digital artifact, "
"because nobody can own it."
msgstr ""
"Artefatos digitais podem ter proprietários. Um número não é um artefato digital, "
"porque ninguém pode possuí-lo."

#: src/digital-artifacts.md:27
msgid ""
"Digital artifacts are complete. An NFT that points to off-chain content on "
"IPFS or Arweave is incomplete, and thus not a digital artifact."
msgstr ""
"Os artefatos digitais são completos. Uma NFT que aponta para um conteúdo fora da cadeia, "
"em IPFS ou Arweave, está incompleta e, portanto, não é um artefato digital."

#: src/digital-artifacts.md:30
msgid ""
"Digital artifacts are permissionless. An NFT which cannot be sold without "
"paying a royalty is not permissionless, and thus not a digital artifact."
msgstr ""
"Os artefatos digitais não têm permissão. Uma NFT que não pode ser vendida sem "
"pagar royalties não é algo sem permissão e, portanto, não é um artefato digital."

#: src/digital-artifacts.md:33
msgid ""
"Digital artifacts are uncensorable. Perhaps you can change a database entry "
"on a centralized ledger today, but maybe not tomorrow, and thus one cannot "
"be a digital artifact."
msgstr ""
"Os artefatos digitais não podem ser censurados. Talvez você possa alterar uma "
"entrada em um banco de dados de um livro-razão centralizado hoje, mas talvez não "
"amanhã e, portanto, não pode ser um artefato digital."

#: src/digital-artifacts.md:37
msgid ""
"Digital artifacts are immutable. An NFT with an upgrade key is not a digital "
"artifact."
msgstr ""
"Os artefatos digitais são imutáveis. Uma NFT com chave de atualização não é um artefato "
"digital."

#: src/digital-artifacts.md:40
msgid ""
"The definition of a digital artifact is intended to reflect what NFTs "
"_should_ be, sometimes are, and what inscriptions _always_ are, by their "
"very nature."
msgstr ""
"A definição de artefato digital pretende refletir o que NFTs "
"_deveriam_ ser, às vezes são, e o que as inscrições _sempre_ são, por "
"natureza própria."

#: src/inscriptions.md:4
msgid ""
"Inscriptions inscribe sats with arbitrary content, creating bitcoin-native "
"digital artifacts, more commonly known as NFTs. Inscriptions do not require "
"a sidechain or separate token."
msgstr ""
"Inscrições inscrevem conteúdo arbitrário em sats, criando artefatos digitais "
"nativos de bitcoin, mais comumente conhecidos como NFTs. As inscrições não exigem "
"uma sidechain ou token separado."

#: src/inscriptions.md:8
msgid ""
"These inscribed sats can then be transferred using bitcoin transactions, "
"sent to bitcoin addresses, and held in bitcoin UTXOs. These transactions, "
"addresses, and UTXOs are normal bitcoin transactions, addresses, and UTXOS "
"in all respects, with the exception that in order to send individual sats, "
"transactions must control the order and value of inputs and outputs "
"according to ordinal theory."
msgstr ""
"Esses sats inscritos podem então ser transferidos usando transações de bitcoin, "
"enviados para endereços de bitcoin e mantidos em UTXOs de bitcoin. Essas transações, "
"endereços e UTXOs são transações, endereços e UTXOS normais de bitcoin "
"em todos os aspectos, com a exceção de que, para enviar sats individuais, "
"as transações devem controlar tanto a ordem quanto o valor das entradas e das saídas "
"de acordo com a teoria ordinal."

#: src/inscriptions.md:15
msgid ""
"The inscription content model is that of the web. An inscription consists of "
"a content type, also known as a MIME type, and the content itself, which is "
"a byte string. This allows inscription content to be returned from a web "
"server, and for creating HTML inscriptions that use and remix the content of "
"other inscriptions."
msgstr ""
"O modelo de conteúdo da inscrição é o da web. Uma inscrição consiste em "
"um tipo de conteúdo, também conhecido como tipo MIME, e o próprio conteúdo, que é "
"uma string de bytes. Isso permite que o conteúdo de inscrições seja retornado de um servidor "
"web, e usado para criar inscrições HTML que usam e remixam o conteúdo de "
"outras inscrições."

#: src/inscriptions.md:21
msgid ""
"Inscription content is entirely on-chain, stored in taproot script-path "
"spend scripts. Taproot scripts have very few restrictions on their content, "
"and additionally receive the witness discount, making inscription content "
"storage relatively economical."
msgstr ""
"O conteúdo da inscrição é inteiramente on-chain, armazenado em scripts de gasto "
"do caminho do script taproot. Os scripts taproot têm pouquíssimas restrições em seu conteúdo "
"e adicionalmente recebem o desconto da Witness, fazendo com que o armazenamento do "
"conteúdo da inscrição seja relativamente econômico."

#: src/inscriptions.md:26
msgid ""
"Since taproot script spends can only be made from existing taproot outputs, "
"inscriptions are made using a two-phase commit/reveal procedure. First, in "
"the commit transaction, a taproot output committing to a script containing "
"the inscription content is created. Second, in the reveal transaction, the "
"output created by the commit transaction is spent, revealing the inscription "
"content on-chain."
msgstr ""
"Como os gastos com script taproot só podem ser feitos a partir de saídas taproot existentes, "
"as inscrições são feitas usando um procedimento de confirmação/revelação de duas fases. Primeiro, na "
"transação de confirmação, uma saída taproot confirmando um script contendo "
"o conteúdo da inscrição é criado. Depois, na transação de revelação, "
"a saída criada pela transação de confirmação é gasta, revelando o conteúdo da inscrição "
"na rede."

#: src/inscriptions.md:33
msgid ""
"Inscription content is serialized using data pushes within unexecuted "
"conditionals, called \"envelopes\". Envelopes consist of an `OP_FALSE OP_IF "
"… OP_ENDIF` wrapping any number of data pushes. Because envelopes are "
"effectively no-ops, they do not change the semantics of the script in which "
"they are included, and can be combined with any other locking script."
msgstr ""
"O conteúdo da inscrição é serializado usando envios (pushes) de dados em condicionais "
"não executados, chamadas \"envelopes\". Os envelopes consistem em um `OP_FALSE OP_IF "
"… OP_ENDIF` envolvendo qualquer quantidade de envios de dados. Porque os envelopes são "
"efetivamente autônomos, eles não alteram a semântica do script em que "
"eles estão incluídos, e podem ser combinados com qualquer outro script de bloqueio."

#: src/inscriptions.md:39
msgid ""
"A text inscription containing the string \"Hello, world!\" is serialized as "
"follows:"
msgstr ""
"Uma inscrição de texto contendo a string \"Hello, world!\" é serializada como "
"segue:"

#: src/inscriptions.md:42
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions.md:53
msgid ""
"First the string `ord` is pushed, to disambiguate inscriptions from other "
"uses of envelopes."
msgstr ""
"Primeiro, se faz um push da string `ord`, para desambiguar inscrições de outros "
"usos de envelopes."

#: src/inscriptions.md:56
msgid ""
"`OP_PUSH 1` indicates that the next push contains the content type, and "
"`OP_PUSH 0`indicates that subsequent data pushes contain the content itself. "
"Multiple data pushes must be used for large inscriptions, as one of "
"taproot's few restrictions is that individual data pushes may not be larger "
"than 520 bytes."
msgstr ""
"`OP_PUSH 1` indica que o próximo push contém o tipo de conteúdo, e "
"`OP_PUSH 0`indica que os pushes subsequentes contêm o próprio conteúdo. "
"Múltiplos pushes de dados devem ser usados para inscrições grandes, porque uma das poucas"
"restrições do taproot é que os pushes de dados individuais não podem ser maiores"
"que 520 bytes."

#: src/inscriptions.md:62
msgid ""
"The inscription content is contained within the input of a reveal "
"transaction, and the inscription is made on the first sat of its input. This "
"sat can then be tracked using the familiar rules of ordinal theory, allowing "
"it to be transferred, bought, sold, lost to fees, and recovered."
msgstr ""
"O conteúdo da inscrição está contido na entrada de uma transação "
"de revelação, e a inscrição é feita no primeiro sat de sua entrada. Este "
"sat pode então ser rastreado usando as regras familiares da teoria ordinal, permitindo "
"que seja transferido, comprado, vendido, perdido em taxas e recuperado."

#: src/inscriptions.md:67
msgid "Content"
msgstr "Conteúdo"

#: src/inscriptions.md:70
msgid ""
"The data model of inscriptions is that of a HTTP response, allowing "
"inscription content to be served by a web server and viewed in a web browser."
msgstr ""
"O modelo de dados das inscrições é o de uma resposta HTTP, permitindo "
"que os conteúdos de inscrições sejam servidos por um servidor web e visualizados em um navegador web."

#: src/inscriptions.md:73
msgid "Fields"
msgstr "Campos"

#: src/inscriptions.md:76
msgid ""
"Inscriptions may include fields before an optional body. Each field consists "
"of two data pushes, a tag and a value."
msgstr ""
"As inscrições podem incluir campos antes de um corpo opcional. Cada campo "
"consiste em dois envios de dados, uma tag e um valor."

#: src/inscriptions.md:79
msgid ""
"Currently, the only defined field is `content-type`, with a tag of `1`, "
"whose value is the MIME type of the body."
msgstr ""
"Atualmente, o único campo definido é `content-type`, com a tag `1`, "
"cujo valor é o tipo MIME do corpo."

#: src/inscriptions.md:82
msgid ""
"The beginning of the body and end of fields is indicated with an empty data "
"push."
msgstr ""
"O início do corpo e o final dos campos são indicados com um push de dados "
"vazio."

#: src/inscriptions.md:85
msgid ""
"Unrecognized tags are interpreted differently depending on whether they are "
"even or odd, following the \"it's okay to be odd\" rule used by the "
"Lightning Network."
msgstr ""
"Tags não reconhecidas são interpretadas de maneira diferente dependendo se são "
"pares ou ímpares, seguindo a regra \"não há problema ser ímpar\" usada pela
"Rede Lightning."

#: src/inscriptions.md:89
msgid ""
"Even tags are used for fields which may affect creation, initial assignment, "
"or transfer of an inscription. Thus, inscriptions with unrecognized even "
"fields must be displayed as \"unbound\", that is, without a location."
msgstr ""
"Etiquetas pares são usadas para campos que podem afetar a criação, atribuição inicial "
"ou transferência de uma inscrição. Assim, inscrições com campos pares "
"não reconhecidos devem ser exibidas como \"não vinculadas\", ou seja, sem localização."

#: src/inscriptions.md:93
msgid ""
"Odd tags are used for fields which do not affect creation, initial "
"assignment, or transfer, such as additional metadata, and thus are safe to "
"ignore."
msgstr ""
"Tags ímpares são usadas para campos que não afetam a criação, atribuição "
"inicial ou transferência, como metadados adicionais, e, portanto, podem ser ignorados "
"com segurança."

#: src/inscriptions.md:96
msgid "Inscription IDs"
msgstr "IDs das Inscrições"

#: src/inscriptions.md:99
msgid ""
"The inscriptions are contained within the inputs of a reveal transaction. In "
"order to uniquely identify them they are assigned an ID of the form:"
msgstr ""
"As inscrições estão contidas nas entradas de uma transação de revelação. Para "
"identificá-las de forma única, elas recebem uma ID no formato:"

#: src/inscriptions.md:102
msgid "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"
msgstr "`521f8eccffa4c41a3a7728dd012ea5a4a02feed81f41159231251ecf1e5c79dai0`"

#: src/inscriptions.md:104
msgid ""
"The part in front of the `i` is the transaction ID (`txid`) of the reveal "
"transaction. The number after the `i` defines the index (starting at 0) of "
"new inscriptions being inscribed in the reveal transaction."
msgstr ""
"A parte na frente do `i` é o ID da transação (`txid`) da transação de "
"revelação. O número após o `i` define o índice (começando em 0) de "
"novas inscrições sendo inscritas na transação de revelação."

#: src/inscriptions.md:108
msgid ""
"Inscriptions can either be located in different inputs, within the same "
"input or a combination of both. In any case the ordering is clear, since a "
"parser would go through the inputs consecutively and look for all "
"inscription `envelopes`."
msgstr ""
"As inscrições podem estar localizadas em entradas diferentes, dentro da mesma "
"entrada, ou em uma combinação de ambas. Em qualquer caso, a ordem é clara, já que "
"um analisador percorreria as entradas consecutivamente e procuraria por todos os "
"`envelopes` de inscrição."

#: src/inscriptions.md:112
msgid "Input"
msgstr "Entrada"

#: src/inscriptions.md:112
msgid "Inscription Count"
msgstr "Contagem de inscrições"

#: src/inscriptions.md:112
msgid "Indices"
msgstr "Índice"

#: src/inscriptions.md:114
#: src/inscriptions.md:117
msgid "0"
msgstr "0"

#: src/inscriptions.md:114
#: src/inscriptions.md:116
msgid "2"
msgstr "2"

#: src/inscriptions.md:114
msgid "i0, i1"
msgstr "i0, il"

#: src/inscriptions.md:115
#: src/inscriptions.md:115
#: src/inscriptions.md:118
msgid "1"
msgstr "1"

#: src/inscriptions.md:115
msgid "i2"
msgstr "i2"

#: src/inscriptions.md:116
#: src/inscriptions.md:117
msgid "3"
msgstr "3"

#: src/inscriptions.md:116
msgid "i3, i4, i5"
msgstr "i3. i4, i5"

#: src/inscriptions.md:118
msgid "4"
msgstr "4"

#: src/inscriptions.md:118
msgid "i6"
msgstr "i6"

#: src/inscriptions.md:120
msgid "Sandboxing"
msgstr "Sandboxing"

#: src/inscriptions.md:123
msgid ""
"HTML and SVG inscriptions are sandboxed in order to prevent references to "
"off-chain content, thus keeping inscriptions immutable and self-contained."
msgstr ""
"As inscrições HTML e SVG são isoladas (sandboxed) para evitar referências "
"a conteúdo fora da cadeia, mantendo assim as inscrições imutáveis e autocontidas."

#: src/inscriptions.md:126
msgid ""
"This is accomplished by loading HTML and SVG inscriptions inside `iframes` "
"with the `sandbox` attribute, as well as serving inscription content with "
"`Content-Security-Policy` headers."
msgstr ""
"Isso é feito carregando inscrições HTML e SVG dentro de `iframes` com o "
"atributo `sandbox`, bem como servindo conteúdo de inscrição com "
"cabeçalhos `Content-Security-Policy`."

#: src/inscriptions/provenance.md:4
msgid ""
"The owner of an inscription can create child inscriptions, trustlessly "
"establishing the provenance of those children on-chain as having been "
"created by the owner of the parent inscription. This can be used for "
"collections, with the children of a parent inscription being members of the "
"same collection."
msgstr ""
"O proprietário de uma inscrição pode criar inscrições filhas, estabelecendo, "
"sem confiança, a procedência dessas crianças na cadeia como tendo sido criadas "
"pelo proprietário da inscrição pai. Isso pode ser usado para coleções, com os "
"filhos de uma inscrição pai sendo membros da mesma coleção."

#: src/inscriptions/provenance.md:9
msgid ""
"Children can themselves have children, allowing for complex hierarchies. For "
"example, an artist might create an inscription representing themselves, with "
"sub inscriptions representing collections that they create, with the "
"children of those sub inscriptions being items in those collections."
msgstr ""
"As próprias crianças podem ter filhos, permitindo hierarquias complexas. "
"Por exemplo, um artista pode criar uma inscrição representando a si mesmo, com "
"subinscrições representando coleções que ele cria, sendo os filhos dessas "
"subinscrições itens daquelas coleções."

#: src/inscriptions/provenance.md:14
msgid "Specification"
msgstr "Especificações"

#: src/inscriptions/provenance.md:16
msgid "To create a child inscription C with parent inscription P:"
msgstr "Para criar uma inscrição filha C com inscrição pai P:"

#: src/inscriptions/provenance.md:18
msgid "Create an inscribe transaction T as usual for C."
msgstr "Crie uma transação de inscrição T como de costume para C."

#: src/inscriptions/provenance.md:19
msgid "Spend the parent P in one of the inputs of T."
msgstr "Gaste o pai P em uma das entradas de T."

#: src/inscriptions/provenance.md:20
msgid ""
"Include tag `3`, i.e. `OP_PUSH 3`, in C, with the value of the serialized "
"binary inscription ID of P, serialized as the 32-byte `TXID`, followed by "
"the four-byte little-endian `INDEX`, with trailing zeroes omitted."
msgstr ""
"Inclua a tag `3`, ou seja, `OP_PUSH 3`, em C, com o valor do ID binário "
"serializado da inscrição P, serializado como o `TXID` de 32 bytes, "
"seguido pelo `INDEX` de quatro bytes em formato little-endian, omitindo zeros à direita."

#: src/inscriptions/provenance.md:24
msgid ""
"_NB_ The bytes of a bitcoin transaction ID are reversed in their text "
"representation, so the serialized transaction ID will be in the opposite "
"order."
msgstr ""
"_NB_ Os bytes de uma ID de transação bitcoin são invertidos em sua representação "
"de texto, portanto, a ID serializada da transação estará na ordem oposta."

#: src/inscriptions/provenance.md:27
#: src/guides/testing.md:18
#: src/guides/reindexing.md:15
msgid "Example"
msgstr "Exemplo"

#: src/inscriptions/provenance.md:29
msgid ""
"An example of a child inscription of "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"
msgstr ""
"Um exemplo de uma inscrição filha de "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0`:"

#: src/inscriptions/provenance.md:32
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  OP_PUSH \"ord\"\n"
"  OP_PUSH 1\n"
"  OP_PUSH \"text/plain;charset=utf-8\"\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100\n"
"  OP_PUSH 0\n"
"  OP_PUSH \"Hello, world!\"\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:45
msgid ""
"Note that the value of tag `3` is binary, not hex, and that for the child "
"inscription to be recognized as a child, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` must be "
"spent as one of the inputs of the inscribe transaction."
msgstr ""
"Observe que o valor da tag `3` é binário, não hexadecimal, e que para que "
"a inscrição filha seja reconhecida como filha, "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi0` deve ser "
"gasta como uma das entradas da transação de inscrição."

#: src/inscriptions/provenance.md:50
msgid ""
"Example encoding of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"
msgstr ""
"Exemplo de codificação da ID de inscrição "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi255`:"

#: src/inscriptions/provenance.md:53
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100ff\n"
"  …\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a09080706050403020100ff\n"
"  …\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:63
msgid ""
"And of inscription ID "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"
msgstr ""
"E da ID da inscrição "
"`000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1fi256`:"

#: src/inscriptions/provenance.md:65
msgid ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a090807060504030201000001\n"
"  …\n"
"OP_ENDIF\n"
"```"
msgstr ""
"```\n"
"OP_FALSE\n"
"OP_IF\n"
"  …\n"
"  OP_PUSH 3\n"
"  OP_PUSH "
"0x1f1e1d1c1b1a191817161514131211100f0e0d0c0b0a090807060504030201000001\n"
"  …\n"
"OP_ENDIF\n"
"```"

#: src/inscriptions/provenance.md:75
msgid "Notes"
msgstr "Notas"

#: src/inscriptions/provenance.md:77
msgid ""
"The tag `3` is used because it is the first available odd tag. Unrecognized "
"odd tags do not make an inscription unbound, so child inscriptions would be "
"recognized and tracked by old versions of `ord`."
msgstr ""
"A tag `3` é usada porque é a primeira tag ímpar disponível. Tags ímpares não "
"reconhecidas não desvinculam uma inscrição, portanto, inscrições filhas seriam "
"reconhecidas e rastreadas por versões antigas de `ord`."

#: src/inscriptions/provenance.md:81
msgid ""
"A collection can be closed by burning the collection's parent inscription, "
"which guarantees that no more items in the collection can be issued."
msgstr ""
"Uma coleção pode ser encerrada queimando a inscrição-pai da coleção, o que "
"garante que nenhum outro item poderá ser emitido na coleção."

#: src/inscriptions/recursion.md:4
msgid ""
"An important exception to [sandboxing](../inscriptions.md#sandboxing) is "
"recursion: access to `ord`'s `/content` endpoint is permitted, allowing "
"inscriptions to access the content of other inscriptions by requesting "
"`/content/<INSCRIPTION_ID>`."
msgstr ""
"Uma exceção importante ao [sandboxing](../inscriptions.md#sandboxing) é "
"a recursão: o acesso ao endpoint `/content` do `ord` é permitido, permitindo "
"que inscrições acessem o conteúdo de outras inscrições solicitando "
"`/content/<INSCRIPTION_ID>`."

#: src/inscriptions/recursion.md:9
msgid "This has a number of interesting use-cases:"
msgstr "Isso tem vários casos de uso interessantes:"

#: src/inscriptions/recursion.md:11
msgid "Remixing the content of existing inscriptions."
msgstr "Remixando o conteúdo das inscrições existentes."

#: src/inscriptions/recursion.md:13
msgid ""
"Publishing snippets of code, images, audio, or stylesheets as shared public "
"resources."
msgstr  ""
"Publicar trechos de código, imagens, áudio ou stylesheets como recursos públicos "
"compartilhados."

#: src/inscriptions/recursion.md:16
msgid ""
"Generative art collections where an algorithm is inscribed as JavaScript, "
"and instantiated from multiple inscriptions with unique seeds."
msgstr ""
"Coleções de arte generativas onde um algoritmo é inscrito como JavaScript "
"e instanciado a partir de múltiplas inscrições com sementes únicas."

#: src/inscriptions/recursion.md:19
msgid ""
"Generative profile picture collections where accessories and attributes are "
"inscribed as individual images, or in a shared texture atlas, and then "
"combined, collage-style, in unique combinations in multiple inscriptions."
msgstr ""
"Coleções generativas de fotos de perfil onde acessórios e atributos são "
"inscritos como imagens individuais, ou em um atlas de textura compartilhado, "
"e depois combinados, em estilo de colagem, em combinações únicas em múltiplas inscrições."

#: src/inscriptions/recursion.md:23
msgid "A few other endpoints that inscriptions may access are the following:"
msgstr "Alguns outros endpoints que as inscrições podem acessar são os seguintes:"

#: src/inscriptions/recursion.md:25
msgid "`/blockheight`: latest block height."
msgstr "`/blockheight`: altura do bloco mais recente."

#: src/inscriptions/recursion.md:26
msgid "`/blockhash`: latest block hash."
msgstr "`/blockhash`: hash do bloco mais recente."

#: src/inscriptions/recursion.md:27
msgid "`/blockhash/<HEIGHT>`: block hash at given block height."
msgstr "`/blockhash/<ALTURA>`: hash do bloco em uma determinada altura de bloco."

#: src/inscriptions/recursion.md:28
msgid "`/blocktime`: UNIX time stamp of latest block."
msgstr "`/blocktime`: marca de tempo UNIX do bloco mais recente."

#: src/faq.md:1
msgid "Ordinal Theory FAQ"
msgstr "Perguntas frequentes sobre Teoria Ordinal"

#: src/faq.md:4
msgid "What is ordinal theory?"
msgstr "O que é a Teoria Ordinal?"

#: src/faq.md:7
msgid ""
"Ordinal theory is a protocol for assigning serial numbers to satoshis, the "
"smallest subdivision of a bitcoin, and tracking those satoshis as they are "
"spent by transactions."
msgstr ""
"A teoria ordinal é um protocolo para atribuir números de série aos satoshis, "
"a menor subdivisão de um bitcoin, e rastrear esses satoshis à medida que são "
"gastos nas transações."

#: src/faq.md:11
msgid ""
"These serial numbers are large numbers, like this 804766073970493. Every "
"satoshi, which is ¹⁄₁₀₀₀₀₀₀₀₀ of a bitcoin, has an ordinal number."
msgstr ""
"Esses números de série são números grandes, como este 804766073970493. Cada "
"satoshi, que equivale a ¹⁄₁₀₀₀₀₀₀₀₀ de um bitcoin, tem um número ordinal."

#: src/faq.md:14
msgid ""
"Does ordinal theory require a side chain, a separate token, or changes to "
"Bitcoin?"
msgstr ""
"A teoria ordinal requer uma sidechain, um token separado ou alterações no "
"Bitcoin?"

#: src/faq.md:17
msgid ""
"Nope! Ordinal theory works right now, without a side chain, and the only "
"token needed is bitcoin itself."
msgstr ""
"Não! A teoria ordinal funciona agora, sem uma sidechain, e o único token "
"necessário é o próprio bitcoin."

#: src/faq.md:20
msgid "What is ordinal theory good for?"
msgstr "Para que serve a Teoria Ordinal?"

#: src/faq.md:23
msgid ""
"Collecting, trading, and scheming. Ordinal theory assigns identities to "
"individual satoshis, allowing them to be individually tracked and traded, as "
"curios and for numismatic value."
msgstr ""
"Para colecionar, negociar e inovar. A teoria ordinal atribui identidades a "
"satoshis individuais, permitindo que eles sejam rastreados e negociados "
"individualmente, como colectionáveis e por valor numismático."

#: src/faq.md:27
msgid ""
"Ordinal theory also enables inscriptions, a protocol for attaching arbitrary "
"content to individual satoshis, turning them into bitcoin-native digital "
"artifacts."
msgstr ""
"A teoria ordinal também permite inscrições, um protocolo para anexar conteúdo "
"arbitrário a satoshis individuais, transformando-os em artefatos digitais "
"nativos do bitcoin."

#: src/faq.md:31
msgid "How does ordinal theory work?"
msgstr "Como funciona a Teoria Ordinal?"

#: src/faq.md:34
msgid ""
"Ordinal numbers are assigned to satoshis in the order in which they are "
"mined. The first satoshi in the first block has ordinal number 0, the second "
"has ordinal number 1, and the last satoshi of the first block has ordinal "
"number 4,999,999,999."
msgstr ""
"Os números ordinais são atribuídos aos satoshis na ordem em que são minerados. "
"O primeiro satoshi no primeiro bloco tem o número ordinal 0, o segundo tem o "
"número ordinal 1 e o último satoshi do primeiro bloco tem o número ordinal "
"4.999.999.999."

#: src/faq.md:39
msgid ""
"Satoshis live in outputs, but transactions destroy outputs and create new "
"ones, so ordinal theory uses an algorithm to determine how satoshis hop from "
"the inputs of a transaction to its outputs."
msgstr ""
"Os satoshis vivem nos UTXOs, mas as transações destroem os UTXOs e criam "
"novos, então a teoria ordinal usa um algoritmo para determinar como os satoshis "
"saltam das entradas de uma transação para suas saídas."

#: src/faq.md:43
msgid "Fortunately, that algorithm is very simple."
msgstr "Felizmente, esse algoritmo é muito simples."

#: src/faq.md:45
msgid ""
"Satoshis transfer in first-in-first-out order. Think of the inputs to a "
"transaction as being a list of satoshis, and the outputs as a list of slots, "
"waiting to receive a satoshi. To assign input satoshis to slots, go through "
"each satoshi in the inputs in order, and assign each to the first available "
"slot in the outputs."
msgstr ""
"Os satoshis são transferidos na ordem FIFO: primeiro a entrar, primeiro a sair. "
"Pense nas entradas de uma transação como sendo uma lista de satoshis e nas "
"saídas como uma lista de slots, esperando para receber um satoshi. Para atribuir "
"satoshis da entrada para os slots, percorra todos os satoshis das entradas em ordem "
"e atribua cada um ao primeiro slot disponível nas saídas."

#: src/faq.md:51
msgid ""
"Let's imagine a transaction with three inputs and two outputs. The inputs "
"are on the left of the arrow and the outputs are on the right, all labeled "
"with their values:"
msgstr ""
"Vamos imaginar uma transação com três entradas e duas saídas. As entradas "
"estão à esquerda da seta e as saídas à direita, todas rotuladas com seus "
"valores:"

#: src/faq.md:55
msgid ""
"```\n"
"[2] [1] [3] → [4] [2]\n"
"```"
msgstr ""
"```\n"
"[2] [1] [3] → [4] [2]\n"
"```"

#: src/faq.md:57
msgid ""
"Now let's label the same transaction with the ordinal numbers of the "
"satoshis that each input contains, and question marks for each output slot. "
"Ordinal numbers are large, so let's use letters to represent them:"
msgstr ""
"Agora vamos rotular a mesma transação com os números ordinais dos "
"satoshis que cada entrada contém e pontos de interrogação para cada slot de saída. "
"Os números ordinais são grandes, então vamos usar letras para representá-los:"

#: src/faq.md:61
msgid ""
"```\n"
"[a b] [c] [d e f] → [? ? ? ?] [? ?]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [? ? ? ?] [? ?]\n"
"```"

#: src/faq.md:63
msgid ""
"To figure out which satoshi goes to which output, go through the input "
"satoshis in order and assign each to a question mark:"
msgstr ""
"Para descobrir qual satoshi vai para qual saída, percorra os satoshis "
"de entrada em ordem e atribua um ponto de interrogação a cada um:"

#: src/faq.md:66
msgid ""
"```\n"
"[a b] [c] [d e f] → [a b c d] [e f]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [a b c d] [e f]\n"
"```"

#: src/faq.md:68
msgid ""
"What about fees, you might ask? Good question! Let's imagine the same "
"transaction, this time with a two satoshi fee. Transactions with fees send "
"more satoshis in the inputs than are received by the outputs, so to make our "
"transaction into one that pays fees, we'll remove the second output:"
msgstr ""
"E quanto às taxas, você pode perguntar? Boa pergunta! Vamos imaginar a "
"mesma transação, desta vez com uma taxa de dois satoshis. Transações com taxas "
"enviam mais satoshis nas entradas do que são recebidos pelas saídas, então "
"para transformar nossa transação em uma que paga taxas, removeremos a segunda saída:"

#: src/faq.md:73
msgid ""
"```\n"
"[2] [1] [3] → [4]\n"
"```"
msgstr ""
"```\n"
"[2] [1] [3] → [4]\n"
"```"

#: src/faq.md:75
msgid "The satoshis "
msgstr "Os satoshis "

#: src/faq.md:75
msgid "e"
msgstr "e"

#: src/faq.md:75
msgid " and "
msgstr " & "

#: src/faq.md:75
msgid "f"
msgstr "f"

#: src/faq.md:75
msgid " now have nowhere to go in the outputs:"
msgstr " agora não têm para onde ir nas saídas:"

#: src/faq.md:78
msgid ""
"```\n"
"[a b] [c] [d e f] → [a b c d]\n"
"```"
msgstr ""
"```\n"
"[a b] [c] [d e f] → [a b c d]\n"
"```"

#: src/faq.md:80
msgid ""
"So they go to the miner who mined the block as fees. [The "
"BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) has the "
"details, but in short, fees paid by transactions are treated as extra inputs "
"to the coinbase transaction, and are ordered how their corresponding "
"transactions are ordered in the block. The coinbase transaction of the block "
"might look like this:"
msgstr ""
"Então eles vão, como taxas, para o minerador que minerou o bloco. [O "
"BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) tem os detalhes, "
"mas resumindo, as taxas pagas pelas transações são tratadas como entradas "
"extras para a transação coinbase e são ordenadas como suas transações "
"correspondentes são ordenadas no bloco. A transação coinbase do bloco "
"pode ter a seguinte aparência:"

#: src/faq.md:87
msgid ""
"```\n"
"[SUBSIDY] [e f] → [SUBSIDY e f]\n"
"```"
msgstr ""
"```\n"
"[SUBSIDY] [e f] → [SUBSIDY e f]\n"
"```"

#: src/faq.md:89
msgid "Where can I find the nitty-gritty details?"
msgstr "Onde posso encontrar os detalhes técnicos?"

#: src/faq.md:92
msgid "[The BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"
msgstr "[No BIP!](https://github.com/ordinals/ord/blob/master/bip.mediawiki)"

#: src/faq.md:94
msgid ""
"Why are sat inscriptions called \"digital artifacts\" instead of \"NFTs\"?"
msgstr ""
"Por que as inscrições em sats são chamadas de \"artefatos digitais\" em vez de \"NFTs\"?"

#: src/faq.md:97
msgid ""
"An inscription is an NFT, but the term \"digital artifact\" is used instead, "
"because it's simple, suggestive, and familiar."
msgstr ""
"Uma inscrição é uma NFT, mas o termo \"artefato digital\" é usado em seu lugar, "
"porque é simples, sugestivo e familiar."

#: src/faq.md:100
msgid ""
"The phrase \"digital artifact\" is highly suggestive, even to someone who "
"has never heard the term before. In comparison, NFT is an acronym, and "
"doesn't provide any indication of what it means if you haven't heard the "
"term before."
msgstr ""
"A frase \"artefato digital\" é altamente sugestiva, mesmo para alguém que "
"nunca ouviu o termo antes. Em comparação, NFT é um acrônimo e não fornece "
"nenhuma indicação do que significa se você nunca ouviu o termo anteriormente."

#: src/faq.md:104
msgid ""
"Additionally, \"NFT\" feels like financial terminology, and the both word "
"\"fungible\" and sense of the word \"token\" as used in \"NFT\" is uncommon "
"outside of financial contexts."
msgstr ""
"Além disso, \"NFT\" parece uma terminologia financeira, e a palavra \"fungível\" e "
"o sentido da palavra \"token,\" conforme usado em \"NFT,\" são incomuns fora de "
"contextos financeiros."

#: src/faq.md:108
msgid "How do sat inscriptions compare to…"
msgstr "Como as inscrições em sat se comparam a..."

#: src/faq.md:111
msgid "Ethereum NFTs?"
msgstr "NFTs no Ethereum?"

#: src/faq.md:113
msgid "_Inscriptions are always immutable._"
msgstr "_As inscrições são sempre imutáveis._"

#: src/faq.md:115
msgid ""
"There is simply no way to for the creator of an inscription, or the owner of "
"an inscription, to modify it after it has been created."
msgstr ""
"Simplesmente não há como o criador de uma inscrição, ou o proprietário de uma "
"inscrição, modificá-la depois de ter sido criada."

#: src/faq.md:118
msgid ""
"Ethereum NFTs _can_ be immutable, but many are not, and can be changed or "
"deleted by the NFT contract owner."
msgstr ""
"As NFTs no Ethereum _podem_ ser imutáveis, mas muitas não são, e podem ser "
"alteradas ou excluídas pelo proprietário do contrato NFT."

#: src/faq.md:121
msgid ""
"In order to make sure that a particular Ethereum NFT is immutable, the "
"contract code must be audited, which requires detailed knowledge of the EVM "
"and Solidity semantics."
msgstr ""
"Para garantir que uma determinada NFT no Ethereum seja imutável, o código "
"do contrato deve ser auditado, o que requer conhecimento detalhado de semântica "
"EVM e Solidity."

#: src/faq.md:125
msgid ""
"It is very hard for a non-technical user to determine whether or not a given "
"Ethereum NFT is mutable or immutable, and Ethereum NFT platforms make no "
"effort to distinguish whether an NFT is mutable or immutable, and whether "
"the contract source code is available and has been audited."
msgstr ""
"É muito difícil para um usuário não técnico determinar se uma determinada "
"NFT no Ethereum é mutável ou imutável, e as plataformas daquele ecosistema "
"não fazem nenhum esforço para distinguir se uma NFT é mutável ou imutável, "
"e se o código-fonte do contrato está disponível e foi auditado."

#: src/faq.md:130
msgid "_Inscription content is always on-chain._"
msgstr "_O conteúdo de inscrições estão sempre \"on-chain\" na cadeia do Bitcoin._"

#: src/faq.md:132
msgid ""
"There is no way for an inscription to refer to off-chain content. This makes "
"inscriptions more durable, because content cannot be lost, and scarcer, "
"because inscription creators must pay fees proportional to the size of the "
"content."
msgstr ""
"Não há como uma inscrição se referir a conteúdo fora da rede. Isso torna as "
"inscrições mais duráveis, porque o conteúdo não pode ser perdido, e mais escassas, "
"porque os criadores das inscrições devem pagar taxas proporcionais ao tamanho do "
"conteúdo."

#: src/faq.md:136
msgid ""
"Some Ethereum NFT content is on-chain, but much is off-chain, and is stored "
"on platforms like IPFS or Arweave, or on traditional, fully centralized web "
"servers. Content on IPFS is not guaranteed to continue to be available, and "
"some NFT content stored on IPFS has already been lost. Platforms like "
"Arweave rely on weak economic assumptions, and will likely fail "
"catastrophically when these economic assumptions are no longer met. "
"Centralized web servers may disappear at any time."
msgstr ""
"Existem alguns conteúdos de NFTs no Ethereum on-chain, mas muito está "
"fora da cadeia, \"off-chain,\" e armazenado em plataformas como IPFS ou Arweave, "
"ou em servidores web tradicionais e totalmente centralizados. Não há "
"garantia de que o conteúdo no IPFS continuará disponível, e alguns conteúdos de NFT "
"armazenados em IPFS já foram perdidos. Plataformas como a Arweave dependem de "
"suposições econômicas fracas e provavelmente falharão catastroficamente quando essas "
"suposições econômicas não forem mais atendidas. Servidores web centralizados "
"podem desaparecer a qualquer momento."

#: src/faq.md:144
msgid ""
"It is very hard for a non-technical user to determine where the content of a "
"given Ethereum NFT is stored."
msgstr ""
"É muito difícil para um usuário não técnico determinar onde o conteúdo de uma "
"determinada NFT do Ethereum está armazenada."

#: src/faq.md:147
msgid "_Inscriptions are much simpler._"
msgstr "_As inscrições são muito mais simples._"

#: src/faq.md:149
msgid ""
"Ethereum NFTs depend on the Ethereum network and virtual machine, which are "
"highly complex, constantly changing, and which introduce changes via "
"backwards-incompatible hard forks."
msgstr ""
"As NFTs do Ethereum dependem da rede Ethereum e da máquina virtual, que são "
"altamente complexas, estão em constante mudança e introduzem mudanças por meio "
"de hard forks incompatíveis com versões anteriores."

#: src/faq.md:153
msgid ""
"Inscriptions, on the other hand, depend on the Bitcoin blockchain, which is "
"relatively simple and conservative, and which introduces changes via "
"backwards-compatible soft forks."
msgstr ""
"As inscrições, por outro lado, dependem da blockchain do Bitcoin, que é "
"relativamente simples e conservadora, e que introduz mudanças por meio de "
"soft forks, compatíveis com versões anteriores."

#: src/faq.md:157
msgid "_Inscriptions are more secure._"
msgstr "_As inscrições são mais seguras._"

#: src/faq.md:159
msgid ""
"Inscriptions inherit Bitcoin's transaction model, which allow a user to see "
"exactly which inscriptions are being transferred by a transaction before "
"they sign it. Inscriptions can be offered for sale using partially signed "
"transactions, which don't require allowing a third party, such as an "
"exchange or marketplace, to transfer them on the user's behalf."
msgstr ""
"As inscrições herdam o modelo de transação do Bitcoin, que permite ao usuário "
"ver exatamente quais inscrições estão sendo transferidas por uma transação "
"antes de assiná-la. As inscrições podem ser colocadas à venda usando transações "
"parcialmente assinadas, que não exigem permissão de terceiros – como uma exchange ou "
"marketplace – para transferi-las em nome do usuário."

#: src/faq.md:165
msgid ""
"By comparison, Ethereum NFTs are plagued with end-user security "
"vulnerabilities. It is commonplace to blind-sign transactions, grant "
"third-party apps unlimited permissions over a user's NFTs, and interact with "
"complex and unpredictable smart contracts. This creates a minefield of "
"hazards for Ethereum NFT users which are simply not a concern for ordinal "
"theorists."
msgstr ""
"Em comparação, as NFTs do Ethereum estão infestadas de vulnerabilidades "
"de segurança para o usuário final. É comum assinar transações às cegas, "
"conceder permissões ilimitadas a aplicativos de terceiros sobre as NFTs "
"de um usuário e interagir com contratos inteligentes complexos e imprevisíveis. "
"Isso cria um campo minado de perigos para os usuários das NFTs do Ethereum que "
"simplesmente não são uma preocupação para os teóricos de Ordinals."

#: src/faq.md:171
msgid "_Inscriptions are scarcer._"
msgstr "_As inscrições são mais escassas._"

#: src/faq.md:173
msgid ""
"Inscriptions require bitcoin to mint, transfer, and store. This seems like a "
"downside on the surface, but the raison d'etre of digital artifacts is to be "
"scarce and thus valuable."
msgstr ""
"As inscrições exigem que bitcoin seja usado para criá-las, transferi-las e armazená-las. "
"Isso pode parecer uma desvantagem, mas a raison d'etre dos artefatos digitais "
"é ser escasso e, portanto, valioso."

#: src/faq.md:177
msgid ""
"Ethereum NFTs, on the other hand, can be minted in virtually unlimited "
"qualities with a single transaction, making them inherently less scarce, and "
"thus, potentially less valuable."
msgstr ""
"As NFTs do Ethereum, por outro lado, podem ser criadas em qualidades "
"virtualmente ilimitadas com uma única transação, tornando-as inerentemente "
"menos escassas e, portanto, potencialmente menos valiosas."

#: src/faq.md:181
msgid "_Inscriptions do not pretend to support on-chain royalties._"
msgstr "_As inscrições não pretendem apoiar royalties on-chain._"

#: src/faq.md:183
msgid ""
"On-chain royalties are a good idea in theory but not in practice. Royalty "
"payment cannot be enforced on-chain without complex and invasive "
"restrictions. The Ethereum NFT ecosystem is currently grappling with "
"confusion around royalties, and is collectively coming to grips with the "
"reality that on-chain royalties, which were messaged to artists as an "
"advantage of NFTs, are not possible, while platforms race to the bottom and "
"remove royalty support."
msgstr ""
"Os royalties na blockchain são, na teoria, uma boa ideia; mas não na prática. "
"O pagamento de royalties não pode ser aplicado on-chain sem restrições complexas "
"e invasivas. O ecossistema de NFTs do Ethereum está atualmente lutando contra a "
"confusão em torno dos royalties e está enfrentando coletivamente a realidade que "
"os royalties on-chain, que foram transmitidos aos artistas como uma vantagem das NFTs, "
"não são possíveis, enquanto as plataformas correm para o fundo do poço e removem o "
"apoio aos royalties."

#: src/faq.md:190
msgid ""
"Inscriptions avoid this situation entirely by making no false promises of "
"supporting royalties on-chain, thus avoiding the confusion, chaos, and "
"negativity of the Ethereum NFT situation."
msgstr ""
"As inscrições evitam totalmente esta situação, não fazendo falsas promessas "
"de apoio a royalties on-chain, evitando assim a confusão, o caos e a "
"negatividade da situação das NFTs do Ethereum."

#: src/faq.md:194
msgid "_Inscriptions unlock new markets._"
msgstr "_As inscrições abrem as portas para novos mercados._"

#: src/faq.md:196
msgid ""
"Bitcoin's market capitalization and liquidity are greater than Ethereum's by "
"a large margin. Much of this liquidity is not available to Ethereum NFTs, "
"since many Bitcoiners prefer not to interact with the Ethereum ecosystem due "
"to concerns related to simplicity, security, and decentralization."
msgstr ""
"A capitalização de mercado e a liquidez do Bitcoin são maiores do que as do "
"Ethereum por uma grande margem. Grande parte dessa liquidez não está disponível "
"para NFTs do Ethereum, uma vez que muitos Bitcoinheiros preferem não interagir com o "
"ecossistema do Ethereum devido a preocupações relacionadas à simplicidade, segurança "
"e descentralização."

#: src/faq.md:201
msgid ""
"Such Bitcoiners may be more interested in inscriptions than Ethereum NFTs, "
"unlocking new classes of collector."
msgstr ""
"Esses Bitcoinheiros podem estar mais interessados em inscrições do que em "
"NFTs do Ethereum, desbloqueando novas classes de colecionadores."

#: src/faq.md:204
msgid "_Inscriptions have a richer data model._"
msgstr "_As inscrições têm um modelo de dados mais rico._"

#: src/faq.md:206
msgid ""
"Inscriptions consist of a content type, also known as a MIME type, and "
"content, which is an arbitrary byte string. This is the same data model used "
"by the web, and allows inscription content to evolve with the web, and come "
"to support any kind of content supported by web browsers, without requiring "
"changes to the underlying protocol."
msgstr ""
"As inscrições consistem em um tipo de conteúdo, também conhecido como tipo MIME, "
"e conteúdo em si, que é uma sequência de bytes arbitrária. Este é o mesmo modelo "
"de dados usado pela web e permite que o conteúdo da inscrição evolua com a web e "
"venha a suportar qualquer tipo de conteúdo suportado por navegadores web, sem exigir "
"alterações no protocolo subjacente."

#: src/faq.md:212
msgid "RGB and Taro assets?"
msgstr "RGB e ativos do Taro?"

#: src/faq.md:214
msgid ""
"RGB and Taro are both second-layer asset protocols built on Bitcoin. "
"Compared to inscriptions, they are much more complicated, but much more "
"featureful."
msgstr ""
"RGB e Taro são protocolos de ativos de segunda camada construídos no Bitcoin. "
"Comparados às inscrições, eles são muito mais complicados, mas com muito "
"mais recursos."

#: src/faq.md:217
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas the primary use-case of RGB and Taro are fungible tokens, so the "
"user experience for inscriptions is likely to be simpler and more polished "
"than the user experience for RGB and Taro NFTs."
msgstr ""
"A teoria ordinal foi projetada desde o início para artefatos digitais, "
enquanto o principal caso de uso de RGB e Taro são tokens fungíveis, então a "
"experiência do usuário para inscrições provavelmente será mais simples e refinada "
"do que a experiência do usuário para NFTs do RGB e Taro."

#: src/faq.md:222
msgid ""
"RGB and Taro both store content off-chain, which requires additional "
"infrastructure, and which may be lost. By contrast, inscription content is "
"stored on-chain, and cannot be lost."
msgstr ""
"RGB e Taro armazenam conteúdo fora da cadeia (off-chain), o que requer infraestrutura "
"adicional e pode ser perdido. Por outro lado, o conteúdo de inscrição é "
"armazenado na cadeia (on-chain) e não pode ser perdido."

#: src/faq.md:226
msgid ""
"Ordinal theory, RGB, and Taro are all very early, so this is speculation, "
"but ordinal theory's focus may give it the edge in terms of features for "
"digital artifacts, including a better content model, and features like "
"globally unique symbols."
msgstr ""
"Tanto a teoria ordinal quanto RGB e Taro são muito novos, então isso é especulação, "
"mas o foco da teoria ordinal pode dar-lhe vantagem em termos de recursos para "
"artefatos digitais, incluindo um modelo de conteúdo melhor e recursos como "
"símbolos globalmente únicos."

#: src/faq.md:231
msgid "Counterparty assets?"
msgstr "Ativos do Counterparty?"

#: src/faq.md:233
msgid ""
"Counterparty has its own token, XCP, which is required for some "
"functionality, which makes most bitcoiners regard it as an altcoin, and not "
"an extension or second layer for bitcoin."
msgstr ""
"Counterparty tem seu próprio token, XCP, que é necessário para algumas "
"funcionalidades, algo que faz com que a maioria dos bitcoinheiros o considere uma altcoin, "
"e não uma extensão ou segunda camada do bitcoin."

#: src/faq.md:237
msgid ""
"Ordinal theory has been designed from the ground up for digital artifacts, "
"whereas Counterparty was primarily designed for financial token issuance."
msgstr ""
"A teoria ordinal foi projetada desde o início para artefatos digitais, "
"enquanto que o Counterparty foi projetado principalmente para a emissão de tokens financeiros."

#: src/faq.md:240
msgid "Inscriptions for…"
msgstr "Inscrições para..."

#: src/faq.md:243
msgid "Artists"
msgstr "Artistas"

#: src/faq.md:245
msgid ""
"_Inscriptions are on Bitcoin._ Bitcoin is the digital currency with the "
"highest status and greatest chance of long-term survival. If you want to "
"guarantee that your art survives into the future, there is no better way to "
"publish it than as inscriptions."
msgstr ""
"_As inscrições estão no Bitcoin._ O Bitcoin é a moeda digital com o status "
"mais alto e maior chance de sobrevivência a longo prazo. Se você deseja garantir "
"que sua arte sobreviverá no futuro, não há melhor maneira de publicá-la "
"do que como inscrições."

#: src/faq.md:250
msgid ""
"_Cheaper on-chain storage._ At $20,000 per BTC and the minimum relay fee of "
"1 satoshi per vbyte, publishing inscription content costs $50 per 1 million "
"bytes."
msgstr ""
"_Armazenamento on-chain mais barato._ Com o preço do BTC a 20 mil dólares e a taxa de "
"retransmissão mínima de 1 satoshi por vbyte, a publicação de conteúdo de inscrição "
"custa 50 dólares por 1 milhão de bytes."

#: src/faq.md:254
msgid ""
"_Inscriptions are early!_ Inscriptions are still in development, and have "
"not yet launched on mainnet. This gives you an opportunity to be an early "
"adopter, and explore the medium as it evolves."
msgstr ""
"_As inscrições são novas!_ As inscrições ainda estão em desenvolvimento e "
"ainda não foram lançadas na rede principal. Isso lhe dá a oportunidade de "
"ser um dos primeiros a adotar e explorar este meio à medida que ele evolui."

#: src/faq.md:258
msgid ""
"_Inscriptions are simple._ Inscriptions do not require writing or "
"understanding smart contracts."
msgstr ""
"_As inscrições são simples._ As inscrições não requerem redação ou "
"compreensão de contratos inteligentes."

#: src/faq.md:261
msgid ""
"_Inscriptions unlock new liquidity._ Inscriptions are more accessible and "
"appealing to bitcoin holders, unlocking an entirely new class of collector."
msgstr ""
"_As inscrições desbloqueiam nova liquidez._ As inscrições são mais acessíveis "
"e atraentes para os detentores de bitcoin, desbloqueando uma classe inteiramente "
"nova de colecionadores."

#: src/faq.md:264
msgid ""
"_Inscriptions are designed for digital artifacts._ Inscriptions are designed "
"from the ground up to support NFTs, and feature a better data model, and "
"features like globally unique symbols and enhanced provenance."
msgstr ""
"_As inscrições são projetadas para artefatos digitais._ As inscrições são projetadas "
"desde o início para oferecer suporte a NFTs e apresentam um modelo de dados melhor "
"e recursos como símbolos globalmente exclusivos e procedência aprimorada."

#: src/faq.md:268
msgid ""
"_Inscriptions do not support on-chain royalties._ This is negative, but only "
"depending on how you look at it. On-chain royalties have been a boon for "
"creators, but have also created a huge amount of confusion in the Ethereum "
"NFT ecosystem. The ecosystem now grapples with this issue, and is engaged in "
"a race to the bottom, towards a royalties-optional future. Inscriptions have "
"no support for on-chain royalties, because they are technically infeasible. "
"If you choose to create inscriptions, there are many ways you can work "
"around this limitation: withhold a portion of your inscriptions for future "
"sale, to benefit from future appreciation, or perhaps offer perks for users "
"who respect optional royalties."
msgstr ""
"_As inscrições não suportam royalties na rede._ Isso é negativo, mas depende "
"apenas de como você olha para isso. Os royalties na rede têm sido uma bênção "
"para os criadores, mas também criaram uma enorme confusão no ecossistema de NFTs "
"do Ethereum. O ecossistema agora enfrenta essa questão e está engajado em uma "
"corrida para o fundo do poço, em direção a um futuro com royalties opcionais. "
"As inscrições não têm suporte para royalties na rede, porque são tecnicamente inviáveis. "
"Se você optar por criar inscrições, haverá muitas maneiras de contornar essa limitação: "
"reter uma parte de suas inscrições para venda futura, para se beneficiar de valorização "
"futura, ou talvez oferecer vantagens para usuários que respeitem royalties opcionais."

#: src/faq.md:279
msgid "Collectors"
msgstr "Colecionadores"

#: src/faq.md:281
msgid ""
"_Inscriptions are simple, clear, and have no surprises._ They are always "
"immutable and on-chain, with no special due diligence required."
msgstr ""
"_As inscrições são simples, claras e sem surpresas._ Elas são sempre "
"imutáveis e on-line, sem necessidade de diligência especial."

#: src/faq.md:284
msgid ""
"_Inscriptions are on Bitcoin._ You can verify the location and properties of "
"inscriptions easily with Bitcoin full node that you control."
msgstr ""
"_As inscrições estão no Bitcoin._ Você pode verificar facilmente a localização "
"e as propriedades das inscrições com o nó completo do Bitcoin que você controla."

#: src/faq.md:287
msgid "Bitcoiners"
msgstr "Bitcoinheiros"

#: src/faq.md:289
msgid ""
"Let me begin this section by saying: the most important thing that the "
"Bitcoin network does is decentralize money. All other use-cases are "
"secondary, including ordinal theory. The developers of ordinal theory "
"understand and acknowledge this, and believe that ordinal theory helps, at "
"least in a small way, Bitcoin's primary mission."
msgstr ""
"Deixe-me começar esta seção dizendo: a coisa mais importante que a rede "
"Bitcoin faz é descentralizar o dinheiro. Todos os outros casos de uso são "
"secundários, incluindo a teoria ordinal. Os desenvolvedores da teoria ordinal "
"entendem e reconhecem isso, e acreditam que a teoria ordinal ajuda, pelo menos "
"em pequena escala, a missão principal do Bitcoin."

#: src/faq.md:295
msgid ""
"Unlike many other things in the altcoin space, digital artifacts have merit. "
"There are, of course, a great deal of NFTs that are ugly, stupid, and "
"fraudulent. However, there are many that are fantastically creative, and "
"creating and collecting art has been a part of the human story since its "
"inception, and predates even trade and money, which are also ancient "
"technologies."
msgstr ""
"Ao contrário de muitas outras coisas no espaço altcoin, os artefatos digitais "
"têm mérito. Existem, é claro, muitas NFTs que são feias, estúpidas e fraudulentas. "
"No entanto, muitos são fantasticamente criativos, e criar e coletar arte tem sido parte"
"da história humana desde o seu início e antecede até mesmo o comércio e o dinheiro, "
"que também são tecnologias antigas."

#: src/faq.md:302
msgid ""
"Bitcoin provides an amazing platform for creating and collecting digital "
"artifacts in a secure, decentralized way, that protects users and artists in "
"the same way that it provides an amazing platform for sending and receiving "
"value, and for all the same reasons."
msgstr ""
"O Bitcoin fornece uma plataforma incrível para criar e coletar artefatos digitais "
"de forma segura e descentralizada, que protege usuários e artistas da mesma forma "
"que fornece uma plataforma incrível para enviar e receber valor, e pelos mesmos motivos."

#: src/faq.md:307
msgid ""
"Ordinals and inscriptions increase demand for Bitcoin block space, which "
"increase Bitcoin's security budget, which is vital for safeguarding "
"Bitcoin's transition to a fee-dependent security model, as the block subsidy "
"is halved into insignificance."
msgstr ""
"Os Ordinals e inscrições aumentam a demanda por espaço de blocos do Bitcoin, "
"o que aumenta o orçamento de segurança do Bitcoin, o que é vital para salvaguardar "
"a transição do Bitcoin para um modelo de segurança dependente de taxas, já que o "
"subsídio de blocos é reduzido pela metade e se torna insignificante."

#: src/faq.md:312
msgid ""
"Inscription content is stored on-chain, and thus the demand for block space "
"for use in inscriptions is unlimited. This creates a buyer of last resort "
"for _all_ Bitcoin block space. This will help support a robust fee market, "
"which ensures that Bitcoin remains secure."
msgstr ""
"O conteúdo da inscrição é armazenado na cadeia (on-chain) e, portanto, "
"a demanda por espaço de bloco para uso em inscrições é ilimitada. Isso cria "
"um comprador de último recurso para _todos_ os espaços de blocos do Bitcoin. "
"Isso ajudará a apoiar um mercado de taxas robusto, que garante que o Bitcoin permaneça seguro."

#: src/faq.md:317
msgid ""
"Inscriptions also counter the narrative that Bitcoin cannot be extended or "
"used for new use-cases. If you follow projects like DLCs, Fedimint, "
"Lightning, Taro, and RGB, you know that this narrative is false, but "
"inscriptions provide a counter argument which is easy to understand, and "
"which targets a popular and proven use case, NFTs, which makes it highly "
"legible."
msgstr ""
"As inscrições também contrariam a narrativa de que o Bitcoin não pode ser estendido "
"ou usado para novos casos de uso. Se você acompanha projetos como DLCs, Fedimint, "
"Lightning, Taro e RGB, você sabe que essa narrativa é falsa, mas as inscrições fornecem "
"um contra-argumento que é fácil de entender e tem como alvo um caso de uso popular "
"e comprovado, as NFTs, o que o torna altamente legível."

#: src/faq.md:323
msgid ""
"If inscriptions prove, as the authors hope, to be highly sought after "
"digital artifacts with a rich history, they will serve as a powerful hook "
"for Bitcoin adoption: come for the fun, rich art, stay for the decentralized "
"digital money."
msgstr ""
"Se as inscrições provarem, como os autores esperam, serem artefatos digitais "
"muito procurados com uma história rica, elas servirão como um gancho poderoso "
"para a adoção do Bitcoin: venha pela diversão e arte rica, fique pelo dinheiro "
"digital descentralizado."

#: src/faq.md:327
msgid ""
"Inscriptions are an extremely benign source of demand for block space. "
"Unlike, for example, stablecoins, which potentially give large stablecoin "
"issuers influence over the future of Bitcoin development, or DeFi, which "
"might centralize mining by introducing opportunities for MEV, digital art "
"and collectables on Bitcoin, are unlikely to produce individual entities "
"with enough power to corrupt Bitcoin. Art is decentralized."
msgstr ""
"As inscrições são uma fonte extremamente benigna de demanda por espaço de bloco. "
"Ao contrário, por exemplo, das stablecoins, que potencialmente dão aos grandes "
"emissores de stablecoin influência sobre o futuro do desenvolvimento do Bitcoin, "
"ou DeFi, que pode centralizar a mineração introduzindo oportunidades para MEV, "
"arte digital e colecionáveis no Bitcoin são improváveis de produzir entidades "
"individuais com poder suficiente para corromper o Bitcoin. A arte é descentralizada."

#: src/faq.md:334
msgid ""
"Inscription users and service providers are incentivized to run Bitcoin full "
"nodes, to publish and track inscriptions, and thus throw their economic "
"weight behind the honest chain."
msgstr ""
"Os usuários de inscrições e provedores de serviços são incentivados a executar "
"nós completos do Bitcoin, a publicar e rastrear inscrições e, assim, colocar seu "
"peso econômico na cadeia honesta."

#: src/faq.md:338
msgid ""
"Ordinal theory and inscriptions do not meaningfully affect Bitcoin's "
"fungibility. Bitcoin users can ignore both and be unaffected."
msgstr ""
"A teoria ordinal e as inscrições não afetam significativamente a fungibilidade "
"do Bitcoin. Os usuários do Bitcoin podem ignorar ambas e não serem afetados."

#: src/faq.md:341
msgid ""
"We hope that ordinal theory strengthens and enriches bitcoin, and gives it "
"another dimension of appeal and functionality, enabling it more effectively "
"serve its primary use case as humanity's decentralized store of value."
msgstr ""
"Esperamos que a teoria ordinal fortaleça e enriqueça o bitcoin, e lhe dê outra "
"dimensão de apelo e funcionalidade, permitindo-lhe servir de forma mais eficaz "
"o seu caso de uso principal como reserva descentralizada de valor da humanidade."

#: src/contributing.md:1
msgid "Contributing to `ord`"
msgstr "Contribuindo para `ord`"

#: src/contributing.md:4
msgid "Suggested Steps"
msgstr "Etapas sugeridas"

#: src/contributing.md:7
msgid "Find an issue you want to work on."
msgstr "Encontre um problema no qual deseja trabalhar."

#: src/contributing.md:8
msgid ""
"Figure out what would be a good first step towards resolving the issue. This "
"could be in the form of code, research, a proposal, or suggesting that it be "
"closed, if it's out of date or not a good idea in the first place."
msgstr ""
"Descubra qual seria um bom primeiro passo para resolver o problema. Isso pode "
"ser na forma de código, pesquisa, uma proposta ou sugestão de que seja encerrado, "
"se estiver desatualizado ou não for uma boa ideia em primeiro lugar."

#: src/contributing.md:11
msgid ""
"Comment on the issue with an outline of your suggested first step, and "
"asking for feedback. Of course, you can dive in and start writing code or "
"tests immediately, but this avoids potentially wasted effort, if the issue "
"is out of date, not clearly specified, blocked on something else, or "
"otherwise not ready to implement."
msgstr ""
"Comente sobre o problema com um esboço da primeira etapa sugerida e peça feedback. "
"Claro, você pode mergulhar e começar a escrever código ou testes imediatamente, "
"mas isso evita um esforço potencialmente desperdiçado, se o problema estiver desatualizado, "
"não claramente especificado, bloqueado em outra coisa ou de outra forma não pronto para implementação."

#: src/contributing.md:16
msgid ""
"If the issue requires a code change or bugfix, open a draft PR with tests, "
"and ask for feedback. This makes sure that everyone is on the same page "
"about what needs to be done, or what the first step in solving the issue "
"should be. Also, since tests are required, writing the tests first makes it "
"easy to confirm that the change can be tested easily."
msgstr ""
"Se o problema exigir uma alteração de código ou correção de bug, abra um rascunho "
"de PR com testes e peça feedback. Isso garante que todos estejam na mesma página "
"sobre o que precisa ser feito ou qual deve ser o primeiro passo para resolver o problema. "
"Além disso, como os testes são obrigatórios, escrever os testes primeiro facilita a "
"confirmação de que a alteração pode ser testada facilmente."

#: src/contributing.md:21
msgid ""
"Mash the keyboard randomly until the tests pass, and refactor until the code "
"is ready to submit."
msgstr ""
"Esmague o teclado aleatoriamente até que os testes sejam aprovados e refatore "
"até que o código esteja pronto para ser enviado."

#: src/contributing.md:23
msgid "Mark the PR as ready to review."
msgstr "Marque a PR como pronto para revisão."

#: src/contributing.md:24
msgid "Revise the PR as needed."
msgstr "Revise a PR conforme necessário."

#: src/contributing.md:25
msgid "And finally, mergies!"
msgstr "E finalmente, fusões!"

#: src/contributing.md:27
msgid "Start small"
msgstr "Comece pequeno"

#: src/contributing.md:30
msgid ""
"Small changes will allow you to make an impact quickly, and if you take the "
"wrong tack, you won't have wasted much time."
msgstr ""
"Pequenas mudanças permitirão que você cause um impacto rapidamente e, se você "
"tomar a atitude errada, não perderá muito tempo."

#: src/contributing.md:33
msgid "Ideas for small issues:"
msgstr "Ideias para pequenos problemas:"

#: src/contributing.md:34
msgid "Add a new test or test case that increases test coverage"
msgstr "Adicione um novo teste ou caso de teste que aumente a cobertura do teste"

#: src/contributing.md:35
msgid "Add or improve documentation"
msgstr "Adicionar ou melhorar a documentação"

#: src/contributing.md:36
msgid ""
"Find an issue that needs more research, and do that research and summarize "
"it in a comment"
msgstr ""
"Encontre um problema que precise de mais pesquisa, faça essa pesquisa e resuma "
"em um comentário"

#: src/contributing.md:38
msgid "Find an out-of-date issue and comment that it can be closed"
msgstr "Encontre um problema desatualizado e comente que ele pode ser resolvido"

#: src/contributing.md:39
msgid ""
"Find an issue that shouldn't be done, and provide constructive feedback "
"detailing why you think that is the case"
msgstr ""
"Encontre um problema que não deveria ser resolvido e forneça feedback construtivo "
"detalhando por que você acha que esse é o caso"

#: src/contributing.md:42
msgid "Merge early and often"
msgstr "Mescle cedo e frequentemente"

#: src/contributing.md:45
msgid ""
"Break up large tasks into multiple smaller steps that individually make "
"progress. If there's a bug, you can open a PR that adds a failing ignored "
"test. This can be merged, and the next step can be to fix the bug and "
"unignore the test. Do research or testing, and report on your results. Break "
"a feature into small sub-features, and implement them one at a time."
msgstr ""
"Divida tarefas grandes em várias etapas menores que progridem individualmente. "
"Se houver um bug, você pode abrir uma PR que adiciona um teste ignorado com falha. "
"Isso pode ser mesclado e a próxima etapa pode ser corrigir o bug e ignorar o teste. "
"Faça pesquisas ou testes e relate seus resultados. Divida um recurso em pequenos "
"subrecursos e implemente-os um de cada vez."

#: src/contributing.md:51
msgid ""
"Figuring out how to break down a larger PR into smaller PRs where each can "
"be merged is an art form well-worth practicing. The hard part is that each "
"PR must itself be an improvement."
msgstr ""
"Descobrir como dividir uma PR maior em PRs menores, onde cada uma pode ser mesclada, "
"é uma forma de arte que vale a pena praticar. A parte difícil é que cada PR "
"deve ser, por si só, uma melhoria."

#: src/contributing.md:55
msgid ""
"I strive to follow this advice myself, and am always better off when I do."
msgstr ""
"Eu mesmo me esforço para seguir esse conselho e sempre fico melhor quando o faço."

#: src/contributing.md:57
msgid ""
"Small changes are fast to write, review, and merge, which is much more fun "
"than laboring over a single giant PR that takes forever to write, review, "
"and merge. Small changes don't take much time, so if you need to stop "
"working on a small change, you won't have wasted much time as compared to a "
"larger change that represents many hours of work. Getting a PR in quickly "
"improves the project a little bit immediately, instead of having to wait a "
"long time for larger improvement. Small changes are less likely to "
"accumulate merge conflict. As the Athenians said: _The fast commit what they "
"will, the slow merge what they must._"
msgstr ""
"Pequenas mudanças são rápidas de escrever, revisar e mesclar, o que é muito mais "
"divertido do que trabalhar em uma única PR gigante que leva uma eternidade para "
"escrever, revisar e mesclar. Pequenas mudanças não levam muito tempo, então se você "
"precisar parar de trabalhar em uma pequena mudança, você não terá perdido muito tempo "
"em comparação com uma mudança maior que representa muitas horas de trabalho. Conseguir "
"uma PR rapidamente melhora o projeto um pouco imediatamente, em vez de ter que esperar "
"muito tempo por melhorias maiores. Pequenas mudanças têm menos probabilidade de acumular "
"conflitos de fusão. Como disseram os atenienses: _Os rápidos comprometem o que querem, "
"os lentos fundem o que devem._ "

#: src/contributing.md:67
msgid "Get help"
msgstr "Busque ajuda"

#: src/contributing.md:70
msgid ""
"If you're stuck for more than 15 minutes, ask for help, like a Rust Discord, "
"Stack Exchange, or in a project issue or discussion."
msgstr ""
"Se você ficar preso por mais de 15 minutos, peça ajuda, como um Discord de Rust, "
"Stack Exchange ou em um problema ou discussão de projeto."

#: src/contributing.md:73
msgid "Practice hypothesis-driven debugging"
msgstr "Pratique a depuração baseada em hipóteses"

#: src/contributing.md:76
msgid ""
"Formulate a hypothesis as to what is causing the problem. Figure out how to "
"test that hypothesis. Perform that tests. If it works, great, you fixed the "
"issue or now you know how to fix the issue. If not, repeat with a new "
"hypothesis."
msgstr ""
"Formule uma hipótese sobre o que está causando o problema. Descubra como testar "
"essa hipótese. Execute esses testes. Se funcionar, ótimo, você corrigiu o problema "
"ou agora sabe como corrigi-lo. Caso contrário, repita com um nova hipótese."

#: src/contributing.md:81
msgid "Pay attention to error messages"
msgstr "Preste atenção às mensagens de erro"

#: src/contributing.md:84
msgid "Read all error messages and don't tolerate warnings."
msgstr "Leia todas as mensagens de erro e não tolere avisos."

#: src/donate.md:4
msgid ""
"Ordinals is open-source and community funded. The current lead maintainer of "
"`ord` is [raphjaph](https://github.com/raphjaph/). Raph's work on `ord` is "
"entirely funded by donations. If you can, please consider donating!"
msgstr ""
Translation result
"Ordinals tem código aberto e é financiado pela comunidade. O atual mantenedor "
"principal do `ord` é [raphjaph](https://github.com/raphjaph/). O trabalho de Raph "
"no `ord` é inteiramente financiado por doações. Se você puder, por favor considere doar!"

#: src/donate.md:8
msgid ""
"The donation address for Bitcoin is "
"[**************************************************************](https://mempool.space/address/**************************************************************). "
"The donation address for inscriptions is "
"[**************************************************************](https://mempool.space/address/**************************************************************)."
msgstr ""
"O endereço de doação para bitcoin é "
"[**************************************************************](https://mempool.space/address/**************************************************************). "
"O endereço de doação para inscrições é "
"[**************************************************************](https://mempool.space/address/**************************************************************)."

#: src/donate.md:11
msgid ""
"Both addresses are in a 2 of 4 multisig wallet with keys held by "
"[raphjaph](https://twitter.com/raphjaph), "
"[erin](https://twitter.com/realizingerin), "
"[rodarmor](https://twitter.com/rodarmor), and "
"[ordinally](https://twitter.com/veryordinally)."
msgstr ""
"Ambos os endereços estão em uma carteira multisig 2 de 4 com chaves em poder de "
"[raphjaph](https://twitter.com/raphjaph), "
"[erin](https://twitter.com/realizingerin), "
"[rodarmor](https://twitter.com/rodarmor) e "
"[ordinally](https://twitter.com/veryordinally)."

#: src/donate.md:17
msgid ""
"Donations received will go towards funding maintenance and development of "
"`ord`, as well as hosting costs for [ordinals.com](https://ordinals.com)."
msgstr ""
"As doações recebidas serão destinadas ao financiamento da manutenção e desenvolvimento "
"do `ord`, bem como aos custos de hospedagem do [ordinals.com](https://ordinals.com)."

#: src/donate.md:20
msgid "Thank you for donating!"
msgstr "Obrigado por doar!"

#: src/guides.md:1
msgid "Ordinal Theory Guides"
msgstr "Guias de teoria ordinal"

#: src/guides.md:4
msgid ""
"See the table of contents for a list of guides, including a guide to the "
"explorer, a guide for sat hunters, and a guide to inscriptions."
msgstr ""
"Veja o índice para obter uma lista de guias, incluindo um guia para o explorador, "
"um guia para caçadores de sat e um guia para inscrições."

#: src/guides/explorer.md:1
msgid "Ordinal Explorer"
msgstr "Explorador Ordinal"

#: src/guides/explorer.md:4
msgid ""
"The `ord` binary includes a block explorer. We host a instance of the block "
"explorer on mainnet at [ordinals.com](https://ordinals.com), and on signet "
"at [signet.ordinals.com](https://signet.ordinals.com)."
msgstr ""
"O binário do `ord` inclui um explorador de blocos. Hospedamos uma instância "
"do explorador de blocos na mainnet em [ordinals.com](https://ordinals.com) "
"e no signet em [signet.ordinals.com](https://signet.ordinals.com)."

#: src/guides/explorer.md:8
msgid "Running The Explorer"
msgstr "Executando o Explorador"

#: src/guides/explorer.md:9
msgid "The server can be run locally with:"
msgstr "O servidor pode ser executado localmente com:"

#: src/guides/explorer.md:11
msgid "`ord server`"
msgstr "`ord server`"

#: src/guides/explorer.md:13
msgid "To specify a port add the `--http-port` flag:"
msgstr "Para especificar uma porta, adicione o sinalizador `--http-port`:"

#: src/guides/explorer.md:15
msgid "`ord server --http-port 8080`"
msgstr "`ord server --http-port 8080`"

#: src/guides/explorer.md:17
msgid "To test how your inscriptions will look you can run:"
msgstr "Para testar a aparência de suas inscrições, você pode executar:"

#: src/guides/explorer.md:19
msgid "`ord preview <FILE1> <FILE2> ...`"
msgstr "`ord preview <ARCHIVO1> <ARCHIVO2> ...`"

#: src/guides/explorer.md:21
msgid "Search"
msgstr "Procurar"

#: src/guides/explorer.md:24
msgid "The search box accepts a variety of object representations."
msgstr "A caixa de pesquisa aceita uma variedade de representações de objetos."

#: src/guides/explorer.md:26
msgid "Blocks"
msgstr "Blocos"

#: src/guides/explorer.md:28
msgid "Blocks can be searched by hash, for example, the genesis block:"
msgstr "Os blocos podem ser pesquisados por hash, por exemplo, o bloco gênesis:"

#: src/guides/explorer.md:30
msgid ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://ordinals.com/search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"
msgstr ""
"[000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f](https://ordinals.com/search/000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f)"

#: src/guides/explorer.md:32
msgid "Transactions"
msgstr "Transações"

#: src/guides/explorer.md:34
msgid ""
"Transactions can be searched by hash, for example, the genesis block "
"coinbase transaction:"
msgstr ""
"As transações podem ser pesquisadas por hash, por exemplo, a transação "
"coinbase do bloco gênesis:"

#: src/guides/explorer.md:37
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b)"

#: src/guides/explorer.md:39
msgid "Outputs"
msgstr "Saídas"

#: src/guides/explorer.md:41
msgid ""
"Transaction outputs can searched by outpoint, for example, the only output "
"of the genesis block coinbase transaction:"
msgstr ""
"As saídas da transação podem ser pesquisadas por ponto final, por exemplo, "
"a única saída da transação coinbase do bloco gênesis:"

#: src/guides/explorer.md:44
msgid ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"
msgstr ""
"[4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0](https://ordinals.com/search/4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b:0)"

#: src/guides/explorer.md:46
msgid "Sats"
msgstr "Sats"

#: src/guides/explorer.md:48
msgid ""
"Sats can be searched by integer, their position within the entire bitcoin "
"supply:"
msgstr ""
"Sats podem ser pesquisados por número inteiro, sua posição dentro de toda a "
"oferta de bitcoin "

#: src/guides/explorer.md:51
msgid "[2099994106992659](https://ordinals.com/search/2099994106992659)"
msgstr "[2099994106992659](https://ordinals.com/search/2099994106992659)"

#: src/guides/explorer.md:53
msgid "By decimal, their block and offset within that block:"
msgstr "Por decimal, seu bloco e deslocamento dentro daquele bloco:"

#: src/guides/explorer.md:55
msgid "[481824.0](https://ordinals.com/search/481824.0)"
msgstr "[481824.0](https://ordinals.com/search/481824.0)"

#: src/guides/explorer.md:57
msgid ""
"By degree, their cycle, blocks since the last halving, blocks since the last "
"difficulty adjustment, and offset within their block:"
msgstr ""
"Por grau, seu ciclo, blocos desde o último halving, blocos desde o último ajuste "
"de dificuldade e deslocamento dentro do bloco:"

#: src/guides/explorer.md:60
msgid "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"
msgstr "[1°0′0″0‴](https://ordinals.com/search/1°0′0″0‴)"

#: src/guides/explorer.md:62
msgid ""
"By name, their base 26 representation using the letters \"a\" through \"z\":"
msgstr ""
"Por nome, sua representação na base 26 usando as letras \"a\" até \"z\":"

#: src/guides/explorer.md:64
msgid "[ahistorical](https://ordinals.com/search/ahistorical)"
msgstr "[ahistorical](https://ordinals.com/search/ahistorical)"

#: src/guides/explorer.md:66
msgid ""
"Or by percentile, the percentage of bitcoin's supply that has been or will "
"have been issued when they are mined:"
msgstr ""
"Ou por percentil, a porcentagem do oferta de bitcoin que foi ou terá sido "
"emitida quando ele for minerado:"

#: src/guides/explorer.md:69
msgid "[100%](https://ordinals.com/search/100%)"
msgstr "[100%](https://ordinals.com/search/100%)"

#: src/guides/inscriptions.md:1
msgid "Ordinal Inscription Guide"
msgstr "Guia de inscrição ordinal"

#: src/guides/inscriptions.md:4
msgid ""
"Individual sats can be inscribed with arbitrary content, creating "
"Bitcoin-native digital artifacts that can be held in a Bitcoin wallet and "
"transferred using Bitcoin transactions. Inscriptions are as durable, "
"immutable, secure, and decentralized as Bitcoin itself."
msgstr ""
"Sats individuais podem ser inscritos com conteúdo arbitrário, criando "
"artefatos digitais nativos do Bitcoin que podem ser mantidos em uma carteira "
"Bitcoin e transferidos usando transações Bitcoin. As inscrições são tão duráveis, "
"imutáveis, seguras e descentralizadas quanto o próprio Bitcoin."

#: src/guides/inscriptions.md:9
msgid ""
"Working with inscriptions requires a Bitcoin full node, to give you a view "
"of the current state of the Bitcoin blockchain, and a wallet that can create "
"inscriptions and perform sat control when constructing transactions to send "
"inscriptions to another wallet."
msgstr ""
"Trabalhar com inscrições requer um nó completo do Bitcoin, para fornecer uma "
"visão do estado atual da blockchain do Bitcoin, e uma carteira que pode criar "
"inscrições e realizar controle de sat ao construir transações para enviar "
"inscrições para outra carteira."

#: src/guides/inscriptions.md:14
msgid ""
"Bitcoin Core provides both a Bitcoin full node and wallet. However, the "
"Bitcoin Core wallet cannot create inscriptions and does not perform sat "
"control."
msgstr ""
"O Bitcoin Core fornece um nó completo e uma carteira Bitcoin. No entanto, "
"a carteira Bitcoin Core não pode criar inscrições e não realiza controle de sat."

#: src/guides/inscriptions.md:17
msgid ""
"This requires [`ord`](https://github.com/ordinals/ord), the ordinal utility. "
"`ord` doesn't implement its own wallet, so `ord wallet` subcommands interact "
"with Bitcoin Core wallets."
msgstr ""
"Isso requer [`ord`](https://github.com/ordinals/ord), o utilitário ordinal. "
"`ord` não implementa sua própria carteira, então os subcomandos `ord wallet` "
"interagem com carteiras Bitcoin Core."

#: src/guides/inscriptions.md:21
msgid "This guide covers:"
msgstr "Este guia cobre:"

#: src/guides/inscriptions.md:23
#: src/guides/inscriptions.md:39
msgid "Installing Bitcoin Core"
msgstr "Instalando Bitcoin Core"

#: src/guides/inscriptions.md:24
msgid "Syncing the Bitcoin blockchain"
msgstr "Sincronizando a blockchain do Bitcoin"

#: src/guides/inscriptions.md:25
msgid "Creating a Bitcoin Core wallet"
msgstr "Criando uma carteira Bitcoin Core"

#: src/guides/inscriptions.md:26
msgid "Using `ord wallet receive` to receive sats"
msgstr "Usando `ord wallet receive` para receber sats"

#: src/guides/inscriptions.md:27
msgid "Creating inscriptions with `ord wallet inscribe`"
msgstr "Criando inscrições com `ord wallet inscribe`"

#: src/guides/inscriptions.md:28
msgid "Sending inscriptions with `ord wallet send`"
msgstr ""Enviando inscrições com `ord wallet send`"

#: src/guides/inscriptions.md:29
msgid "Receiving inscriptions with `ord wallet receive`"
msgstr "Recebendo inscrições com `ord wallet receive`"

#: src/guides/inscriptions.md:31
msgid "Getting Help"
msgstr "Obtendo Ajuda"

#: src/guides/inscriptions.md:34
msgid ""
"If you get stuck, try asking for help on the [Ordinals Discord "
"Server](https://discord.com/invite/87cjuz4FYg), or checking GitHub for "
"relevant [issues](https://github.com/ordinals/ord/issues) and "
"[discussions](https://github.com/ordinals/ord/discussions)."
msgstr ""
"Se você tiver dúvidas, tente pedir ajuda no [Ordinals Discord "
"Server](https://discord.com/invite/87cjuz4FYg) ou verifique o GitHub para "
"[problemas relevantes](https://github.com/ordinals/ord/issues) e "
"[discussões](https://github.com/ordinals/ord/discussions)."

#: src/guides/inscriptions.md:42
msgid ""
"Bitcoin Core is available from [bitcoincore.org](https://bitcoincore.org/) "
"on the [download page](https://bitcoincore.org/en/download/)."
msgstr ""
"O Bitcoin Core está disponível em [bitcoincore.org](https://bitcoincore.org/) "
"na [página de download](https://bitcoincore.org/en/download/)."

#: src/guides/inscriptions.md:45
msgid "Making inscriptions requires Bitcoin Core 24 or newer."
msgstr "Fazer inscrições requer Bitcoin Core 24 ou mais recente."

#: src/guides/inscriptions.md:47
msgid ""
"This guide does not cover installing Bitcoin Core in detail. Once Bitcoin "
"Core is installed, you should be able to run `bitcoind -version` "
"successfully from the command line."
msgstr ""
"Este guia não cobre a instalação do Bitcoin Core em detalhes. Depois que o "
"Bitcoin Core estiver instalado, você poderá executar `bitcoind -version` "
"com sucesso a partir da linha de comando."

#: src/guides/inscriptions.md:51
msgid "Configuring Bitcoin Core"
msgstr "Configurando o Bitcoin Core"

#: src/guides/inscriptions.md:54
msgid "`ord` requires Bitcoin Core's transaction index."
msgstr "`ord` requer o índice de transação do Bitcoin Core."

#: src/guides/inscriptions.md:56
msgid ""
"To configure your Bitcoin Core node to maintain a transaction index, add the "
"following to your `bitcoin.conf`:"
msgstr ""
"Para configurar seu nó Bitcoin Core para manter um índice de transação, adicione "
"o seguinte ao seu `bitcoin.conf`:"

#: src/guides/inscriptions.md:59
#: src/guides/sat-hunting.md:30
msgid ""
"```\n"
"txindex=1\n"
"```"
msgstr ""
"```\n"
"txindex=1\n"
"```"

#: src/guides/inscriptions.md:63
msgid "Or, run `bitcoind` with `-txindex`:"
msgstr "Ou execute `bitcoind` com `-txindex`:"

#: src/guides/inscriptions.md:65
#: src/guides/inscriptions.md:74
msgid ""
"```\n"
"bitcoind -txindex\n"
"```"
msgstr ""
"```\n"
"bitcoind -txindex\n"
"```"

#: src/guides/inscriptions.md:69
msgid "Syncing the Bitcoin Blockchain"
msgstr ""Sincronizando a Blockchain do Bitcoin"

#: src/guides/inscriptions.md:72
msgid "To sync the chain, run:"
msgstr "Para sincronizar a cadeia, execute:"

#: src/guides/inscriptions.md:78
msgid "…and leave it running until `getblockcount`:"
msgstr "…e deixe rodando até `getblockcount`:"

#: src/guides/inscriptions.md:80
msgid ""
"```\n"
"bitcoin-cli getblockcount\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli getblockcount\n"
"```"

#: src/guides/inscriptions.md:84
msgid ""
"agrees with the block count on a block explorer like [the mempool.space "
"block explorer](https://mempool.space/). `ord` interacts with `bitcoind`, so "
"you should leave `bitcoind` running in the background when you're using "
"`ord`."
msgstr ""
"concorde com a contagem de blocos em um explorador de blocos como [o explorador "
"de blocos mempool.space](https://mempool.space/). `ord` interage com `bitcoind`, "
"então você deve deixar `bitcoind` rodando em segundo plano quando você estiver "
"usando o `ord`."

#: src/guides/inscriptions.md:88
msgid "Installing `ord`"
msgstr "Instalando o `ord`"

#: src/guides/inscriptions.md:91
msgid ""
"The `ord` utility is written in Rust and can be built from "
"[source](https://github.com/ordinals/ord). Pre-built binaries are available "
"on the [releases page](https://github.com/ordinals/ord/releases)."
msgstr ""
"O utilitário `ord` é escrito em Rust e pode ser compilado a partir da "
"[fonte](https://github.com/ordinals/ord). Binários pré-construídos estão disponíveis "
"na [página de lançamentos](https://github.com/ordinals/ord/releases)."

#: src/guides/inscriptions.md:95
msgid "You can install the latest pre-built binary from the command line with:"
msgstr "Você pode instalar o binário pré-construído mais recente a partir da linha de comando com:"

#: src/guides/inscriptions.md:97
msgid ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"
msgstr ""
"```sh\n"
"curl --proto '=https' --tlsv1.2 -fsLS https://ordinals.com/install.sh | bash "
"-s\n"
"```"

#: src/guides/inscriptions.md:101
msgid "Once `ord` is installed, you should be able to run:"
msgstr "Depois que o `ord` estiver instalado, você poderá executar:"

#: src/guides/inscriptions.md:103
msgid ""
"```\n"
"ord --version\n"
"```"
msgstr ""
"```\n"
"ord --version\n"
"```"

#: src/guides/inscriptions.md:107
msgid "Which prints out `ord`'s version number."
msgstr "Que imprime o número da versão do `ord`."

#: src/guides/inscriptions.md:109
msgid "Creating a Bitcoin Core Wallet"
msgstr "Criando uma carteira Bitcoin Core"

#: src/guides/inscriptions.md:112
msgid ""
"`ord` uses Bitcoin Core to manage private keys, sign transactions, and "
"broadcast transactions to the Bitcoin network."
msgstr ""
"`ord` usa o Bitcoin Core para gerenciar chaves privadas, assinar transações "
"e transmitir transações para a rede do Bitcoin."

#: src/guides/inscriptions.md:115
msgid "To create a Bitcoin Core wallet named `ord` for use with `ord`, run:"
msgstr "Para criar uma carteira do Bitcoin Core chamada `ord` para usar com `ord`, execute:"

#: src/guides/inscriptions.md:117
msgid ""
"```\n"
"ord wallet create\n"
"```"
msgstr ""
"```\n"
"ord wallet create\n"
"```"

#: src/guides/inscriptions.md:121
msgid "Receiving Sats"
msgstr "Recebendo Sats"

#: src/guides/inscriptions.md:124
msgid ""
"Inscriptions are made on individual sats, using normal Bitcoin transactions "
"that pay fees in sats, so your wallet will need some sats."
msgstr ""
"As inscrições são feitas em sats individuais, usando transações normais de Bitcoin "
"que pagam taxas em sats, então sua carteira precisará de alguns sats."

#: src/guides/inscriptions.md:127
msgid "Get a new address from your `ord` wallet by running:"
msgstr "Obtenha um novo endereço da sua carteira `ord` executando:"

#: src/guides/inscriptions.md:129
#: src/guides/inscriptions.md:201
#: src/guides/inscriptions.md:229
msgid ""
"```\n"
"ord wallet receive\n"
"```"
msgstr ""
"```\n"
"ord wallet receive\n"
"```"

#: src/guides/inscriptions.md:133
msgid "And send it some funds."
msgstr "E envie alguns fundos."

#: src/guides/inscriptions.md:135
msgid "You can see pending transactions with:"
msgstr "Você pode ver transações pendentes com:"

#: src/guides/inscriptions.md:137
#: src/guides/inscriptions.md:213
#: src/guides/inscriptions.md:240
msgid ""
"```\n"
"ord wallet transactions\n"
"```"
msgstr ""
"```\n"
"ord wallet transactions\n"
"```"

#: src/guides/inscriptions.md:141
msgid ""
"Once the transaction confirms, you should be able to see the transactions "
"outputs with `ord wallet outputs`."
msgstr ""
"Assim que a transação for confirmada, você poderá ver as saídas da transação "
"com `ord wallet outputs`."

#: src/guides/inscriptions.md:144
msgid "Creating Inscription Content"
msgstr "Criando conteúdo de inscrição"

#: src/guides/inscriptions.md:147
msgid ""
"Sats can be inscribed with any kind of content, but the `ord` wallet only "
"supports content types that can be displayed by the `ord` block explorer."
msgstr ""
"Sats pode ser inscrito com qualquer tipo de conteúdo, mas a carteira `ord` "
"só suporta tipos de conteúdo que podem ser exibidos pelo explorador de blocos `ord`."

#: src/guides/inscriptions.md:150
msgid ""
"Additionally, inscriptions are included in transactions, so the larger the "
"content, the higher the fee that the inscription transaction must pay."
msgstr ""
"Além disso, as inscrições estão incluídas nas transações, portanto, quanto maior "
"o conteúdo, maior será a taxa que a transação de inscrição deverá pagar."

#: src/guides/inscriptions.md:153
msgid ""
"Inscription content is included in transaction witnesses, which receive the "
"witness discount. To calculate the approximate fee that an inscribe "
"transaction will pay, divide the content size by four and multiply by the "
"fee rate."
msgstr ""
"O conteúdo da inscrição está incluído nas testemunhas da transação, que recebem "
"o desconto de testemunha. Para calcular a taxa aproximada que uma transação de "
"inscrição pagará, divida o tamanho do conteúdo por quatro e multiplique pela "
"taxa da rede." BOOKMARK

#: src/guides/inscriptions.md:157
msgid ""
"Inscription transactions must be less than 400,000 weight units, or they "
"will not be relayed by Bitcoin Core. One byte of inscription content costs "
"one weight unit. Since an inscription transaction includes not just the "
"inscription content, limit inscription content to less than 400,000 weight "
"units. 390,000 weight units should be safe."
msgstr ""
"As transações de inscrição devem ter menos de 400.000 unidades de peso ou "
"não serão retransmitidas pelo Bitcoin Core. Um byte de conteúdo de inscrição "
"custa uma unidade de peso. Como uma transação de inscrição não inclui apenas "
"o conteúdo da inscrição, limite o conteúdo da inscrição a menos de 400.000 "
"unidades de peso. 390.000 unidades de peso é uma margem segura."

#: src/guides/inscriptions.md:163
msgid "Creating Inscriptions"
msgstr "Criando Inscrições"

#: src/guides/inscriptions.md:166
msgid "To create an inscription with the contents of `FILE`, run:"
msgstr "Para criar uma inscrição com o conteúdo de `ARQUIVO`, execute:"

#: src/guides/inscriptions.md:168
msgid ""
"```\n"
"ord wallet inscribe --fee-rate FEE_RATE --file FILE\n"
"```"
msgstr ""
"```\n"
"ord wallet inscribe --fee-rate TAXA --file ARQUIVO\n"
"```"

#: src/guides/inscriptions.md:172
msgid ""
"Ord will output two transactions IDs, one for the commit transaction, and "
"one for the reveal transaction, and the inscription ID. Inscription IDs are "
"of the form `TXIDiN`, where `TXID` is the transaction ID of the reveal "
"transaction, and `N` is the index of the inscription in the reveal "
"transaction."
msgstr ""
"Ord produzirá dois IDs de transação, um para a transação de confirmação, "
"outro para a transação de revelação, e o ID de inscrição. Os IDs de inscrição "
"têm o formato `TXIDiN`, onde `TXID` é o ID da transação de revelação e `N` "
"é o índice da inscrição na transação de revelação."

#: src/guides/inscriptions.md:177
msgid ""
"The commit transaction commits to a tapscript containing the content of the "
"inscription, and the reveal transaction spends from that tapscript, "
"revealing the content on chain and inscribing it on the first sat of the "
"input that contains the corresponding tapscript."
msgstr ""
"A transação de commit confirma um tapscript contendo o conteúdo da inscrição, "
"e a transação de revelação gasta a partir desse tapscript, revelando o conteúdo "
"na cadeia e inscrevendo-o no primeiro sat da entrada "
"que contém o tapscript correspondente."

#: src/guides/inscriptions.md:182
msgid ""
"Wait for the reveal transaction to be mined. You can check the status of the "
"commit and reveal transactions using  [the mempool.space block "
"explorer](https://mempool.space/)."
msgstr ""
"Aguarde até que a transação de revelação seja extraída. Você pode verificar o "
"status do commit e revelar transações usando [o explorador de blocos "
"mempool.space](https://mempool.space/)."

#: src/guides/inscriptions.md:186
msgid ""
"Once the reveal transaction has been mined, the inscription ID should be "
"printed when you run:"
msgstr ""
"Depois que a transação de revelação for minerada, o ID da inscrição deverá "
"ser impresso quando você executar:"

#: src/guides/inscriptions.md:189
#: src/guides/inscriptions.md:220
#: src/guides/inscriptions.md:246
msgid ""
"```\n"
"ord wallet inscriptions\n"
"```"
msgstr ""
"```\n"
"ord wallet inscriptions\n"
"```"

#: src/guides/inscriptions.md:193
msgid ""
"And when you visit [the ordinals explorer](https://ordinals.com/) at "
"`ordinals.com/inscription/INSCRIPTION_ID`."
msgstr ""
"E quando você visita [o explorador de Ordinals](https://ordinals.com/) "
"em `ordinals.com/inscription/INSCRIPTION_ID`."

#: src/guides/inscriptions.md:196
msgid "Sending Inscriptions"
msgstr "Enviando Inscrições"

#: src/guides/inscriptions.md:199
msgid "Ask the recipient to generate a new address by running:"
msgstr "Peça ao destinatário para gerar um novo endereço executando:"

#: src/guides/inscriptions.md:205
msgid "Send the inscription by running:"
msgstr "Envie a inscrição executando:"

#: src/guides/inscriptions.md:207
msgid ""
"```\n"
"ord wallet send --fee-rate <FEE_RATE> <ADDRESS> <INSCRIPTION_ID>\n"
"```"
msgstr ""
"```\n"
"ord wallet send --fee-rate <TAXA> <ENDEREÇO> <ID_INSCRIÇãO>\n"
"```"


#: src/guides/inscriptions.md:211
#: src/guides/inscriptions.md:239
msgid "See the pending transaction with:"
msgstr "Veja a transação pendente com:"

#: src/guides/inscriptions.md:217
msgid ""
"Once the send transaction confirms, the recipient can confirm receipt by "
"running:"
msgstr ""
"Assim que a transação de envio for confirmada, o destinatário poderá confirmar "
"o recebimento executando:"

#: src/guides/inscriptions.md:224
msgid "Receiving Inscriptions"
msgstr "Recebendo Inscrições"

#: src/guides/inscriptions.md:227
msgid "Generate a new receive address using:"
msgstr "Gere um novo endereço de recebimento usando:"

#: src/guides/inscriptions.md:233
msgid "The sender can transfer the inscription to your address using:"
msgstr "O remetente pode transferir a inscrição para o seu endereço usando:"

#: src/guides/inscriptions.md:235
msgid ""
"```\n"
"ord wallet send ADDRESS INSCRIPTION_ID\n"
"```"
msgstr ""
"```\n"
"ord wallet send ENDEREÇO ID_INSCRIÇãO\n"
"```"

#: src/guides/inscriptions.md:244
msgid ""
"Once the send transaction confirms, you can can confirm receipt by running:"
msgstr ""
"Assim que a transação de envio for confirmada, você poderá confirmar o recebimento executando:"

#: src/guides/sat-hunting.md:4
msgid ""
"_This guide is out of date. Since it was written, the `ord` binary was "
"changed to only build the full satoshi index when the `--index-sats` flag is "
"supplied. Additionally, `ord` now has a built-in wallet that wraps a Bitcoin "
"Core wallet. See `ord wallet --help`._"
msgstr ""
"_Este guia está desatualizado. Desde que foi escrito, o binário `ord` foi "
"alterado para construir apenas o índice completo de satoshis quando o sinalizador "
"`--index-sats` for fornecido. Além disso, `ord` agora possui uma carteira "
"integrada que envolve uma carteira Bitcoin Core. Veja `ord wallet --help`._"

#: src/guides/sat-hunting.md:9
msgid ""
"Ordinal hunting is difficult but rewarding. The feeling of owning a wallet "
"full of UTXOs, redolent with the scent of rare and exotic sats, is beyond "
"compare."
msgstr ""
"A caça de Ordinals é difícil, mas gratificante. A sensação de possuir uma "
"carteira cheia de UTXOs impregnada com o cheiro de sats raros e exóticos "
"é incomparável."

#: src/guides/sat-hunting.md:12
msgid ""
"Ordinals are numbers for satoshis. Every satoshi has an ordinal number and "
"every ordinal number has a satoshi."
msgstr ""
"Os Ordinals são números para satoshis. Todo satoshi possui um número ordinal "
"e todo número ordinal possui um satoshi."

#: src/guides/sat-hunting.md:15
msgid "Preparation"
msgstr "Preparação"

#: src/guides/sat-hunting.md:18
msgid "There are a few things you'll need before you start."
msgstr "Existem algumas coisas que você precisa antes de começar."

#: src/guides/sat-hunting.md:20
msgid ""
"First, you'll need a synced Bitcoin Core node with a transaction index. To "
"turn on transaction indexing, pass `-txindex` on the command-line:"
msgstr ""
"Primeiro, você precisará de um nó Bitcoin Core sincronizado com um índice de transação. Para ativar a indexação de transações, passe `-txindex` na linha de comando:"

#: src/guides/sat-hunting.md:23
msgid ""
"```sh\n"
"bitcoind -txindex\n"
"```"
msgstr ""
"```sh\n"
"bitcoind -txindex\n"
"```"

#: src/guides/sat-hunting.md:27
msgid ""
"Or put the following in your [Bitcoin configuration "
"file](https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"
msgstr ""
"Ou coloque o seguinte em seu [arquivo de configuração Bitcoin](https://github.com/bitcoin/bitcoin/blob/master/doc/bitcoin-conf.md#configuration-file-path):"

#: src/guides/sat-hunting.md:34
msgid ""
"Launch it and wait for it to catch up to the chain tip, at which point the "
"following command should print out the current block height:"
msgstr ""
"Execute-o e espere que ele alcance a ponta da cadeia, momento em que o seguinte comando deverá imprimir a altura atual do bloco:"

#: src/guides/sat-hunting.md:37
msgid ""
"```sh\n"
"bitcoin-cli getblockcount\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getblockcount\n"
"```"

#: src/guides/sat-hunting.md:41
msgid "Second, you'll need a synced `ord` index."
msgstr "Em segundo lugar, você precisará de um índice `ord` sincronizado."

#: src/guides/sat-hunting.md:43
msgid "Get a copy of `ord` from [the repo](https://github.com/ordinals/ord/)."
msgstr "Obtenha uma cópia do `ord` no [repo](https://github.com/ordinals/ord/)."

#: src/guides/sat-hunting.md:45
msgid ""
"Run `RUST_LOG=info ord index`. It should connect to your bitcoin core node "
"and start indexing."
msgstr ""
"Execute `RUST_LOG = info or index`. Ele deve se conectar ao nó principal do bitcoin e iniciar a indexação."

#: src/guides/sat-hunting.md:48
msgid "Wait for it to finish indexing."
msgstr "Aguarde a conclusão da indexação."

#: src/guides/sat-hunting.md:50
msgid "Third, you'll need a wallet with UTXOs that you want to search."
msgstr "Terceiro, você precisará de uma carteira com UTXOs que deseja pesquisar."

#: src/guides/sat-hunting.md:52
msgid "Searching for Rare Ordinals"
msgstr "Procurando por Ordinais Raros"

#: src/guides/sat-hunting.md:55
msgid "Searching for Rare Ordinals in a Bitcoin Core Wallet"
msgstr "Procurando por Ordinais Raros em uma Carteira Bitcoin Core"

#: src/guides/sat-hunting.md:57
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"searching for rare ordinals in a Bitcoin Core wallet is Easy. Assuming your "
"wallet is named `foo`:"
msgstr ""
"O comando `ord wallet` é apenas um wrapper da API RPC do Bitcoin Core, portanto, procurar por ordinais raros em uma carteira Bitcoin Core é fácil. Supondo que sua carteira se chame `foo`:"

#: src/guides/sat-hunting.md:61
msgid "Load your wallet:"
msgstr "Carregue sua carteira:"

#: src/guides/sat-hunting.md:63
msgid ""
"```sh\n"
"bitcoin-cli loadwallet foo\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli loadwallet foo\n"
"```"

#: src/guides/sat-hunting.md:67
msgid "Display any rare ordinals wallet `foo`'s UTXOs:"
msgstr "Exiba os UTXOs da carteira `foo` de ordinais raros:"

#: src/guides/sat-hunting.md:69
#: src/guides/sat-hunting.md:132
#: src/guides/sat-hunting.md:233
msgid ""
"```sh\n"
"ord wallet sats\n"
"```"
msgstr ""
"```sh\n"
"ord wallet sats\n"
"```"

#: src/guides/sat-hunting.md:73
msgid "Searching for Rare Ordinals in a Non-Bitcoin Core Wallet"
msgstr "Procurando por ordinais raros em uma carteira que não seja Bitcoin Core"

#: src/guides/sat-hunting.md:75
msgid ""
"The `ord wallet` command is just a wrapper around Bitcoin Core's RPC API, so "
"to search for rare ordinals in a non-Bitcoin Core wallet, you'll need to "
"import your wallet's descriptors into Bitcoin Core."
msgstr ""
"O comando `ord wallet` é apenas um wrapper da API RPC do Bitcoin Core, portanto, para procurar ordinais raros em uma carteira que não seja do Bitcoin Core, "
"você precisará importar os descritores da sua carteira para o Bitcoin Core."

#: src/guides/sat-hunting.md:79
msgid ""
"[Descriptors](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors.md) "
"describe the ways that wallets generate private keys and public keys."
msgstr ""
"[Os descritores](https://github.com/bitcoin/bitcoin/blob/master/doc/descriptors.md) descrevem as maneiras pelas quais as carteiras geram chaves privadas e chaves públicas."

#: src/guides/sat-hunting.md:82
msgid ""
"You should only import descriptors into Bitcoin Core for your wallet's "
"public keys, not its private keys."
msgstr ""
"Você só deve importar descritores para o Bitcoin Core para as chaves públicas da sua carteira, não para as chaves privadas."

#: src/guides/sat-hunting.md:85
msgid ""
"If your wallet's public key descriptor is compromised, an attacker will be "
"able to see your wallet's addresses, but your funds will be safe."
msgstr ""
"Se o descritor de chave pública da sua carteira for comprometido, um invasor poderá ver os endereços da sua carteira, mas seus fundos estarão seguros."

#: src/guides/sat-hunting.md:88
msgid ""
"If your wallet's private key descriptor is compromised, an attacker can "
"drain your wallet of funds."
msgstr ""
"Se o descritor de chave privada da sua carteira estiver comprometido, um invasor poderá drenar os fundos da sua carteira."

#: src/guides/sat-hunting.md:91
msgid ""
"Get the wallet descriptor from the wallet whose UTXOs you want to search for "
"rare ordinals. It will look something like this:"
msgstr ""
"Obtenha o descritor da carteira cujos UTXOs você deseja pesquisar por ordinais raros. Vai parecer algo assim:"

#: src/guides/sat-hunting.md:94
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\n"
"```"

#: src/guides/sat-hunting.md:98
msgid "Create a watch-only wallet named `foo-watch-only`:"
msgstr "CCrie uma carteira somente de leitura chamada `foo-apenas-leitura`:"

#: src/guides/sat-hunting.md:100
msgid ""
"```sh\n"
"bitcoin-cli createwallet foo-watch-only true true\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli createwallet foo-apenas-leitura true true\n"
"```"

#: src/guides/sat-hunting.md:104
msgid "Feel free to give it a better name than `foo-watch-only`!"
msgstr "Sinta-se à vontade para dar um nome melhor do que `foo-apenas-leitura`!"

#: src/guides/sat-hunting.md:106
msgid "Load the `foo-watch-only` wallet:"
msgstr "Carregue a carteira `foo-apenas-leitura`:"

#: src/guides/sat-hunting.md:108
#: src/guides/sat-hunting.md:199
msgid ""
"```sh\n"
"bitcoin-cli loadwallet foo-watch-only\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli loadwallet foo-apenas-leitura\n"
"```"

#: src/guides/sat-hunting.md:112
msgid "Import your wallet descriptors into `foo-watch-only`:"
msgstr "Importe os descritores da sua carteira para `foo-apenas-leitura`:"

#: src/guides/sat-hunting.md:114
msgid ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\", "
"\"timestamp\":0 }]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli importdescriptors \\\n"
"  '[{ \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\", "
"\"timestamp\":0 }]'\n"
"```"

#: src/guides/sat-hunting.md:119
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of `\"timestamp\"` instead of "
"`0`. This will reduce the time it takes for Bitcoin Core to search for your "
"wallet's UTXOs."
msgstr ""
"Se você sabe o timestamp de data/hora Unix de quando sua carteira começou a receber transações, "
"você pode usá-lo para o valor de `\"timestamp\"` em vez de `0`. Isso reduzirá o tempo que o Bitcoin Core leva para procurar os UTXOs da sua carteira."

#: src/guides/sat-hunting.md:124
#: src/guides/sat-hunting.md:225
msgid "Check that everything worked:"
msgstr "Verifique se tudo funcionou:"

#: src/guides/sat-hunting.md:126
#: src/guides/sat-hunting.md:227
msgid ""
"```sh\n"
"bitcoin-cli getwalletinfo\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getwalletinfo\n"
"```"

#: src/guides/sat-hunting.md:130
#: src/guides/sat-hunting.md:231
msgid "Display your wallet's rare ordinals:"
msgstr "Exiba os ordinais raros da sua carteira:"

#: src/guides/sat-hunting.md:136
msgid ""
"Searching for Rare Ordinals in a Wallet that Exports Multi-path Descriptors"
msgstr ""
"Procurando Ordinais Raros em uma Carteira que Exporta Descritores de Múltiplos Caminhos (Multi-Path)"

#: src/guides/sat-hunting.md:138
msgid ""
"Some descriptors describe multiple paths in one descriptor using angle "
"brackets, e.g., `<0;1>`. Multi-path descriptors are not yet supported by "
"Bitcoin Core, so you'll first need to convert them into multiple "
"descriptors, and then import those multiple descriptors into Bitcoin Core."
msgstr ""
"AAlguns descritores descrevem vários caminhos em um descritor usando colchetes angulares, por exemplo, `<0;1>`. "
"Descritores de múltiplos caminhos ainda não são suportados pelo Bitcoin Core, então primeiro você precisará convertê-los em múltiplos descritores "
"e, em seguida, importar esses múltiplos descritores para o Bitcoin Core."

#: src/guides/sat-hunting.md:143
msgid ""
"First get the multi-path descriptor from your wallet. It will look something "
"like this:"
msgstr ""
"Primeiro, obtenha o descritor de múltiplos caminhos da sua carteira. Vai parecer algo assim:"

#: src/guides/sat-hunting.md:146
msgid ""
"```\n"
"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/<0;1>/*)#fw76ulgt\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/<0;1>/*)#fw76ulgt\n"
"```"

#: src/guides/sat-hunting.md:150
msgid "Create a descriptor for the receive address path:"
msgstr "Crie um descritor para o caminho do endereço de recebimento:"

#: src/guides/sat-hunting.md:152
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)\n"
"```"

#: src/guides/sat-hunting.md:156
msgid "And the change address path:"
msgstr "E o caminho do endereço de troco:"

#: src/guides/sat-hunting.md:158
msgid ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)\n"
"```"
msgstr ""
"```\n"
"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)\n"
"```"

#: src/guides/sat-hunting.md:162
msgid ""
"Get and note the checksum for the receive address descriptor, in this case "
"`tpnxnxax`:"
msgstr ""
"Obtenha e anote a soma de verificação (checksum) do descritor do endereço de recebimento, neste caso `tpnxnxax`:"

#: src/guides/sat-hunting.md:165
msgid ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)'\n"
"```"

#: src/guides/sat-hunting.md:170
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#csvefu29\",\n"
"  \"checksum\": \"tpnxnxax\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src/guides/sat-hunting.md:180
msgid "And for the change address descriptor, in this case `64k8wnd7`:"
msgstr "E para o descritor do endereço de troco, neste caso `64k8wnd7`:"

#: src/guides/sat-hunting.md:182
msgid ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli getdescriptorinfo \\\n"
"  "
"'wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)'\n"
"```"

#: src/guides/sat-hunting.md:187
msgid ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"
msgstr ""
"```json\n"
"{\n"
"  \"descriptor\": "
"\"wpkh([bf1dd55e/84'/0'/0']xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#fyfc5f6a\",\n"
"  \"checksum\": \"64k8wnd7\",\n"
"  \"isrange\": true,\n"
"  \"issolvable\": true,\n"
"  \"hasprivatekeys\": false\n"
"}\n"
"```"

#: src/guides/sat-hunting.md:197
msgid "Load the wallet you want to import the descriptors into:"
msgstr "Carregue a carteira para a qual deseja importar os descritores:"

#: src/guides/sat-hunting.md:203
msgid ""
"Now import the descriptors, with the correct checksums, into Bitcoin Core."
msgstr ""
"Agora importe os descritores, com os checksums corretos, para o Bitcoin Core."

#: src/guides/sat-hunting.md:205
msgid ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"
msgstr ""
"```sh\n"
"bitcoin-cli \\\n"
" importdescriptors \\\n"
" '[\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/0/*)#tpnxnxax\"\n"
"     \"timestamp\":0\n"
"   },\n"
"   {\n"
"     \"desc\": "
"\"wpkh([bf1dd55e/84h/0h/0h]xpub6CcJtWcvFQaMo39ANFi1MyXkEXM8T8ZhnxMtSjQAdPmVSTHYnc8Hwoc11VpuP8cb8JUTboZB5A7YYGDonYySij4XTawL6iNZvmZwdnSEEep/1/*)#64k8wnd7\",\n"
"     \"timestamp\":0\n"
"   }\n"
" ]'\n"
"```"

#: src/guides/sat-hunting.md:220
msgid ""
"If you know the Unix timestamp when your wallet first started receive "
"transactions, you may use it for the value of the `\"timestamp\"` fields "
"instead of `0`. This will reduce the time it takes for Bitcoin Core to "
"search for your wallet's UTXOs."
msgstr ""
"Se você conhece o timestamp de data/hora Unix quando sua carteira começou a receber transações, "
"você pode usá-lo para o valor dos campos `\"timestamp\"` em vez de `0`. Isso reduzirá o tempo que o Bitcoin Core leva para procurar os UTXOs da sua carteira."

#: src/guides/sat-hunting.md:237
msgid "Exporting Descriptors"
msgstr "Exportando Descritores"

#: src/guides/sat-hunting.md:241
msgid ""
"Navigate to the `Settings` tab, then to `Script Policy`, and press the edit "
"button to display the descriptor."
msgstr ""
"Navegue até a guia `Configurações`, depois até `Política de script` e pressione o botão de edição para exibir o descritor."

#: src/guides/sat-hunting.md:244
msgid "Transferring Ordinals"
msgstr "Transferindo Ordinals"

#: src/guides/sat-hunting.md:246
msgid ""
"The `ord` wallet supports transferring specific satoshis. You can also use "
"`bitcoin-cli` commands `createrawtransaction`, "
"`signrawtransactionwithwallet`, and `sendrawtransaction`, how to do so is "
"complex and outside the scope of this guide."
msgstr ""
"A carteira `ord` suporta a transferência de satoshis específicos. Você também pode usar os comandos de `bitcoin-cli` `createrawtransaction`, "
"`signrawtransactionwithwallet` e `sendrawtransaction`, mas fazer isso é complexo e está fora do escopo deste guia."

#: src/guides/collecting.md:4
msgid ""
"Currently, [ord](https://github.com/ordinals/ord/) is the only wallet "
"supporting sat-control and sat-selection, which are required to safely store "
"and send rare sats and inscriptions, hereafter ordinals."
msgstr ""
"Atualmente, [ord](https://github.com/ordinals/ord/) é a única carteira que suporta controle e seleção de sats, que são necessários para armazenar e enviar com segurança sats e inscrições raras, doravante ordinais."

#: src/guides/collecting.md:8
msgid ""
"The recommended way to send, receive, and store ordinals is with `ord`, but "
"if you are careful, it is possible to safely store, and in some cases send, "
"ordinals with other wallets."
msgstr ""
"A forma recomendada de enviar, receber e armazenar ordinais é com `ord`, mas se você for cuidadoso, é possível armazenar com segurança e, em alguns casos, enviar ordinais com outras carteiras."

#: src/guides/collecting.md:12
msgid ""
"As a general note, receiving ordinals in an unsupported wallet is not "
"dangerous. Ordinals can be sent to any bitcoin address, and are safe as long "
"as the UTXO that contains them is not spent. However, if that wallet is then "
"used to send bitcoin, it may select the UTXO containing the ordinal as an "
"input, and send the inscription or spend it to fees."
msgstr ""
"Como observação geral, receber ordinais em uma carteira não suportada não é perigoso. "
"Os ordinais podem ser enviados para qualquer endereço bitcoin e são seguros desde que o UTXO que os contém não seja gasto. "
"Porém, se essa carteira for usada para enviar bitcoin, ela poderá selecionar o UTXO contendo o ordinal como entrada e enviar a inscrição ou gastá-la em taxas."

#: src/guides/collecting.md:18
msgid ""
"A [guide](./collecting/sparrow-wallet.md) to creating an `ord`\\-compatible "
"wallet with [Sparrow Wallet](https://sparrowwallet.com/), is available in "
"this handbook."
msgstr ""
"Um [guia](./collecting/sparrow-wallet.md) para criar uma carteira compatível com `ord` com [Sparrow Wallet](https://sparrowwallet.com/) está disponível neste manual."

#: src/guides/collecting.md:21
msgid ""
"Please note that if you follow this guide, you should not use the wallet you "
"create to send BTC, unless you perform manual coin-selection to avoid "
"sending ordinals."
msgstr ""
"Observe que se você seguir este guia, não deverá usar a carteira que criou para enviar BTC, "
"a menos que realize a seleção manual de moedas para evitar o envio de ordinais."

#: src/guides/collecting/sparrow-wallet.md:1
msgid "Collecting Inscriptions and Ordinals with Sparrow Wallet"
msgstr "Colecionando Inscrições e Ordinals com Sparrow"

#: src/guides/collecting/sparrow-wallet.md:4
msgid ""
"Users who cannot or have not yet set up the "
"[ord](https://github.com/ordinals/ord) wallet can receive inscriptions and "
"ordinals with alternative bitcoin wallets, as long as they are _very_ "
"careful about how they spend from that wallet."
msgstr ""
"Usuários que não podem ou ainda não configuraram a carteira [ord](https://github.com/ordinals/ord) podem receber inscrições e ordinais com carteiras bitcoin alternativas, "
"desde que sejam _muito_ cuidadosos sobre como gastam fundos daquela carteira."

#: src/guides/collecting/sparrow-wallet.md:6
msgid ""
"This guide gives some basic steps on how to create a wallet with [Sparrow "
"Wallet](https://sparrowwallet.com/) which is compatible with `ord` and can "
"be later imported into `ord`"
msgstr ""
"Este guia fornece alguns passos básicos sobre como criar uma carteira com [Sparrow Wallet](https://sparrowwallet.com/) que é compatível com `ord` e pode ser posteriormente importada para `ord`."

#: src/guides/collecting/sparrow-wallet.md:8
msgid "⚠️⚠️ Warning!! ⚠️⚠️"
msgstr "⚠️⚠️ Aviso!! ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:9
msgid ""
"As a general rule if you take this approach, you should use this wallet with "
"the Sparrow software as a receive-only wallet."
msgstr ""
"Como regra geral, se você adotar essa abordagem, deverá usar esta carteira com o software Sparrow como uma carteira somente para recebimento."

#: src/guides/collecting/sparrow-wallet.md:11
msgid ""
"Do not spend any satoshis from this wallet unless you are sure you know what "
"you are doing. You could very easily inadvertently lose access to your "
"ordinals and inscriptions if you don't heed this warning."
msgstr ""
"Não gaste nenhum satoshi desta carteira, a menos que tenha certeza de que sabe o que está fazendo. Você pode facilmente perder inadvertidamente o acesso aos seus ordinais e inscrições se não prestar atenção a este aviso."

#: src/guides/collecting/sparrow-wallet.md:13
msgid "Wallet Setup & Receiving"
msgstr "Configuração da Carteira & Recebendo"

#: src/guides/collecting/sparrow-wallet.md:15
msgid ""
"Download the Sparrow Wallet from the [releases "
"page](https://sparrowwallet.com/download/) for your particular operating "
"system."
msgstr ""
"Baixe a Sparrow Wallet na [página de lançamentos](https://sparrowwallet.com/download/) para seu sistema operacional específico."

#: src/guides/collecting/sparrow-wallet.md:17
msgid "Select `File -> New Wallet` and create a new wallet called `ord`."
msgstr "Selecione `File -> New Wallet` e crie uma nova carteira chamada `ord`."

#: src/guides/collecting/sparrow-wallet.md:19
msgid "![](images/wallet_setup_01.png)"
msgstr "![](images/wallet_setup_01.png)"

#: src/guides/collecting/sparrow-wallet.md:21
msgid ""
"Change the `Script Type` to `Taproot (P2TR)` and select the `New or Imported "
"Software Wallet` option."
msgstr ""
"Altere o `Script Type` (Tipo de script) para `Taproot (P2TR)` e selecione a opção `New or Imported Software Wallet`."

#: src/guides/collecting/sparrow-wallet.md:23
msgid "![](images/wallet_setup_02.png)"
msgstr "![](images/wallet_setup_02.png)"

#: src/guides/collecting/sparrow-wallet.md:25
msgid ""
"Select `Use 12 Words` and then click `Generate New`. Leave the passphrase "
"blank."
msgstr ""
"Selecione `Use 12 Words` (Usar 12 palavras) e clique em `Generate New` (Gerar novo). Deixe a passphrase em branco."

#: src/guides/collecting/sparrow-wallet.md:27
msgid "![](images/wallet_setup_03.png)"
msgstr "![](images/wallet_setup_03.png)"

#: src/guides/collecting/sparrow-wallet.md:29
msgid ""
"A new 12 word BIP39 seed phrase will be generated for you. Write this down "
"somewhere safe as this is your backup to get access to your wallet. NEVER "
"share or show this seed phrase to anyone else."
msgstr ""
"Uma nova frase inicial BIP39 de 12 palavras será gerada para você. Anote isso em algum lugar seguro, pois este é o seu backup para ter acesso à sua carteira. NUNCA compartilhe ou mostre esta frase-semente para ninguém."

#: src/guides/collecting/sparrow-wallet.md:31
msgid "Once you have written down the seed phrase click `Confirm Backup`."
msgstr "Depois de escrever a frase inicial, clique em `Confirm Backup`."

#: src/guides/collecting/sparrow-wallet.md:33
msgid "![](images/wallet_setup_04.png)"
msgstr "![](images/wallet_setup_04.png)"

#: src/guides/collecting/sparrow-wallet.md:35
msgid ""
"Re-enter the seed phrase which you wrote down, and then click `Create "
"Keystore`."
msgstr "Digite novamente a frase inicial que você anotou e clique em `Create Keystore`."

#: src/guides/collecting/sparrow-wallet.md:37
msgid "![](images/wallet_setup_05.png)"
msgstr "![](images/wallet_setup_05.png)"

#: src/guides/collecting/sparrow-wallet.md:39
msgid "Click `Import Keystore`."
msgstr "Clique em `Import Keystore`."

#: src/guides/collecting/sparrow-wallet.md:41
msgid "![](images/wallet_setup_06.png)"
msgstr "![](images/wallet_setup_06.png)"

#: src/guides/collecting/sparrow-wallet.md:43
msgid "Click `Apply`. Add a password for the wallet if you want to."
msgstr "Clique em `Apply` (aplicar). Adicione uma senha para a carteira se desejar."

#: src/guides/collecting/sparrow-wallet.md:45
msgid "![](images/wallet_setup_07.png)"
msgstr "![](images/wallet_setup_07.png)"

#: src/guides/collecting/sparrow-wallet.md:47
msgid ""
"You now have a wallet which is compatible with `ord`, and can be imported "
"into `ord` using the BIP39 Seed Phrase. To receive ordinals or inscriptions, "
"click on the `Receive` tab and copy a new address."
msgstr ""
"Agora você tem uma carteira compatível com `ord` e pode ser importada para `ord` usando a frase inicial BIP39. Para receber ordinais ou inscrições, clique na aba `Receive` e copie um novo endereço."

#: src/guides/collecting/sparrow-wallet.md:49
msgid ""
"Each time you want to receive you should use a brand-new address, and not "
"re-use existing addresses."
msgstr ""
"Cada vez que desejar receber, você deve usar um endereço totalmente novo e não reutilizar endereços existentes."

#: src/guides/collecting/sparrow-wallet.md:51
msgid ""
"Note that bitcoin is different to some other blockchain wallets, in that "
"this wallet can generate an unlimited number of new addresses. You can "
"generate a new address by clicking on the `Get Next Address` button. You can "
"see all of your addresses in the `Addresses` tab of the app."
msgstr ""
"Observe que o bitcoin é diferente de algumas outras carteiras de blockchain, pois esta carteira pode gerar um número ilimitado de novos endereços. "
"Você pode gerar um novo endereço clicando no botão `Get Next Address`. Você pode ver todos os seus endereços na guia `Addresses` do aplicativo."

#: src/guides/collecting/sparrow-wallet.md:53
msgid ""
"You can add a label to each address, so you can keep track of what it was "
"used for."
msgstr ""
"Você pode adicionar um rótulo (label) a cada endereço para saber para que ele foi usado."

#: src/guides/collecting/sparrow-wallet.md:55
msgid "![](images/wallet_setup_08.png)"
msgstr "![](images/wallet_setup_08.png)"

#: src/guides/collecting/sparrow-wallet.md:57
msgid "Validating / Viewing Received Inscriptions"
msgstr "Validando / Visualizando Inscrições Recebidas"

#: src/guides/collecting/sparrow-wallet.md:59
msgid ""
"Once you have received an inscription you will see a new transaction in the "
"`Transactions` tab of Sparrow, as well as a new UTXO in the `UTXOs` tab."
msgstr ""
"Depois de receber uma inscrição você verá uma nova transação na aba `Transactions` do Sparrow, bem como um novo UTXO na aba `UTXOs`."

#: src/guides/collecting/sparrow-wallet.md:61
msgid ""
"Initially this transaction may have an \"Unconfirmed\" status, and you will "
"need to wait for it to be mined into a bitcoin block before it is fully "
"received."
msgstr ""
"Inicialmente essa transação pode ter um status \"Unconfirmed\" e você precisará esperar que ela seja minerada em um bloco de bitcoin antes de ser totalmente recebida."

#: src/guides/collecting/sparrow-wallet.md:63
msgid "![](images/validating_viewing_01.png)"
msgstr "![](images/validating_viewing_01.png)"

#: src/guides/collecting/sparrow-wallet.md:65
msgid ""
"To track the status of your transaction you can right-click on it,  select "
"`Copy Transaction ID` and then paste that transaction id into "
"[mempool.space](https://mempool.space)."
msgstr ""
"Para rastrear o status da sua transação, você pode clicar com o botão direito sobre ela, selecionar `Copy Transaction ID` e colar o ID da transação no [mempool.space](https://mempool.space)."

#: src/guides/collecting/sparrow-wallet.md:67
msgid "![](images/validating_viewing_02.png)"
msgstr "![](images/validating_viewing_02.png)"

#: src/guides/collecting/sparrow-wallet.md:69
msgid ""
"Once the transaction has confirmed, you can validate and view your "
"inscription by heading over to the `UTXOs` tab, finding the UTXO you want to "
"check, right-clicking on the `Output` and selecting `Copy Transaction "
"Output`. This transaction output id can then be pasted into the "
"[ordinals.com](https://ordinals.com) search."
msgstr ""
"Uma vez confirmada a transação, você pode validar e visualizar sua inscrição indo até a aba `UTXOs`, encontrando o UTXO que deseja verificar, "
"clicando com o botão direito em `Output` e selecionando `Copy Transaction Output`. Esse ID de saída da transação pode então ser usado para busca em [ordinals.com](https://ordinals.com)."

#: src/guides/collecting/sparrow-wallet.md:72
msgid "Freezing UTXO's"
msgstr "Congelando  UTXOs"

#: src/guides/collecting/sparrow-wallet.md:73
msgid ""
"As explained above, each of your inscriptions is stored in an Unspent "
"Transaction Output (UTXO). You want to be very careful not to accidentally "
"spend your inscriptions, and one way to make it harder for this to happen is "
"to freeze the UTXO."
msgstr ""
"Conforme explicado acima, cada uma de suas inscrições é armazenada em uma saída de transação não gasta (UTXO). "
"Você deve ter muito cuidado para não gastar acidentalmente suas inscrições, e uma maneira de dificultar que isso aconteça é congelar o UTXO."

#: src/guides/collecting/sparrow-wallet.md:75
msgid ""
"To do this, go to the `UTXOs` tab, find the UTXO you want to freeze, "
"right-click on the `Output` and select `Freeze UTXO`."
msgstr ""
"Para fazer isso, vá até a aba `UTXOs`, encontre o UTXO que deseja congelar, clique com o botão direito em `Output` e selecione `Frreeze UTXO` (Congelar UTXO)."

#: src/guides/collecting/sparrow-wallet.md:77
msgid ""
"This UTXO (Inscription) is now un-spendable within the Sparrow Wallet until "
"you unfreeze it."
msgstr ""
"Agora este UTXO (Inscrição) não pode ser gasto na carteira Sparrow até que você o descongele."

#: src/guides/collecting/sparrow-wallet.md:79
msgid "Importing into `ord` wallet"
msgstr "Importando para a carteira `ord`"

#: src/guides/collecting/sparrow-wallet.md:81
msgid ""
"For details on setting up Bitcoin Core and the `ord` wallet check out the "
"[Inscriptions Guide](../inscriptions.md)"
msgstr ""
"Para obter detalhes sobre como configurar o Bitcoin Core e a carteira `ord`, consulte o [Guia de inscrições](../inscriptions.md)."

#: src/guides/collecting/sparrow-wallet.md:83
msgid ""
"When setting up `ord`, instead of running `ord wallet create` to create a "
"brand-new wallet, you can import your existing wallet using `ord wallet "
"restore \"BIP39 SEED PHRASE\"` using the seed phrase you generated with "
"Sparrow Wallet."
msgstr ""
"Ao configurar `ord`, em vez de executar `ord wallet create` para criar uma carteira totalmente nova, você pode importar sua carteira existente usando "
"`ord wallet restore \"BIP39 SEED PHRASE\"` usando a frase inicial que você gerou na sua carteira Sparrow."

#: src/guides/collecting/sparrow-wallet.md:85
msgid ""
"There is currently a [bug](https://github.com/ordinals/ord/issues/1589) "
"which causes an imported wallet to not be automatically rescanned against "
"the blockchain. To work around this you will need to manually trigger a "
"rescan using the bitcoin core cli: `bitcoin-cli -rpcwallet=ord "
"rescanblockchain 767430`"
msgstr ""
"Atualmente existe um [bug](https://github.com/ordinals/ord/issues/1589) que faz com que uma carteira importada não seja automaticamente verificada novamente na blockchain. "
"Para contornar isso, você precisará acionar manualmente uma nova verificação usando o bitcoin core cli: `bitcoin-cli -rpcwallet=ord rescanblockchain 767430`"

#: src/guides/collecting/sparrow-wallet.md:88
msgid ""
"You can then check your wallet's inscriptions using `ord wallet inscriptions`"
msgstr ""
"Você pode então verificar as inscrições da sua carteira usando`ord wallet inscriptions`"

#: src/guides/collecting/sparrow-wallet.md:90
msgid ""
"Note that if you have previously created a wallet with `ord`, then you will "
"already have a wallet with the default name, and will need to give your "
"imported wallet a different name. You can use the `--wallet` parameter in "
"all `ord` commands to reference a different wallet, eg:"
msgstr ""
"Observe que se você já criou uma carteira com `ord`, então você já terá uma carteira com o nome padrão e precisará dar um nome diferente à sua carteira importada. "
"Você pode usar o parâmetro `--wallet` em todos os comandos `ord` para referenciar uma carteira diferente, por exemplo:"

#: src/guides/collecting/sparrow-wallet.md:92
msgid "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"
msgstr "`ord --wallet ord_from_sparrow wallet restore \"BIP39 SEED PHRASE\"`"

#: src/guides/collecting/sparrow-wallet.md:94
msgid "`ord --wallet ord_from_sparrow wallet inscriptions`"
msgstr "`ord --wallet ord_from_sparrow wallet inscriptions`"

#: src/guides/collecting/sparrow-wallet.md:96
msgid "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"
msgstr "`bitcoin-cli -rpcwallet=ord_from_sparrow rescanblockchain 767430`"

#: src/guides/collecting/sparrow-wallet.md:98
msgid "Sending inscriptions with Sparrow Wallet"
msgstr "Enviando inscrições com a Sparrow Wallet"

#: src/guides/collecting/sparrow-wallet.md:100
msgid "⚠️⚠️ Warning ⚠️⚠️"
msgstr "⚠️⚠️ Aviso ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:101
msgid ""
"While it is highly recommended that you set up a bitcoin core node and run "
"the `ord` software, there are certain limited ways you can send inscriptions "
"out of Sparrow Wallet in a safe way. Please note that this is not "
"recommended, and you should only do this if you fully understand what you "
"are doing."
msgstr ""
"Embora seja altamente recomendado que você configure um nó de bitcoin core e execute o software `ord`, existem certas maneiras limitadas de "
"enviar inscrições da Sparrow Wallet de maneira segura. Observe que isso não é recomendado e você só deve fazer isso se compreender totalmente o que está fazendo."

#: src/guides/collecting/sparrow-wallet.md:103
msgid ""
"Using the `ord` software will remove much of the complexity we are "
"describing here, as it is able to automatically and safely handle sending "
"inscriptions in an easy way."
msgstr ""
"O uso do software `ord` removerá grande parte da complexidade que descrevemos aqui, pois ele é capaz de lidar com o envio de inscrições de maneira fácil e automática."

#: src/guides/collecting/sparrow-wallet.md:105
msgid "⚠️⚠️ Additional Warning ⚠️⚠️"
msgstr "⚠️⚠️ Aviso Adicional ⚠️⚠️"

#: src/guides/collecting/sparrow-wallet.md:106
msgid ""
"Don't use your sparrow inscriptions wallet to do general sends of "
"non-inscription bitcoin. You can setup a separate wallet in sparrow if you "
"need to do normal bitcoin transactions, and keep your inscriptions wallet "
"separate."
msgstr ""
"Não use sua carteira Sparrow com inscrições para fazer envios gerais de bitcoins sem inscrição. "
"Você pode configurar uma carteira separada no Sparrow se precisar fazer transações normais de Bitcoin e manter sua carteira de inscrições separada."

#: src/guides/collecting/sparrow-wallet.md:108
msgid "Bitcoin's UTXO model"
msgstr "O Modelo UTXO do Bitcoin"

#: src/guides/collecting/sparrow-wallet.md:109
msgid ""
"Before sending any transaction it's important that you have a good mental "
"model for bitcoin's Unspent Transaction Output (UTXO) system. The way "
"Bitcoin works is fundamentally different to many other blockchains such as "
"Ethereum. In Ethereum generally you have a single address in which you store "
"ETH, and you cannot differentiate between any of the ETH -  it is just all a "
"single value of the total amount in that address. Bitcoin works very "
"differently in that we generate a new address in the wallet for each "
"receive, and every time you receive sats to an address in your wallet you "
"are creating a new UTXO. Each UTXO can be seen and managed individually. You "
"can select specific UTXO's which you want to spend, and you can choose not "
"to spend certain UTXO's."
msgstr ""
"Antes de enviar qualquer transação, é importante que você tenha um bom modelo mental para o sistema Unspent Transaction Output (UTXO) do bitcoin. "
"A forma como o Bitcoin funciona é fundamentalmente diferente de muitas outras blockchains, como o Ethereum. No Ethereum geralmente você tem um "
"único endereço no qual armazena ETH e não pode diferenciar nenhum dos ETH - é apenas um valor único do valor total nesse endereço. "
"O Bitcoin funciona de maneira muito diferente, pois geramos um novo endereço na carteira para cada recebimento, e toda vez que você recebe sats "
"em um endereço da sua carteira, você está criando um novo UTXO. Cada UTXO pode ser visto e gerenciado individualmente. "
"Você pode selecionar UTXOs específicos que deseja gastar e pode optar por não gastar determinados UTXOs."

#: src/guides/collecting/sparrow-wallet.md:111
msgid ""
"Some Bitcoin wallets do not expose this level of detail, and they just show "
"you a single summed up value of all the bitcoin in your wallet. However, "
"when sending inscriptions it is important that you use a wallet like Sparrow "
"which allows for UTXO control."
msgstr ""
"Algumas carteiras Bitcoin não expõem esse nível de detalhe e apenas mostram um único valor resumido de todos os bitcoins em sua carteira. "
"Porém, ao enviar inscrições é importante que você utilize uma carteira como a Sparrow que permite o controle de UTXOs."

#: src/guides/collecting/sparrow-wallet.md:113
msgid "Inspecting your inscription before sending"
msgstr "Inspecionando sua inscrição antes de enviar"

#: src/guides/collecting/sparrow-wallet.md:114
msgid ""
"Like we have previously described inscriptions are inscribed onto sats, and "
"sats are stored within UTXOs. UTXO's are a collection of satoshis with some "
"particular value of the number of satoshis (the output value). Usually (but "
"not always) the inscription will be inscribed on the first satoshi in the "
"UTXO."
msgstr ""
"Como descrevemos anteriormente, as inscrições são inscritas em sats e os sats são armazenados em UTXOs. "
"UTXOs são uma coleção de satoshis com algum valor específico do número de satoshis (o valor de saída). "
"Normalmente (mas nem sempre) a inscrição será inscrita no primeiro satoshi do UTXO."

#: src/guides/collecting/sparrow-wallet.md:116
msgid ""
"When inspecting your inscription before sending the main thing you will want "
"to check is which satoshi in the UTXO your inscription is inscribed on."
msgstr ""
"Ao inspecionar sua inscrição antes de enviá-la, o principal que você deve verificar é em qual satoshi do UTXO sua inscrição está inscrita."

#: src/guides/collecting/sparrow-wallet.md:118
msgid ""
"To do this, you can follow the [Validating / Viewing Received "
"Inscriptions](./sparrow-wallet.md#validating--viewing-received-inscriptions) "
"described above to find the inscription page for your inscription on "
"ordinals.com"
msgstr ""
"Para fazer isso, você pode seguir [Validando/Visualizando Inscrições Recebidas](./sparrow-wallet.md#validating--viewing-received-inscriptions) "
"descrito acima para encontrar a página de inscrição para sua inscrição em ordinals.com."

#: src/guides/collecting/sparrow-wallet.md:120
msgid ""
"There you will find some metadata about your inscription which looks like "
"the following:"
msgstr ""
"Lá você encontrará alguns metadados sobre sua inscrição que se parecem com os seguintes:"

#: src/guides/collecting/sparrow-wallet.md:122
msgid "![](images/sending_01.png)"
msgstr "![](images/sending_01.png)"

#: src/guides/collecting/sparrow-wallet.md:124
msgid "There is a few of important things to check here:"
msgstr "Há algumas coisas importantes para verificar aqui:"

#: src/guides/collecting/sparrow-wallet.md:125
msgid ""
"The `output` identifier matches the identifier of the UTXO you are going to "
"send"
msgstr ""
"O identificador `output` corresponde ao identificador do UTXO que você vai enviar."

#: src/guides/collecting/sparrow-wallet.md:126
msgid ""
"The `offset` of the inscription is `0` (this means that the inscription is "
"located on the first sat in the UTXO)"
msgstr ""
"O `offset` da inscrição é `0` (isto significa que a inscrição está localizada no primeiro sat no UTXO)"

#: src/guides/collecting/sparrow-wallet.md:127
msgid ""
"the `output_value` has enough sats to cover the transaction fee (postage) "
"for sending the transaction. The exact amount you will need depends on the "
"fee rate you will select for the transaction"
msgstr ""
"O `output_value` tem sats suficientes para cobrir a taxa de transação (postagem) para enviar a transação. O valor exato que você precisará depende da taxa que você selecionará para a transação."

#: src/guides/collecting/sparrow-wallet.md:129
msgid ""
"If all of the above are true for your inscription, it should be safe for you "
"to send it using the method below."
msgstr ""
"Se todos os itens acima forem verdadeiros para sua inscrição, será seguro enviá-la usando o método abaixo."

#: src/guides/collecting/sparrow-wallet.md:131
msgid ""
"⚠️⚠️ Be very careful sending your inscription particularly if the `offset` "
"value is not `0`. It is not recommended to use this method if that is the "
"case, as doing so you could accidentally send your inscription to a bitcoin "
"miner unless you know what you are doing."
msgstr ""
"⚠️⚠️ Tenha muito cuidado ao enviar sua inscrição, principalmente se o valor `offset` não for `0`. "
"Não é recomendado usar este método se for esse o caso, pois ao fazê-lo você pode enviar acidentalmente "
"sua inscrição para um minerador de bitcoin, a menos que saiba o que está fazendo."

#: src/guides/collecting/sparrow-wallet.md:133
msgid "Sending your inscription"
msgstr "Enviando sua inscrição"

#: src/guides/collecting/sparrow-wallet.md:134
msgid ""
"To send an inscription navigate to the `UTXOs` tab, and find the UTXO which "
"you previously validated contains your inscription."
msgstr ""
"Para enviar uma inscrição navegue até a aba `UTXOs` e encontre o UTXO que você validou anteriormente que contém sua inscrição."

#: src/guides/collecting/sparrow-wallet.md:136
msgid ""
"If you previously froze the UXTO you will need to right-click on it and "
"unfreeze it."
msgstr ""
"Se você congelou o UXTO anteriormente, precisará clicar com o botão direito nele e descongelá-lo."

#: src/guides/collecting/sparrow-wallet.md:138
msgid ""
"Select the UTXO you want to send, and ensure that is the _only_ UTXO is "
"selected. You should see `UTXOs 1/1` in the interface. Once you are sure "
"this is the case you can hit `Send Selected`."
msgstr ""
"Selecione o UTXO que deseja enviar e certifique-se de que _apenas_ aquele UTXO esteja selecionado. "
"Você deverá ver `UTXOs 1/1` na interface. Quando tiver certeza de que este é o caso, você pode clicar em `Send Selected`."

#: src/guides/collecting/sparrow-wallet.md:140
msgid "![](images/sending_02.png)"
msgstr "![](images/sending_02.png)"

#: src/guides/collecting/sparrow-wallet.md:142
msgid ""
"You will then be presented with the transaction construction interface. "
"There is a few things you need to check here to make sure that this is a "
"safe send:"
msgstr ""
"Em seguida, será apresentada a interface de construção da transação. Há algumas coisas que você precisa verificar aqui para ter certeza de que este é um envio seguro:"

#: src/guides/collecting/sparrow-wallet.md:144
msgid ""
"The transaction should have only 1 input, and this should be the UTXO with "
"the label you want to send"
msgstr ""
"A transação deve ter apenas 1 entrada (input), e esta deve ser o UTXO com o rótulo que você deseja enviar"

#: src/guides/collecting/sparrow-wallet.md:145
msgid ""
"The transaction should have only 1 output, which is the address/label where "
"you want to send the inscription"
msgstr ""
"A transação deverá ter apenas 1 saída (output), que é o endereço/etiqueta para onde deseja enviar a inscrição"

#: src/guides/collecting/sparrow-wallet.md:147
msgid ""
"If your transaction looks any different, for example you have multiple "
"inputs, or multiple outputs then this may not be a safe transfer of your "
"inscription, and you should abandon sending until you understand more, or "
"can import into the `ord` wallet."
msgstr ""
"Se a sua transação parecer diferente, por exemplo, você tem múltiplas entradas ou múltiplas saídas, "
"então esta pode não ser uma transferência segura da sua inscrição e você deve abandonar o envio até entender mais, ou importar para a carteira `ord`."

#: src/guides/collecting/sparrow-wallet.md:149
msgid ""
"You should set an appropriate transaction fee, Sparrow will usually "
"recommend a reasonable one, but you can also check "
"[mempool.space](https://mempool.space) to see what the recommended fee rate "
"is for sending a transaction."
msgstr ""
"Você deve definir uma taxa de transação apropriada; o Sparrow geralmente recomendará uma taxa razoável, mas você também pode verificar "
"[mempool.space](https://mempool.space) para ver qual é a taxa de taxa recomendada para o envio de uma transação no momento."

#: src/guides/collecting/sparrow-wallet.md:151
msgid ""
"You should add a label for the recipient address, a label like `alice "
"address for inscription #123` would be ideal."
msgstr ""
"Você deve adicionar um rótulo (label) para o endereço do destinatário, um rótulo como `endereço de Alice para inscrição 123` seria o ideal."

#: src/guides/collecting/sparrow-wallet.md:153
msgid ""
"Once you have checked the transaction is a safe transaction using the checks "
"above, and you are confident to send it you can click `Create Transaction`."
msgstr ""
"Depois de verificar se a transação é segura usando as verificações acima e estiver confiante para enviá-la, você pode clicar em `Create Transaction`."

#: src/guides/collecting/sparrow-wallet.md:155
msgid "![](images/sending_03.png)"
msgstr "![](images/sending_03.png)"

#: src/guides/collecting/sparrow-wallet.md:157
msgid ""
"Here again you can double check that your transaction looks safe, and once "
"you are confident you can click `Finalize Transaction for Signing`."
msgstr ""
"Aqui, novamente, você pode verificar se sua transação parece segura e, quando estiver confiante, clique em `Finalize Transaction for Signing`."

#: src/guides/collecting/sparrow-wallet.md:159
msgid "![](images/sending_04.png)"
msgstr "![](images/sending_04.png)"

#: src/guides/collecting/sparrow-wallet.md:161
msgid "Here you can triple check everything before hitting `Sign`."
msgstr "Aqui você pode verificar tudo três vezes antes de clicar em `Sign` para assinar."

#: src/guides/collecting/sparrow-wallet.md:163
msgid "![](images/sending_05.png)"
msgstr "![](images/sending_05.png)"

#: src/guides/collecting/sparrow-wallet.md:165
msgid ""
"And then actually you get very very last chance to check everything before "
"hitting `Broadcast Transaction`. Once you broadcast the transaction it is "
"sent to the bitcoin network, and starts being propagated into the mempool."
msgstr ""
"E então, na verdade, você terá a última chance de verificar tudo antes de clicar em `Broadcast Transaction`. Depois de transmitir a transação, ela é enviada para a rede Bitcoin e começa a ser propagada na mempool."

#: src/guides/collecting/sparrow-wallet.md:167
msgid "![](images/sending_06.png)"
msgstr "![](images/sending_06.png)"

#: src/guides/collecting/sparrow-wallet.md:169
msgid ""
"If you want to track the status of your transaction you can copy the "
"`Transaction Id (Txid)` and paste that into "
"[mempool.space](https://mempool.space)"
msgstr ""
"Se quiser acompanhar o status da sua transação, você pode copiar o `Transaction Id (Txid)` e colá-lo em [mempool.space](https://mempool.space)."

#: src/guides/collecting/sparrow-wallet.md:171
msgid ""
"Once the transaction has confirmed you can check the inscription page on "
"[ordinals.com](https://ordinals.com) to validate that it has moved to the "
"new output location and address."
msgstr ""
"Assim que a transação for confirmada, você pode verificar a página de inscrição em [ordinals.com](https://ordinals.com) para validar que ela foi movida para o novo local e endereço de saída."

#: src/guides/collecting/sparrow-wallet.md:173
msgid "Troubleshooting"
msgstr "Solução de problemas"

#: src/guides/collecting/sparrow-wallet.md:175
msgid ""
"Sparrow wallet is not showing a transaction/UTXO, but I can see it on "
"mempool.space!"
msgstr ""
"A carteira Sparrow não mostra uma transação/UTXO, mas posso ver em mempool.space!"

#: src/guides/collecting/sparrow-wallet.md:177
msgid ""
"Make sure that your wallet is connected to a bitcoin node. To validate this, "
"head into the `Preferences`\\-> `Server` settings, and click `Edit Existing "
"Connection`."
msgstr ""
"Certifique-se de que sua carteira esteja conectada a um nó bitcoin. Para validar isso, vá até `Preferences`\\-> configurações de `Server` e clique em `Edit Existing "
"Connection`."

#: src/guides/collecting/sparrow-wallet.md:179
msgid "![](images/troubleshooting_01.png)"
msgstr "![](images/troubleshooting_01.png)"

#: src/guides/collecting/sparrow-wallet.md:181
msgid ""
"From there you can select a node and click `Test Connection` to validate "
"that Sparrow is able to connect successfully."
msgstr ""
"A partir daí você pode selecionar um nó e clicar em `Test Connection` para validar se o Sparrow é capaz de se conectar com sucesso."

#: src/guides/collecting/sparrow-wallet.md:183
msgid "![](images/troubleshooting_02.png)"
msgstr "![](images/troubleshooting_02.png)"

#: src/guides/testing.md:4
msgid ""
"Ord can be tested using the following flags to specify the test network. For "
"more information on running Bitcoin Core for testing, see [Bitcoin's "
"developer "
"documentation](https://developer.bitcoin.org/examples/testing.html)."
msgstr ""
"Ord pode ser testado usando os seguintes sinalizadores para especificar a rede de teste. "
"Para obter mais informações sobre como executar o Bitcoin Core para testes, consulte a [documentação do desenvolvedor do Bitcoin](https://developer.bitcoin.org/examples/testing.html)."

#: src/guides/testing.md:7
msgid ""
"Most `ord` commands in [inscriptions](inscriptions.md) and "
"[explorer](explorer.md) can be run with the following network flags:"
msgstr ""
"A maioria dos comandos `ord` em [inscrições](inscriptions.md) e [explorador](explorer.md) podem ser executados com os seguintes sinalizadores de rede:"

#: src/guides/testing.md:10
msgid "Network"
msgstr "Rede"

#: src/guides/testing.md:10
msgid "Flag"
msgstr "Bandeira"

#: src/guides/testing.md:12
msgid "Testnet"
msgstr "Testnet"

#: src/guides/testing.md:12
msgid "`--testnet` or `-t`"
msgstr "`--testnet` ou `-t`"

#: src/guides/testing.md:13
msgid "Signet"
msgstr "Signet"

#: src/guides/testing.md:13
msgid "`--signet` or `-s`"
msgstr "`--signet` o `-s`"

#: src/guides/testing.md:14
msgid "Regtest"
msgstr "Regtest"

#: src/guides/testing.md:14
msgid "`--regtest` or `-r`"
msgstr "`--regtest` o `-r`"

#: src/guides/testing.md:16
msgid "Regtest doesn't require downloading the blockchain or indexing ord."
msgstr "O Regtest não requer o download da blockchain ou da indexação do ord."

#: src/guides/testing.md:21
msgid "Run bitcoind in regtest with:"
msgstr "Execute o bitcoind em regtest com:"

#: src/guides/testing.md:22
msgid ""
"```\n"
"bitcoind -regtest -txindex\n"
"```"
msgstr ""
"```\n"
"bitcoind -regtest -txindex\n"
"```"

#: src/guides/testing.md:25
msgid "Create a wallet in regtest with:"
msgstr "Crie uma carteira em regtest com:"

#: src/guides/testing.md:26
msgid ""
"```\n"
"ord -r wallet create\n"
"```"
msgstr ""
"```\n"
"ord -r wallet create\n"
"```"

#: src/guides/testing.md:29
msgid "Get a regtest receive address with:"
msgstr "Obtenha um endereço de recebimento de regtest com:"

#: src/guides/testing.md:30
msgid ""
"```\n"
"ord -r wallet receive\n"
"```"
msgstr ""
"```\n"
"ord -r wallet receive\n"
"```"

#: src/guides/testing.md:33
msgid "Mine 101 blocks (to unlock the coinbase) with:"
msgstr "Minere 101 blocos (para desbloquear a coinbase) com:"

#: src/guides/testing.md:34
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 101 <receive address>\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 101 <receive address>\n"
"```"

#: src/guides/testing.md:37
msgid "Inscribe in regtest with:"
msgstr "Inscreva no regtest com:"

#: src/guides/testing.md:38
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file <file>\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file <arquivo>\n"
"```"

#: src/guides/testing.md:41
msgid "Mine the inscription with:"
msgstr "Minere a inscrição:"

#: src/guides/testing.md:42
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 1 <receive address>\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 1 <receive address>\n"
"```"

#: src/guides/testing.md:45
msgid "View the inscription in the regtest explorer:"
msgstr "Veja a inscrição no explorador de blocos regtest:"

#: src/guides/testing.md:46
msgid ""
"```\n"
"ord -r server\n"
"```"
msgstr ""
"```\n"
"ord -r server\n"
"```"

#: src/guides/testing.md:50
msgid "Testing Recursion"
msgstr "Testando Recursão"

#: src/guides/testing.md:53
msgid ""
"When testing out [recursion](../inscriptions/recursion.md), inscribe the "
"dependencies first (example with [p5.js](https://p5js.org)):"
msgstr ""
"Ao testar [recursão](../inscriptions/recursion.md), inscreva as dependências primeiro (exemplo com [p5.js](https://p5js.org)):"

#: src/guides/testing.md:55
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file p5.js\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file p5.js\n"
"```"

#: src/guides/testing.md:58
msgid ""
"This should return a `inscription_id` which you can then reference in your "
"recursive inscription."
msgstr ""
"Isso deve retornar um `inscription_id` que você pode referenciar em sua inscrição recursiva."

#: src/guides/testing.md:61
msgid ""
"ATTENTION: These ids will be different when inscribing on mainnet or signet, "
"so be sure to change those in your recursive inscription for each chain."
msgstr ""
"ATENÇÃO: Esses ids serão diferentes ao inscrever-se na mainnet ou signet, portanto, certifique-se de alterá-los em sua inscrição recursiva para cada cadeia."

#: src/guides/testing.md:65
msgid "Then you can inscribe your recursive inscription with:"
msgstr "Então você pode inscrever sua inscrição recursiva com:"

#: src/guides/testing.md:66
msgid ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file recursive-inscription.html\n"
"```"
msgstr ""
"```\n"
"ord -r wallet inscribe --fee-rate 1 --file recursive-inscription.html\n"
"```"

#: src/guides/testing.md:69
msgid "Finally you will have to mine some blocks and start the server:"
msgstr "Finalmente você terá que minerar alguns blocos e iniciar o servidor:"

#: src/guides/testing.md:70
msgid ""
"```\n"
"bitcoin-cli generatetoaddress 6 <receive address>\n"
"ord -r server\n"
"```"
msgstr ""
"```\n"
"bitcoin-cli generatetoaddress 6 <receive address>\n"
"ord -r server\n"
"```"

#: src/guides/moderation.md:4
msgid ""
"`ord` includes a block explorer, which you can run locally with `ord server`."
msgstr ""
"`ord` inclui um explorador de blocos, que você pode executar localmente com `ord server`."

#: src/guides/moderation.md:6
msgid ""
"The block explorer allows viewing inscriptions. Inscriptions are "
"user-generated content, which may be objectionable or unlawful."
msgstr ""
"O explorador de blocos permite visualizar inscrições. As inscrições são conteúdos gerados pelo usuário, que podem ser questionáveis ou ilegais."

#: src/guides/moderation.md:9
msgid ""
"It is the responsibility of each individual who runs an ordinal block "
"explorer instance to understand their responsibilities with respect to "
"unlawful content, and decide what moderation policy is appropriate for their "
"instance."
msgstr ""
"É de responsabilidade de cada indivíduo que executa uma instância do explorador de bloco ordinal compreender suas responsabilidades em relação ao conteúdo ilegal "
"e decidir qual política de moderação é apropriada para sua própria instância."

#: src/guides/moderation.md:13
msgid ""
"In order to prevent particular inscriptions from being displayed on an `ord` "
"instance, they can be included in a YAML config file, which is loaded with "
"the `--config` option."
msgstr ""
"Para evitar que inscrições específicas sejam exibidas em uma instância `ord`, elas podem ser incluídas em um arquivo de configuração YAML, que é carregado com a opção `--config`."

#: src/guides/moderation.md:17
msgid ""
"To hide inscriptions, first create a config file, with the inscription ID "
"you want to hide:"
msgstr ""
"Para ocultar inscrições, primeiro crie um arquivo de configuração, com o ID da inscrição que deseja ocultar:"

#: src/guides/moderation.md:20
msgid ""
"```yaml\n"
"hidden:\n"
"- 0000000000000000000000000000000000000000000000000000000000000000i0\n"
"```"
msgstr ""
"```yaml\n"
"hidden:\n"
"- 0000000000000000000000000000000000000000000000000000000000000000i0\n"
"```"

#: src/guides/moderation.md:25
msgid ""
"The suggested name for `ord` config files is `ord.yaml`, but any filename "
"can be used."
msgstr ""
"O nome sugerido para os arquivos de configuração do `ord` é `ord.yaml`, mas qualquer nome de arquivo pode ser usado."

#: src/guides/moderation.md:28
msgid "Then pass the file to `--config` when starting the server:"
msgstr "Em seguida, passe o arquivo para `--config` ao iniciar o servidor:"

#: src/guides/moderation.md:30
msgid "`ord --config ord.yaml server`"
msgstr "`ord --config ord.yaml server`"

#: src/guides/moderation.md:32
msgid ""
"Note that the `--config` option comes after `ord` but before the `server` "
"subcommand."
msgstr ""
"Observe que a opção `--config` vem depois de `ord`, mas antes do subcomando `server`."

#: src/guides/moderation.md:35
msgid "`ord` must be restarted in to load changes to the config file."
msgstr "`ord` deve ser reiniciado para carregar as alterações no arquivo de configuração."

#: src/guides/moderation.md:37
msgid "`ordinals.com`"
msgstr "`ordinals.com`"

#: src/guides/moderation.md:40
msgid ""
"The `ordinals.com` instances use `systemd` to run the `ord server` service, "
"which is called `ord`, with a config file located at `/var/lib/ord/ord.yaml`."
msgstr ""
"As instâncias `ordinals.com` usam `systemd` para executar o serviço `ord server`, "
"que é chamado `ord`, com um arquivo de configuração localizado em `/var/lib/ord/ord.yaml`."

#: src/guides/moderation.md:43
msgid "To hide an inscription on `ordinals.com`:"
msgstr "Para ocultar uma inscrição em `ordinals.com`:"

#: src/guides/moderation.md:45
msgid "SSH into the server"
msgstr "Conecte-se ao servidor com SSH"

#: src/guides/moderation.md:46
msgid "Add the inscription ID to `/var/lib/ord/ord.yaml`"
msgstr "Adicione o ID da inscrição a `/var/lib/ord/ord.yaml`"

#: src/guides/moderation.md:47
msgid "Restart the service with `systemctl restart ord`"
msgstr "Reinicie o serviço com `systemctl restart ord`"

#: src/guides/moderation.md:48
msgid "Monitor the restart with `journalctl -u ord`"
msgstr "Monitore a reinicialização com `journalctl -u ord`"

#: src/guides/moderation.md:50
msgid ""
"Currently, `ord` is slow to restart, so the site will not come back online "
"immediately."
msgstr ""
"Atualmente, `ord` demora para reiniciar, então o site não voltará a ficar online imediatamente."

#: src/guides/reindexing.md:4
msgid ""
"Sometimes the `ord` database must be reindexed, which means deleting the "
"database and restarting the indexing process with either `ord index update` or "
"`ord server`. Reasons to reindex are:"
msgstr ""
"Às vezes, o banco de dados `ord` deve ser reindexado, o que significa excluir o banco de dados e reiniciar o processo de indexação com `ord index update` ou `ord server`. Os motivos para reindexar são:"

#: src/guides/reindexing.md:8
msgid "A new major release of ord, which changes the database scheme"
msgstr "Uma nova versão principal do ord, que altera o esquema do banco de dados"

#: src/guides/reindexing.md:9
msgid "The database got corrupted somehow"
msgstr "CO banco de dados foi corrompido de alguma forma"

#: src/guides/reindexing.md:11
msgid ""
"The database `ord` uses is called [redb](https://github.com/cberner/redb), "
"so we give the index the default file name `index.redb`. By default we store "
"this file in different locations depending on your operating system."
msgstr ""
"O banco de dados que `ord` usa é chamado [redb](https://github.com/cberner/redb), "
"então damos ao índice o nome de arquivo padrão `index.redb`. Por padrão, armazenamos "
"esse arquivo em locais diferentes, dependendo do seu sistema operacional."

#: src/guides/reindexing.md:15
msgid "Platform"
msgstr "Plataforma"

#: src/guides/reindexing.md:15
msgid "Value"
msgstr "Valor"

#: src/guides/reindexing.md:17
msgid "Linux"
msgstr "Linux"

#: src/guides/reindexing.md:17
msgid "`$XDG_DATA_HOME`/ord or `$HOME`/.local/share/ord"
msgstr "`$XDG_DATA_HOME`/ord ou `$HOME`/.local/share/ord"

#: src/guides/reindexing.md:17
msgid "/home/<USER>/.local/share/ord"
msgstr "/home/<USER>/.local/share/ord"

#: src/guides/reindexing.md:18
msgid "macOS"
msgstr "macOS"

#: src/guides/reindexing.md:18
msgid "`$HOME`/Library/Application Support/ord"
msgstr "`$HOME`/Library/Application Support/ord"

#: src/guides/reindexing.md:18
msgid "/Users/<USER>/Library/Application Support/ord"
msgstr "/Users/<USER>/Library/Application Support/ord"

#: src/guides/reindexing.md:19
msgid "Windows"
msgstr "Windows"

#: src/guides/reindexing.md:19
msgid "`{FOLDERID_RoamingAppData}`\\\\ord"
msgstr "`{FOLDERID_RoamingAppData}`\\\\ord"

#: src/guides/reindexing.md:19
msgid "C:\\Users\\<USER>\\AppData\\Roaming\\ord"
msgstr "C:\\Users\\<USER>\\AppData\\Roaming\\ord"

#: src/guides/reindexing.md:21
msgid ""
"So to delete the database and reindex on MacOS you would have to run the "
"following commands in the terminal:"
msgstr ""
"Portanto, para deletar o banco de dados e reindexar no MacOS você teria que executar os seguintes comandos no terminal:"

#: src/guides/reindexing.md:24
msgid ""
"```bash\n"
"rm ~/Library/Application Support/ord/index.redb\n"
"ord index update\n"
"```"
msgstr ""
"```bash\n"
"rm ~/Library/Application Support/ord/index.redb\n"
"ord index update\n"
"```"

#: src/guides/reindexing.md:29
msgid ""
"You can of course also set the location of the data directory yourself with "
"`ord --datadir <DIR> index update` or give it a specific filename and path "
"with `ord --index <FILENAME> index update`."
msgstr ""
"É claro que você também pode definir a localização do diretório de dados com `ord --datadir <DIR> index update` "
"ou fornecer um nome de arquivo e caminho específicos com `ord --index <ARQUIVO> index update`."

#: src/bounties.md:1
msgid "Ordinal Bounty Hunting Hints"
msgstr "Dicas para Caça à Recompensa de Ordinals"


#: src/bounties.md:4
msgid ""
"The `ord` wallet can send and receive specific satoshis. Additionally, "
"ordinal theory is extremely simple. A clever hacker should be able to write "
"code from scratch to manipulate satoshis using ordinal theory in no time."
msgstr ""
"A carteira `ord` pode enviar e receber satoshis específicos. Além disso, a teoria ordinal é extremamente simples. "
"Um hacker inteligente deve ser capaz de escrever código do zero para manipular satoshis usando a teoria ordinal rapidamente."

#: src/bounties.md:8
msgid ""
"For more information about ordinal theory, check out the [FAQ](./faq.md) for "
"an overview, the "
"[BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) for the "
"technical details, and the [ord repo](https://github.com/ordinals/ord) for "
"the `ord` wallet and block explorer."
msgstr ""
"Para obter mais informações sobre a teoria ordinal, consulte o [FAQ](./faq.md) para uma visão geral, "
"o [BIP](https://github.com/ordinals/ord/blob/master/bip.mediawiki) para os detalhes técnicos e o "
"[repositório ord](https://github.com/ordinals/ord) para a carteira `ord` e o explorador de blocos."

#: src/bounties.md:14
msgid ""
"Satoshi was the original developer of ordinal theory. However, he knew that "
"others would consider it heretical and dangerous, so he hid his knowledge, "
"and it was lost to the sands of time. This potent theory is only now being "
"rediscovered. You can help by researching rare satoshis."
msgstr ""
"Satoshi foi o desenvolvedor original da teoria ordinal. No entanto, ele sabia que outros considerariam isso herético e perigoso, "
"então ele escondeu seu conhecimento e ele se perdeu nas areias do tempo. "
"Esta poderosa teoria só agora está sendo redescoberta. Você pode ajudar pesquisando satoshis raros."

#: src/bounties.md:19
msgid "Good luck and godspeed!"
msgstr "Boa sorte!"

#: src/bounty/0.md:1
msgid "Ordinal Bounty 0"
msgstr "Recompensa Ordinal 0"

#: src/bounty/0.md:4
#: src/bounty/1.md:4
#: src/bounty/2.md:4
#: src/bounty/3.md:4
msgid "Criteria"
msgstr "Critérios"

#: src/bounty/0.md:7
msgid ""
"Send a sat whose ordinal number ends with a zero to the submission address:"
msgstr ""
"Enviar um sat cujo número ordinal termina em zero para o endereço de entrega:"

#: src/bounty/0.md:9
msgid "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"
msgstr "✅: [1857578125803250](https://ordinals.com/ordinal/1857578125803250)"

#: src/bounty/0.md:11
msgid "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"
msgstr "❌: [1857578125803251](https://ordinals.com/ordinal/1857578125803251)"

#: src/bounty/0.md:13
msgid "The sat must be the first sat of the output you send."
msgstr "O sat deve ser o primeiro sat da saída que você envie."

#: src/bounty/0.md:15
#: src/bounty/1.md:14
#: src/bounty/2.md:15
#: src/bounty/3.md:63
msgid "Reward"
msgstr "Recompensa"

#: src/bounty/0.md:18
msgid "100,000 sats"
msgstr "100.000 sats"

#: src/bounty/0.md:20
#: src/bounty/1.md:19
#: src/bounty/2.md:20
#: src/bounty/3.md:70
msgid "Submission Address"
msgstr "Endereço de Entrega"

#: src/bounty/0.md:23
msgid ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"
msgstr ""
"[`1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3`](https://mempool.space/address/1PE7u4wbDP2RqfKN6geD1bG57v9Gj9FXm3)"

#: src/bounty/0.md:25
#: src/bounty/1.md:24
#: src/bounty/2.md:25
#: src/bounty/3.md:75
msgid "Status"
msgstr "Estado"

#: src/bounty/0.md:28
msgid ""
"Claimed by "
"[@count_null](https://twitter.com/rodarmor/status/1560793241473400833)!"
msgstr ""
"Reivindicado por "
"[@count_null](https://twitter.com/rodarmor/status/1560793241473400833)!"

#: src/bounty/1.md:1
msgid "Ordinal Bounty 1"
msgstr "Recompensa Ordinal 1"

#: src/bounty/1.md:7
msgid ""
"The transaction that submits a UTXO containing the oldest sat, i.e., that "
"with the lowest number, amongst all submitted UTXOs will be judged the "
"winner."
msgstr ""
"Será considerada vencedora a transação que entregar um UTXO contendo o sat mais antigo, ou seja, aquele com o menor número, entre todos os UTXOs entregues."

#: src/bounty/1.md:10
msgid ""
"The bounty is open for submissions until block 753984—the first block of "
"difficulty adjustment period 374. Submissions included in block 753984 or "
"later will not be considered."
msgstr ""
"A recompensa está aberta para inscrições até o bloco 753984 – o primeiro bloco do período de ajuste de dificuldade 374. As inscrições incluídas no bloco 753984 ou posterior não serão consideradas."

#: src/bounty/1.md:17
msgid "200,000 sats"
msgstr "200.000 sats"

#: src/bounty/1.md:22
msgid ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"
msgstr ""
"[`145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap`](https://mempool.space/address/145Z7PFHyVrwiMWwEcUmDgFbmUbQSU9aap)"

#: src/bounty/1.md:27
msgid ""
"Claimed by "
"[@ordinalsindex](https://twitter.com/rodarmor/status/1569883266508853251)!"
msgstr ""
"Reivindicado por "
"[@ordinalsindex](https://twitter.com/rodarmor/status/1569883266508853251)!"

#: src/bounty/2.md:1
msgid "Ordinal Bounty 2"
msgstr "Recompensa Ordinal 2"

#: src/bounty/2.md:7
msgid "Send an uncommon  sat to the submission address:"
msgstr "Envie um sat incomum para o endereço de entrega:"

#: src/bounty/2.md:9
msgid "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"
msgstr "✅: [347100000000000](https://ordinals.com/sat/347100000000000)"

#: src/bounty/2.md:11
msgid "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"
msgstr "❌: [6685000001337](https://ordinals.com/sat/6685000001337)"

#: src/bounty/2.md:13
msgid ""
"Confirm that the submission address has not received transactions before "
"submitting your entry. Only the first successful submission will be rewarded."
msgstr ""
"Confirme se o endereço de entrega não recebeu transações antes de enviar sua inscrição. Somente o primeiro envio bem-sucedido será recompensado."

#: src/bounty/2.md:18
msgid "300,000 sats"
msgstr "300.000 sats"

#: src/bounty/2.md:23
msgid ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"
msgstr ""
"[`1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH`](https://mempool.space/address/1Hyr94uypwWq5CQffaXHvwUMEyBPp3TUZH)"

#: src/bounty/2.md:28
msgid ""
"Claimed by "
"[@utxoset](https://twitter.com/rodarmor/status/1582424455615172608)!"
msgstr ""
"Reivindicado por "
"[@utxoset](https://twitter.com/rodarmor/status/1582424455615172608)!"

#: src/bounty/3.md:1
msgid "Ordinal Bounty 3"
msgstr "Recompensa Ordinal 3"

#: src/bounty/3.md:7
msgid ""
"Ordinal bounty 3 has two parts, both of which are based on _ordinal names_. "
"Ordinal names are a modified base-26 encoding of ordinal numbers. To avoid "
"locking short names inside the unspendable genesis block coinbase reward, "
"ordinal names get _shorter_ as the ordinal number gets _longer_. The name of "
"sat 0, the first sat to be mined is `nvtdijuwxlp` and the name of sat "
"2,099,999,997,689,999, the last sat to be mined, is `a`."
msgstr ""
"A Recompensa Ordinal 3 tem duas partes, ambas baseadas em _nomes ordinais_. "
"Os nomes ordinais são uma codificação modificada de base 26 de números ordinais. "
"Para evitar o bloqueio de nomes curtos dentro da recompensa da coinbase do bloco genesis, que não pode ser gasta, "
"os nomes ordinais ficam _mais curtos_ à medida que o número ordinal fica _mais longo_. O nome do sat 0, o primeiro sat a ser extraído é `nvtdijuwxlp` "
"e o nome do sat 2.099.999.997.689.999, o último sat a ser extraído, é `a`."

#: src/bounty/3.md:14
msgid ""
"The bounty is open for submissions until block 840000—the first block after "
"the fourth halvening. Submissions included in block 840000 or later will not "
"be considered."
msgstr ""
"A recompensa está aberta para envios até o bloco 840000 – o primeiro bloco após o quarto halving. "
"As inscrições incluídas no bloco 840000 ou posterior não serão consideradas."

#: src/bounty/3.md:18
msgid ""
"Both parts use [frequency.tsv](frequency.tsv), a list of words and the "
"number of times they occur in the [Google Books Ngram "
"dataset](http://storage.googleapis.com/books/ngrams/books/datasetsv2.html). "
"filtered to only include the names of sats which will have been mined by the "
"end of the submission period, that appear at least 5000 times in the corpus."
msgstr ""
"Ambas as partes usam [frequency.tsv](frequency.tsv), uma lista de palavras e o número de vezes que elas ocorrem "
"no [conjunto de dados Ngram do Google Books](http://storage.googleapis.com/books/ngrams/books/datasetsv2.html) "
"filtrado para incluir apenas os nomes dos sats que terão sido minerados até o final do período de entrega, que aparecem pelo menos 5.000 vezes no corpus."

#: src/bounty/3.md:24
msgid ""
"`frequency.tsv` is a file of tab-separated values. The first column is the "
"word, and the second is the number of times it appears in the corpus. The "
"entries are sorted from least-frequently occurring to most-frequently "
"occurring."
msgstr ""
"`frequency.tsv` é um arquivo de valores separados por tabulações. A primeira coluna é a palavra e a segunda é o número de vezes que ela aparece no corpus. "
"As entradas são classificadas da que ocorre com menos frequência para a que ocorre com mais frequência."

#: src/bounty/3.md:29
msgid ""
"`frequency.tsv` was compiled using [this "
"program](https://github.com/casey/onegrams)."
msgstr ""
"`frequency.tsv` foi compilado usando [este "
"programa](https://github.com/casey/onegrams)."

#: src/bounty/3.md:32
msgid ""
"To search an `ord` wallet for sats with a name in `frequency.tsv`, use the "
"following [`ord`](https://github.com/ordinals/ord) command:"
msgstr ""
"Para pesquisar sats em uma carteira `ord` com um nome em `frequency.tsv`, use o seguinte comando [`ord`](https://github.com/ordinals/ord):"

#: src/bounty/3.md:35
msgid ""
"```\n"
"ord wallet sats --tsv frequency.tsv\n"
"```"
msgstr ""
"```\n"
"ord wallet sats --tsv frequency.tsv\n"
"```"

#: src/bounty/3.md:39
msgid ""
"This command requires the sat index, so `--index-sats` must be passed to ord "
"when first creating the index."
msgstr ""
"Este comando requer o índice sat (sat index), então `--index-sats` deve ser passado ao ord ao criar o índice pela primeira vez."

#: src/bounty/3.md:42
msgid "Part 0"
msgstr "Parte 0"

#: src/bounty/3.md:44
msgid "_Rare sats pair best with rare words._"
msgstr "_Sats raros combinam melhor com palavras raras._"

#: src/bounty/3.md:46
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the lowest number of occurrences in `frequency.tsv` shall be the winner "
"of part 0."
msgstr ""
"A transação que enviar o UTXO contendo o sat cujo nome apareça com menor número de ocorrências em `frequency.tsv` será a vencedora da parte 0."

#: src/bounty/3.md:50
msgid "Part 1"
msgstr "Parte 1"

#: src/bounty/3.md:52
msgid "_Popularity is the font of value._"
msgstr "_Popularidade é a fonte do valor._"

#: src/bounty/3.md:54
msgid ""
"The transaction that submits the UTXO containing the sat whose name appears "
"with the highest number of occurrences in `frequency.tsv` shall be the "
"winner of part 1."
msgstr ""
"A transação que enviar o UTXO contendo o sat cujo nome apareça com maior número de ocorrências em `frequency.tsv` será a vencedora da parte 1."

#: src/bounty/3.md:58
msgid "Tie Breaking"
msgstr "Desempate"

#: src/bounty/3.md:60
msgid ""
"In the case of a tie, where two submissions occur with the same frequency, "
"the earlier submission shall be the winner."
msgstr ""
"Em caso de empate, onde ocorrerem dois envios com a mesma frequência, o envio que foi realizado primeiro será o vencedor."

#: src/bounty/3.md:66
msgid "Part 0: 200,000 sats"
msgstr "Parte 0: 200.000 sats"

#: src/bounty/3.md:67
msgid "Part 1: 200,000 sats"
msgstr "Parte 1: 200.000 sats"

#: src/bounty/3.md:68
msgid "Total: 400,000 sats"
msgstr "Total: 400.000 sats"

#: src/bounty/3.md:73
msgid ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"
msgstr ""
"[`17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg`](https://mempool.space/address/17m5rvMpi78zG8RUpCRd6NWWMJtWmu65kg)"

#: src/bounty/3.md:78
msgid "Unclaimed!"
msgstr "Não foi reivindicado!"
