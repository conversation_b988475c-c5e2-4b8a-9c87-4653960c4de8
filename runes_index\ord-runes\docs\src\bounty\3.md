Ordinal Bounty 3
================

Criteria
--------

Ordinal bounty 3 has two parts, both of which are based on *ordinal names*.
Ordinal names are a modified base-26 encoding of ordinal numbers. To avoid
locking short names inside the unspendable genesis block coinbase reward,
ordinal names get *shorter* as the ordinal number gets *longer*. The name of
sat 0, the first sat to be mined is `nvtdijuwxlp` and the name of sat
2,099,999,997,689,999, the last sat to be mined, is `a`.

The bounty is open for submissions until block 840000—the first block after the
fourth halving. Submissions included in block 840000 or later will not be
considered.

Both parts use [frequency.tsv](frequency.tsv), a list of words and the number
of times they occur in the [Google Books Ngram
dataset](http://storage.googleapis.com/books/ngrams/books/datasetsv2.html).
filtered to only include the names of sats which will have been mined by the
end of the submission period, that appear at least 5000 times in the corpus.

`frequency.tsv` is a file of tab-separated values. The first column is the
word, and the second is the number of times it appears in the corpus. The
entries are sorted from least-frequently occurring to most-frequently
occurring.

`frequency.tsv` was compiled using [this
program](https://github.com/casey/onegrams).

To search an `ord` wallet for sats with a name in `frequency.tsv`, use the
following [`ord`](https://github.com/ordinals/ord) command:

```
ord wallet sats --tsv frequency.tsv
```

This command requires the sat index, so `--index-sats` must be passed to ord
when first creating the index.

### Part 0

*Rare sats pair best with rare words.*

The transaction that submits the UTXO containing the sat whose name appears
with the lowest number of occurrences in `frequency.tsv` shall be the winner of
part 0.

### Part 1

*Popularity is the font of value.*

The transaction that submits the UTXO containing the sat whose name appears
with the highest number of occurrences in `frequency.tsv` shall be the winner
of part 1.

### Tie Breaking

In the case of a tie, where two submissions occur with the same frequency, the
earlier submission shall be the winner.

Reward
------

- Part 0: 200,000 sats
- Part 1: 200,000 sats
- Total: 400,000 sats

Submission Address
------------------

[`**********************************`](https://mempool.space/address/**********************************)

Status
------

Unclaimed!
