name: Release

on:
  push:
    tags:
    - '*'

defaults:
  run:
    shell: bash

jobs:
  release:
    strategy:
      fail-fast: false
      matrix:
        target:
        - aarch64-apple-darwin
        - x86_64-apple-darwin
        - x86_64-pc-windows-msvc
        - x86_64-unknown-linux-gnu
        include:
        - target: aarch64-apple-darwin
          os: macos-latest
          target_rustflags: ''
        - target: x86_64-apple-darwin
          os: macos-latest
          target_rustflags: ''
        - target: x86_64-pc-windows-msvc
          os: windows-latest
          target_rustflags: ''
        - target: x86_64-unknown-linux-gnu
          os: ubuntu-latest
          target_rustflags: ''

    runs-on: ${{matrix.os}}

    steps:
    - uses: actions/checkout@v2

    - name: Install Rust Toolchain Components
      uses: actions-rs/toolchain@v1
      with:
        override: true
        target: ${{ matrix.target }}
        toolchain: stable

    - name: Install Linux Dependencies
      if: ${{ matrix.os == 'ubuntu-latest' }}
      run: |
        sudo apt-get update
        sudo apt-get install musl-tools libssl-dev pkg-config

    - name: Release Type
      id: release-type
      run: |
        if [[ ${{ github.ref }} =~ ^refs/tags/[0-9]+[.][0-9]+[.][0-9]+$ ]]; then
            echo ::set-output name=value::release
        else
            echo ::set-output name=value::prerelease
        fi

    - name: Package
      id: package
      env:
        TARGET: ${{ matrix.target }}
        REF: ${{ github.ref }}
        OS: ${{ matrix.os }}
        TARGET_RUSTFLAGS: ${{ matrix.target_rustflags }}
      run: ./bin/package
      shell: bash

    - name: Publish Archive
      uses: softprops/action-gh-release@v0.1.15
      if: ${{ startsWith(github.ref, 'refs/tags/') }}
      with:
        draft: false
        files: ${{ steps.package.outputs.archive }}
        prerelease: ${{ steps.release-type.outputs.value == 'prerelease' }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
