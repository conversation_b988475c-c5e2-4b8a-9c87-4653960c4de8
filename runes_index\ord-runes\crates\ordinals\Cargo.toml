[package]
name = "ordinals"
version = "0.0.7"
edition = "2021"
description = "Library for interoperating with ordinals and inscriptions"
homepage = "https://github.com/ordinals/ord"
repository = "https://github.com/ordinals/ord"
license = "CC0-1.0"
rust-version = "1.74.0"

[dependencies]
bitcoin = { version = "0.30.1", features = ["rand"] }
derive_more = "0.99.17"
serde = { version = "1.0.137", features = ["derive"] }
serde_with = "3.7.0"
thiserror = "1.0.56"

[dev-dependencies]
serde_json = { version = "1.0.81", features = ["preserve_order"] }
pretty_assertions = "1.2.1"
